server:
  port: 0
spring:
  application:
    name: authorization
  profiles:
    active: core,dev
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        prefix: authorization
        namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
      discovery:
        enabled: true
        server-addr: 127.0.0.1:8848
        namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 127.0.0.1:8079
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
            namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
