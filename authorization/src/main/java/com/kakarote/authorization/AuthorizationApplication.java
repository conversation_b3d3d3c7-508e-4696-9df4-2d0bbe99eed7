package com.kakarote.authorization;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.kakarote.authorization.common.AuthorizationConst;
import com.kakarote.core.CoreApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackageClasses = {CoreApplication.class, AuthorizationApplication.class})
@ComponentScan(basePackageClasses = {CoreApplication.class, AuthorizationApplication.class})
@EnableMethodCache(basePackages = "com.kakarote.authorization",order = -9999)
@EnableCreateCacheAnnotation
public class AuthorizationApplication {

	public static void main(String[] args) {
		SpringApplication.run(AuthorizationApplication.class, args);
		RSA rsa = SecureUtil.rsa(AuthorizationConst.AUTO_LOGIN_PRIVATE_KEY, null);
		System.out.println("解密：" + rsa.decryptStr("IKoY3eG+72K5TBD28IvP5JfYp8ZhK+j4GAiEKo3zD0wYIY9anId94w4jbP/3H8smhef7DyWO+7DBqcymf+S+7SlNQ0tJUE4P41S/qfGOtyoxpXbS5OdD9rPxiVPfTucAFJBuOafTbw/IrY+yomEbg+k3d13ZlOyn/1O/2MnRRZ33Um0nqN8NStt+oLouopOx4MjhkWV2s7rBv8lAmh8sQ68n8hyjtIz8dct9tCbpeO+gCDzCjPumJr4AHUdgdh9BjlE7HAHh+0My3cH1ZO6wzEHQ2Lk/WbuepsJYta+U2QBvBftuNzKSj4qcNikSUc1xG1MsnkADoUHIOvCD/RStyQ==", KeyType.PrivateKey));
	}
}
