package com.kakarote.authorization.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.kakarote.authorization.common.AuthorizationCodeEnum;
import com.kakarote.authorization.entity.AuthorizationUser;
import com.kakarote.authorization.entity.VO.LoginVO;
import com.kakarote.authorization.service.LoginService;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.ParamAspect;
import com.kakarote.core.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;

/**
 * <AUTHOR>
 * 添加权限的controller
 */
@RestController
@Api(tags = "用户登录相关接口")
public class AuthController {

    @Autowired
    private LoginService loginService;

    @RequestMapping(value = "/permission")
    @ParamAspect
    public Result permission(@RequestParam("url") String url, @RequestParam("method") String method, HttpServletRequest request) {
        String token = request.getHeader(Const.TOKEN_NAME);
        String proxyHost = request.getHeader("proxyHost");
        return loginService.permission(token, url, proxyHost);
    }


    @RequestMapping("/getLoginQrCode")
    @ParamAspect
    @ApiOperation(tags = "获取手机端登录二维码", httpMethod = "POST", value = "getLoginQrCode")
    public Result<String> getLoginQrCode(HttpServletRequest request) {
        String loginQrCode = loginService.getLoginQrCode(request);
        return Result.ok(loginQrCode);
    }

    @RequestMapping("/getLoginQrInfo/{token}")
    @ParamAspect
    @ApiOperation(tags = "轮询获取登录信息", httpMethod = "POST", value = "getLoginQrCode")
    public Result<LoginVO> getLoginQrInfo(@PathVariable("token") String token) {
        LoginVO loginQrInfo = loginService.getLoginQrInfo(token);
        return Result.ok(loginQrInfo);
    }

    @RequestMapping("/setQrInfo/{token}")
    @ApiOperation(tags = "手机端设置信息", httpMethod = "POST", value = "getLoginQrCode")
    public Result setQrInfo(@PathVariable("token") String token, HttpServletRequest request) {
        loginService.setQrInfo(token);
        return Result.ok();
    }

    /**
     * 登录方法，限流由sentinel处理
     */
    @PostMapping(value = "/login")
    @ApiOperation(tags = "用户登录", httpMethod = "POST", value = "/doLogin")
    public Result doLogin(@Valid @RequestBody AuthorizationUser user, HttpServletResponse response, HttpServletRequest request) {
        if (StrUtil.isNotEmpty(user.getUsername())) {
            if (StrUtil.trimToNull(user.getUsername()) == null) {
                return Result.error(AuthorizationCodeEnum.AUTHORIZATION_USERNAME_REQUIRED);
            }
            if (StrUtil.trimToNull(user.getPassword()) == null && StrUtil.trimToNull(user.getSmscode()) == null) {
                return Result.error(AuthorizationCodeEnum.AUTHORIZATION_PASSWORD_REQUIRED);
            }
        }
        return loginService.doLogin(user, response, request);
    }

    /**
     * 给统一登录平台提供的自动登录接口
     * 传入格式：RSA加密后的字符串，解密后格式为：用户ID|加密密码|时间戳
     * 例如：cxm|594940393489779789|1640995200000
     * @param token RSA加密的登录信息
     * @return 登录结果，包含token信息
     */
    @GetMapping(value = "/user/autologin")
    @ApiOperation(tags = "自动登录", httpMethod = "GET", value = "/user/autologin")
    public Result autologin(@RequestParam("token") String token, HttpServletResponse response, HttpServletRequest request) {
        if (StrUtil.isBlank(token)) {
            return Result.error(AuthorizationCodeEnum.AUTHORIZATION_LOGIN_ERR);
        }
        return loginService.autoLogin(token, request, response);
    }

    @RequestMapping(value = "/logout")
    @ApiOperation(tags = "用户注销", httpMethod = "GET", value = "/logout")
    @ParamAspect
    public Result logout(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getHeader(Const.TOKEN_NAME);
        if (StrUtil.isNotEmpty(token)) {
            loginService.logout(token);
        }
        String serverName = StrUtil.isNotEmpty(request.getHeader("proxyHost")) ? request.getHeader("proxyHost") : request.getServerName();
        int index = serverName.indexOf(".");
        String tokenName = "User";
        for (String user : Arrays.asList(Const.TOKEN_NAME, tokenName)) {
            Cookie cookie = ServletUtil.getCookie(request, user);
            if (cookie != null) {
                cookie.setMaxAge(0);
                cookie.setValue(null);
                cookie.setPath("/");
                cookie.setDomain(index != -1 ? serverName.substring(index) : serverName);
                response.addCookie(cookie);
            }
        }

        return Result.ok();
    }
}
