package com.kakarote.authorization.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface FindUserMapper {

    /**
     * 根据用户名查询用户
     * @param username:用户名
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     */
    @Select("SELECT a.username,a.password,a.salt,b.company_name FROM wk_admin_user AS a JOIN wk_admin_company AS b ON a.username=b.phone WHERE a.username=#{username}")
    public List<Map<String, Object>> findAllUser(@Param("username") String username);

    /**
     * 根据名字查询用户量
     * @param username:用户名
     * @return java.lang.Integer
     */
    @SqlParser(filter = true)
    @Select("SELECT count(1) FROM wk_admin_user WHERE username = #{username}")
    public Integer queryUserNum(@Param("username") String username);
}
