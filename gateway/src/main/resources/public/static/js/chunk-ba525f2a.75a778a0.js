(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ba525f2a"],{2932:function(e,t,s){},a155:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-dialog",{attrs:{visible:e.dialogVisible,"close-on-click-modal":!1,title:"选择位置",width:"500px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[s("flexbox",{attrs:{align:"stretch"}},[s("flexbox-item",[s("div",{staticClass:"area-title"},[e._v("定位")]),e._v(" "),s("el-autocomplete",{staticStyle:{width:"100%"},attrs:{"fetch-suggestions":e.querySearchAsync,placeholder:"详细位置名称"},on:{blur:e.inputBlur,focus:e.inputFocus,select:e.handleSelect},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.item;return[s("div",{staticClass:"name"},[e._v(e._s(a.address+a.title))])]}}]),model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}}),e._v(" "),s("div",{ref:"chosemap",staticClass:"map"})],1)],1),e._v(" "),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.selectSure}},[e._v("确定")]),e._v(" "),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)},n=[],i=(s("d81d"),s("14d9"),s("e9f5"),s("ab43"),s("d3b7"),s("ac1f"),s("841c"),s("5a8c")),c=s("ed08"),o={name:"ChangeAddress",components:{VDistpicker:i["a"]},props:{show:{type:Boolean,default:!1},value:{type:Object,default:function(){return{}}}},data:function(){return{map:null,searchInput:"",dialogVisible:!1,searchCopyInput:"",pointAddress:null}},computed:{},watch:{show:function(e){var t=this;this.dialogVisible=e,e&&this.$nextTick((function(){Object(c["q"])().then((function(){var e=new BMap.Map(t.$refs.chosemap,{enableMapClick:!0}),s=t.value;e.centerAndZoom(s,14),e.enableScrollWheelZoom(),t.map=e,t.addMarkerLabel(s)}))}))}},mounted:function(){},methods:{querySearchAsync:function(e,t){if(e){var s={onSearchComplete:function(e){if(a.getStatus()==BMAP_STATUS_SUCCESS){for(var s=[],n=0;n<e.getCurrentNumPois();n++)s.push(e.getPoi(n));t(s)}else t([])},pageCapacity:20},a=new BMap.LocalSearch(this.map,s);a.search(e)}else t([])},handleSelect:function(e){this.searchInput=e.address+e.title,this.searchCopyInput=this.searchInput,this.addMarkerLabel(e.point),this.pointAddress=e},inputBlur:function(){this.searchCopyInput!==this.searchInput&&(this.searchInput=this.searchCopyInput)},inputFocus:function(){this.searchCopyInput=this.searchInput},addMarkerLabel:function(e){this.map.clearOverlays(),this.map.centerAndZoom(e,14),this.map.addOverlay(new BMap.Marker(e))},close:function(){this.$emit("close")},selectSure:function(){this.$emit("select",this.pointAddress),this.close()}}},l=o,r=(s("f934"),s("2877")),u=Object(r["a"])(l,a,n,!1,null,"7bf0cc47",null);t["default"]=u.exports},f934:function(e,t,s){"use strict";s("2932")}}]);