(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0230aa6d"],{"375d":function(e,t,l){"use strict";l.r(t);var n=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"system-customer main"},[l("xr-header",{attrs:{label:"自定义字段设置"}}),e._v(" "),l("div",{staticClass:"main-body"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.tableList,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight,"highlight-current-row":""}},[l("el-table-column",{attrs:{width:"100",label:"模块图标"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("div",{staticClass:"table-icon"},[l("i",{class:e.getLableIcon(t.row.label)})])]}}])}),e._v(" "),l("el-table-column",{attrs:{prop:"name",label:"模块","show-overflow-tooltip":""}}),e._v(" "),l("el-table-column",{attrs:{prop:"name",label:"更新时间","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(0==t.row.updateTime?"暂无":t.row.updateTime))])]}}])}),e._v(" "),l("el-table-column",{attrs:{fixed:"right",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(l){e.handleCustomField("edit",t.row,t.$index)}}},[e._v("编辑")])]}}])})],1)],1)],1)},a=[],i=(l("14d9"),l("c73d"),l("ea20")),o=l("f468"),c={name:"CustomField",components:{XrHeader:o["a"]},data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-140,tableList:[]}},created:function(){window.onresize=function(){self.tableHeight=document.documentElement.clientHeight-140},this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,Object(i["C"])().then((function(t){e.tableList=t.data,e.loading=!1})).catch((function(){e.loading=!1}))},handleCustomField:function(e,t,l){"edit"===e&&this.$router.push({name:"customField",params:{type:{1:"crm_leads",2:"crm_customer",3:"crm_contacts",4:"crm_product",5:"crm_business",6:"crm_contract",7:"crm_receivables",8:"crm_receivables_plan",17:"crm_visit",18:"crm_invoice"}[t.label],id:"none",label:t.label}})},getLableIcon:function(e){return{1:"wk wk-leads",2:"wk wk-customer",3:"wk wk-contacts",4:"wk wk-product",5:"wk wk-business",6:"wk wk-contract",7:"wk wk-receivables",8:"wk wk-icon-plan-solid",17:"wk wk-icon-visit-solid",18:"wk wk-invoice"}[e]||"wk wk-icon-all-solid"}}},s=c,r=(l("8c64"),l("2877")),u=Object(r["a"])(s,n,a,!1,null,"46209c24",null);t["default"]=u.exports},"5fcd":function(e,t,l){},"8c64":function(e,t,l){"use strict";l("5fcd")},c73d:function(e,t,l){"use strict";var n=l("23e7"),a=l("cfe9"),i=l("edd0"),o=l("83ab"),c=TypeError,s=Object.defineProperty,r=a.self!==a;try{if(o){var u=Object.getOwnPropertyDescriptor(a,"self");!r&&u&&u.get&&u.enumerable||i(a,"self",{get:function(){return a},set:function(e){if(this!==a)throw new c("Illegal invocation");s(a,"self",{value:e,writable:!0,configurable:!0,enumerable:!0})},configurable:!0,enumerable:!0})}else n({global:!0,simple:!0,forced:r},{self:a})}catch(d){}}}]);