(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-724d1707"],{2602:function(t,e,a){"use strict";a.d(e,"r",(function(){return r})),a.d(e,"s",(function(){return o})),a.d(e,"t",(function(){return s})),a.d(e,"o",(function(){return i})),a.d(e,"m",(function(){return c})),a.d(e,"n",(function(){return u})),a.d(e,"c",(function(){return p})),a.d(e,"f",(function(){return l})),a.d(e,"g",(function(){return d})),a.d(e,"h",(function(){return m})),a.d(e,"u",(function(){return h})),a.d(e,"v",(function(){return f})),a.d(e,"x",(function(){return b})),a.d(e,"a",(function(){return y})),a.d(e,"b",(function(){return T})),a.d(e,"i",(function(){return C})),a.d(e,"j",(function(){return j})),a.d(e,"p",(function(){return v})),a.d(e,"q",(function(){return g})),a.d(e,"l",(function(){return O})),a.d(e,"k",(function(){return x})),a.d(e,"d",(function(){return S})),a.d(e,"w",(function(){return F})),a.d(e,"e",(function(){return U}));var n=a("b775");function r(t){return Object(n["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(n["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(n["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(n["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(n["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(n["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(n["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(n["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(n["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(n["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(n["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(n["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(n["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(n["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(n["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(t){return Object(n["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function F(t){return Object(n["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function U(t){return Object(n["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),s=a("7b0b"),i=a("07fa"),c=a("083a"),u=a("577e"),p=a("d039"),l=a("addb"),d=a("a640"),m=a("3f7e"),h=a("99f4"),f=a("1212"),b=a("ea83"),y=[],T=r(y.sort),C=r(y.push),j=p((function(){y.sort(void 0)})),v=p((function(){y.sort(null)})),g=d("sort"),O=!p((function(){if(f)return f<70;if(!(m&&m>3)){if(h)return!0;if(b)return b<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:e+n,v:a})}for(y.sort((function(t,e){return e.v-t.v})),n=0;n<y.length;n++)e=y[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),x=j||!v||!g||!O,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:u(e)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:x},{sort:function(t){void 0!==t&&o(t);var e=s(this);if(O)return void 0===t?T(e):T(e,t);var a,n,r=[],u=i(e);for(n=0;n<u;n++)n in e&&C(r,e[n]);l(r,S(t)),a=i(r),n=0;while(n<a)e[n]=r[n++];while(n<u)c(e,n++);return e}})},7998:function(t,e,a){},bf0a:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return o})),a.d(e,"e",(function(){return s})),a.d(e,"f",(function(){return i})),a.d(e,"c",(function(){return c})),a.d(e,"d",(function(){return u})),a.d(e,"g",(function(){return p})),a.d(e,"h",(function(){return l}));a("d3b7");var n=a("b775");function r(t){var e=t.tableType;if(!e)return Promise.reject();delete t.tableType;var a={count:"biEmployee/contractNumStats",money:"biEmployee/contractMoneyStats",back:"biEmployee/receivablesMoneyStats"}[e];return a?Object(n["a"])({url:a,method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}}):Promise.reject()}function o(t){var e=t.tableType;if(!e)return Promise.reject();delete t.tableType;var a={count:"biEmployee/contractNumStatsExport",money:"biEmployee/contractMoneyStatsExport",back:"biEmployee/receivablesMoneyStatsExport"}[e];return a?Object(n["a"])({url:a,method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}}):Promise.reject()}function s(t){return Object(n["a"])({url:"biEmployee/totalContract",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(n["a"])({url:"biEmployee/totalContractExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biEmployee/invoiceStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"biEmployee/invoiceStatsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"crmBiSearch/searchInvoicePageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"crmBiSearch/searchReceivablesPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},c48e:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"客户总量分析","module-type":"customer"},on:{load:function(e){t.loading=!0},change:t.searchClick}}),t._v(" "),a("div",{staticClass:"content"},[t._m(0),t._v(" "),a("div",{staticClass:"table-content"},[a("div",{staticClass:"handle-bar"},[a("el-button",{staticClass:"export-btn",attrs:{type:"primary",size:"small"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),t.showTable?a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{data:t.list,"cell-class-name":t.cellClassName,stripe:t.WKConfig.tableStyle.stripe,"summary-method":t.getSummaries,height:"400","show-summary":"",size:"small","highlight-current-row":""},on:{"row-click":t.handleRowClick,"sort-change":function(e){var a=e.prop,n=e.order;return t.mixinSortFn(t.list,a,n)}}},[t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,"min-width":"130",sortable:"custom","show-overflow-tooltip":""}})})),t._v(" "),a("template",{slot:"empty"},[a("el-empty",{attrs:{description:"暂无数据","image-size":120}})],1)],2):t._e()],1)]),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},r=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"axis-content"},[a("div",{attrs:{id:"axismain"}})])}],o=a("5530"),s=(a("d81d"),a("14d9"),a("13d5"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("d3b7"),a("159b"),a("df55")),i=a("f643"),c=a("b80b"),u=a("f4f4"),p=a("313e"),l=a("2602"),d=a("ef89"),m=a("bf0a"),h={name:"CustomerTotalStatistics",mixins:[s["a"],i["a"],u["a"],c["a"]],data:function(){return{loading:!1,axisOption:null,postParams:{},dataIndex:null,list:[],axisList:[],fieldList:[{field:"realname",name:"员工姓名",sortable:!0},{field:"customerNum",name:"新增客户数",sortable:!0},{field:"dealCustomerNum",name:"成交客户数",sortable:!0},{field:"dealCustomerRate",name:"客户成交率(%)",sortable:!0},{field:"contractMoney",name:"合同总金额111",sortable:!0},{field:"receivablesMoney",name:"回款金额",sortable:!0}],detailFields:[{name:"customerNum",list:[{formType:"user",name:"ownerUserId",type:3,values:[]}],request:l["d"],params:null},{name:"dealCustomerNum",list:[{formType:"dealStatus",name:"dealStatus",type:1,values:[1]},{formType:"user",name:"ownerUserId",type:3,values:[]}],request:l["d"],params:null},{name:"dealCustomerRate",list:[{formType:"dealStatus",name:"dealStatus",type:1,values:[1]},{formType:"user",name:"ownerUserId",type:3,values:[]}],request:l["d"],params:null},{name:"contractMoney",crmType:"contract",timeName:"orderDate",list:[{formType:"checkStatus",name:"checkStatus",type:1,values:[1,10]},{formType:"user",name:"ownerUserId",type:3,values:[]}],request:d["d"],params:null},{name:"receivablesMoney",crmType:"receivables",timeName:"returnTime",list:[{formType:"checkStatus",name:"checkStatus",type:1,values:[1,10]},{formType:"user",name:"ownerUserId",type:3,values:[]}],request:m["h"],params:null}]}},mounted:function(){this.initAxis()},methods:{searchClick:function(t){this.postParams=t,this.getDataList(),this.getRecordList()},getDataList:function(){var t=this;this.loading=!0,Object(l["r"])(this.postParams).then((function(e){t.loading=!1;var a=e.data||[];t.axisList=a;for(var n=[],r=[],o=[],s=0;s<a.length;s++){var i=a[s];n.push(i.dealCustomerNum),r.push(i.customerNum),o.push(i.type)}t.axisOption.xAxis[0].data=o,t.axisOption.series[0].data=n,t.axisOption.series[1].data=r,t.chartObj.setOption(t.axisOption,!0)})).catch((function(){t.loading=!1}))},getRecordList:function(t){var e=this;this.dataIndex=t,this.list=[],this.loading=!0,Object(l["s"])(this.postParams).then((function(t){e.loading=!1,e.list=t.data||[]})).catch((function(){e.loading=!1}))},getSummaries:function(t){var e=t.columns,a=t.data,n=[];return e.forEach((function(t,e){if(0!==e)if(3!==e){var r=a.map((function(e){return Number(e[t.property])}));r.every((function(t){return isNaN(t)}))?n[e]="":n[e]=r.reduce((function(t,e){var a=Number(e);return isNaN(a)?t:t+e}),0)}else n[e]="";else n[e]="合计"})),n},initAxis:function(){var t=this;this.chartObj=p["b"](document.getElementById("axismain")),this.chartObj.on("click",(function(e){t.getRecordList(e.dataIndex)})),this.axisOption={color:this.echartLineBarColors,toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(o["a"])({data:["成交客户数","新增客户数"]},this.chartDefaultOptions.legend),grid:this.chartDefaultOptions.grid,xAxis:[Object(o["a"])({type:"category",data:[]},this.chartXAxisStyle)],yAxis:[Object(o["a"])({type:"value",name:"新增客户数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}个"}}))],series:[{name:"成交客户数",type:"bar",yAxisIndex:0,barMaxWidth:15,data:[]},{name:"新增客户数",type:"bar",yAxisIndex:0,barMaxWidth:15,data:[]}]}},exportClick:function(){this.requestExportInfo(l["t"],this.postParams)}}},f=h,b=(a("d0e9"),a("2877")),y=Object(b["a"])(f,n,r,!1,null,"0af82de0",null);e["default"]=y.exports},d0e9:function(t,e,a){"use strict";a("7998")},ef89:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return o})),a.d(e,"g",(function(){return s})),a.d(e,"h",(function(){return i})),a.d(e,"c",(function(){return c})),a.d(e,"e",(function(){return u})),a.d(e,"f",(function(){return p})),a.d(e,"d",(function(){return l})),a.d(e,"i",(function(){return d}));var n=a("b775");function r(t){return Object(n["a"])({url:"biAchievement/taskCompleteStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biAchievement/taskCompleteStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biProduct/productStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(n["a"])({url:"biProduct/productStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"crmBiSearch/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"crmBiSearch/searchContractPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(n["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f4f4:function(t,e,a){"use strict";a("d81d"),a("13d5"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("d3b7"),a("159b");e["a"]={data:function(){return{summaryData:null}},methods:{getSummariesData:function(t){this.summaryData=t||{}},getSummaries:function(t){var e=t.columns,a=t.data,n=[];return e.forEach((function(t,e){if(0!==e){var r=a.map((function(e){return Number(e[t.property])}));r.every((function(t){return isNaN(t)}))?n[e]="":n[e]=r.reduce((function(t,e){var a=Number(e);return isNaN(a)?t:t+e}),0)}else n[e]="合计"})),n}}}},f643:function(t,e,a){"use strict";a("4e82"),a("a9e3"),a("d3b7"),a("25f0");e["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(t))return[];if(!e)return t;function n(t,n){if(t[e]===n[e])return 0;var r=!isNaN(Number(t[e]))&&!isNaN(Number(n[e])),o=r?Number(t[e])<Number(n[e]):t[e]<n[e];return"descending"===a?o?1:-1:o?-1:1}t.sort(n)}}}}}]);