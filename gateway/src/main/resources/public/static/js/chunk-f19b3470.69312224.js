(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f19b3470"],{"0427":function(t,e,a){},"08c2":function(t,e,a){"use strict";var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),t._v(" "),a("span",{staticClass:"text"},[t._v(t._s(t.title))])]),t._v(" "),t.showFilterView?[t.showYearSelect?t._e():a("time-type-select",{on:{change:t.timeTypeChange}}),t._v(" "),t.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":t.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.yearValue,callback:function(e){t.yearValue=e},expression:"yearValue"}}):t._e(),t._v(" "),t._t("after-time"),t._v(" "),t.showSimpleChoose?[t.showUserSelect&&t.showDeptSelect?a("el-select",{model:{value:t.simpleChooseType,callback:function(e){t.simpleChooseType=e},expression:"simpleChooseType"}},t._l(t.simpleOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))):t._e(),t._v(" "),1===t.simpleChooseType&&t.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:t.structuresSelectValue,callback:function(e){t.structuresSelectValue=e},expression:"structuresSelectValue"}}):t._e(),t._v(" "),2===t.simpleChooseType&&t.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:t.userSelectValue,callback:function(e){t.userSelectValue=e},expression:"userSelectValue"}}):t._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:t.dataTypeOptions,"user-checked-data":t.filterValue.userList,"dep-checked-data":t.filterValue.deptList,width:250},on:{select:t.radioMenuSelect},model:{value:t.filterDataType,callback:function(e){t.filterDataType=e},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.avatarData.realname,callback:function(e){t.$set(t.avatarData,"realname",e)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),t.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:t.businessStatusValue,callback:function(e){t.businessStatusValue=e},expression:"businessStatusValue"}},t._l(t.businessOptions,(function(t){return a("el-option",{key:t.flowId,attrs:{label:t.flowName,value:t.flowId}})}))):t._e(),t._v(" "),t.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:t.productValue,callback:function(e){t.productValue=e},expression:"productValue"}}):t._e(),t._v(" "),t.showCustomSelect?a("el-select",{on:{change:t.customSelectChange},model:{value:t.customValue,callback:function(e){t.customValue=e},expression:"customValue"}},t._l(t.customOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}))):t._e(),t._v(" "),t._t("append"),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(e){return t.emitFilter(e)}}},[t._v("查询")]),t._v(" "),t._t("default")]:t._e()],2)},i=[],l=a("5530"),n=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),o=a("ea20"),r=a("657f"),c=a("bfba"),u=a("8f81"),h=a("83f1"),p=a("2f62"),d={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:h["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(l["a"])(Object(l["a"])({},Object(p["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var t=(this.filterValue.userList||[]).map((function(t){return t.realname})),e=(this.filterValue.deptList||[]).map((function(t){return t.name}));return{realname:t.concat(e).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var t=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){t.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(t){var e=this;Object(n["r"])().then((function(a){e.businessOptions=a.data||[],e.businessOptions.length>0&&(e.businessStatusValue=e.businessOptions[0].flowId),t(!0)})).catch((function(){e.$emit("error")}))},getProductCategoryIndex:function(){var t=this;Object(o["T"])({type:"tree"}).then((function(e){t.productOptions=e.data})).catch((function(){}))},radioMenuSelect:function(t,e){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=e.users,this.filterValue.deptList=e.strucs)},timeTypeChange:function(t){this.timeTypeValue=t},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var t=this,e={};this.showSimpleChoose?1===this.simpleChooseType?e.deptList=(this.structuresSelectValue||"").split(",").filter((function(t){return!!t})):e.userList=(this.userSelectValue||"").split(",").filter((function(t){return!!t})):"custom"!==this.filterValue.dataType?e.dataType=this.filterValue.dataType:(e.dataType=0,e.deptList=(this.filterValue.deptList||[]).map((function(t){return t.deptId})),e.userList=(this.filterValue.userList||[]).map((function(t){return t.userId}))),this.showYearSelect?(e.dateFilter="custom",e.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(e.startDate=this.timeTypeValue.startTime,e.endDate=this.timeTypeValue.endTime,e.dateFilter="custom"):e.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(e.typeId=this.businessStatusValue,e.businessItem=this.businessOptions.map((function(e){if(e.flowId===t.businessStatusValue)return e}))),this.showProductSelect&&(e.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",e)}}},f=d,m=(a("965d"),a("2877")),b=Object(m["a"])(f,s,i,!1,null,"6d7c8f9a",null);e["a"]=b.exports},"5b28":function(t,e,a){"use strict";a("0427")},6711:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"项目转换率分析","module-type":"business"},on:{load:function(e){t.loading=!0},change:t.searchClick}}),t._v(" "),a("div",{staticClass:"content"},[t._m(0),t._v(" "),a("div",{staticClass:"table-content"},[a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,stripe:t.WKConfig.tableStyle.stripe,"cell-class-name":t.cellClassName,height:"150","highlight-current-row":""},on:{"row-click":t.handleRowClick}},t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.field,label:t.name.toString(),"show-overflow-tooltip":""}})})))],1)]),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,"crm-type":"business",placeholder:t.reportData.placeholder,request:t.reportData.request,params:t.reportData.params,"field-list":t.fieldReportList},on:{"update:show":function(e){t.reportListShow=e}}})],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"axis-content"},[a("div",{attrs:{id:"axismain"}})])}],l=a("5530"),n=a("3835"),o=(a("14d9"),a("f2ec")),r=a("2a64"),c=a("df55"),u=a("313e"),h=a("8add"),p={name:"BusinessWinStatistics",components:{ReportList:r["a"]},mixins:[c["a"]],data:function(){return{loading:!1,axisOption:null,list:[],postParams:{},axisList:[],fieldList:[],reportListShow:!1,fieldReportList:null,reportData:{title:"",placeholder:"",request:null,params:null}}},computed:{},mounted:function(){this.initAxis()},methods:{searchClick:function(t){this.postParams=t,this.getDataList()},getDataList:function(){var t=this;this.loading=!0,Object(o["c"])(this.postParams).then((function(e){t.loading=!1,t.axisList=e.data||[];for(var a=[{name:"日期",field:"name"}],s=[],i=[],l=[],n=[],o={name:"转化率"},r=0;r<t.axisList.length;r++){var c=t.axisList[r];s.push(c.winBusinessNum),i.push(c.businessNum),l.push(c.businessConversion),n.push(c.type),a.push({name:c.type,field:"type".concat(r)}),o["type".concat(r)]=c.businessConversion+"%"}t.fieldList=a,t.list=[o],t.axisOption.xAxis[0].data=n,t.axisOption.series[0].data=l,t.axisOption.series[1].data=i,t.axisOption.series[2].data=s,t.chartObj.setOption(t.axisOption,!0)})).catch((function(){t.loading=!1}))},cellClassName:function(t){t.row,t.column,t.rowIndex;var e=t.columnIndex;return e?"can-visit--underline":""},handleRowClick:function(t,e,a){if("name"!=e.property){this.reportData.title="".concat(e.label,"详情"),this.reportData.request=o["d"];var s=Object(h["a"])(e.label,this.postParams.dateFilter),i=Object(n["a"])(s,2),r=i[0],c=i[1],u=Object(l["a"])(Object(l["a"])({},this.postParams),{},{dataType:this.postParams.dataType});u.type=1,u.dateFilter="custom",u.startDate=r,u.endDate=c,this.reportData.params=u,this.reportListShow=!0}},initAxis:function(){var t=u["b"](document.getElementById("axismain")),e={color:["#FF5630","#505F79","#1890ff"],toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(l["a"])({data:["赢单转化率","项目总数","赢单项目数"]},this.chartDefaultOptions.legend),grid:this.chartDefaultOptions.grid,xAxis:[Object(l["a"])({type:"category",data:[]},this.chartXAxisStyle)],yAxis:[Object(l["a"])({type:"value",name:"赢单转化率"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}%"}})),Object(l["a"])({type:"value",name:"项目数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}个"},splitLine:{show:!0}}))],series:[Object(l["a"])(Object(l["a"])({},this.chartDefaultOptions.seriesLine),{},{name:"赢单转化率",type:"line",yAxisIndex:0,data:[]}),{name:"项目总数",type:"bar",yAxisIndex:1,barMaxWidth:15,data:[]},{name:"赢单项目数",type:"bar",yAxisIndex:1,barMaxWidth:15,data:[]}]};t.setOption(e,!0),t.on("click",(function(t){})),this.axisOption=e,this.chartObj=t}}},d=p,f=(a("5b28"),a("2877")),m=Object(f["a"])(d,s,i,!1,null,"1ae7936d",null);e["default"]=m.exports},"8add":function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var s=a("3835"),i=(a("99af"),a("caad"),a("c1df")),l=a.n(i);function n(t,e){var a=["year","lastYear","quarter","lastQuarter"].includes(e),i=t.split("-"),n=Object(s["a"])(i,2),o=n[0],r=n[1],c="";if(a){var u=l()("".concat(o,"-").concat(r,"-01")).endOf("month").format("YYYY-MM-DD");return[t,u]}return c="".concat(t," 23:59:59"),["".concat(t," 00:00:00"),c]}},"965d":function(t,e,a){"use strict";a("c558")},c558:function(t,e,a){},df55:function(t,e,a){"use strict";var s=a("5530"),i=(a("d3b7"),a("08c2")),l=a("7a1a"),n=a("ed08"),o=a("a347"),r=a.n(o);e["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},textColor:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{lineStyle:{color:r.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:r.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:i["a"]},props:{},computed:{},watch:{},mounted:function(){var t=this;this.debouncedResize=Object(l["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",t.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(t){this.pageData.limit=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(t){this.pageData.page=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(t,e){return new Promise((function(a,s){t(e).then((function(t){Object(n["g"])(t),a&&a(t)})).catch((function(t){s&&s(t)}))}))},getChartYAxisStyle:function(t){var e=Object(n["D"])(this.chartYAxisStyle);if(!t)return e;for(var a in t){var i=e[a],l=t[a];e[a]=i?Object(s["a"])(Object(s["a"])({},i),l):l}return e}},deactivated:function(){}}},f2ec:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return n})),a.d(e,"d",(function(){return o}));var s=a("b775");function i(t){return Object(s["a"])({url:"biFunnel/addBusinessAnalyze",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(s["a"])({url:"biFunnel/win",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(t){return Object(s["a"])({url:"crmBiSearch/searchBusinessPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(s["a"])({url:"crmInstrument/queryContendBusinessList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}}}]);