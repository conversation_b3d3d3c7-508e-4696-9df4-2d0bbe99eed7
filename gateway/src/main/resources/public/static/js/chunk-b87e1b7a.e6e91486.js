(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b87e1b7a"],{2602:function(t,e,n){"use strict";n.d(e,"r",(function(){return o})),n.d(e,"s",(function(){return a})),n.d(e,"t",(function(){return i})),n.d(e,"o",(function(){return s})),n.d(e,"m",(function(){return c})),n.d(e,"n",(function(){return u})),n.d(e,"c",(function(){return p})),n.d(e,"f",(function(){return l})),n.d(e,"g",(function(){return d})),n.d(e,"h",(function(){return h})),n.d(e,"u",(function(){return f})),n.d(e,"v",(function(){return m})),n.d(e,"x",(function(){return b})),n.d(e,"a",(function(){return T})),n.d(e,"b",(function(){return C})),n.d(e,"i",(function(){return j})),n.d(e,"j",(function(){return y})),n.d(e,"p",(function(){return v})),n.d(e,"q",(function(){return g})),n.d(e,"l",(function(){return F})),n.d(e,"k",(function(){return O})),n.d(e,"d",(function(){return U})),n.d(e,"w",(function(){return S})),n.d(e,"e",(function(){return w}));var r=n("b775");function o(t){return Object(r["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function a(t){return Object(r["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(r["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(r["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(r["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(r["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(r["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(r["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(r["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(r["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(r["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(r["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(r["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(r["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(r["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(r["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(r["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(r["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(r["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function F(t){return Object(r["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(r["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function U(t){return Object(r["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(t){return Object(r["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(t){return Object(r["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},3866:function(t,e,n){"use strict";n("54de")},"4e82":function(t,e,n){"use strict";var r=n("23e7"),o=n("e330"),a=n("59ed"),i=n("7b0b"),s=n("07fa"),c=n("083a"),u=n("577e"),p=n("d039"),l=n("addb"),d=n("a640"),h=n("3f7e"),f=n("99f4"),m=n("1212"),b=n("ea83"),T=[],C=o(T.sort),j=o(T.push),y=p((function(){T.sort(void 0)})),v=p((function(){T.sort(null)})),g=d("sort"),F=!p((function(){if(m)return m<70;if(!(h&&h>3)){if(f)return!0;if(b)return b<603;var t,e,n,r,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)T.push({k:e+r,v:n})}for(T.sort((function(t,e){return e.v-t.v})),r=0;r<T.length;r++)e=T[r].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),O=y||!v||!g||!F,U=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:u(e)>u(n)?1:-1}};r({target:"Array",proto:!0,forced:O},{sort:function(t){void 0!==t&&a(t);var e=i(this);if(F)return void 0===t?C(e):C(e,t);var n,r,o=[],u=s(e);for(r=0;r<u;r++)r in e&&j(o,e[r]);l(o,U(t)),n=s(o),r=0;while(r<n)e[r]=o[r++];while(r<u)c(e,r++);return e}})},"54de":function(t,e,n){},"919b":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[n("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"产品满意度分析","module-type":"contract"},on:{load:function(e){t.loading=!0},change:t.getDataList}},[n("el-button",{staticClass:"export-button",attrs:{size:"small",type:"primary"},nativeOn:{click:function(e){return t.exportClick(e)}}},[t._v("导出")])],1),t._v(" "),n("div",{staticClass:"content"},[n("div",{staticClass:"table-content"},[n("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:t.tableHeight,"highlight-current-row":"","cell-class-name":t.cellClassName},on:{"row-click":t.handleRowClick,"sort-change":function(e){var n=e.prop,r=e.order;return t.mixinSortFn(t.list,n,r)}}},t._l(t.fieldList,(function(t,e){return n("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,sortable:"custom","show-overflow-tooltip":""}})})))],1)]),t._v(" "),n("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},o=[],a=(n("99af"),n("14d9"),n("ac1f"),n("00b4"),n("ef89")),i=n("f643"),s=n("df55"),c=n("b80b"),u=n("2602"),p=n("08c2"),l={name:"ProductSatisfaction",components:{FiltrateHandleView:p["a"]},mixins:[s["a"],i["a"],c["a"]],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-160,postParams:{},list:[],fieldList:[],reportData:{crmType:"contract"},detailFields:[]}},computed:{},mounted:function(){var t=this;window.onresize=function(){t.tableHeight=document.documentElement.clientHeight-160}},methods:{getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,Object(u["l"])(t).then((function(t){var n=t.data||[];if(0===e.fieldList.length&&n.length>0){var r=n[0],o=[{field:"productName",name:"产品名称"},{field:"visitNum",name:"回访次数"}],i=[],s=/[^a-zA-Z]+/;for(var c in r)"productName"!==c&&e.detailFields.push({name:c,crmType:"contract",fieldType:"productPopular",request:a["f"],params:{searchType:"visitNum"===c?null:c}}),s.test(c)&&"其他"!==c&&i.push({field:c,name:c});e.fieldList=o.concat(i)}e.list=n,e.loading=!1})).catch((function(){e.loading=!1}))},exportClick:function(){this.requestExportInfo(u["k"],this.postParams)}}},d=l,h=(n("3866"),n("2877")),f=Object(h["a"])(d,r,o,!1,null,"7b534ccb",null);e["default"]=f.exports},ef89:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return a})),n.d(e,"g",(function(){return i})),n.d(e,"h",(function(){return s})),n.d(e,"c",(function(){return c})),n.d(e,"e",(function(){return u})),n.d(e,"f",(function(){return p})),n.d(e,"d",(function(){return l})),n.d(e,"i",(function(){return d}));var r=n("b775");function o(t){return Object(r["a"])({url:"biAchievement/taskCompleteStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function a(t){return Object(r["a"])({url:"biAchievement/taskCompleteStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(r["a"])({url:"biProduct/productStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(r["a"])({url:"biProduct/productStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(r["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(r["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(r["a"])({url:"crmBiSearch/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(r["a"])({url:"crmBiSearch/searchContractPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(r["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f643:function(t,e,n){"use strict";n("4e82"),n("a9e3"),n("d3b7"),n("25f0");e["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(t))return[];if(!e)return t;function r(t,r){if(t[e]===r[e])return 0;var o=!isNaN(Number(t[e]))&&!isNaN(Number(r[e])),a=o?Number(t[e])<Number(r[e]):t[e]<r[e];return"descending"===n?a?1:-1:a?-1:1}t.sort(r)}}}}}]);