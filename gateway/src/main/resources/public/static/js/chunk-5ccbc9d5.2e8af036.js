(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5ccbc9d5"],{"761a":function(e,t,i){},"8f29":function(e,t,i){"use strict";i("761a")},e630:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("flexbox",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fields-index body",attrs:{align:"flex-start",justify:"flex-start"}},[n("div",{staticClass:"body-left"},[n("div",{staticClass:"body-left_title"},[e._v("字段库"),n("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"238"}})]),e._v(" "),n("ul",[n("draggable",{staticClass:"lib-wrapper",attrs:{list:e.fieldLibList,options:e.dragLeftConfig,clone:e.dragLeftMove},on:{end:e.dragLeftEnd}},e._l(e.fieldLibList,(function(t){return n("div",{key:t.id,staticClass:"lib-item",on:{click:function(i){e.handleLibFieldClick(t)}}},[n("i",{staticClass:"lib-item-icon",class:t.icon}),e._v(" "),n("span",[e._v(e._s(t.name))])])})))],1)]),e._v(" "),n("div",{staticClass:"body-content"},[n("flexbox",{staticClass:"body-content-warp",attrs:{align:"flex-start",justify:"flex-start",direction:"column"}},[n("el-header",[n("div",{staticClass:"title"},[e._v("编辑"+e._s(e.title)+"字段")]),e._v(" "),n("div",[n("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleSave,expression:"handleSave"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),n("el-button",{on:{click:e.handleCancel}},[e._v("返回")])],1)]),e._v(" "),n("flexbox-item",{staticClass:"body-content-main",staticStyle:{"margin-left":"0"}},[n("el-main",[n("draggable",{staticClass:"field-list",attrs:{list:e.fieldArr,options:e.dragListConfig},on:{end:e.dragListEnd}},e._l(e.fieldArr,(function(t,i){return n("flexbox",{key:i,staticClass:"field-row",attrs:{align:"flex-start",justify:"flex-start"}},e._l(t,(function(t,r){return n(e._f("typeToComponentName")(t),{key:r,ref:"fieldItem",refInFor:!0,tag:"component",attrs:{field:t,"field-arr":e.fieldArr,point:[i,r],"active-point":e.selectedPoint},on:{action:e.handleAction,"child-drag-add":e.handleChildDragAdd,click:function(t){e.handleSelect([i,r])}}})})))}))),e._v(" "),e.fieldArr&&0!==e.fieldArr.length?e._e():n("el-empty",{attrs:{image:i("0437"),description:"拖拽或点击左侧字段创建表单"}})],1)],1)],1)],1),e._v(" "),n("div",{staticClass:"body-right",staticStyle:{"margin-left":"0"}},[e.selectedField?n("setting-field",{attrs:{field:e.selectedField,point:e.selectedPoint,"field-arr":e.fieldArr,"can-transform":e.canTransform,"transform-data":e.transformData},on:{"child-edit":e.handleChildEdit,"update-width":e.handleUpdateFieldWidth}}):e._e()],1)])},r=[],a=i("5530"),l=(i("99af"),i("4de4"),i("7db0"),i("a630"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("ab43"),i("a9e3"),i("d3b7"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("2532"),i("3ca3"),i("498a"),i("159b"),i("ddb0"),i("ea20")),d=i("e170"),s=i("6bfe"),o=i("60c3"),c=i("2c81"),f=i("b76a"),h=i.n(f),u=i("4f45"),m=i("5240"),g=i("ed08"),p=i("6683"),v=i("6d94"),b={name:"FieldsIndex",components:{FieldInput:o["i"],FieldTextarea:o["p"],FieldSelect:o["m"],FieldCheckbox:o["c"],FieldFile:o["g"],FieldBoolean:o["b"],FieldPercent:o["k"],FieldPosition:o["l"],FieldLocation:o["j"],FieldDetailTable:o["f"],FieldWritingSign:o["q"],FieldDateInterval:o["d"],FieldDescText:o["e"],FieldSerialNumber:o["n"],FieldTag:o["o"],FieldAttention:o["a"],FieldGroup:o["h"],SettingField:c["a"],draggable:h.a},filters:{typeToComponentName:function(e){return Object(v["b"])(e)}},data:function(){return{moduleType:"",loading:!1,fieldLibList:[],dragLeftConfig:{group:{pull:"clone",put:!1,name:"libList"},forceFallback:!0,sort:!1},movedField:null,dragListConfig:{delay:100,group:{name:"list",put:["libList"],pull:!0},forceFallback:!0,fallbackClass:"draggingStyle"},fieldArr:[],rejectHandle:!0,isChildDragAdd:!1,selectedPoint:[null,null],selectedField:null,transformData:null}},computed:{canTransform:function(){var e=this.selectedPoint[0],t=this.selectedPoint[1];if(!Object(s["b"])(e)&&!Object(s["b"])(t)){var i=this.fieldArr[e][t];if("detail_table"===i.formType)return!1}return"crm_leads"===this.moduleType},title:function(){return{crm_leads:"线索",crm_customer:"客户",crm_contacts:"联系人",crm_business:"项目",crm_contract:"合同",crm_product:"产品",crm_receivables:"回款",crm_visit:"客户回访",crm_marketing:"市场活动",crm_receivables_plan:"回款计划",crm_invoice:"发票"}[this.moduleType]||""}},created:function(){this.moduleType=this.$route.params.type||"",this.initCom(),this.canTransform&&this.getTransformField()},methods:{initCom:function(){"crm_marketing"===this.moduleType?this.fieldLibList=m["a"].filter((function(e){return!["user","structure","file","desc_text","position","location","handwriting_sign","detail_table","serial_number","field_attention","field_group","pic"].includes(e.formType)})):this.fieldLibList=m["a"].filter((function(e){return"pic"!==e.formType})),this.getFieldList()},getFieldList:function(){var e=null,t={},i=this.$route.params;"crm_marketing"===this.moduleType?(e=l["t"],t.id=i.id):"oa_examine"===this.moduleType?(e=l["E"],t.categoryId=i.id):e=l["D"],i.label&&(t.label=i.label),this.getFieldListReq(e,t)},getFieldListReq:function(e,t){var i=this;this.loading=!0,e(t).then((function(e){var t=e.data||[];t.forEach((function(e){e.forEach((function(e){e.formAssistId=e.fieldId,"crm_marketing"===i.moduleType&&(e.operating=253)}))})),i.fieldArr=t,t.length>0&&i.handleSelect([0,0]),i.rejectHandle=!1,i.loading=!1})).catch((function(){i.loading=!1}))},generateFormAssistId:function(e){var t=1e3,i=function(t){var n=t+1;return e.includes(n)?i(n):n};return i(t)},handleLibFieldClick:function(e){this.movedField=e,this.dragLeftEnd()},dragLeftMove:function(e){this.movedField=e},dragLeftEnd:function(e){var t=this;if(!this.rejectHandle){var i=[],n=!0;if(this.fieldArr.forEach((function(e){e.forEach((function(e){i.push(e.formAssistId),"field_tag"===e.formType&&"field_tag"===t.movedField.formType&&(n=!1)}))})),n){var r=new u["a"]({name:this.movedField.name,formType:this.movedField.formType});switch(r.stylePercent=100,r.operating="crm_marketing"===this.moduleType?253:255,r.formAssistId=this.generateFormAssistId(i),"none"!==this.$route.params.label&&(r.label=this.$route.params.label),this.movedField.formType){case"desc_text":r.name="";break;case"select":case"checkbox":r.options="选1,选2,选3",r.setting=["选1","选2","选3"];break;case"detail_table":r.operating=232,r.fieldExtendList=[],r.defaultValue=null,r.remark="添加".concat(r.name);break;case"serial_number":r.operating=245,r.isUnique=1,r.setting=[{type:1,resetType:4,startNumber:void 0,stepNumber:void 0}];break;case"field_tag":r.operating=249;break;case"field_attention":r.operating=250,r.defaultValue=null;break;case"field_group":r.operating=224;break}if(delete r.fieldId,this.isChildDragAdd)return["detail_table","desc_text","handwriting_sign","pic","serial_number","field_tag","field_attention","field_group"].includes(r.formType)?void this.$message.error("此字段内部不能添加该类型的字段"):void this.childDragAddEnd(r,e);var a=null;a=e&&"clone"===e.pullMode&&!Object(s["b"])(e.newIndex)?e.newIndex:0===this.fieldArr.length?0:this.selectedPoint[0]+1,this.fieldArr.splice(a,0,[r]),this.handleSelect([a,0])}else this.$message.error("只允许添加一个自定义标签字段")}},handleChildDragAdd:function(e,t){this.selectedPoint=e,this.isChildDragAdd=!0},childDragAddEnd:function(e,t){e.stylePercent=50,e.operating=171;var i=m["a"].find((function(t){return e.formType===t.formType}));i&&(e.type=i.type);var n=this.selectedPoint[0],r=this.selectedPoint[1],a=this.fieldArr[n][r];Object(s["b"])(a.fieldExtendList)&&(a.fieldExtendList=[]),a.fieldExtendList.length>=20?this.$message.error("明细表格类型内部字段不能超过20个"):(e.fieldName=this.generateFieldName(a.fieldExtendList),a.fieldExtendList.push(e),this.$set(this.fieldArr,n,this.fieldArr[n]),this.handleSelect(this.selectedPoint,e),this.isChildDragAdd=!1)},dragListEnd:function(e){this.selectedPoint.splice(0,1,e.newIndex)},handleAction:function(e,t){switch(e){case"top":this.handleActionMoveTop(t);break;case"bottom":this.handleActionMoveBottom(t);break;case"left":this.handleActionExchange(t,-1);break;case"right":this.handleActionExchange(t,1);break;case"copy":this.handleActionCopy(t);break;case"delete":this.handleDelete(t)}},handleActionMoveTop:function(e){var t=this.fieldArr[e[0]-1];if(t&&4!==t.length){var i=this.fieldArr[e[0]][e[1]],n=t[0];if("detail_table"===i.formType||"detail_table"===n.formType||"field_group"===i.formType||"field_group"===n.formType){var r=[this.fieldArr[e[0]],this.fieldArr[e[0]-1]];this.fieldArr[e[0]-1]=r[0],this.fieldArr[e[0]]=r[1],this.handleSelect([e[0]-1,0])}else{t.push(Object(g["D"])(i));var a=this.getWidth(t.length);t.forEach((function(e){e.stylePercent=a.stylePercent})),this.$set(this.fieldArr,e[0]-1,t);var l=this.fieldArr[e[0]];l.splice(e[1],1),0===l.length?this.fieldArr.splice(e[0],1):(a=this.getWidth(l.length),l.forEach((function(e){e.stylePercent=a.stylePercent})),this.$set(this.fieldArr,e[0],l)),this.handleSelect([e[0]-1,t.length-1])}}},handleActionMoveBottom:function(e){var t=this.fieldArr[e[0]][e[1]],i=this.fieldArr[e[0]+1][0],n=this.fieldArr[e[0]];if("detail_table"===t.formType||"detail_table"===i.formType||"field_group"===t.formType||"field_group"===i.formType||1===n.length){var r=[this.fieldArr[e[0]],this.fieldArr[e[0]+1]];this.fieldArr[e[0]+1]=r[0],this.fieldArr[e[0]]=r[1],this.handleSelect([e[0]+1,0])}else{t.stylePercent=100,this.fieldArr.splice(e[0]+1,0,[t]),this.fieldArr[e[0]].splice(e[1],1);var a=this.getWidth(n.length);n.forEach((function(e){e.stylePercent=a.stylePercent})),this.$set(this.fieldArr,e[0],n),this.handleSelect([e[0]+1,0])}},handleActionExchange:function(e,t){var i=this.fieldArr[e[0]],n=this.fieldArr[e[0]][e[1]];i.splice(e[1],1),i.splice(e[1]+t,0,n),this.handleSelect([e[0],e[1]+t])},handleActionCopy:function(e){var t=this.fieldArr[e[0]][e[1]],i=Object(g["D"])(t);if(delete i.fieldId,delete i.fieldName,delete i.relevant,i.fieldType=0,i.operating="crm_marketing"===this.moduleType?253:255,"pic"===i.formType&&(i.operating=235),"desc_text"===i.formType&&(i.name=""),"serial_number"===i.formType&&(i.operating=245),"field_tag"==i.formType){var n=!0;if(this.fieldArr.forEach((function(e){e.forEach((function(e){e.formType==i.formType&&(n=!1)}))})),!n)return void this.$message.error("只允许添加一个自定义标签字段")}"field_tag"===i.formType&&(i.operating=249),"field_attention"===i.formType&&(i.operating=250,i.defaultValue=null),"field_group"===i.formType&&(i.operating=224),this.fieldArr.splice(e[0]+1,0,[i]),this.handleSelect([e[0]+1,e[1]])},handleUpdateFieldWidth:function(){for(var e=this.fieldArr[this.selectedPoint[0]],t=[],i=[],n=0,r=0;r<e.length;r++){var a=e[r];n+=a.stylePercent,n<100?i.push(a):n>100?(t.push(Object(g["D"])(i)),i=[],i.push(a),n=a.stylePercent):(i.push(a),t.push(Object(g["D"])(i)),i=[],n=0)}if(i.length>0&&t.push(i),t.length>1){var l,d=this.selectedPoint[0],s=this.selectedPoint[1];(l=this.fieldArr).splice.apply(l,[d,1].concat(t));for(var o=0,c=0;c<t.length;c++)if(o+=t[c].length,o>=s+1){d+=c,s=s-o+t[c].length;break}this.handleSelect([d,s])}},getWidth:function(e){return 1===e?{stylePercent:100}:2===e?{stylePercent:50}:e>2?{stylePercent:25}:void 0},handleDelete:function(e){var t=this;this.$confirm("确定删除该自定义字段吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.selectedPoint=[null,null],t.selectedField=null,t.fieldArr[e[0]].splice([e[1]],1),0===t.fieldArr[e[0]].length&&t.fieldArr.splice(e[0],1)})).catch((function(){}))},handleSelect:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.selectedPoint=e,this.selectedField=t||this.fieldArr[e[0]][e[1]]},handleChildEdit:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e?this.selectedField=e:this.handleSelect(this.selectedPoint)},generateFieldName:function(e){var t=e.map((function(e){return e.fieldName})),i=function(e){for(var n="abcdefghijklmnopqrstuvwxyz",r="",a=0;a<e;a++){var l=Math.ceil(25*Math.random());r+=n[l]}var d="field_"+r;return t.includes(d)?i(e):d};return i(6)},handleSave:function(){var e=this;if(!this.rejectHandle){var t=[];this.loading=!0,Object(g["D"])(this.fieldArr).forEach((function(e,i){e.forEach((function(e,n){t.push(Object(a["a"])(Object(a["a"])({},e),{},{formPosition:"".concat(i,",").concat(n)}))}))}));for(var i,n="select|update|union|and|or|delete|insert|trancate|char|substr|ascii|declare|exec|count|master|into|drop|execute".split("|"),r=[],d=function(){var i=t[o],a=i.formPosition.split(","),l="第".concat(Number(a[0])+1,"行第").concat(Number(a[1])+1,"列");if(i.name=(i.name||"").trim(),"desc_text"!==i.formType){if(!i.name)return e.$message.error(l+"自定义字段，标识名不能为空"),e.loading=!1,{v:void 0};if(n.includes(i.name))return e.$message.error(l+"“".concat(i.name,"”字段，标识名与系统字段重复，请使用其他字段！")),e.loading=!1,{v:void 0};if(r.includes(i.name))return e.$message.error(l+"“".concat(i.name,"”字段，标识名重复")),e.loading=!1,{v:void 0};if(1===i.isNull&&1===i.isHidden)return e.$message.error(l+"“".concat(i.name,"”字段，不能同时设置必填和隐藏")),e.loading=!1,{v:void 0};if("serial_number"===i.formType&&!e.validateSerialNumberField(i))return e.$message.error(l+"“".concat(i.name,"”字段，自定义编号规则有误")),e.loading=!1,{v:void 0};if("field_tag"===i.formType&&(!i.setting||!i.setting.length))return e.$message.error(l+"“".concat(i.name,"”字段，自定义标签未配置标签项")),e.loading=!1,{v:void 0};if("detail_table"===i.formType){if(Object(s["b"])(i.fieldExtendList))return e.$message.error(l+"“".concat(i.name,"”字段，不能为空")),e.loading=!1,{v:void 0};for(var d=0;d<i.fieldExtendList.length;d++){var c=i.fieldExtendList[d];if(delete c.companyId,delete c.id,Object(s["b"])(c.defaultValue)&&(c.defaultValue=null),c.name=(c.name||"").trim(),!c.name)return e.$message.error(l+"“".concat(c.name,"”字段，标识名不能为空")),e.loading=!1,{v:void 0};if(n.includes(c.name))return e.$message.error(l+"“".concat(c.name,"”字段，标识名与系统字段重复，请使用其他字段！")),e.loading=!1,{v:void 0}}var f=i.fieldExtendList.map((function(e){return e.name}));if(f.length!==Array.from(new Set(f)).length)return e.$message.error(l+"“".concat(i.name,"”字段，标识名重复")),e.loading=!1,{v:void 0}}r.push(i.name)}else if(!Object(s["b"])(i.defaultValue)&&i.defaultValue.length>2e3)return e.$message.error(l+"“".concat(i.name,"”字段，描述文字类型字段最多设置2000字")),e.loading=!1,{v:void 0};if(!i.type){var h=m["a"].find((function(e){return e.formType===i.formType}));h&&(i.type=h.type)}i.hasOwnProperty("optionsData")&&delete i.optionsData},o=0;o<t.length;o++)if(i=d(),i)return i.v;var c={data:t},f=this.$route.params;"none"!==f.label&&(c.label=f.label),"oa_examine"===this.moduleType?c.categoryId=f.id:"crm_marketing"===this.moduleType&&(c.formId=f.id);var h={crm_marketing:l["m"],oa_examine:l["F"]}[this.moduleType]||l["B"];h(c).then((function(){e.$message({type:"success",message:"操作成功"}),e.loading=!1,e.getFieldList()})).catch((function(){e.loading=!1}))}},validateSerialNumberField:function(e){if(!e.setting||!e.setting.length)return!1;for(var t=0;t<e.setting.length;t++){var i=e.setting[t];if(1===i.type){if(!i.startNumber&&0!==i.startNumber)return!1;if(!i.stepNumber)return!1}else if(!i.value)return!1}return!0},getTransformField:function(){var e=this;Object(d["F"])({label:p["a"]["customer"],type:1}).then((function(t){for(var i={text:[],textarea:[],select:[],checkbox:[],number:[],floatnumber:[],mobile:[],email:[],date:[],datetime:[],user:[],structure:[],boolean_value:[],percent:[],position:[],location:[],handwriting_sign:[],date_interval:[]},n=0;n<t.data.length;n++){var r=t.data[n],a=i[r.formType];a&&a.push({label:r.name,value:r.fieldId})}e.transformData=i})).catch((function(){}))},handleCancel:function(){this.$router.go(-1)}}},y=b,_=(i("8f29"),i("2877")),A=Object(_["a"])(y,n,r,!1,null,"0f50ea6a",null);t["default"]=A.exports}}]);