(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-be3a0588"],{"00ed":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"日志分析","module-type":"oa"},on:{load:function(t){e.loading=!0},change:e.getDataList}},[a("el-button",{staticClass:"export-button",attrs:{size:"small",type:"primary"},nativeOn:{click:function(t){return e.exportExcel(t)}}},[e._v("导出")])],1),e._v(" "),a("div",{staticClass:"content"},[a("div",{staticClass:"table-content"},[e.showTable?a("el-table",{class:e.WKConfig.tableStyle.class,attrs:{size:"small",data:e.list,"cell-class-name":e.cellClassName,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight,"highlight-current-row":""},on:{"sort-change":function(t){var a=t.prop,s=t.order;return e.mixinSortFn(e.list,a,s)},"row-click":e.handleRowClick}},e._l(e.fieldList,(function(e,t){return a("el-table-column",{key:t,attrs:{prop:e.field,label:e.name,sortable:"custom"}})}))):e._e()],1)]),e._v(" "),a("c-r-m-full-screen-detail",{attrs:{id:e.relationID,params:e.nparams,visible:e.reportListShow,"crm-type":"list"},on:{"update:visible":function(t){e.reportListShow=t}}})],1)},i=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("f643")),l=a("cf9f"),o=a("08c2"),r=a("ed08"),u={name:"LogStatistics",components:{FiltrateHandleView:o["a"],CRMFullScreenDetail:function(){return Promise.resolve().then(a.bind(null,"df3e"))}},mixins:[n["a"]],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-170,nparams:{},postParams:{},list:[],fieldList:[{field:"realname",name:"员工"},{field:"count",name:"填写数"},{field:"unCommentCount",name:"未评论数"},{field:"commentCount",name:"已评论数"}],reportListShow:!1,relationID:""}},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-170}},methods:{getDataList:function(e){var t=this;this.nparams=this.postParams=e,this.loading=!0,Object(l["e"])(e).then((function(e){t.list=e.data||[],t.loading=!1})).catch((function(){t.loading=!1}))},handleRowClick:function(e,t,a){"realname"!==t.property&&(this.nparams.createUserId=e.userId,this.relationID=t.property,this.reportListShow=!0)},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"realname"!==t.property?"can-visit--underline":""},exportExcel:function(){var e=this;this.loading=!0,Object(l["d"])(this.postParams).then((function(t){e.loading=!1,Object(r["g"])(t)})).catch((function(){e.loading=!1}))}}},c=u,d=(a("3e3f"),a("2877")),p=Object(d["a"])(c,s,i,!1,null,"127f70a3",null);t["default"]=p.exports},"08c2":function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),e._v(" "),a("span",{staticClass:"text"},[e._v(e._s(e.title))])]),e._v(" "),e.showFilterView?[e.showYearSelect?e._e():a("time-type-select",{on:{change:e.timeTypeChange}}),e._v(" "),e.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":e.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:e.yearValue,callback:function(t){e.yearValue=t},expression:"yearValue"}}):e._e(),e._v(" "),e._t("after-time"),e._v(" "),e.showSimpleChoose?[e.showUserSelect&&e.showDeptSelect?a("el-select",{model:{value:e.simpleChooseType,callback:function(t){e.simpleChooseType=t},expression:"simpleChooseType"}},e._l(e.simpleOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),1===e.simpleChooseType&&e.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:e.structuresSelectValue,callback:function(t){e.structuresSelectValue=t},expression:"structuresSelectValue"}}):e._e(),e._v(" "),2===e.simpleChooseType&&e.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:e.userSelectValue,callback:function(t){e.userSelectValue=t},expression:"userSelectValue"}}):e._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:e.dataTypeOptions,"user-checked-data":e.filterValue.userList,"dep-checked-data":e.filterValue.deptList,width:250},on:{select:e.radioMenuSelect},model:{value:e.filterDataType,callback:function(t){e.filterDataType=t},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:e.avatarData.realname,callback:function(t){e.$set(e.avatarData,"realname",t)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),e._v(" "),e.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:e.businessStatusValue,callback:function(t){e.businessStatusValue=t},expression:"businessStatusValue"}},e._l(e.businessOptions,(function(e){return a("el-option",{key:e.flowId,attrs:{label:e.flowName,value:e.flowId}})}))):e._e(),e._v(" "),e.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:e.productValue,callback:function(t){e.productValue=t},expression:"productValue"}}):e._e(),e._v(" "),e.showCustomSelect?a("el-select",{on:{change:e.customSelectChange},model:{value:e.customValue,callback:function(t){e.customValue=t},expression:"customValue"}},e._l(e.customOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):e._e(),e._v(" "),e._t("append"),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(t){return e.emitFilter(t)}}},[e._v("查询")]),e._v(" "),e._t("default")]:e._e()],2)},i=[],n=a("5530"),l=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),o=a("ea20"),r=a("657f"),u=a("bfba"),c=a("8f81"),d=a("83f1"),p=a("2f62"),h={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:u["a"],WkUserDialogSelect:c["a"],XrRadioMenu:d["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(n["a"])(Object(n["a"])({},Object(p["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var e=(this.filterValue.userList||[]).map((function(e){return e.realname})),t=(this.filterValue.deptList||[]).map((function(e){return e.name}));return{realname:e.concat(t).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var e=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){e.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(e){var t=this;Object(l["r"])().then((function(a){t.businessOptions=a.data||[],t.businessOptions.length>0&&(t.businessStatusValue=t.businessOptions[0].flowId),e(!0)})).catch((function(){t.$emit("error")}))},getProductCategoryIndex:function(){var e=this;Object(o["T"])({type:"tree"}).then((function(t){e.productOptions=t.data})).catch((function(){}))},radioMenuSelect:function(e,t){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=t.users,this.filterValue.deptList=t.strucs)},timeTypeChange:function(e){this.timeTypeValue=e},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var e=this,t={};this.showSimpleChoose?1===this.simpleChooseType?t.deptList=(this.structuresSelectValue||"").split(",").filter((function(e){return!!e})):t.userList=(this.userSelectValue||"").split(",").filter((function(e){return!!e})):"custom"!==this.filterValue.dataType?t.dataType=this.filterValue.dataType:(t.dataType=0,t.deptList=(this.filterValue.deptList||[]).map((function(e){return e.deptId})),t.userList=(this.filterValue.userList||[]).map((function(e){return e.userId}))),this.showYearSelect?(t.dateFilter="custom",t.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(t.startDate=this.timeTypeValue.startTime,t.endDate=this.timeTypeValue.endTime,t.dateFilter="custom"):t.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(t.typeId=this.businessStatusValue,t.businessItem=this.businessOptions.map((function(t){if(t.flowId===e.businessStatusValue)return t}))),this.showProductSelect&&(t.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",t)}}},f=h,m=(a("965d"),a("2877")),b=Object(m["a"])(f,s,i,!1,null,"6d7c8f9a",null);t["a"]=b.exports},"3e3f":function(e,t,a){"use strict";a("8eb3")},"4e82":function(e,t,a){"use strict";var s=a("23e7"),i=a("e330"),n=a("59ed"),l=a("7b0b"),o=a("07fa"),r=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),p=a("a640"),h=a("3f7e"),f=a("99f4"),m=a("1212"),b=a("ea83"),y=[],v=i(y.sort),w=i(y.push),S=c((function(){y.sort(void 0)})),g=c((function(){y.sort(null)})),V=p("sort"),C=!c((function(){if(m)return m<70;if(!(h&&h>3)){if(f)return!0;if(b)return b<603;var e,t,a,s,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(s=0;s<47;s++)y.push({k:t+s,v:a})}for(y.sort((function(e,t){return t.v-e.v})),s=0;s<y.length;s++)t=y[s].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}})),T=S||!g||!V||!C,k=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};s({target:"Array",proto:!0,forced:T},{sort:function(e){void 0!==e&&n(e);var t=l(this);if(C)return void 0===e?v(t):v(t,e);var a,s,i=[],u=o(t);for(s=0;s<u;s++)s in t&&w(i,t[s]);d(i,k(e)),a=o(i),s=0;while(s<a)t[s]=i[s++];while(s<u)r(t,s++);return t}})},"8eb3":function(e,t,a){},"965d":function(e,t,a){"use strict";a("c558")},c558:function(e,t,a){},cf9f:function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return l})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return r}));var s=a("b775");function i(e){return Object(s["a"])({url:"biWork/logStatistics",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(e){return Object(s["a"])({url:"biWork/logStatisticsExport",method:"post",data:e,responseType:"blob",timeout:6e5,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(s["a"])({url:"biWork/examineStatistics",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(e){return Object(s["a"])({url:"biWork/examineInfo",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(e){return Object(s["a"])({url:"biWork/examineStatisticsExport",method:"post",data:e,responseType:"blob",timeout:6e5,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f643:function(e,t,a){"use strict";a("4e82"),a("a9e3"),a("d3b7"),a("25f0");t["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(e))return[];if(!t)return e;function s(e,s){if(e[t]===s[t])return 0;var i=!isNaN(Number(e[t]))&&!isNaN(Number(s[t])),n=i?Number(e[t])<Number(s[t]):e[t]<s[t];return"descending"===a?n?1:-1:n?-1:1}e.sort(s)}}}}}]);