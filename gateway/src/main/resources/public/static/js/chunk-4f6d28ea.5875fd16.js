(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4f6d28ea"],{2602:function(t,e,r){"use strict";r.d(e,"r",(function(){return n})),r.d(e,"s",(function(){return o})),r.d(e,"t",(function(){return i})),r.d(e,"o",(function(){return s})),r.d(e,"m",(function(){return c})),r.d(e,"n",(function(){return u})),r.d(e,"c",(function(){return d})),r.d(e,"f",(function(){return l})),r.d(e,"g",(function(){return p})),r.d(e,"h",(function(){return h})),r.d(e,"u",(function(){return m})),r.d(e,"v",(function(){return f})),r.d(e,"x",(function(){return b})),r.d(e,"a",(function(){return y})),r.d(e,"b",(function(){return C})),r.d(e,"i",(function(){return T})),r.d(e,"j",(function(){return j})),r.d(e,"p",(function(){return g})),r.d(e,"q",(function(){return v})),r.d(e,"l",(function(){return x})),r.d(e,"k",(function(){return O})),r.d(e,"d",(function(){return w})),r.d(e,"w",(function(){return S})),r.d(e,"e",(function(){return F}));var a=r("b775");function n(t){return Object(a["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(a["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(a["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(a["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(a["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(a["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(a["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(a["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(a["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(a["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(a["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(a["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(a["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(a["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(a["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(t){return Object(a["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(t){return Object(a["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function F(t){return Object(a["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"4ca6":function(t,e,r){"use strict";r("6b48")},"4e82":function(t,e,r){"use strict";var a=r("23e7"),n=r("e330"),o=r("59ed"),i=r("7b0b"),s=r("07fa"),c=r("083a"),u=r("577e"),d=r("d039"),l=r("addb"),p=r("a640"),h=r("3f7e"),m=r("99f4"),f=r("1212"),b=r("ea83"),y=[],C=n(y.sort),T=n(y.push),j=d((function(){y.sort(void 0)})),g=d((function(){y.sort(null)})),v=p("sort"),x=!d((function(){if(f)return f<70;if(!(h&&h>3)){if(m)return!0;if(b)return b<603;var t,e,r,a,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(a=0;a<47;a++)y.push({k:e+a,v:r})}for(y.sort((function(t,e){return e.v-t.v})),a=0;a<y.length;a++)e=y[a].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),O=j||!g||!v||!x,w=function(t){return function(e,r){return void 0===r?-1:void 0===e?1:void 0!==t?+t(e,r)||0:u(e)>u(r)?1:-1}};a({target:"Array",proto:!0,forced:O},{sort:function(t){void 0!==t&&o(t);var e=i(this);if(x)return void 0===t?C(e):C(e,t);var r,a,n=[],u=s(e);for(a=0;a<u;a++)a in e&&T(n,e[a]);l(n,w(t)),r=s(n),a=0;while(a<r)e[a]=n[a++];while(a<u)c(e,a++);return e}})},"6b48":function(t,e,r){},acf6:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[r("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"客户跟进次数分析","module-type":"customer"},on:{load:function(e){t.loading=!0},change:t.searchClick}}),t._v(" "),r("div",{staticClass:"content"},[t._m(0),t._v(" "),r("div",{staticClass:"table-content"},[r("div",{staticClass:"handle-bar"},[r("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),t.showTable?r("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",stripe:t.WKConfig.tableStyle.stripe,data:t.list,"cell-class-name":t.cellClassName,"summary-method":t.getSummaries,height:"400","show-summary":"","highlight-current-row":""},on:{"row-click":t.handleRowClick,"sort-change":function(e){var r=e.prop,a=e.order;return t.mixinSortFn(t.list,r,a)}}},t._l(t.fieldList,(function(t,e){return r("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,sortable:"custom","show-overflow-tooltip":""}})}))):t._e()],1)]),t._v(" "),r("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}}),t._v(" "),t.recordShow?r("record-list",{attrs:{"crm-type":t.rowType,request:t.recordRequest,params:t.recordParams},on:{handle:t.getList,hide:function(e){t.recordShow=!1}}}):t._e()],1)},n=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"axis-content"},[r("div",{attrs:{id:"axismain"}})])}],o=r("5530"),i=(r("14d9"),r("df55")),s=r("f643"),c=r("f4f4"),u=r("b80b"),d=r("a837"),l=r("313e"),p=r("2602"),h=r("e170"),m={name:"CustomerRecordStatistics",components:{RecordList:d["a"]},mixins:[i["a"],s["a"],c["a"],u["a"]],data:function(){return{loading:!1,axisOption:null,list:[],postParams:{},dataIndex:null,axisList:[],fieldList:[{field:"realname",name:"员工姓名"},{field:"recordNum",name:"跟进次数"},{field:"outSignRecordNum",name:"外勤签到次数"},{field:"customerNum",name:"跟进客户数"},{field:"recordNumRate",name:"客户跟进占比"}],detailFields:[{name:"recordNum",list:[],followType:"times",fieldType:"followTimes",request:h["c"]},{name:"customerNum",fieldType:"followCustomes",customerNum:!0,list:[],request:p["e"]}],recordShow:!1,rowType:"record",recordRequest:h["c"],recordParams:{activityType:2,dateFilter:"",isOa:!0,limit:15,page:1,queryType:1,recordType:1,userList:[]}}},mounted:function(){this.initAxis()},methods:{getList:function(){},searchClick:function(t){this.postParams=t,this.getDataList(),this.getRecordList()},getDataList:function(){var t=this;this.loading=!0,Object(p["o"])(this.postParams).then((function(e){t.loading=!1,t.axisList=e.data||[];for(var r=[],a=[],n=[],o=0;o<e.data.length;o++){var i=e.data[o];r.push(i.customerNum),a.push(i.recordNum),n.push(i.type)}t.axisOption.xAxis[0].data=n,t.axisOption.series[0].data=r,t.axisOption.series[1].data=a,t.chartObj.setOption(t.axisOption,!0)})).catch((function(){t.loading=!1}))},getRecordList:function(t){var e=this;this.dataIndex=t,this.list=[],this.loading=!0,Object(p["m"])(this.postParams).then((function(t){e.loading=!1,e.list=t.data||[]})).catch((function(){e.loading=!1}))},initAxis:function(){var t=this,e=l["b"](document.getElementById("axismain")),r={color:this.echartLineBarColors,toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(o["a"])({data:["跟进客户数","跟进次数"]},this.chartDefaultOptions.legend),grid:this.chartDefaultOptions.grid,xAxis:[Object(o["a"])({type:"category",data:[]},this.chartXAxisStyle)],yAxis:[Object(o["a"])({type:"value",name:"跟进客户数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value} 个"}})),Object(o["a"])({type:"value",name:"跟进次数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value} 次"},splitLine:{show:!0}}))],series:[{name:"跟进客户数",type:"bar",yAxisIndex:0,barMaxWidth:15,data:[]},{name:"跟进次数",type:"bar",yAxisIndex:1,barMaxWidth:15,data:[]}]};e.setOption(r,!0),e.on("click",(function(e){t.getRecordList(e.dataIndex)})),this.axisOption=r,this.chartObj=e},exportClick:function(){this.requestExportInfo(p["n"],this.postParams)}}},f=m,b=(r("4ca6"),r("2877")),y=Object(b["a"])(f,a,n,!1,null,"b2eb6a40",null);e["default"]=y.exports},f4f4:function(t,e,r){"use strict";r("d81d"),r("13d5"),r("e9f5"),r("d866"),r("7d54"),r("ab43"),r("9485"),r("a9e3"),r("d3b7"),r("159b");e["a"]={data:function(){return{summaryData:null}},methods:{getSummariesData:function(t){this.summaryData=t||{}},getSummaries:function(t){var e=t.columns,r=t.data,a=[];return e.forEach((function(t,e){if(0!==e){var n=r.map((function(e){return Number(e[t.property])}));n.every((function(t){return isNaN(t)}))?a[e]="":a[e]=n.reduce((function(t,e){var r=Number(e);return isNaN(r)?t:t+e}),0)}else a[e]="合计"})),a}}}},f643:function(t,e,r){"use strict";r("4e82"),r("a9e3"),r("d3b7"),r("25f0");e["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(t))return[];if(!e)return t;function a(t,a){if(t[e]===a[e])return 0;var n=!isNaN(Number(t[e]))&&!isNaN(Number(a[e])),o=n?Number(t[e])<Number(a[e]):t[e]<a[e];return"descending"===r?o?1:-1:o?-1:1}t.sort(a)}}}}}]);