(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5bb7990d"],{6952:function(e,t,a){},dae4:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("wk-page-header",{attrs:{title:e.config.showModuleName?"回访管理":"",help:e.getHelpObj(e.crmType,"index"),dropdowns:e.getDefaultHeaderHandes()},on:{command:e.pageHeaderCommand}},[a("template",{slot:"right"},[e.saveAuth?a("el-button",{attrs:{type:"primary"},on:{click:e.createClick}},[e._v("新建回访")]):e._e()],1)],2),e._v(" "),a("div",{directives:[{name:"empty",rawName:"v-empty",value:!e.indexAuth,expression:"!indexAuth"}],staticClass:"crm-container",attrs:{"xs-empty-icon":"nopermission","xs-empty-text":"暂无权限"}},[a("wk-table-header",{attrs:{search:e.search,tabs:e.sceneList,"active-tab":e.sceneId,"selection-list":e.tableSelectionList,operations:e.handleOperations,"condition-type-fun":void 0,fields:e.getFilterFields,props:e.tableHeaderProps.props,"filter-header-props":e.tableHeaderProps.filterHeaderProps,"filter-form-props":e.tableHeaderProps.filterFormProps,"scene-set-props":e.tableHeaderProps.sceneSetProps,"scene-create-props":e.tableHeaderProps.sceneCreateProps},on:{"update:search":function(t){e.search=t},"update:activeTab":function(t){e.sceneId=t},"tabs-change":e.sceneSelect,"operations-click":e.tableOperationsClick,"event-change":e.tableHeaderHandle,"filter-change":e.handleFilter}}),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.crmTableClass,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,"row-key":e.crmType+"Id",stripe:e.tableStyleObj.stripe,"use-virtual":"","highlight-current-row":""},on:{"row-click":e.handleRowClick,"sort-change":e.sortChange,"header-dragend":e.handleHeaderDragend,"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"","reserve-selection":"",type:"selection",fixed:"",align:"center",width:"55"}}),e._v(" "),e._l(e.fieldList,(function(t,o){return a("el-table-column",{key:o,attrs:{fixed:1===t.isLock,prop:t.prop,label:t.label,width:t.width,"class-name":t.width>60?"column":"",sortable:"custom","show-overflow-tooltip":""},scopedSlots:e._u([{key:"otherHeader",fn:function(o){return t.width>60?[a("el-button",{staticClass:"el-lock-btn",attrs:{icon:1===t.isLock?"wk wk-unlock":"wk wk-lock",type:"text"},on:{click:function(a){a.stopPropagation(),e.fieldFixed(t)}}}),e._v(" "),e.showFilter(t)?a("el-button",{staticClass:"el-filter-btn",attrs:{type:"text",icon:"wk wk-screening"},on:{click:function(a){a.stopPropagation(),e.showFilterClick(t)}}}):e._e()]:void 0}},{key:"default",fn:function(o){var n=o.row,i=o.column;return[a("wk-field-view",{attrs:{props:t,"form-type":t.formType,value:n[i.property]}},[[e._v("\n              "+e._s(e.fieldFormatter(n,i,n[i.property],t))+"\n            ")]],2)]}}])})})),e._v(" "),a("el-table-column"),e._v(" "),a("wk-empty",{attrs:{slot:"empty",props:{buttonTitle:"新建回访",showButton:e.saveAuth}},on:{click:e.createClick},slot:"empty"}),e._v(" "),a("field-set",{attrs:{slot:"other","crm-type":e.crmType},on:{change:e.setSave},slot:"other"})],2),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-dropdown",{attrs:{trigger:"click",placement:"top"}},[a("el-button",{staticClass:"dropdown-btn"},[a("i",{staticClass:"el-icon-s-fold"})]),e._v(" "),a("el-dropdown-menu",{staticClass:"wk-table-style-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.rightBorderShow,callback:function(t){e.$set(e.tableStyleObj,"rightBorderShow",t)},expression:"tableStyleObj.rightBorderShow"}}),e._v("显示竖向分割线")],1)]),e._v(" "),a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.bottomBorderShow,callback:function(t){e.$set(e.tableStyleObj,"bottomBorderShow",t)},expression:"tableStyleObj.bottomBorderShow"}}),e._v("显示横向分割线")],1)]),e._v(" "),a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.stripe,callback:function(t){e.$set(e.tableStyleObj,"stripe",t)},expression:"tableStyleObj.stripe"}}),e._v("显示斑马纹")],1)])],1)],1),e._v(" "),a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("c-r-m-all-detail",{staticClass:"d-view",attrs:{id:e.rowID,visible:e.showDview,"crm-type":e.rowType,"page-list":e.crmType==e.rowType?e.list:[],"page-index":e.rowIndex},on:{"update:id":function(t){e.rowID=t},"update:visible":function(t){e.showDview=t},"update:pageIndex":function(t){e.rowIndex=t},handle:e.handleHandle}}),e._v(" "),e.createShow?a("visit-create",{on:{close:function(t){e.createShow=!1},"save-success":e.handleHandle}}):e._e()],1)},n=[],i=(a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ce1c")),r=a("e65a"),l=a("c8fa"),s=a("e505"),c={name:"VisitIndex",components:{VisitCreate:r["a"],CRMAllDetail:l["a"]},mixins:[s["a"]],data:function(){return{crmType:"visit",createShow:!1}},computed:{handleOperations:function(){return this.getOperations(["delete"])}},mounted:function(){},methods:{tableOperationsClick:function(e){var t=this;"delete"===e&&this.$confirm("确定删除选中的".concat(this.selectionList.length,"项吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(i["a"])(t.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]}))).then((function(a){t.loading=!1,t.$message({type:"success",message:"删除成功"}),t.handleHandle({type:e})})).catch((function(){t.handleHandle({type:e}),t.loading=!1}))})).catch((function(){}))},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"visitNumber"===t.property||"customerName"===t.property||"contractNum"===t.property||"contactsName"===t.property?"can-visit--underline":""},createClick:function(){this.createShow=!0}}},p=c,d=(a("e764"),a("2877")),u=Object(d["a"])(p,o,n,!1,null,"c14faf36",null);t["default"]=u.exports},e764:function(e,t,a){"use strict";a("6952")}}]);