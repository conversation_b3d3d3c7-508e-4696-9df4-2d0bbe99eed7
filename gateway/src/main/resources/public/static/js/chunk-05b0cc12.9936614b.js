(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-05b0cc12"],{"0042":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-map-position",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("flexbox",{staticClass:"box-select"},[i("div",{class:{placeholder:!Bo<PERSON>an(e.areaText)}},[e._v("\n      "+e._s(e.areaText||"请选择")+"\n    ")]),e._v(" "),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),1===e.field.precisions?i("div",{staticClass:"box-textarea"},[i("div",{class:{placeholder:!<PERSON><PERSON><PERSON>(e.detailAddress)}},[e._v("\n      "+e._s(e.detailAddress||"详细地址")+"\n    ")])]):e._e()],1)},n=[],a=(i("4de4"),i("7db0"),i("a15b"),i("d81d"),i("b0c0"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("d3b7"),i("f44c")),s=i("a46a"),o=i("6bfe"),r={name:"FieldPosition",components:{FieldWrapper:a["a"]},mixins:[s["a"]],computed:{areaText:function(){return Object(o["b"])(this.field.defaultValue)?"":this.field.defaultValue.filter((function(e){return 4!==e.id})).map((function(e){return e.name})).join("/")},detailAddress:function(){if(Object(o["b"])(this.field.defaultValue))return"";var e=this.field.defaultValue.find((function(e){return 4===e.id}));return e?e.name:""}},methods:{}},c=r,d=(i("7869"),i("2877")),u=Object(d["a"])(c,l,n,!1,null,"238d449a",null);t["default"]=u.exports},"0044":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-textarea",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"box"},[i("div",{staticClass:"default-val"},[e._v("\n      "+e._s("string"==typeof e.field.defaultValue?e.field.defaultValue:"")+"\n    ")]),e._v(" "),i("div",{staticClass:"max-tips"},[e._v("\n      "+e._s((e.field.defaultValue||"").length+"/"+(e.field.maxLength||800))+"\n    ")])])])},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldTextarea",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("2dac"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"46b1ac92",null);t["default"]=d.exports},"013f":function(e,t,i){"use strict";i("aea1")},"0437":function(e,t,i){e.exports=i.p+"static/img/drag.505e3d3d.png"},"0bf4":function(e,t,i){},"0ea5":function(e,t,i){"use strict";i("543a")},1452:function(e,t,i){},"156f":function(e,t,i){"use strict";i("bfdd")},"1a52":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-writing-sign",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"box"})])},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldWritingSign",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("3edd"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"69453261",null);t["default"]=d.exports},"1df2":function(e,t,i){},"1f90":function(e,t,i){"use strict";i("fb73")},2099:function(e,t,i){"use strict";i("3bb8")},"20da":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-current-position",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("flexbox",{staticClass:"box",attrs:{align:"center"}},[i("flexbox-item",{staticClass:"default-val"}),e._v(" "),i("span",{staticClass:"wk wk-icon-location"})],1)],1)},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldCurrentPosition",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("0ea5"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"3c56395e",null);t["default"]=d.exports},2108:function(e,t,i){},2593:function(e,t,i){},"264e":function(e,t,i){},"2c81":function(e,t,i){"use strict";var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.typeObj?i("div",{directives:[{name:"clickoutside",rawName:"v-clickoutside",value:e.clickOutSide,expression:"clickOutSide"}],staticClass:"field-setting"},[i("div",{staticClass:"setting-title"},[e._v("\n    "+e._s(e.typeObj.name)),e.titleHelpObj?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{slot:"label","data-type":e.titleHelpObj.type,"data-id":e.titleHelpObj.id},on:{click:function(e){e.stopPropagation()}},slot:"label"}):e._e()]),e._v(" "),i("div",{staticClass:"setting-body"},[e.isDescText?e._e():[i("div",{staticClass:"item-section"},[e._m(0),e._v(" "),i("el-input",{attrs:{maxlength:250,disabled:!e.fieldAuth.nameEdit},model:{value:e.field.name,callback:function(t){e.$set(e.field,"name",t)},expression:"field.name"}}),e._v(" "),i("div",{staticClass:"input-tips"},[e._v("标识名不能为空")])],1),e._v(" "),i("div",{staticClass:"item-section"},[e._m(1),e._v(" "),i("el-input",{attrs:{rows:3,maxlength:60,disabled:!e.fieldAuth.nameEdit,type:"textarea",resize:"none"},model:{value:e.field.inputTips,callback:function(t){e.$set(e.field,"inputTips",t)},expression:"field.inputTips"}}),e._v(" "),i("div",{staticClass:"input-tips"},[e._v("显示在标识名右侧的说明文字")])],1),e._v(" "),"detail_table"===e.field.formType?i("setting-detail-table",{attrs:{field:e.field},on:{"child-edit":e.emitChildEdit}}):e._e(),e._v(" "),e.canOptions?[i("div",{staticClass:"item-section"},[e._m(2),e._v(" "),i("div",{staticClass:"input-tips"},[e._v("修改选项后该项设置的逻辑表单会失效")]),e._v(" "),i("setting-options",{attrs:{field:e.field,"is-table-child":e.isTableChild}})],1),e._v(" "),!e.isTableChild&&e.showLogic?i("div",{staticClass:"item-section"},[e._m(3),e._v(" "),i("setting-logic-form",{attrs:{field:e.field,point:e.point,"field-arr":e.fieldArr}})],1):e._e()]:e._e(),e._v(" "),e.canPrecisions?i("div",{staticClass:"item-section"},[i("div",{staticClass:"name"},[e._v("\n          "+e._s(e.precisionsTitle)+"\n        ")]),e._v(" "),i("setting-precisions",{attrs:{field:e.field}})],1):e._e(),e._v(" "),e.isAttention?i("div",{staticClass:"item-section"},[i("setting-attention",{attrs:{field:e.field}})],1):e._e(),e._v(" "),e.canDefault?i("div",{staticClass:"item-section"},[i("div",{staticClass:"name"},[e._v("默认值")]),e._v(" "),i("setting-default",{attrs:{field:e.field}})],1):e._e(),e._v(" "),e.canNumber?i("div",{staticClass:"item-section"},[i("setting-number",{attrs:{field:e.field}})],1):e._e(),e._v(" "),e.isSerialNumber?i("div",{staticClass:"item-section"},[i("div",{staticClass:"name"},[e._v("编号规则")]),e._v(" "),i("setting-serial-number",{attrs:{field:e.field,point:e.point,"field-arr":e.fieldArr}})],1):e._e(),e._v(" "),e.isTag?i("div",{staticClass:"item-section"},[e._m(4),e._v(" "),i("setting-tag",{attrs:{field:e.field}})],1):e._e()],e._v(" "),e.isDescText?i("div",{staticClass:"item-section"},[i("div",{staticClass:"name"},[e._v("内容")]),e._v(" "),i("setting-desc-text",{attrs:{field:e.field}})],1):e._e(),e._v(" "),e.fieldAuth.percentEdit?i("div",{staticClass:"item-section"},[e._m(5),e._v(" "),i("el-radio-group",{attrs:{size:"medium"},on:{change:e.emitUpdateWidth},model:{value:e.field.stylePercent,callback:function(t){e.$set(e.field,"stylePercent",t)},expression:"field.stylePercent"}},e._l(e.widthOptions,(function(t){return i("el-radio-button",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.value))])})))],1):e._e(),e._v(" "),e.canTransform&&e.transformData&&e.transformData[e.field.formType]?i("div",{staticClass:"item-section"},[i("div",{staticClass:"name"},[e._v("转化客户字段")]),e._v(" "),i("el-select",{attrs:{clearable:""},model:{value:e.field.relevant,callback:function(t){e.$set(e.field,"relevant",t)},expression:"field.relevant"}},e._l(e.transformData[e.field.formType],(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1):e._e(),e._v(" "),!e.isDescText&&(e.fieldAuth.nullEdit||e.fieldAuth.uniqueEdit||e.fieldAuth.hiddenEdit)?i("div",{staticClass:"item-section"},[e.fieldAuth.nullEdit?i("div",{staticClass:"item-check-section"},[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.field.isNull,callback:function(t){e.$set(e.field,"isNull",t)},expression:"field.isNull"}},[e._v("设为必填")]),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"243"}})],1):e._e(),e._v(" "),e.fieldAuth.uniqueEdit?i("div",{staticClass:"item-check-section"},[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.field.isUnique,callback:function(t){e.$set(e.field,"isUnique",t)},expression:"field.isUnique"}},[e._v("设为唯一")]),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"244"}})],1):e._e(),e._v(" "),e.fieldAuth.hiddenEdit?i("div",{staticClass:"item-check-section"},[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.field.isHidden,callback:function(t){e.$set(e.field,"isHidden",t)},expression:"field.isHidden"}},[e._v("隐藏字段")]),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"245"}})],1):e._e()]):e._e()],2)]):e._e()},n=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[i("span",[e._v("*")]),e._v("标识名"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"240"}})])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[i("span",[e._v("*")]),e._v("提示文字"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"241"}})])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[i("span",[e._v("*")]),e._v("选项内容")])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[e._v("逻辑表单"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"248"}})])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[e._v("标签管理"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"253"}})])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[e._v("\n        字段占比 %"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"242"}})])}],a=(i("7db0"),i("caad"),i("e9f5"),i("f665"),i("a9e3"),i("d3b7"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-default"},["text"===e.type?i("el-input",{attrs:{maxlength:e.field.maxLength||100,disabled:e.disabled},on:{blur:e.inputBlur},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}}):"textarea"===e.type?i("el-input",{attrs:{maxlength:e.field.maxLength||800,disabled:e.disabled},on:{blur:e.inputBlur},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}}):"datePicker"===e.type?i("el-date-picker",{attrs:{disabled:e.disabled,type:"date"===e.field.formType?"date":"datetime","value-format":"date"===e.field.formType?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss",placeholder:"请选择"},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}}):"date_interval"===e.type?i("el-date-picker",{attrs:{type:1===e.field.precisions?"daterange":"datetimerange","value-format":1===e.field.precisions?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss",disabled:e.disabled,"start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}}):"select"===e.type?i("el-select",{key:e.field.formType,attrs:{clearable:e.canClearable,multiple:"checkbox"===e.field.formType,disabled:e.disabled,placeholder:"请选择"},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}},e._l(e.options,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label||e.name,value:e.value}})}))):"number"===e.type?[i("el-input",{attrs:{disabled:e.disabled},on:{blur:e.inputBlur},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}},["percent"===e.field.formType?i("div",{staticClass:"el-input__icon",attrs:{slot:"suffix"},slot:"suffix"},[e._v("%")]):e._e()]),e._v(" "),i("div",{staticClass:"input-tips"},[i("span",[e._v("*")]),e._v("\n      数字的位数必须少于"+e._s("percent"===e.field.formType?10:15)+"位\n    ")])]:"position"===e.type?[i("wk-distpicker",{attrs:{"hide-area":e.field.precisions>=3,"only-province":4===e.field.precisions,disabled:e.disabled,clearable:""},on:{change:e.handleCascaderChange},model:{value:e.selectedMapValue,callback:function(t){e.selectedMapValue=t},expression:"selectedMapValue"}}),e._v(" "),1===e.field.precisions?i("el-input",{staticStyle:{"margin-top":"5px"},attrs:{rows:3,maxlength:100,disabled:e.disabled,type:"textarea"},on:{change:e.inputPositionChange},model:{value:e.detailAddress,callback:function(t){e.detailAddress=t},expression:"detailAddress"}}):e._e()]:e._e()],2)}),s=[],o=(i("4de4"),i("c740"),i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("910d"),i("ab43"),i("ac1f"),i("5319"),i("5047")),r=i("6bfe"),c=i("ed08"),d=i("6d94"),u={name:"SettingDefault",components:{WkDistpicker:o["a"]},props:{field:{type:Object,required:!0}},data:function(){return{selectedMapValue:[],detailAddress:"",oldPrecisions:null,areaData:[]}},computed:{disabled:function(){return!Object(d["a"])(this.field.operating).defaultEdit},canClearable:function(){var e=this.field.formType;return!["boolean_value"].includes(e)},type:function(){var e=this.field.formType;if(["date","datetime"].includes(e))return"datePicker";if(["number","floatnumber","percent"].includes(e))return"number";if(["select","checkbox","boolean_value","field_attention"].includes(e))return"select";switch(this.field.formType){case"date_interval":return"date_interval";case"position":return"position";case"textarea":return"textarea";default:return"text"}},options:function(){if("select"!==this.type)return[];var e=this.field.formType;if(["select","checkbox"].includes(e))return this.field.setting&&this.field.setting.length>0&&this.itemIsObject(this.field.setting[0])?this.field.setting:this.field.setting.map((function(e){return{label:e,value:e}}));switch(e){case"boolean_value":return[{label:"选中",value:"1"},{label:"不选中",value:"0"}];case"field_attention":return[{label:"一星",value:1},{label:"二星",value:2},{label:"三星",value:3},{label:"四星",value:4},{label:"五星",value:5}];default:return[]}}},watch:{field:{handler:function(){if("boolean_value"!==this.field.formType)if("field_attention"!==this.field.formType){if("select"===this.type&&Object(r["b"])(this.field.setting)&&!Object(r["b"])(this.field.options)&&this.$set(this.field,"setting",this.field.options.split(",")),"position"===this.type)if(this.resetDefaultValue(),Object(r["b"])(this.field.defaultValue))this.selectedMapValue=[],Object(r["a"])(this.field.defaultValue)||(this.field.defaultValue=[]),this.detailAddress="";else if(this.selectedMapValue=this.field.defaultValue.filter((function(e){return 4!==e.id})),1===this.field.precisions){var e=this.field.defaultValue.find((function(e){return 4===e.id}));this.detailAddress=e?e.name:""}}else this.field.defaultValue=Object(r["b"])(this.field.defaultValue)?null:Number(this.field.defaultValue);else this.field.defaultValue=Object(r["b"])(this.field.defaultValue)?"0":this.field.defaultValue},deep:!0,immediate:!0}},methods:{inputBlur:function(){if(this.field.defaultValue)if("mobile"===this.field.formType)Object(c["F"])(this.field.defaultValue)||(this.$message.error("输入的手机格式有误"),this.field.defaultValue="");else if("email"===this.field.formType)Object(c["E"])(this.field.defaultValue)||(this.$message.error("输入的邮箱格式有误"),this.field.defaultValue="");else if("number"===this.type){var e=Number(this.field.defaultValue);if(isNaN(e))return void(this.field.defaultValue=null);this.field.defaultValue=String(e);var t=String(e).split("."),i=String(e).replace(".","").replace("-","").length,l="percent"===this.field.formType?10:15;if(i>l)return this.$message.error("最多支持".concat(l,"位数字（包含小数位）")),void(this.field.defaultValue=null);var n=Object(r["b"])(this.field.minNumRestrict)?-1/0:Number(this.field.minNumRestrict||-1/0),a=Object(r["b"])(this.field.maxNumRestrict)?1/0:Number(this.field.maxNumRestrict||1/0);if(e<n)return this.$message.error("默认值不能小于最小值"),void(this.field.defaultValue=null);if(e>a)return this.$message.error("默认值不能大于最大值"),void(this.field.defaultValue=null);if(Object(r["b"])(this.field.precisions))return void(this.field.defaultValue=t[0]);if(0===this.field.precisions)return;t.length>1&&t[1].length>Number(this.field.precisions)&&(this.$message.error("默认值的小数位不能大于".concat(this.field.precisions)),this.field.defaultValue=null)}},resetDefaultValue:function(){this.oldPrecisions&&this.oldPrecisions!==this.field.precisions?(this.oldPrecisions=this.field.precisions,this.selectedMapValue=[],Object(r["b"])(this.field.defaultValue)||(this.field.defaultValue=[])):this.oldPrecisions=this.field.precisions},inputPositionChange:function(){if(1!==this.field.precisions){var e=this.field.defaultValue.findIndex((function(e){return 4===e.id}));if(-1===e)return;this.field.defaultValue.splice(e,1)}else{var t=this.field.defaultValue.find((function(e){return 4===e.id}));t?t.name=this.detailAddress:this.field.defaultValue.push({code:"",name:this.detailAddress,id:4})}},handleCascaderChange:function(){this.field.defaultValue=this.selectedMapValue,this.inputPositionChange()},getCascaderValArr:function(e,t){var i=[];if(0===t.length)return i;var l=0,n=Object(c["D"])(e);do{var a=n.find((function(e){return e.code===t[l]}));a&&(n=a.children||[],i.push({code:a.code,name:a.name,id:l+1})),l++}while(l<=t.length);return i},itemIsObject:function(e){return Object(r["c"])(e)}}},f=u,p=(i("1f90"),i("2877")),h=Object(p["a"])(f,a,s,!1,null,"1dd508c0",null),m=h.exports,b=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-options"},[i("draggable",{attrs:{list:e.optionsList,options:e.dragConfig},on:{sort:e.handleChange}},e._l(e.optionsList,(function(t,l){return i("div",{key:l,staticClass:"option-item"},[i("el-input",{attrs:{disabled:!e.optionsEditAuth},on:{change:e.handleChange},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}},[e.optionsEditAuth?i("flexbox",{attrs:{slot:"suffix"},slot:"suffix"},[i("div",{staticClass:"el-input__icon drag-hook wk wk-grid"}),e._v(" "),i("el-button",{staticClass:"el-input__icon wk wk-icon-bin",attrs:{disabled:e.optionsList.length<=1,type:"text"},on:{click:function(t){e.handleDelete(l)}}})],1):e._e()],1)],1)}))),e._v(" "),e.showOther?i("div",{staticClass:"option-item other-item"},[i("el-input",{attrs:{value:"其他",disabled:""}},[e.optionsEditAuth?i("flexbox",{attrs:{slot:"suffix"},slot:"suffix"},[i("el-button",{staticClass:"el-input__icon wk wk-icon-bin",attrs:{type:"text"},on:{click:function(t){e.handleDelete(-1)}}})],1):e._e()],1)],1):e._e(),e._v(" "),e.optionsEditAuth?i("el-button",{staticClass:"add-btn",on:{click:e.handleAdd}},[i("i",{staticClass:"el-icon-plus"}),e._v(" 添加新选项\n  ")]):e._e(),e._v(" "),e.optionsEditAuth?i("flexbox",{attrs:{align:"center",justify:"center"}},[i("div",{staticClass:"add-other-btn",on:{click:e.handleAddOther}},[e._v("\n      添加其他\n    ")]),e._v(" "),i("flexbox-item"),e._v(" "),i("div",{staticClass:"add-other-btn",on:{click:e.handleUpdateAll}},[e._v("批量编辑")])],1):e._e(),e._v(" "),i("el-dialog",{staticClass:"edit-dialog",attrs:{visible:e.dialogVisible,"before-close":e.handleCloseDialog,title:"批量编辑","append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",[i("div",{staticClass:"edit-tips"},[e._v("\n        每行内容对应一个选项，点击完成后，逻辑表单设置将失效\n      ")]),e._v(" "),i("el-input",{attrs:{rows:10,resize:"none",type:"textarea"},model:{value:e.dialogContentVal,callback:function(t){e.dialogContentVal=t},expression:"dialogContentVal"}})],1),e._v(" "),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleCloseDialog}},[e._v("\n        取消\n      ")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.handleDialogConfirm}},[e._v("\n        确定\n      ")])],1)])],1)},v=[],g=(i("99af"),i("a630"),i("a15b"),i("7d54"),i("e9c4"),i("b64b"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("2532"),i("3ca3"),i("1276"),i("498a"),i("159b"),i("ddb0"),i("b76a")),_=i.n(g),y={name:"SettingOptions",components:{draggable:_.a},props:{field:{type:Object,required:!0},isTableChild:{type:Boolean,default:!1}},data:function(){return{optionsList:[],dialogVisible:!1,dialogContentVal:""}},computed:{optionsEditAuth:function(){return Object(d["a"])(this.field.operating).optionsEdit},showOther:function(){return this.field.setting.includes("其他")},dragConfig:function(){return{group:Object(c["A"])(),forceFallback:!1,disabled:!this.optionsEditAuth,fallbackClass:"draggingStyle",handle:".drag-hook",filter:".el-input__inner",preventOnFilter:!1}}},watch:{field:{handler:function(e,t){if(!t||e.options!==t.options){var i=this.field.setting,l=i.length>0&&Object(r["c"])(i[0]);this.optionsList=l?i.map((function(e){return{value:e.name}})):i.filter((function(e){return"其他"!==e})).map((function(e){return{value:e}}))}},deep:!0,immediate:!0}},methods:{handleChange:function(){var e=this,t=this.optionsList.map((function(e){return e.value})).filter((function(e){return!Object(r["b"])(e)&&"其他"!==e}));if(0===t.length&&(this.optionsList=[{value:"选1"}],t=["选1"]),t=Array.from(new Set(t)),t.length!==this.optionsList.length&&(this.optionsList=t.map((function(e){return{value:e}}))),this.showOther&&t.push("其他"),this.field.setting=t,"options_type"===this.field.remark){var i={},l=Object.keys(this.field.optionsData);this.optionsList.forEach((function(t){l.includes(t.value)?i[t.value]=e.field.optionsData[t.value]:i[t.value]=[]})),Object.keys(i).forEach((function(t){var l=e.optionsList.find((function(e){return e.value===t&&"其他"!==t}));l||delete i[t]})),this.showOther?l.includes("其他")&&(i["其他"]=this.field.optionsData["其他"]):delete i["其他"],this.field.options=JSON.stringify(i),this.$set(this.field,"optionsData",i)}else this.field.options=t.join(",");this.$set(this.field,"setting",this.field.setting),this.$set(this.field,"options",this.field.options),this.$nextTick((function(){e.checkDefaultValue()}))},handleDelete:function(e){var t=null;if(-1!==e)t=this.field.setting[e],this.optionsList.splice(e,1),this.field.setting.splice(e,1);else{var i=this.field.setting.lastIndexOf("其他");-1!==i&&(this.field.setting.splice(i,1),t="其他")}"options_type"===this.field.remark?(delete this.field.optionsData[t],this.$set(this.field,"options",JSON.stringify(this.field.optionsData))):this.$set(this.field,"options",this.field.setting.join(",")),this.$set(this.field,"setting",this.field.setting),this.checkDefaultValue()},handleAdd:function(){var e=this.getAddValue(this.optionsList.length+1);this.optionsList.push({value:e}),this.handleChange()},handleAddOther:function(){-1===this.field.setting.indexOf("其他")&&this.field.setting.push("其他"),this.$set(this.field,"setting",this.field.setting),"options_type"===this.field.remark?(this.field.optionsData["其他"]=[],this.$set(this.field,"optionsData",this.field.optionsData),this.$set(this.field,"options",JSON.stringify(this.field.optionsData))):this.$set(this.field,"options",this.field.setting.join(","))},handleUpdateAll:function(){this.dialogContentVal=this.optionsList.map((function(e){return e.value})).join("\n"),this.dialogVisible=!0},handleCloseDialog:function(){this.dialogVisible=!1},handleDialogConfirm:function(){var e=this.dialogContentVal.split(/\n|\r/);e=Array.from(new Set(e)).map((function(e){return e.trim()})).filter((function(e){return!Object(r["b"])(e)&&"其他"!==e})),this.optionsList=e.map((function(e){return{value:e}})),this.$set(this.field,"remark",null),this.$set(this.field,"optionsData",null),this.handleChange(),this.handleCloseDialog()},getAddValue:function(e){var t=this.optionsList.find((function(t){return t.value==="选".concat(e)}));return t?this.getAddValue(e+1):"选".concat(e)},checkDefaultValue:function(){var e=this;if(!Object(r["b"])(this.field.defaultValue))if(Object(r["a"])(this.field.defaultValue)){var t=[];this.field.defaultValue.forEach((function(i){var l=e.optionsList.find((function(e){return e.value===i}));l&&t.push(i)})),this.$set(this.field,"defaultValue",[].concat(t))}else{var i=this.optionsList.find((function(t){return t.value===e.field.defaultValue}));i||this.$set(this.field,"defaultValue",null)}}}},k=y,x=(i("f957"),Object(p["a"])(k,b,v,!1,null,"729780a0",null)),C=x.exports,w=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-number"},[i("flexbox",{staticClass:"setting-number-item",attrs:{align:"center",justify:"flex-start"}},[i("el-checkbox",{on:{change:e.checkedChange},model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),e._v(" "),i("span",{staticStyle:{"font-size":"13px"}},[e._v("支持小数")]),e._v(" "),e.checked?[i("span",{staticStyle:{margin:"0 4px"}},[e._v("限制")]),e._v(" "),i("el-select",{attrs:{size:"small",placeholder:""},on:{change:e.handleSelectChange},model:{value:e.field.precisions,callback:function(t){e.$set(e.field,"precisions",t)},expression:"field.precisions"}},e._l(e.precisionList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),i("span",[e._v(" 位")])]:e._e()],2),e._v(" "),i("flexbox",{staticClass:"setting-number-item",attrs:{align:"flex-start",justify:"flex-start",direction:"column"}},[i("el-checkbox",{on:{change:e.limitChange},model:{value:e.limitChecked,callback:function(t){e.limitChecked=t},expression:"limitChecked"}},[e._v("\n      限制数值范围\n    ")]),e._v(" "),e.limitChecked?i("flexbox",{staticClass:"number-range-body",attrs:{align:"center",justify:"flex-start"}},[i("flexbox-item",[i("el-input-number",{attrs:{controls:!1,placeholder:"最小值"},on:{change:function(t){e.handleChangeNumber("minNumRestrict")}},model:{value:e.minNumRestrict,callback:function(t){e.minNumRestrict=t},expression:"minNumRestrict"}})],1),e._v(" "),i("div",{staticClass:"number-range-text"},[e._v("~")]),e._v(" "),i("flexbox-item",[i("el-input-number",{attrs:{controls:!1,placeholder:"最大值"},on:{change:function(t){e.handleChangeNumber("maxNumRestrict")}},model:{value:e.maxNumRestrict,callback:function(t){e.maxNumRestrict=t},expression:"maxNumRestrict"}})],1)],1):e._e()],1)],1)},T=[],F={name:"SettingNumber",props:{field:{type:Object,required:!0}},data:function(){return{checked:!1,precisionList:[],limitChecked:!1,minNumRestrict:void 0,maxNumRestrict:void 0}},watch:{field:{handler:function(){if(["number","floatnumber","percent"].includes(this.field.formType)){this.field.hasOwnProperty("minNumRestrict")||(this.field.minNumRestrict=null),this.field.hasOwnProperty("maxNumRestrict")||(this.field.maxNumRestrict=null),this.minNumRestrict=Object(r["b"])(this.field.minNumRestrict)?void 0:Number(this.field.minNumRestrict),this.maxNumRestrict=Object(r["b"])(this.field.maxNumRestrict)?void 0:Number(this.field.maxNumRestrict);var e="percent"===this.field.formType?5:14;this.precisionList=Array.from({length:e}).map((function(e,t){return{label:t+1,value:t+1}})),this.field.hasOwnProperty("precisions")||(this.field.precisions="number"===this.field.formType?4:2),this.field.precisions>e&&(this.field.precisions=e),this.checked=!Object(r["b"])(this.field.precisions),this.limitChecked=!Object(r["b"])(this.minNumRestrict)||!Object(r["b"])(this.maxNumRestrict)}},deep:!0,immediate:!0}},methods:{checkedChange:function(){this.checked?this.field.precisions=2:this.field.precisions=null},handleSelectChange:function(){this.$set(this.field,"precisions",this.field.precisions),this.$forceUpdate()},limitChange:function(){this.limitChecked||(this.minNumRestrict=void 0,this.maxNumRestrict=void 0,this.field.minNumRestrict="",this.field.maxNumRestrict="")},handleChangeNumber:function(e){var t=this[e],i=String(t||"").replace(".","").replace("-","").length,l="percent"===this.field.formType?10:15;if(i>l)return this.$message.error("最多支持".concat(l,"位数字")),void(this.field[e]=null);var n=this.minNumRestrict,a=this.maxNumRestrict;Object(r["b"])(n)||Object(r["b"])(a)||Number(n)>Number(a)&&(this.$message.error("请输入正确的数值范围"),this.field[e]=null);var s=Object(r["b"])(n)?"":n,o=Object(r["b"])(a)?"":a;this.field.minNumRestrict=null!==this.minNumRestrict?String(s):null,this.field.maxNumRestrict=null!==this.maxNumRestrict?String(o):null}}},V=F,A=(i("8b32"),i("156f"),Object(p["a"])(V,w,T,!1,null,"3bcc723c",null)),O=A.exports,E=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-precisions"},[i("el-select",{attrs:{disabled:!e.optionsEditAuth,placeholder:"请选择"},model:{value:e.field.precisions,callback:function(t){e.$set(e.field,"precisions",t)},expression:"field.precisions"}},e._l(e.options,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1)},$=[],N={name:"SettingPrecisions",props:{field:{type:Object,required:!0}},data:function(){return{options:[]}},computed:{optionsEditAuth:function(){return Object(d["a"])(this.field.operating).optionsEdit}},watch:{field:{handler:function(){["date_interval","position","select","checkbox"].includes(this.field.formType)&&("date_interval"===this.field.formType?this.options=[{label:"日期",value:1},{label:"日期时间",value:2}]:"position"===this.field.formType?this.options=[{label:"省/地区、市、区/县、详细地址",value:1},{label:"省/地区、市、区/县",value:2},{label:"省/地区、市",value:3},{label:"省/地区",value:4}]:(this.options=[{label:"平铺",value:1},{label:"下拉",value:2}],this.field.precisions||this.$set(this.field,"precisions","checkbox"===this.field.formType?1:2)),this.field.precisions||this.$set(this.field,"precisions",1))},deep:!0,immediate:!0}}},j=N,L=(i("b71a"),Object(p["a"])(j,E,$,!1,null,"dbce85d6",null)),D=L.exports,S=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-rich-text"},[i("tinymce",{ref:"createTinymce",staticClass:"rich-txt",attrs:{init:e.getEditConfig(),height:200,toolbar:"bold italic underline strikethrough | fontselect | forecolor backcolor | fontsizeselect | numlist bullist | alignleft aligncenter alignright | image link | removeformat"},on:{input:e.debouncedEditorInput},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}})],1)},I=[],P=(i("4d63"),i("c607"),i("2c3e"),i("25f0"),i("8256")),R=i("7a1a"),W={name:"SettingRichText",components:{Tinymce:P["a"]},props:{field:{type:Object,required:!0}},data:function(){return{debouncedEditorInput:null}},created:function(){this.debouncedEditorInput=Object(R["debounce"])(300,this.editInputChange)},methods:{getEditConfig:function(){return{menubar:!1,statusbar:!1,paste_data_images:!0,paste_enable_default_filters:!1,placeholder:"描述文字内容",content_style:" * {color: #262626; margin: 0;} body { margin: 8px; font-size: 14px; }",paste_retain_style_properties:"border",toolbar_mode:"scrolling",paste_preprocess:function(e,t){var i=["b","strong","i","em"];i.forEach((function(e){var i=new RegExp("(<".concat(e,">)|(</").concat(e,">)]"),"g");t.content=t.content.replace(i,"")}));var l=["h1","h2","h3","h4","h5","h6"];l.forEach((function(e){var i=new RegExp("<".concat(e,">"),"g"),l=new RegExp("</".concat(e,">"),"g");t.content=t.content.replace(i,"<p>"),t.content=t.content.replace(l,"</p>")})),t.content=t.content.replace(/<\/font>/gi,"").replace(/<font[^>]+>/gi,"")},paste_postprocess:function(e,t){var i=Array.from(t.node.querySelectorAll("*"));i.forEach((function(e){e.style.color="",e.style.fontWeight="",e.style.fontFamily="",e.style.fontSize="",e.style.background=""}))}}},editInputChange:function(){}}},B=W,H=Object(p["a"])(B,S,I,!1,null,"9604d066",null),M=H.exports,q=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-detail-table"},[i("div",{staticClass:"item-section"},[i("div",{staticClass:"name"},[e._v("表格字段")]),e._v(" "),i("draggable",{attrs:{list:e.field.fieldExtendList,options:e.dragConfig},on:{sort:e.handleChange}},e._l(e.field.fieldExtendList,(function(t,l){return i("flexbox",{key:l,staticClass:"option-item",attrs:{align:"center",justify:"flex-start"}},[i("i",{staticClass:"type-icon",class:e.typeObj(t.formType).icon}),e._v(" "),i("div",{staticClass:"option-item__name"},[e._v(e._s(t.name))]),e._v(" "),i("el-button",{staticClass:"option-item__icon wk wk-write",attrs:{type:"text"},on:{click:function(t){e.handleEdit(l)}}}),e._v(" "),i("el-button",{staticClass:"option-item__icon wk wk-icon-bin",attrs:{type:"text"},on:{click:function(t){e.handleDelete(l)}}}),e._v(" "),i("div",{staticClass:"option-item__icon drag-hook wk wk-grid"})],1)})))],1),e._v(" "),i("div",{staticClass:"item-section"},[e._m(0),e._v(" "),i("div",[i("el-input",{attrs:{maxlength:10},model:{value:e.field.remark,callback:function(t){e.$set(e.field,"remark",t)},expression:"field.remark"}})],1)]),e._v(" "),i("div",{staticClass:"item-section"},[e._m(1),e._v(" "),i("el-radio-group",{model:{value:e.field.precisions,callback:function(t){e.$set(e.field,"precisions",t)},expression:"field.precisions"}},[i("el-radio",{attrs:{label:1}},[e._v("列表")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("表格")])],1)],1)])},G=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[e._v("动作名"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"251"}})])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"name"},[e._v("\n      填写方式"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"264"}})])}],z=i("5240"),U={name:"SettingDetailTable",components:{draggable:_.a},props:{field:{type:Object,required:!0}},data:function(){return{dragConfig:{group:Object(c["A"])(),forceFallback:!1,fallbackClass:"draggingStyle",handle:".drag-hook",filter:".el-input__inner",preventOnFilter:!1}}},watch:{field:{handler:function(){this.field.precisions||this.$set(this.field,"precisions",1),this.$set(this.field,"precisions",this.field.precisions),this.$set(this.field,"remark",this.field.remark)},deep:!0,immediate:!0}},methods:{typeObj:function(e){return z["a"].find((function(t){return t.formType===e}))},handleChange:function(){this.$set(this.field,"fieldExtendList",this.field.fieldExtendList)},handleEdit:function(e){this.$emit("child-edit",this.field.fieldExtendList[e])},handleDelete:function(e){this.field.fieldExtendList.splice(e,1),this.$set(this.field,"fieldExtendList",this.field.fieldExtendList)}}},J=U,K=(i("81d9"),Object(p["a"])(J,q,G,!1,null,"2fe33b35",null)),Q=K.exports,X=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-logic-form"},[i("el-button",{staticClass:"add-btn",attrs:{disabled:!e.optionsEditAuth},on:{click:e.handleToSet}},[e._v("\n    点击配置\n  ")]),e._v(" "),i("el-dialog",{staticClass:"edit-dialog",attrs:{visible:e.dialogVisible,"before-close":e.handleCloseDialog,"close-on-click-modal":!1,"append-to-body":"",title:"添加逻辑表单规则",width:"500px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",[i("div",{staticClass:"edit-tips"},[e._v("\n        选择选项后，才会显示所设置的其他字段\n      ")]),e._v(" "),i("div",{staticClass:"edit-table"},[i("flexbox",{staticClass:"edit-table__header row",attrs:{align:"center",justify:"flex-start"}},[i("div",{staticClass:"label"},[e._v("选项内容")]),e._v(" "),i("flexbox-item",{staticClass:"content"},[e._v("显示字段")])],1),e._v(" "),e.list.length>0&&e.fieldLibArr.length>0?i("div",{staticClass:"edit-table__body"},e._l(e.list,(function(t,l){return i("flexbox",{key:l,staticClass:"row",attrs:{align:"center",justify:"flex-start"}},[i("div",{staticClass:"label"},[e._v(e._s(t.name))]),e._v(" "),i("flexbox-item",{staticClass:"content"},[i("el-select",{attrs:{placeholder:"请选择",multiple:""},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}},e._l(e.fieldLibArr,(function(e,t){return i("el-option",{key:t,attrs:{label:"desc_text"===e.formType?"描述文字":e.name||"未命名",value:e.formAssistId}})})))],1)],1)}))):e._e()],1)]),e._v(" "),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleCloseDialog}},[e._v("\n        取消\n      ")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.handleDialogConfirm}},[e._v("\n        确定\n      ")])],1)])],1)},Y=[],Z={name:"SettingLogicForm",props:{field:{type:Object,required:!0},fieldArr:{type:Array,required:!0},point:{type:Array,required:!0}},data:function(){return{list:[],dialogVisible:!1}},computed:{optionsEditAuth:function(){return Object(d["a"])(this.field.operating).optionsEdit},fieldLibArr:function(){var e=this,t=[],i=[];return this.fieldArr.forEach((function(e){e.forEach((function(e){e.hasOwnProperty("formAssistId")&&!Object(r["b"])(e.formAssistId)&&i.push(e.formAssistId)}))})),this.fieldArr.forEach((function(i,l){i.forEach((function(i,n){l===e.point[0]&&n===e.point[1]||t.push(i)}))})),t.filter((function(e){return!["customer","business","contract"].includes(e.formType)}))},allFormAssistId:function(){return this.fieldLibArr.map((function(e){return e.formAssistId}))}},watch:{field:{handler:function(){if("options_type"!==this.field.remark)this.list=this.field.setting.map((function(e){return{name:e,value:null}}));else{var e={};if(this.field.optionsData)e=this.field.optionsData||{};else try{e=JSON.parse(this.field.options)||{}}catch(t){return this.list=this.field.setting.map((function(e){return{name:e,value:null}})),this.$set(this.field,"remark",null),this.$set(this.field,"optionsData",null),void this.$set(this.field,"options",this.field.setting.join(","))}this.list=Object.keys(e).map((function(t){return{name:t,value:Object(r["b"])(e[t])?[]:e[t]}}))}},deep:!0,immediate:!0},allFormAssistId:{handler:function(){var e=this;if("options_type"===this.field.remark){this.list.forEach((function(t){var i=[];t.value&&t.value.forEach((function(t){e.allFormAssistId.includes(t)&&i.push(t)})),t.value=i}));var t={};this.list.forEach((function(e){t[e.name]=e.value})),this.$set(this.field,"optionsData",t)}},deep:!0,immediate:!0}},methods:{handleToSet:function(){this.dialogVisible=!0},handleCloseDialog:function(){var e=this;this.$set(this.field,"_remark",""),this.$nextTick((function(){delete e.field._remark})),this.dialogVisible=!1},handleDialogConfirm:function(){var e={};this.list.forEach((function(t){e[t.name]=t.value}));var t=this.list.filter((function(e){return!Object(r["b"])(e.value)})).length;if(0!==t){this.$set(this.field,"remark","options_type"),this.$set(this.field,"optionsData",e);var i=JSON.stringify(e);this.$set(this.field,"options",i)}else this.$set(this.field,"remark",null),this.$set(this.field,"optionsData",null),this.$set(this.field,"options",this.field.setting.join(","));this.handleCloseDialog()}}},ee=Z,te=(i("6b8e"),Object(p["a"])(ee,X,Y,!1,null,"3f51b381",null)),ie=te.exports,le=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-serial-number"},[i("el-button",{staticClass:"add-btn",attrs:{disabled:!e.optionsEditAuth},on:{click:e.handleUpdateDialogVisible}},[e._v("\n    点击配置\n  ")]),e._v(" "),i("div",{staticClass:"rule"},[i("div",{staticClass:"rule-item"},[e._v("\n      编号示例："+e._s(e.serialNumberExample)+"\n    ")]),e._v(" "),e._l(e.field.setting,(function(t,l){return i("div",{key:l,staticClass:"rule-item"},[1===t.type?[e._v("\n        自动计数：起始编号"+e._s(t.startNumber)+" 递增数"+e._s(t.stepNumber)+" "+e._s(e.getResetTypeStr(t))+"\n      ")]:2===t.type?[e._v("固定字符："+e._s(t.value))]:3===t.type?[e._v("表单内字段："+e._s(t.value))]:e._e()],2)}))],2),e._v(" "),i("el-dialog",{staticClass:"edit-dialog",attrs:{visible:e.dialogVisible,"before-close":e.handleUpdateDialogVisible,"close-on-click-modal":!1,"append-to-body":"",width:"780px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("span",{staticClass:"el-dialog__title",attrs:{slot:"title"},slot:"title"},[e._v("设置编号规则"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"27","data-id":"252"}})]),e._v(" "),i("div",{staticClass:"edit-dialog-body"},[i("div",{staticClass:"edit-tips"},[i("div",[e._v("\n          提示：编号规则中如果表单内字段选择了日期类型字段，若此日期字段值为空时，默认取创建时间的值\n        ")]),e._v(" "),e.serialNumberEditExample?i("div",[e._v("\n          编号示例："+e._s(e.serialNumberEditExample)+"\n        ")]):e._e()]),e._v(" "),i("draggable",{staticClass:"edit-form",attrs:{list:e.ruleList,options:e.dragConfig}},e._l(e.ruleList,(function(t,l){return i("flexbox",{key:l,staticClass:"edit-form-item"},[1===t.type?i("flexbox",{staticClass:"left"},[i("div",{staticClass:"el-input__icon drag-hook wk wk-grid"}),e._v(" "),i("el-input",{attrs:{value:"自动计数",disabled:""}}),e._v(" "),i("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number",value:"positiveNum",expression:"'positiveNum'"}],attrs:{maxlength:12,placeholder:"起始编号"},model:{value:t.startNumber,callback:function(i){e.$set(t,"startNumber",i)},expression:"item.startNumber"}}),e._v(" "),i("el-input-number",{attrs:{maxlength:4,controls:!1,placeholder:"递增数"},model:{value:t.stepNumber,callback:function(i){e.$set(t,"stepNumber",i)},expression:"item.stepNumber"}}),e._v(" "),i("el-select",{attrs:{placeholder:"请选择"},model:{value:t.resetType,callback:function(i){e.$set(t,"resetType",i)},expression:"item.resetType"}},e._l(e.resetOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1):i("flexbox",{staticClass:"left"},[i("div",{staticClass:"el-input__icon drag-hook wk wk-grid"}),e._v(" "),i("el-select",{attrs:{placeholder:"请选择"},on:{change:function(i){e.typeChange(t)}},model:{value:t.type,callback:function(i){e.$set(t,"type",i)},expression:"item.type"}},e._l(e.typeOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),2===t.type?i("el-input",{staticClass:"el-input",model:{value:t.value,callback:function(i){e.$set(t,"value","string"===typeof i?i.trim():i)},expression:"item.value"}}):e._e(),e._v(" "),3===t.type?[i("el-select",{attrs:{placeholder:"请选择"},on:{change:function(i){e.fieldChange(t)}},model:{value:t.formAssistId,callback:function(i){e.$set(t,"formAssistId",i)},expression:"item.formAssistId"}},e._l(e.fieldLibArr,(function(e,t){return i("el-option",{key:t,attrs:{label:e.name,value:e.formAssistId}})}))),e._v(" "),e.isTimeField(t)?i("el-select",{attrs:{placeholder:"请选择"},model:{value:t.textFormat,callback:function(i){e.$set(t,"textFormat",i)},expression:"item.textFormat"}},e._l(e.timeFormatOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e()]:e._e()],2),e._v(" "),1!==t.type?i("el-button",{staticClass:"el-input__icon wk wk-icon-bin",attrs:{type:"text"},on:{click:function(t){e.handleDelete(l)}}}):e._e()],1)}))),e._v(" "),i("el-button",{staticClass:"add",attrs:{type:"text"},nativeOn:{click:function(t){return e.handleAdd(t)}}},[i("i",{staticClass:"el-icon-plus"}),e._v("\n        新增编号规则\n      ")])],1),e._v(" "),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleUpdateDialogVisible}},[e._v("\n        取消\n      ")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.handleDialogConfirm}},[e._v("\n        确定\n      ")])],1)])],1)},ne=[],ae=i("5530"),se=(i("fb6a"),{name:"SettingSerialNumber",components:{draggable:_.a},props:{field:{type:Object,required:!0},fieldArr:{type:Array,required:!0},point:{type:Array,required:!0}},data:function(){return{typeOptions:[{label:"固定字符",value:2},{label:"表单内字段",value:3}],timeFormatOptions:[{label:"yyyyMMdd（年月日）",value:"yyyyMMdd"},{label:"yyyyMM（年月）",value:"yyyyMM"},{label:"yyyy（年）",value:"yyyy"}],dialogVisible:!1,ruleList:[]}},computed:{optionsEditAuth:function(){return Object(d["a"])(this.field.operating).optionsEdit},dragConfig:function(){return{group:"serial-number-drag",forceFallback:!1,disabled:!this.optionsEditAuth,fallbackClass:"draggingStyle",handle:".drag-hook",filter:".el-input__inner",preventOnFilter:!1}},fieldLibArr:function(){var e=this,t=[],i=["date","datetime","text","select","number"];this.fieldArr.forEach((function(l,n){l.forEach((function(l,a){!i.includes(l.formType)||n===e.point[0]&&a===e.point[1]||t.push(l)}))}));var l=t.find((function(e){return"创建时间"===e.name}));return l||t.push({name:"创建时间",fieldName:"createTime",formType:"datetime",formAssistId:-100}),t},resetOptions:function(){var e=this,t=[{label:"每天开始重新编号",value:1},{label:"每月开始重新编号",value:2},{label:"每年开始重新编号",value:3},{label:"从不开始重新编号",value:4}],i={yyyyMMdd:0,yyyyMM:1,yyyy:2},l=null;return this.ruleList.forEach((function(t){if(3===t.type&&t.textFormat&&e.isTimeField(t)){var n=i[t.textFormat];n>=(l||0)&&(l=n)}})),null===l&&(l=3),t.slice(l)},serialNumberExample:function(){return this.getNumberExample(this.field.setting)},serialNumberEditExample:function(){return this.getNumberExample(this.ruleList)}},watch:{field:{handler:function(){var e=this,t=Object(c["D"])(this.field.setting);t.forEach((function(t){if(t.hasOwnProperty("textFormat")||(t.textFormat=null),3===t.type){var i=e.fieldLibArr.find((function(e){return e.name===t.value}));i&&(t.formAssistId=i.formAssistId)}})),this.ruleList=t},deep:!0,immediate:!0},resetOptions:{handler:function(){var e=this.ruleList.find((function(e){return 1===e.type}));if(e){var t=this.resetOptions.find((function(t){return t.value===e.resetType}));t||this.$set(e,"resetType",this.resetOptions[0].value)}},deep:!0,immediate:!0}},methods:{getResetTypeStr:function(e){return{1:"每天开始重新编号",2:"每月开始重新编号",3:"每年开始重新编号",4:"从不开始重新编号"}[e.resetType]},getNumberExample:function(e){var t=[];return e.forEach((function(e){switch(e.type){case 1:t.push(e.startNumber);break;case 2:t.push(e.value);break;case 3:t.push(e.value);break}})),t.join("-")},handleUpdateDialogVisible:function(){this.dialogVisible=!this.dialogVisible},isTimeField:function(e){var t=this.fieldLibArr.find((function(t){return t.formAssistId===e.value||t.name===e.value}));return t&&["date","datetime"].includes(t.formType)},typeChange:function(e){this.$set(e,"value",null),this.$set(e,"formAssistId",null),3===e.type&&this.$set(e,"textFormat","")},fieldChange:function(e){var t=this;this.$nextTick((function(){var i=t.fieldLibArr.find((function(t){return t.formAssistId===e.formAssistId}))||{};t.$set(e,"value",i.name),["date","datetime"].includes(i.formType)?t.$set(e,"textFormat","yyyyMMdd"):t.$set(e,"textFormat","")}))},handleAdd:function(){this.ruleList.push({type:2,value:null,textFormat:null,formAssistId:null})},handleDelete:function(e){this.ruleList.splice(e,1)},handleDialogConfirm:function(){for(var e=[],t=0;t<this.ruleList.length;t++){var i=this.ruleList[t];if(1===i.type){if(!i.startNumber&&0!==i.startNumber)return void this.$message.error("起始编号不能为空");if(!i.stepNumber)return void this.$message.error("递增数不能为空或等于0")}else{if(2===i.type&&!i.value)return void this.$message.error("固定字符不能为空");if(3===i.type&&!i.value)return void this.$message.error("请选择规则字段")}e.push(Object(ae["a"])({},i))}e.forEach((function(e){3!==e.type&&delete e.textFormat,delete e.formAssistId})),this.$set(this.field,"setting",e),this.handleUpdateDialogVisible()}}}),oe=se,re=(i("013f"),Object(p["a"])(oe,le,ne,!1,null,"19728a17",null)),ce=re.exports,de=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-tag"},[i("el-button",{staticClass:"add-btn",attrs:{disabled:!e.optionsEditAuth},on:{click:e.handleToSet}},[e._v("\n    点击配置\n  ")]),e._v(" "),i("el-dialog",{staticClass:"edit-dialog",attrs:{visible:e.dialogVisible,"before-close":e.handleCloseDialog,"close-on-click-modal":!1,title:"标签设置",width:"800px","append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("div",{staticClass:"edit-dialog-body"},[i("div",{staticClass:"group-box is-tabs"},[i("div",{staticClass:"nav"},[i("flexbox",{staticClass:"nav__hd",attrs:{justify:"space-between"}},[e._v("\n            标签分组\n            "),i("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus"},on:{click:e.newGroupBtn}},[e._v("创建分组")])],1),e._v(" "),i("draggable",{staticClass:"group-nav-box",attrs:{list:e.groupList}},e._l(e.groupList,(function(t,l){return i("div",{key:l,staticClass:"group-nav-item",class:{"is-select":l==e.groupActiveIndex},on:{click:function(i){e.groupMenuSelect(t,l)}}},[i("span",{staticClass:"name"},[e._v(e._s(t.name))]),e._v(" "),i("div",{staticClass:"handle-icon"},[i("el-dropdown",{attrs:{trigger:"click"},on:{command:e.groupHandleClick}},[i("i",{staticClass:"el-icon-arrow-down",on:{click:function(t){e.groupDropdownClick(l)}}}),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{command:"edit"}},[e._v("编辑")]),e._v(" "),i("el-dropdown-item",{attrs:{command:"delete"}},[e._v("删除")])],1)],1)],1)])})))],1),e._v(" "),i("el-dialog",{attrs:{title:e.groupTitle,visible:e.newGroupVisible,"before-close":e.newGroupClose,"close-on-click-modal":!1,"append-to-body":"",width:"400px"},on:{"update:visible":function(t){e.newGroupVisible=t}}},[i("label",{staticClass:"label-title"},[e._v("分组名称")]),e._v(" "),i("el-input",{staticClass:"input-group",attrs:{maxlength:100},model:{value:e.group.name,callback:function(t){e.$set(e.group,"name",t)},expression:"group.name"}}),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.newGroupSubmit}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.newGroupClose}},[e._v("取消")])],1)],1),e._v(" "),i("div",{staticClass:"content-box"},[i("div",{staticClass:"content-table"},[i("flexbox",{staticClass:"content-table-header",attrs:{justify:"space-between"}},[i("div",[e._v(e._s(e.groupActive?e.groupActive.name:"分组"))]),e._v(" "),i("el-button",{staticClass:"xr-btn--orange",attrs:{disabled:0===e.groupList.length,size:"medium",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.newTagBtn(t)}}},[e._v("新建标签")])],1),e._v(" "),i("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.tagList,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight}},[i("el-table-column",{attrs:{prop:"name","show-overflow-tooltip":"",label:"标签名称"}}),e._v(" "),i("el-table-column",{attrs:{prop:"color",width:"200",label:"颜色"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{style:{background:e.row.color,padding:"10px","border-radius":"4px"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,n=(t.column,t.$index);return[i("el-button",{attrs:{icon:"wk wk-edit",type:"text"},on:{click:function(t){e.tagHandleClick("editTag",l,n)}}}),e._v(" "),i("el-button",{attrs:{icon:"wk wk-delete",type:"text"},on:{click:function(t){e.tagHandleClick("delete",l,n)}}})]}}])})],1)],1)]),e._v(" "),i("el-dialog",{staticClass:"tag-add-box",attrs:{title:e.tagTitle,visible:e.newTagVisible,"before-close":e.newTagClose,"close-on-click-modal":!1,"append-to-body":"",width:"400px"},on:{"update:visible":function(t){e.newTagVisible=t}}},[i("div",{staticClass:"content"},[i("div",{staticClass:"infrastructure"},[i("div",{staticClass:"row"},[i("span",{staticClass:"label name"},[e._v("标签名称")]),e._v(" "),i("div",{staticClass:"color-dynamic"},[i("el-input",{attrs:{maxlength:50,size:"mini"},model:{value:e.tag.name,callback:function(t){e.$set(e.tag,"name",t)},expression:"tag.name"}}),e._v(" "),i("span",{staticClass:"dynamic-span",style:{background:e.tag.color}})],1)]),e._v(" "),i("div",{staticClass:"row"},[i("span",{staticClass:"label"},[e._v("标签颜色")]),e._v(" "),i("div",{staticClass:"color-box"},e._l(e.colorList,(function(t,l){return i("span",{key:l,style:{background:t},on:{click:function(i){e.selectColor(t)}}})})))]),e._v(" "),i("div",{staticClass:"row"},[i("span",{staticClass:"label name"},[e._v("所属分组")]),e._v(" "),i("div",{staticClass:"color-dynamic"},[i("el-select",{attrs:{disabled:"编辑标签"==e.tagTitle},model:{value:e.tag.groupIndex,callback:function(t){e.$set(e.tag,"groupIndex",t)},expression:"tag.groupIndex"}},e._l(e.groupList,(function(e,t){return i("el-option",{key:t,attrs:{label:e.name,value:t}})})))],1)])])]),e._v(" "),i("div",{staticClass:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),e.newTagSubmit(t)}}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:function(t){return t.stopPropagation(),e.newTagClose(t)}}},[e._v("取消")])],1)])],1)]),e._v(" "),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleCloseDialog}},[e._v("\n        取消\n      ")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.handleDialogConfirm}},[e._v("\n        确定\n      ")])],1)])],1)},ue=[],fe={name:"SettingTag",components:{draggable:_.a},props:{field:{type:Object,required:!0}},data:function(){return{groupList:[],tagList:[],dialogVisible:!1,tableHeight:document.documentElement.clientHeight-600,groupTitle:"",newGroupVisible:!1,group:{},groupActive:{},groupActiveIndex:0,dropdownHandleGroup:null,tagTitle:"",newTagVisible:!1,tag:{},colorList:["#1890ff","#00A3BF","#DE350B","#5243AA","#00875A","#FF991F","#091E42"]}},computed:{optionsEditAuth:function(){return Object(d["a"])(this.field.operating).optionsEdit}},watch:{field:{handler:function(){this.groupList=Object(c["D"])(this.field.setting)||[],this.groupList.length>0?(this.groupActive=this.groupList[0],this.groupActiveIndex=0,this.tagList=this.groupList[0].labelList):(this.groupActive=[],this.groupActiveIndex=0,this.tagList=[])},deep:!0,immediate:!0}},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-600}},methods:{groupMenuSelect:function(e,t){this.groupActive=e,this.groupActiveIndex=t,this.refreshTagList()},newGroupClose:function(){this.newGroupVisible=!1},newGroupBtn:function(){this.groupTitle="新建分组",this.group={},this.newGroupVisible=!0},groupEditBtn:function(e){this.groupTitle="编辑分组",this.group={name:this.groupList[e].name},this.newGroupVisible=!0},groupDelect:function(e){this.groupList.splice(e,1)},newGroupSubmit:function(){if(this.group.name){if("编辑分组"==this.groupTitle)this.groupList[this.dropdownHandleGroup].name=this.group.name;else{var e={name:this.group.name,labelList:[]};0==this.groupList.length&&this.groupMenuSelect(e,0),this.groupList.push(e)}this.newGroupVisible=!1}else this.$message.error("分组名称不能为空")},groupDropdownClick:function(e){this.dropdownHandleGroup=e},groupHandleClick:function(e){"edit"==e?this.groupEditBtn(this.dropdownHandleGroup):"delete"==e&&this.groupDelect(this.dropdownHandleGroup)},refreshTagList:function(){this.getTagList()},getTagList:function(){this.tagList=this.groupActive.labelList||[]},tagHandleClick:function(e,t,i){"delete"===e?this.groupActive.labelList.splice(i,1):"editTag"===e&&(this.tag={name:t.name,color:t.color,index:i,groupIndex:this.groupActiveIndex},this.tagTitle="编辑标签",this.newTagVisible=!0)},newTagClose:function(){this.newTagVisible=!1},newTagBtn:function(){this.tag={name:"",color:"#1890ff",groupIndex:this.groupActiveIndex},this.tagTitle="新建标签",this.newTagVisible=!0},selectColor:function(e){this.$set(this.tag,"color",e)},newTagSubmit:function(){if(this.tag.name)if(""!==this.tag.groupIndex){if("编辑标签"==this.tagTitle)this.$set(this.groupActive.labelList,this.tag.index,{name:this.tag.name,color:this.tag.color});else{var e=this.groupList[this.tag.groupIndex];e.labelList.push({name:this.tag.name,color:this.tag.color})}this.newTagVisible=!1}else this.$message.error("所属分组不能为空");else this.$message.error("标签名称不能为空")},handleToSet:function(){this.dialogVisible=!0},handleCloseDialog:function(){var e=this;this.$set(this.field,"_remark",""),this.$nextTick((function(){delete e.field._remark})),this.dialogVisible=!1},handleDialogConfirm:function(){this.$set(this.field,"setting",this.groupList);var e=JSON.stringify(this.groupList);this.$set(this.field,"options",e),this.handleCloseDialog()}}},pe=fe,he=(i("6cd4"),Object(p["a"])(pe,de,ue,!1,null,"b0032fe4",null)),me=he.exports,be=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"setting-attention"},[i("el-checkbox",{on:{change:e.handleChange},model:{value:e.isOpen,callback:function(t){e.isOpen=t},expression:"isOpen"}},[e._v("启用关注度自动变更规则")]),e._v(" "),i("div",{staticClass:"rule"},[e._v("\n    超过\n    "),i("el-input",{directives:[{name:"WkNumber",rawName:"v-WkNumber",value:"positiveInt",expression:"'positiveInt'"}],attrs:{disabled:!e.isOpen},on:{blur:e.handleChange},model:{value:e.num,callback:function(t){e.num=t},expression:"num"}}),e._v("天未跟进/未更新，\n    自动减少\n    "),i("el-select",{attrs:{disabled:!e.isOpen,placeholder:"请选择"},on:{change:e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v("\n    星，依次类推，降到0为止\n  ")],1)],1)},ve=[],ge={name:"SettingAttention",props:{field:{type:Object,required:!0}},data:function(){return{options:[{label:"一",value:1},{label:"二",value:2},{label:"三",value:3},{label:"四",value:4},{label:"五",value:5}],isOpen:!1,num:1,value:""}},computed:{optionsEditAuth:function(){return Object(d["a"])(this.field.operating).optionsEdit}},watch:{field:{handler:function(){this.field.setting&&this.field.setting.length>0?(this.isOpen=!0,this.num=this.field.setting[0].num,this.value=this.field.setting[0].value):(this.isOpen=!1,this.num="",this.value="")},deep:!0,immediate:!0}},methods:{handleChange:function(){if(this.isOpen){(!this.num||this.num<=0)&&(this.num=1);var e=[{num:this.num,value:this.value}];this.$set(this.field,"setting",e),this.$set(this.field,"options",JSON.stringify(e))}else this.$set(this.field,"setting",[]),this.$set(this.field,"options",JSON.stringify([]))}}},_e=ge,ye=(i("2099"),Object(p["a"])(_e,be,ve,!1,null,"88d8cf00",null)),ke=ye.exports,xe={name:"FieldSetting",components:{SettingDefault:m,SettingOptions:C,SettingNumber:O,SettingPrecisions:D,SettingDescText:M,SettingDetailTable:Q,SettingLogicForm:ie,SettingSerialNumber:ce,SettingTag:me,SettingAttention:ke},props:{canTransform:Boolean,transformData:Object,field:{type:Object,required:!0},fieldArr:{type:Array,required:!0},point:{type:Array,required:!0},showLogic:{type:Boolean,default:!0}},data:function(){return{widthOptions:[{value:25},{value:50},{value:75},{value:100}],stylePercentValue:[]}},computed:{typeObj:function(){var e=this,t=z["a"].find((function(t){return t.formType===e.field.formType}));return t||this.field},fieldAuth:function(){return Object(d["a"])(this.field.operating)},canDefault:function(){return!["user","structure","file","location","handwriting_sign","detail_table","serial_number","field_tag","field_group"].includes(this.field.formType)},canOptions:function(){return["select","checkbox"].includes(this.field.formType)},canNumber:function(){return["number","floatnumber","percent"].includes(this.field.formType)},canPrecisions:function(){return["date_interval","position","select","checkbox"].includes(this.field.formType)},precisionsTitle:function(){if(!this.canPrecisions)return"";switch(this.field.formType){case"date_interval":return"日期类型";case"position":return"地址精度";case"select":return"展示方式";case"checkbox":return"展示方式";default:return"精度"}},isDescText:function(){return"desc_text"===this.field.formType},isSerialNumber:function(){return"serial_number"===this.field.formType},isTag:function(){return"field_tag"===this.field.formType},isAttention:function(){return"field_attention"===this.field.formType},isTableChild:function(){var e=this.fieldArr[this.point[0]][this.point[1]];return"detail_table"===e.formType},titleHelpObj:function(){var e={boolean_value:"246",detail_table:"250",field_group:"255"}[this.field.formType];return e?{type:"27",id:e}:null}},watch:{field:{handler:function(){this.stylePercentValue=[Number(this.field.stylePercent)||100]},deep:!0,immediate:!0}},methods:{emitUpdateWidth:function(){this.$emit("update-width")},emitChildEdit:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.$emit("child-edit",e)},clickOutSide:function(){this.emitChildEdit()}}},Ce=xe,we=(i("b7a7"),Object(p["a"])(Ce,l,n,!1,null,"6d55d51e",null));t["a"]=we.exports},"2c93":function(e,t,i){"use strict";i("f4a6")},"2dac":function(e,t,i){"use strict";i("7d7a6")},"35b8":function(e,t,i){"use strict";i("d410")},"3bb8":function(e,t,i){},"3c44":function(e,t,i){},"3edd":function(e,t,i){"use strict";i("d5dc")},"4bda":function(e,t,i){},"4d90":function(e,t,i){"use strict";var l=i("23e7"),n=i("0ccb").start,a=i("9a0c");l({target:"String",proto:!0,forced:a},{padStart:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},"4f45":function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));var l=i("d4ec"),n=i("bee2"),a=(i("b0c0"),function(){function e(t){Object(l["a"])(this,e),this.fieldType=0,this.fieldId=t.fieldId||"",this.name=t.name||"",this.formType=t.formType||"",this.isUnique=t.isUnique||0,this.isNull=t.isNull||0,this.isHidden=t.isHidden||0,this.inputTips=t.inputTips||"","textarea"===this.formType&&(this.maxLength=t.maxLength||800),"checkbox"===this.formType?this.defaultValue=t.defaultValue||[]:this.defaultValue=t.defaultValue||"","form"===this.formType&&(this.formValue=t.formValue||[]),this.setting=t.setting||[],this.isDeleted=0}return Object(n["a"])(e,[{key:"check",value:function(){return""===this.name?"字段名称不能为空":""}}])}())},5240:function(e,t,i){"use strict";t["a"]=[{componentName:"FieldInput",formType:"text",name:"单行文本",type:1,icon:"wk wk-icon-text"},{componentName:"FieldTextarea",formType:"textarea",name:"多行文本",type:2,icon:"wk wk-icon-textarea"},{componentName:"FieldInput",formType:"website",name:"网址",type:25,icon:"wk wk-icon-website"},{componentName:"FieldBoolean",formType:"boolean_value",name:"布尔值",type:41,icon:"wk wk-icon-bool"},{componentName:"FieldSelect",formType:"select",name:"单选",type:3,icon:"wk wk-icon-select"},{componentName:"FieldCheckbox",formType:"checkbox",name:"多选",type:9,icon:"wk wk-icon-checkbox"},{componentName:"FieldInput",formType:"number",name:"数字",type:5,icon:"wk wk-icon-int"},{componentName:"FieldInput",formType:"floatnumber",name:"货币",type:6,icon:"wk wk-icon-coin"},{componentName:"FieldPercent",formType:"percent",name:"百分数",type:42,icon:"wk wk-percent-line"},{componentName:"FieldInput",formType:"mobile",name:"手机",type:7,icon:"wk wk-icon-mobile2"},{componentName:"FieldInput",formType:"email",name:"邮箱",type:14,icon:"wk wk-icon-email2"},{componentName:"FieldInput",formType:"date",name:"日期",type:4,icon:"wk wk-icon-date2"},{componentName:"FieldInput",formType:"datetime",name:"日期时间",type:13,icon:"wk wk-icon-datetime2"},{componentName:"FieldDateInterval",formType:"date_interval",name:"日期区间",type:48,icon:"wk wk-icon-range"},{componentName:"FieldPosition",formType:"position",name:"地址",type:43,icon:"wk wk-nearby"},{componentName:"FieldLocation",formType:"location",name:"定位",type:44,icon:"wk wk-icon-nav"},{componentName:"FieldInput",formType:"user",name:"人员",type:10,icon:"wk wk-s-contacts-line"},{componentName:"FieldInput",formType:"structure",name:"部门",type:12,icon:"wk wk-icon-s-seas-line"},{componentName:"FieldFile",formType:"file",name:"附件",type:8,icon:"wk wk-icon-file"},{componentName:"FieldWritingSign",formType:"handwriting_sign",name:"手写签名",type:46,icon:"wk wk-icon-edit-line"},{componentName:"FieldDescText",formType:"desc_text",name:"描述文字",type:50,icon:"wk wk-icon-des"},{componentName:"FieldDetailTable",formType:"detail_table",name:"明细表格",type:45,icon:"wk wk-icon-form"},{componentName:"FieldFile",formType:"pic",name:"图片",type:29,icon:"wk wk-icon-status"},{componentName:"FieldSerialNumber",formType:"serial_number",name:"自定义编号",type:63,icon:"wk wk-icon-catalog2"},{componentName:"FieldTag",formType:"field_tag",name:"自定义标签",type:61,icon:"wk wk-icon-label2"},{componentName:"FieldAttention",formType:"field_attention",name:"关注度",type:62,icon:"wk wk-focus-on-line"},{componentName:"FieldGroup",formType:"field_group",name:"分组标题",type:60,icon:"wk wk-icon-title"}]},5378:function(e,t,i){},"543a":function(e,t,i){},"5b3d":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-date-interval",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("flexbox",{staticClass:"range-box"},[i("i",{staticClass:"el-icon-date icon"}),e._v(" "),i("flexbox-item",{class:{placeholder:!e.isHasValue}},[e._v("\n      "+e._s(e.isHasValue?e.field.defaultValue[0]:"开始时间")+"\n    ")]),e._v(" "),i("span",[e._v("至")]),e._v(" "),i("flexbox-item",{class:{placeholder:!e.isHasValue}},[e._v("\n      "+e._s(e.isHasValue?e.field.defaultValue[1]:"结束时间")+"\n    ")])],1)],1)},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldDateInterval",components:{FieldWrapper:a["a"]},mixins:[s["a"]],computed:{isHasValue:function(){return Array.isArray(this.field.defaultValue)&&2===this.field.defaultValue.length}},methods:{}},r=o,c=(i("c99f"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"3fa747a3",null);t["default"]=d.exports},"5e94":function(e,t,i){"use strict";i("f861")},"60c3":function(e,t,i){"use strict";i.d(t,"i",(function(){return l["default"]})),i.d(t,"p",(function(){return n["default"]})),i.d(t,"m",(function(){return a["default"]})),i.d(t,"c",(function(){return s["default"]})),i.d(t,"g",(function(){return o["default"]})),i.d(t,"b",(function(){return r["default"]})),i.d(t,"k",(function(){return c["default"]})),i.d(t,"l",(function(){return d["default"]})),i.d(t,"j",(function(){return u["default"]})),i.d(t,"f",(function(){return w})),i.d(t,"q",(function(){return T["default"]})),i.d(t,"d",(function(){return F["default"]})),i.d(t,"e",(function(){return V["default"]})),i.d(t,"n",(function(){return j})),i.d(t,"o",(function(){return R})),i.d(t,"a",(function(){return G})),i.d(t,"h",(function(){return Y}));var l=i("b7bb"),n=i("0044"),a=i("f2ea"),s=i("b6c9"),o=i("7b9b"),r=i("c778"),c=i("f14d"),d=i("0042"),u=i("20da"),f=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-detail-table",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"box",class:{"is-empty":e.isEmpty}},[i("draggable",{staticClass:"field-list",class:{"is-table":2===e.field.precisions},attrs:{list:e.list,options:e.dragListConfig},on:{end:e.dragListEnd,add:e.dragAdded}},[e.isEmpty?i("div",{staticClass:"empty-box"},[i("div",{staticClass:"empty-box-title"},[e._v("可拖拽添加多个字段")]),e._v(" "),i("div",{staticClass:"empty-box-desc"},[e._v("（不支持明细中添加明细字段）")])]):e._e(),e._v(" "),e.isEmpty||1!==e.field.precisions?e._e():e._l(e.list,(function(t,l){return i(e._f("typeToComponentName")(t),{key:l,tag:"component",staticClass:"draggable-hook",attrs:{field:t,point:[l,0],"active-point":[null,null]},on:{click:e.emitClick}})})),e._v(" "),e.isEmpty||2!==e.field.precisions?e._e():[i("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.tableData,stripe:e.WKConfig.tableStyle.stripe}},[e._l(e.list,(function(t,l){return i("el-table-column",{key:l,attrs:{prop:t.fieldName,label:t.name},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row,n=t.column;return[i("div",{staticClass:"input-box"},[e._v("\n                "+e._s(l[n.property])+"\n              ")])]}}])})})),e._v(" "),i("el-table-column",{attrs:{label:"操作",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",[e._v("删除")])]}}])})],2)]],2),e._v(" "),e.isEmpty?e._e():i("div",{staticClass:"add-btn"},[i("el-button",{attrs:{type:"text"}},[i("i",{staticClass:"wk wk-l-plus"}),e._v("\n        "+e._s(e.field.remark||"")+"\n      ")])],1)],1)])},p=[],h=(i("a15b"),i("e9f5"),i("7d54"),i("d3b7"),i("3ca3"),i("159b"),i("ddb0"),i("f44c")),m=i("b76a"),b=i.n(m),v=i("a46a"),g=i("6bfe"),_=i("6d94"),y={name:"FieldDetailTable",components:{draggable:b.a,FieldWrapper:h["a"],FieldInput:function(){return Promise.resolve().then(i.bind(null,"b7bb"))},FieldTextarea:function(){return Promise.resolve().then(i.bind(null,"0044"))},FieldSelect:function(){return Promise.resolve().then(i.bind(null,"f2ea"))},FieldCheckbox:function(){return Promise.resolve().then(i.bind(null,"b6c9"))},FieldFile:function(){return Promise.resolve().then(i.bind(null,"7b9b"))},FieldBoolean:function(){return Promise.resolve().then(i.bind(null,"c778"))},FieldPercent:function(){return Promise.resolve().then(i.bind(null,"f14d"))},FieldPosition:function(){return Promise.resolve().then(i.bind(null,"0042"))},FieldLocation:function(){return Promise.resolve().then(i.bind(null,"20da"))},FieldWritingSign:function(){return Promise.resolve().then(i.bind(null,"1a52"))},FieldDateInterval:function(){return Promise.resolve().then(i.bind(null,"5b3d"))},FieldDescText:function(){return Promise.resolve().then(i.bind(null,"a4a1"))}},filters:{typeToComponentName:function(e){return Object(_["b"])(e)}},mixins:[v["a"]],data:function(){return{dragListConfig:{delay:50,group:{name:"childList",put:["libList"],pull:!1},sort:!1,forceFallback:!0,fallbackClass:"draggingStyle",filter:".empty-box"},selectedPoint:[null,null]}},computed:{isEmpty:function(){return Object(g["b"])(this.field.fieldExtendList)},isList:function(){return!0},list:function(){return this.isEmpty?[]:this.field.fieldExtendList},tableData:function(){var e=this,t={};return this.list.forEach((function(i){t[i.fieldName]=e.formatterDefaultValue(i)})),[t]}},methods:{dragListEnd:function(e){},dragAdded:function(e){var t=this;this.$emit("child-drag-add",this.point,e),this.$nextTick((function(){t.selectedPoint=[e.newIndex,0]}))},formatterDefaultValue:function(e){return e.defaultValue?"boolean_value"===e.formType?{0:"不选中",1:"选中"}[e.defaultValue]:"date_interval"===e.formType?e.defaultValue.join("-"):"checkbox"===e.formType?e.defaultValue.join(","):"string"===typeof e.defaultValue?e.defaultValue:void 0:""}}},k=y,x=(i("5e94"),i("2877")),C=Object(x["a"])(k,f,p,!1,null,"21cb3c7a",null),w=C.exports,T=i("1a52"),F=i("5b3d"),V=i("a4a1"),A=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-custom-number",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("flexbox",{staticClass:"box",attrs:{align:"center"}},[i("span",{staticClass:"default-val"},[e._v("\n      "+e._s("string"==typeof e.field.defaultValue?e.field.defaultValue:"")+"\n    ")])])],1)},O=[],E={name:"FieldSerialNumber",components:{FieldWrapper:h["a"]},mixins:[v["a"]],methods:{}},$=E,N=(i("e62a"),Object(x["a"])($,A,O,!1,null,"7825e266",null)),j=N.exports,L=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-tag",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("flexbox",{staticClass:"box",attrs:{align:"center"}},[i("span",{staticClass:"default-val"},[e._v("\n      "+e._s("string"==typeof e.field.defaultValue?e.field.defaultValue:"")+"\n    ")])])],1)},D=[],S={name:"FieldTag",components:{FieldWrapper:h["a"]},mixins:[v["a"]],methods:{}},I=S,P=(i("ccad"),Object(x["a"])(I,L,D,!1,null,"0463bdd4",null)),R=P.exports,W=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-star",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"box"},[i("el-rate",{attrs:{value:e.field.defaultValue||0},on:{change:e.change}})],1)])},B=[],H={name:"FieldAttention",components:{FieldWrapper:h["a"]},mixins:[v["a"]],data:function(){return{}},methods:{change:function(e){this.$set(this.field,"defaultValue",e)}}},M=H,q=(i("75e5"),Object(x["a"])(M,W,B,!1,null,"7fb91ca8",null)),G=q.exports,z=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-title",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag,"hidden-title":""},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"section-header"},[i("div",{staticClass:"section-mark",staticStyle:{"border-left-color":"#2362fb"}}),e._v(" "),i("div",{staticClass:"section-title"},[e._v(e._s(e.field.name))]),e._v(" "),e.field.inputTips?i("span",{staticClass:"section-tips"},[e._v("\n      （"+e._s(e.field.inputTips)+"）\n    ")]):e._e()])])},U=[],J=i("10ff"),K={name:"FieldGroup",components:{FieldWrapper:h["a"],CreateSections:J["a"]},mixins:[v["a"]],methods:{}},Q=K,X=(i("8f98"),Object(x["a"])(Q,z,U,!1,null,"7a961796",null)),Y=X.exports},6521:function(e,t,i){},"68e5":function(e,t,i){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},"6b8e":function(e,t,i){"use strict";i("7ef5")},"6cd4":function(e,t,i){"use strict";i("c7b4")},"6d94":function(e,t,i){"use strict";i.d(t,"a",(function(){return l})),i.d(t,"b",(function(){return n}));i("caad"),i("a9e3"),i("d3b7"),i("25f0"),i("4d90");function l(e){var t=e.toString(2).padStart(8,"0");return{nameEdit:Boolean(Number(t.charAt(0))),deleteEdit:Boolean(Number(t.charAt(1))),defaultEdit:Boolean(Number(t.charAt(2))),percentEdit:Boolean(Number(t.charAt(3))),nullEdit:Boolean(Number(t.charAt(4))),uniqueEdit:Boolean(Number(t.charAt(5))),hiddenEdit:Boolean(Number(t.charAt(6))),optionsEdit:Boolean(Number(t.charAt(7)))}}function n(e){if(0===e.type)return"FieldInput";if(["text","number","floatnumber","mobile","email","date","datetime","user","structure","contacts","customer","contract","business","single_user","website"].includes(e.formType))return"FieldInput";switch(e.formType){case"textarea":return"FieldTextarea";case"select":return"FieldSelect";case"checkbox":return"FieldCheckbox";case"file":return"FieldFile";case"boolean_value":return"FieldBoolean";case"percent":return"FieldPercent";case"position":return"FieldPosition";case"location":return"FieldLocation";case"detail_table":return"FieldDetailTable";case"handwriting_sign":return"FieldWritingSign";case"date_interval":return"FieldDateInterval";case"desc_text":return"FieldDescText";case"pic":return"FieldFile";case"serial_number":return"FieldSerialNumber";case"field_tag":return"FieldTag";case"field_attention":return"FieldAttention";case"field_group":return"FieldGroup";default:return"FieldInput"}}},7503:function(e,t,i){},"75e5":function(e,t,i){"use strict";i("2108")},7869:function(e,t,i){"use strict";i("0bf4")},"7b9b":function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-file",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"box"},[e._v("\n    请选择文件\n  ")])])},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldFile",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("d4e6"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"3f6987eb",null);t["default"]=d.exports},"7d7a6":function(e,t,i){},"7ef5":function(e,t,i){},"81d9":function(e,t,i){"use strict";i("2593")},"86df":function(e,t,i){},8841:function(e,t,i){},"8b32":function(e,t,i){"use strict";i("a4a2")},"8f98":function(e,t,i){"use strict";i("3c44")},"91d3":function(e,t,i){"use strict";i("c34e")},"9ace":function(e,t,i){},a46a:function(e,t,i){"use strict";i("caad"),i("2532");var l=i("6d94"),n=i("6bfe");t["a"]={props:{field:{type:Object,required:!0},fieldArr:{type:Array,default:function(){return[]}},point:{type:Array},activePoint:{type:Array,default:function(){return[]}}},data:function(){return{specialFormType:[]}},computed:{activate:function(){return this.point[0]===this.activePoint[0]&&this.point[1]===this.activePoint[1]},disabled:function(){return!this.fieldAuth.defaultEdit},fieldAuth:function(){return Object(l["a"])(this.field.operating)},topFlag:function(){if(Object(n["b"])(this.fieldArr))return!1;var e=this.point[0];if(0===e)return!1;var t=this.fieldArr[e-1];return 4!==t.length&&!this.specialFormType.includes(t[0].formType)},bottomFlag:function(){if(Object(n["b"])(this.fieldArr))return!1;var e=this.point[0];return e!==this.fieldArr.length-1},leftFlag:function(){if(Object(n["b"])(this.fieldArr))return!1;var e=this.point[1];return 0!==e},rightFlag:function(){if(Object(n["b"])(this.fieldArr))return!1;var e=this.point[1],t=this.point[0];return e!==this.fieldArr[t].length-1},copyFlag:function(){return!Object(n["b"])(this.fieldArr)&&!["customer","business","contacts","contract","receivables_plan","single_user"].includes(this.field.formType)},controlFlag:function(){return{top:this.topFlag,bottom:this.bottomFlag,left:this.leftFlag,right:this.rightFlag,delete:this.fieldAuth.deleteEdit,copy:this.copyFlag}}},methods:{emitClick:function(e){this.$emit("click",e)},handleAction:function(e,t){this.$emit("action",e,this.point,t)},itemIsObject:function(e){return Object(n["c"])(e)}}}},a4a1:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-desc-text",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag,"hidden-title":""},on:{click:e.emitClick,action:e.handleAction}},[i("tinymce",{staticClass:"rich-txt",attrs:{value:e.field.defaultValue,disabled:1,toolbar:[],init:{menubar:!1,toolbar_sticky:!0,statusbar:!1,placeholder:"描述文字内容",quickbars_selection_toolbar:!1,contextmenu:"",content_style:" * {color: #262626; margin: 0;} body { font-size: 14px; }",plugins:"autoresize",autoresize_bottom_margin:0}}}),e._v(" "),i("div",{staticClass:"field-desc-text-cover"})],1)},n=[],a=i("f44c"),s=i("8256"),o=i("a46a"),r={name:"FieldDescText",components:{FieldWrapper:a["a"],Tinymce:s["a"]},mixins:[o["a"]],methods:{}},c=r,d=(i("fd4b"),i("cd6b"),i("2877")),u=Object(d["a"])(c,l,n,!1,null,"d2798a24",null);t["default"]=u.exports},a4a2:function(e,t,i){},a6f2:function(e,t,i){"use strict";i("9ace")},aea1:function(e,t,i){},b6c9:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-checkbox",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[1===e.field.precisions?i("el-checkbox-group",{attrs:{disabled:e.disabled},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}},e._l(e.field.setting,(function(t,l){return i("el-checkbox",{key:l,staticClass:"checkbox",attrs:{label:e.itemIsObject(t)?t.label||t.name:t}})}))):i("div",{staticClass:"select-content"},[i("el-select",{attrs:{multiple:"",placeholder:"请选择"},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}},e._l(e.field.setting,(function(t,l){return i("el-option",{key:l,attrs:{label:e.itemIsObject(t)?t.label||t.name:t,value:e.itemIsObject(t)?t.value:t}})}))),e._v(" "),i("div",{staticClass:"mask"})],1)],1)},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldCheckbox",components:{FieldWrapper:a["a"]},mixins:[s["a"]],watch:{field:{handler:function(){this.field.precisions||this.$set(this.field,"precisions",1)},deep:!0,immediate:!0}}},r=o,c=(i("a6f2"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"e857e1da",null);t["default"]=d.exports},b71a:function(e,t,i){"use strict";i("4bda")},b7a7:function(e,t,i){"use strict";i("1df2")},b7bb:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-input",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("flexbox",{staticClass:"box",attrs:{align:"center"}},[i("span",{staticClass:"default-val"},[e._v("\n      "+e._s("string"==typeof e.field.defaultValue?e.field.defaultValue:"")+"\n    ")])])],1)},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldInput",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("b941"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"4f06b6e4",null);t["default"]=d.exports},b941:function(e,t,i){"use strict";i("7503")},bfdd:function(e,t,i){},c34e:function(e,t,i){},c427:function(e,t,i){"use strict";i("e817")},c778:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-boolean",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}})],1)},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldBoolean",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("2c93"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"06a966e9",null);t["default"]=d.exports},c7b4:function(e,t,i){},c99f:function(e,t,i){"use strict";i("86df")},ccad:function(e,t,i){"use strict";i("1452")},cd6b:function(e,t,i){"use strict";i("264e")},d410:function(e,t,i){},d4e6:function(e,t,i){"use strict";i("6521")},d5dc:function(e,t,i){},e62a:function(e,t,i){"use strict";i("5378")},e817:function(e,t,i){},f14d:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-percent",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[i("div",{staticClass:"box"},[i("span",{staticClass:"default-val"},[e._v(e._s(e.field.defaultValue||""))]),e._v(" "),i("span",{staticClass:"rate"},[e._v("%")])])])},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldPercent",components:{FieldWrapper:a["a"]},mixins:[s["a"]],methods:{}},r=o,c=(i("35b8"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"f5bf08aa",null);t["default"]=d.exports},f2ea:function(e,t,i){"use strict";i.r(t);var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("field-wrapper",{staticClass:"field-select",attrs:{activate:e.activate,field:e.field,"control-flag":e.controlFlag},on:{click:e.emitClick,action:e.handleAction}},[1===e.field.precisions?i("el-radio-group",{model:{value:e.field.defaultValue,callback:function(t){e.$set(e.field,"defaultValue",t)},expression:"field.defaultValue"}},e._l(e.field.setting,(function(t,l){return i("el-radio",{key:l,attrs:{label:e.itemIsObject(t)?t.label||t.name:t}},[e._v("\n      "+e._s(e.itemIsObject(t)?t.label||t.name:t)+"\n    ")])}))):i("flexbox",{staticClass:"select-box"},[i("div",{class:{placeholder:!Boolean(e.field.defaultValue)}},[e._v("\n      "+e._s(e.field.defaultValue?e.field.defaultValue:"请选择")+"\n    ")]),e._v(" "),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})])],1)},n=[],a=i("f44c"),s=i("a46a"),o={name:"FieldSelect",components:{FieldWrapper:a["a"]},mixins:[s["a"]],watch:{field:{handler:function(){this.field.precisions||this.$set(this.field,"precisions",2)},deep:!0,immediate:!0}}},r=o,c=(i("91d3"),i("2877")),d=Object(c["a"])(r,l,n,!1,null,"fab410d0",null);t["default"]=d.exports},f44c:function(e,t,i){"use strict";var l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"field-item",class:{activate:e.activate},style:{width:e.fieldWidth},on:{click:function(t){return t.stopPropagation(),e.emitClick(t)}}},[e.hiddenTitle?e._e():i("div",{staticClass:"field-item_title"},[i("span",{staticClass:"required"},[e._v(e._s(e.field.isNull?"*":""))]),e._v(" "),i("span",[e._v(e._s(e.field.name))]),e._v(" "),e.field.inputTips?i("span",{staticClass:"input-tips"},[e._v("\n      （"+e._s(e.field.inputTips)+"）\n    ")]):e._e()]),e._v(" "),i("div",{staticClass:"field-item_body"},[e._t("default")],2),e._v(" "),e.activate?[e.controlFlag.top?i("div",{staticClass:"control-top control-btn",on:{click:function(t){t.stopPropagation(),e.handleControl("top",t)}}},[i("i",{staticClass:"wk wk-icon-top"})]):e._e(),e._v(" "),e.controlFlag.bottom?i("div",{staticClass:"control-bottom control-btn",on:{click:function(t){t.stopPropagation(),e.handleControl("bottom",t)}}},[i("i",{staticClass:"wk wk-icon-top bottom"})]):e._e(),e._v(" "),e.controlFlag.left?i("div",{staticClass:"control-left control-btn",on:{click:function(t){t.stopPropagation(),e.handleControl("left",t)}}},[i("i",{staticClass:"wk wk-transfer"})]):e._e(),e._v(" "),e.controlFlag.right?i("div",{staticClass:"control-right control-btn",on:{click:function(t){t.stopPropagation(),e.handleControl("right",t)}}},[i("i",{staticClass:"wk wk-transfer"})]):e._e(),e._v(" "),i("div",{staticClass:"edit-box"},[e.controlFlag.copy?i("div",{staticClass:"control-copy control-btn",on:{click:function(t){t.stopPropagation(),e.handleControl("copy",t)}}},[i("el-tooltip",{attrs:{effect:"dark",content:"复制",placement:"bottom"}},[i("i",{staticClass:"wk wk-associated"})])],1):e._e(),e._v(" "),e.controlFlag.delete?i("div",{staticClass:"control-delete control-btn",on:{click:function(t){t.stopPropagation(),e.handleControl("delete",t)}}},[i("i",{staticClass:"wk wk-s-delete"})]):e._e()])]:e._e()],2)},n=[],a={name:"FieldWrapper",props:{field:{type:Object,required:!0},activate:{type:Boolean,default:!1},controlFlag:{type:Object,default:function(){return{top:!1,bottom:!1,left:!1,right:!1,delete:!1,copy:!0}}},hiddenTitle:{type:Boolean,default:!1}},computed:{fieldWidth:function(){return this.field?this.field.stylePercent+"%":"100%"}},watch:{field:{handler:function(){this.field&&!this.field.stylePercent&&(this.field.stylePercent=100)},deep:!0,immediate:!0}},methods:{emitClick:function(e){this.$emit("click",e)},handleControl:function(e,t){this.$emit("action",e,t)}}},s=a,o=(i("c427"),i("2877")),r=Object(o["a"])(s,l,n,!1,null,"7cae89f6",null);t["a"]=r.exports},f4a6:function(e,t,i){},f861:function(e,t,i){},f957:function(e,t,i){"use strict";i("8841")},fb73:function(e,t,i){},fd4b:function(e,t,i){"use strict";i("68e5")}}]);