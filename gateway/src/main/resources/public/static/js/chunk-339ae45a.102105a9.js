(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-339ae45a"],{"22e9":function(t,e,a){"use strict";a("cf4f")},"26b0":function(t,e,a){"use strict";var n=a("5530"),i=a("df55"),s=a("a347"),o=a.n(s);e["a"]={data:function(){return{loading:!1,axisOption:{color:["#1890ff"],toolbox:{showTitle:!1,feature:{saveAsImage:{pixelRatio:2}}},tooltip:{textStyle:{color:o.a.colorBlack,fontWeight:o.a.axisLabelFontWeight},trigger:"axis",formatter:"{b} : {c}元",axisPointer:{type:"shadow"}}},postParams:{},list:[],fieldList:[]}},mixins:[i["a"]],components:{},props:{},computed:{},watch:{},mounted:function(){this.axisOption=Object(n["a"])(Object(n["a"])({},this.axisOption),{},{grid:i["a"].data().chartDefaultOptions.grid,xAxis:[Object(n["a"])(Object(n["a"])({},i["a"].data().chartXAxisStyle),{},{name:"（元）"})],yAxis:[Object(n["a"])(Object(n["a"])({},i["a"].data().chartYAxisStyle),{},{axisLine:{show:!0},type:"category"})],series:[{type:"bar",label:i["a"].data().chartDefaultBase.label,barMaxWidth:20,data:[]}]})},methods:{},deactivated:function(){}}},"5bf4":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("slide-view",{staticClass:"d-view",attrs:{"listener-ids":t.listenerIDs,"no-listener-ids":t.noListenerIDs,"no-listener-class":t.noListenerClass,"body-style":{padding:0,height:"100%"}},on:{close:t.hideView}},[a("div",{staticClass:"examine-list-main"},[a("div",{staticClass:"t-section"},[a("span",{staticClass:"t-name"},[t._v(t._s(t.name))]),t._v(" \n      "),a("span",{staticClass:"t-des"},[t._v("("+t._s(t.name)+"申请："),a("span",{staticClass:"t-value"},[t._v(t._s(t.totalCount)+"次")])]),t._v(" "),t.showDes?a("span",{staticClass:"t-des"},[t._v("    "+t._s(t.desInfo)+"："),a("span",{staticClass:"t-value"},[t._v(t._s(t.sumData+t.desUnit))])]):t._e(),a("span",{staticClass:"t-des"},[t._v(") ")])]),t._v(" "),a("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.getList,expression:"getList"}],key:t.scrollKey,staticClass:"t-content",attrs:{"infinite-scroll-distance":"100","infinite-scroll-disabled":"scrollDisabled"}},[t._l(t.list,(function(e,n){return a("examine-cell",{key:n,attrs:{data:e,"show-handle":!1},on:{"on-handle":t.examineCellHandle}})})),t._v(" "),t.loading?a("p",{staticClass:"scroll-bottom-tips"},[t._v("加载中...")]):t._e(),t._v(" "),t.noMore?a("p",{staticClass:"scroll-bottom-tips"},[t._v("没有更多了")]):t._e()],2)]),t._v(" "),a("c-r-m-full-screen-detail",{attrs:{id:t.relatedID,visible:t.showRelatedDetail,"crm-type":t.relatedCRMType},on:{"update:visible":function(e){t.showRelatedDetail=e},handle:t.refreshList}})],1)},i=[],s=a("5530"),o=(a("99af"),a("a9e3"),a("d3b7"),a("3ca3"),a("ddb0"),a("130f")),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"list"},[a("div",{staticClass:"list-content"},[a("flexbox",{staticClass:"header"},[t.data.createUser?[a("xr-avatar",{attrs:{name:t.data.createUser.realname,size:34,src:t.data.createUser.img}}),t._v(" "),a("div",{staticClass:"name-time"},[a("span",{staticClass:"name"},[t._v(t._s(t.data.createUser.realname))]),t._v(" "),a("span",{staticClass:"time"},[t._v(t._s(t.data.createtime))])])]:t._e(),t._v(" "),a("div",{staticClass:"rt-setting"},[a("span",{staticClass:"bg-color",style:{"background-color":t.getStatusColor(t.data.examineStatus)}}),t._v(" "),a("span",{staticClass:"dep"},[a("span",[t._v(t._s(t.data.categoryTitle)+" - ")]),t._v(" "),a("span",[t._v(t._s(t.getStatusName(t.data.examineStatus)))])]),t._v(" "),t.showHandle&&t.data.permission&&(t.data.permission.isCheck||t.data.permission.isUpdate||t.data.permission.isDelete)?a("el-dropdown",{attrs:{trigger:"click"},on:{command:t.handleCommand}},[a("i",{staticClass:"el-icon-arrow-down el-icon-more",staticStyle:{color:"#cdcdcd",cursor:"pointer"}}),t._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t.data.permission&&t.data.permission.isCheck?a("el-dropdown-item",{attrs:{command:"withdraw"}},[t._v("撤回")]):t._e(),t._v(" "),t.data.permission&&t.data.permission.isUpdate?a("el-dropdown-item",{attrs:{command:"edit"}},[t._v("编辑")]):t._e(),t._v(" "),t.data.permission&&t.data.permission.isDelete?a("el-dropdown-item",{attrs:{command:"delete"}},[t._v("删除")]):t._e()],1)],1):t._e()],1)],2),t._v(" "),a("div",{staticClass:"row",on:{click:function(e){t.checkDetail(t.data)}}},[t.data.content?a("p",{staticClass:"text"},[t._v(t._s(t.data.content))]):t._e(),t._v(" "),t.data.causeTitle?a("p",{staticClass:"title"},[t._v(t._s(t.data.causeTitle))]):t._e()]),t._v(" "),t.data.file&&t.data.file.length>0||t.data.img&&t.data.img.length>0?a("div",{staticClass:"accessory"},[a("div",{staticClass:"upload-img-box"},t._l(t.data.img,(function(e,n){return a("div",{key:n,staticClass:"img-list",on:{click:function(e){t.imgZoom(t.data.img,n)}}},[a("img",{directives:[{name:"src",rawName:"v-src",value:e.url,expression:"imgItem.url"}]})])}))),t._v(" "),a("file-cell",{attrs:{"file-list":t.data.file}})],1):t._e(),t._v(" "),t.relatedListData.contacts.length>0||t.relatedListData.customer.length>0||t.relatedListData.business.length>0||t.relatedListData.contract.length>0?a("div",{staticClass:"related-business"},[a("div",{staticClass:"label"},[t._v("相关信息")]),t._v(" "),t._l(t.relatedListData,(function(e,n){return a("div",{key:n},t._l(e,(function(t,e){return a("related-business-cell",{key:e,attrs:{data:t,"cell-index":e,type:n,"show-foot":!1}})})))}))],2):t._e()],1)])},c=[],l=(a("d81d"),a("e9f5"),a("ab43"),a("5c7d")),d=a("deea"),u=a("7403"),p={name:"ExamineCell",components:{RelatedBusinessCell:l["a"],FileCell:d["a"]},mixins:[u["a"]],props:{data:Object,showHandle:{type:Boolean,default:!0}},data:function(){return{}},computed:{relatedListData:function(){return{contacts:(this.data.contactsList||[]).map((function(t){return t.id&&(t.contactsId=t.id),t})),customer:(this.data.customerList||[]).map((function(t){return t.id&&(t.customerId=t.id),t})),business:(this.data.businessList||[]).map((function(t){return t.id&&(t.businessId=t.id),t})),contract:(this.data.contractList||[]).map((function(t){return t.id&&(t.contractId=t.id),t}))}}},watch:{},mounted:function(){},methods:{imgZoom:function(t,e){this.$wkPreviewFile.preview({index:e,data:t})},handleCommand:function(t){this.$emit("on-handle",{type:t,data:{item:this.data}})},checkDetail:function(t){this.$emit("on-handle",{type:"view",data:{item:this.data}})}}},h=p,m=(a("9aab"),a("2877")),f=Object(m["a"])(h,r,c,!1,null,"560cab80",null),b=f.exports,g=a("8c73"),v={name:"ExamineList",components:{SlideView:o["a"],ExamineCell:b,CRMFullScreenDetail:function(){return Promise.resolve().then(a.bind(null,"df3e"))}},props:{type:[String,Number],name:String,request:Function,params:Object,listenerIDs:{type:Array,default:function(){return["crm-main-container"]}},noListenerIDs:{type:Array,default:function(){return[]}},noListenerClass:{type:Array,default:function(){return["el-table__body"]}}},data:function(){return{totalCount:0,sumData:"",loading:!1,noMore:!1,page:1,list:[],relatedID:"",relatedCRMType:"",showRelatedDetail:!1,scrollKey:Date.now()}},computed:{showDes:function(){return this.type>1&&this.type<=6},desInfo:function(){return 2==this.type?"请假总天数":3==this.type?"出差总天数":4==this.type?"加班总天数":5==this.type?"报销总金额":6==this.type?"借款总金额":""},desUnit:function(){return this.type>1&&this.type<=4?"天":"元"},scrollDisabled:function(){return this.loading||this.noMore}},watch:{params:function(){this.refreshList()}},mounted:function(){},methods:{refreshList:function(){this.sumData="",this.totalCount=0,this.page=1,this.list=[],this.noMore=!1,this.scrollKey=Date.now()},getList:function(){var t=this;this.loading=!0,this.request(Object(s["a"])({page:this.page,limit:15},this.params)).then((function(e){var a=e.data.extraData||{};t.sumData="天"==t.desUnit?a.duration||0:Object(g["h"])(a.money||0),t.noMore||(t.list=t.list.concat(e.data.list),t.page++),t.totalCount=e.data.totalRow,t.loading=!1,t.noMore=!(e.data.list&&15==e.data.list.length)})).catch((function(){t.noMore=!0,t.loading=!1}))},examineCellHandle:function(t){"view"==t.type&&(this.relatedID=t.data.item.examineId,this.relatedCRMType="examine",this.showRelatedDetail=!0)},hideView:function(){this.$emit("hide")}}},y=v,C=(a("22e9"),Object(m["a"])(y,n,i,!1,null,"1f730ebe",null));e["a"]=C.exports},7963:function(t,e,a){},"9aab":function(t,e,a){"use strict";a("c750")},a475:function(t,e,a){"use strict";a.d(e,"f",(function(){return i})),a.d(e,"g",(function(){return s})),a.d(e,"l",(function(){return o})),a.d(e,"m",(function(){return r})),a.d(e,"r",(function(){return c})),a.d(e,"s",(function(){return l})),a.d(e,"j",(function(){return d})),a.d(e,"k",(function(){return u})),a.d(e,"d",(function(){return p})),a.d(e,"e",(function(){return h})),a.d(e,"b",(function(){return m})),a.d(e,"c",(function(){return f})),a.d(e,"p",(function(){return b})),a.d(e,"q",(function(){return g})),a.d(e,"n",(function(){return v})),a.d(e,"o",(function(){return y})),a.d(e,"h",(function(){return C})),a.d(e,"i",(function(){return x})),a.d(e,"a",(function(){return _}));var n=a("b775");function i(t){return Object(n["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biRanking/contractRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(n["a"])({url:"biRanking/receivablesRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"biRanking/contractCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(n["a"])({url:"biRanking/productCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"biRanking/productCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(n["a"])({url:"biRanking/customerCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(n["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(n["a"])({url:"biRanking/contactsCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(n["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(n["a"])({url:"biRanking/recordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(n["a"])({url:"biRanking/customerRecordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(n["a"])({url:"biRanking/customerRecordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(n["a"])({url:"biRanking/travelCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(n["a"])({url:"biRanking/travelCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function _(t){return Object(n["a"])({url:"crmBiSearch/searchContactsPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},a660:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-user-select":!1,title:"出差次数排行","module-type":"ranking"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),a("div",{staticClass:"content"},[a("div",{staticClass:"content-title"},[t._v("出差次数排行（按创建人、出差时间统计）")]),t._v(" "),a("div",{directives:[{name:"empty",rawName:"v-empty",value:0===t.list.length,expression:"list.length === 0"}],staticClass:"axis-content",attrs:{"xs-empty-text":"暂无排行"}},[a("div",{attrs:{id:"axismain"}})]),t._v(" "),a("div",{staticClass:"table-content"},[a("div",{staticClass:"handle-bar"},[a("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:"400","cell-style":t.cellStyle,"highlight-current-row":""},on:{"row-click":t.handleRowClick}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"公司总排名"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.$index+1)+"\n          ")]}}])}),t._v(" "),t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,"show-overflow-tooltip":""}})}))],2)],1)]),t._v(" "),t.showList?a("examine-list",{attrs:{type:t.showType,name:t.showTypeName,request:t.indexRequest,params:t.indexParams},on:{hide:function(e){t.showList=!1}}}):t._e()],1)},i=[],s=(a("a434"),a("b0c0"),a("26b0")),o=a("b80b"),r=a("313e"),c=a("a475"),l=a("cf9f"),d=a("5bf4"),u={name:"RankingExamineStatistics",components:{ExamineList:d["a"]},mixins:[s["a"],o["a"]],data:function(){return{postParams:{},typeList:[],indexParams:{},showType:"",showTypeName:"",showList:!1}},computed:{indexRequest:function(){return l["b"]}},mounted:function(){this.fieldList=[{field:"realname",name:"员工"},{field:"deptName",name:"部门"},{field:"count",name:"出差次数（次）"}],this.initAxis()},methods:{getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,Object(c["h"])(t).then((function(t){e.loading=!1,e.list=t.data||[];for(var a=[],n=[],i=t.data.length>10?10:t.data.length,s=0;s<i;s++){var o=t.data[s];a.splice(0,0,parseFloat(o.count)),n.splice(0,0,o.realname)}e.axisOption.yAxis[0].data=n,e.axisOption.series[0].data=a,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},initAxis:function(){this.chartObj=r["b"](document.getElementById("axismain")),this.axisOption.tooltip.formatter="{b} : {c}次",this.axisOption.xAxis[0].name="（次）",this.chartObj.setOption(this.axisOption,!0)},cellStyle:function(t){var e=t.row,a=t.column;t.rowIndex,t.columnIndex;return"count"===a.property&&e[a.property]?{color:"#2362FB",cursor:"pointer"}:""},handleRowClick:function(t,e,a){if(this.postParams.type=3,"count"===e.property&&t[e.property]){var n={userId:t.userId,categoryId:t.typeId,type:3};this.showType=3,this.showTypeName="出差次数",this.indexParams=n,this.showList=!0}},exportClick:function(){this.requestExportInfo(c["i"],this.postParams)}}},p=u,h=(a("d94e"),a("2877")),m=Object(h["a"])(p,n,i,!1,null,"4a1e40b1",null);e["default"]=m.exports},c750:function(t,e,a){},cf4f:function(t,e,a){},cf9f:function(t,e,a){"use strict";a.d(e,"e",(function(){return i})),a.d(e,"d",(function(){return s})),a.d(e,"c",(function(){return o})),a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return c}));var n=a("b775");function i(t){return Object(n["a"])({url:"biWork/logStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biWork/logStatisticsExport",method:"post",data:t,responseType:"blob",timeout:6e5,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biWork/examineStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(n["a"])({url:"biWork/examineInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biWork/examineStatisticsExport",method:"post",data:t,responseType:"blob",timeout:6e5,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},d94e:function(t,e,a){"use strict";a("7963")}}]);