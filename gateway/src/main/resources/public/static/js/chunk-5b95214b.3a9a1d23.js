(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b95214b"],{5684:function(e,t,a){"use strict";a("6831")},6831:function(e,t,a){},edce:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("wk-page-header",{attrs:{title:e.config.showModuleName?"回款管理":"",help:e.getHelpObj(e.crmType,"index"),dropdowns:e.getDefaultHeaderHandes()},on:{command:e.pageHeaderCommand}},[a("template",{slot:"right"},[e.saveAuth?a("el-button",{attrs:{type:"primary"},on:{click:e.createClick}},[e._v("新建回款")]):e._e()],1)],2),e._v(" "),a("div",{directives:[{name:"empty",rawName:"v-empty",value:!e.indexAuth,expression:"!indexAuth"}],staticClass:"crm-container",attrs:{"xs-empty-icon":"nopermission","xs-empty-text":"暂无权限"}},[a("wk-table-header",{attrs:{search:e.search,tabs:e.sceneList,"active-tab":e.sceneId,"selection-list":e.tableSelectionList,operations:e.handleOperations,"condition-type-fun":void 0,fields:e.getFilterFields,props:e.tableHeaderProps.props,"filter-header-props":e.tableHeaderProps.filterHeaderProps,"filter-form-props":e.tableHeaderProps.filterFormProps,"scene-set-props":e.tableHeaderProps.sceneSetProps,"scene-create-props":e.tableHeaderProps.sceneCreateProps},on:{"update:search":function(t){e.search=t},"update:activeTab":function(t){e.sceneId=t},"tabs-change":e.sceneSelect,"operations-click":e.tableOperationsClick,"event-change":e.tableHeaderHandle,"filter-change":e.handleFilter}}),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.crmTableClass,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,"row-key":e.crmType+"Id",stripe:e.tableStyleObj.stripe,"use-virtual":"","highlight-current-row":""},on:{"row-click":e.handleRowClick,"sort-change":e.sortChange,"header-dragend":e.handleHeaderDragend,"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"","reserve-selection":"",type:"selection",fixed:"",align:"center",width:"55"}}),e._v(" "),e._l(e.fieldList,(function(t,n){return a("el-table-column",{key:n,attrs:{fixed:1===t.isLock,prop:t.prop,label:t.label,width:t.width,"class-name":t.width>60?"column":"",sortable:"custom","show-overflow-tooltip":""},scopedSlots:e._u([{key:"otherHeader",fn:function(n){return t.width>60?[a("el-button",{staticClass:"el-lock-btn",attrs:{icon:1===t.isLock?"wk wk-unlock":"wk wk-lock",type:"text"},on:{click:function(a){a.stopPropagation(),e.fieldFixed(t)}}}),e._v(" "),e.showFilter(t)?a("el-button",{staticClass:"el-filter-btn",attrs:{type:"text",icon:"wk wk-screening"},on:{click:function(a){a.stopPropagation(),e.showFilterClick(t)}}}):e._e()]:void 0}},{key:"default",fn:function(n){var o=n.row,s=n.column;n.$index;return["checkStatus"==t.prop?[a("span",{staticClass:"status-mark",style:e.getStatusStyle(o.checkStatus)}),e._v(" "),a("span",[e._v(e._s(e.getStatusName(o.checkStatus)))])]:a("wk-field-view",{attrs:{props:t,"form-type":t.formType,value:o[s.property]},scopedSlots:e._u([{key:"default",fn:function(a){a.data;return[e._v("\n              "+e._s(e.fieldFormatter(o,s,o[s.property],t))+"\n            ")]}}])})]}}])})})),e._v(" "),a("el-table-column"),e._v(" "),a("wk-empty",{attrs:{slot:"empty",props:{buttonTitle:"新建回款",showButton:e.saveAuth}},on:{click:e.createClick},slot:"empty"}),e._v(" "),a("field-set",{attrs:{slot:"other","crm-type":e.crmType},on:{change:e.setSave},slot:"other"})],2),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-dropdown",{attrs:{trigger:"click",placement:"top"}},[a("el-button",{staticClass:"dropdown-btn"},[a("i",{staticClass:"el-icon-s-fold"})]),e._v(" "),a("el-dropdown-menu",{staticClass:"wk-table-style-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.rightBorderShow,callback:function(t){e.$set(e.tableStyleObj,"rightBorderShow",t)},expression:"tableStyleObj.rightBorderShow"}}),e._v("显示竖向分割线")],1)]),e._v(" "),a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.bottomBorderShow,callback:function(t){e.$set(e.tableStyleObj,"bottomBorderShow",t)},expression:"tableStyleObj.bottomBorderShow"}}),e._v("显示横向分割线")],1)]),e._v(" "),a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.stripe,callback:function(t){e.$set(e.tableStyleObj,"stripe",t)},expression:"tableStyleObj.stripe"}}),e._v("显示斑马纹")],1)])],1)],1),e._v(" "),a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}),e._v(" "),e.moneyPageData?a("span",{staticClass:"money-bar"},[e._v("回款金额"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"12","data-id":"151"}}),e._v("："+e._s(e._f("separator")(e.moneyPageData.receivablesMoney||0)))]):e._e()],1)],1),e._v(" "),a("c-r-m-all-detail",{staticClass:"d-view",attrs:{id:e.rowID,visible:e.showDview,"crm-type":e.rowType,"page-list":e.crmType==e.rowType?e.list:[],"page-index":e.rowIndex},on:{"update:id":function(t){e.rowID=t},"update:visible":function(t){e.showDview=t},"update:pageIndex":function(t){e.rowIndex=t},handle:e.handleHandle}}),e._v(" "),e.createShow?a("receivables-create",{on:{close:function(t){e.createShow=!1},"save-success":e.handleHandle}}):e._e(),e._v(" "),e.transferDialogShow?a("transfer-handle",{attrs:{props:e.transferHandleProps,"dialog-visible":e.transferDialogShow},on:{"update:dialogVisible":function(t){e.transferDialogShow=t},handle:e.handleHandle}}):e._e(),e._v(" "),e.teamsDialogShow?a("teams-handle",{attrs:{props:e.teamsHandleProps,"dialog-visible":e.teamsDialogShow},on:{"update:dialogVisible":function(t){e.teamsDialogShow=t},handle:e.handleHandle}}):e._e()],1)},o=[],s=(a("d81d"),a("e9f5"),a("ab43"),a("e9c4"),a("b680"),a("d3b7"),a("d43a")),r=a("496b"),i=a("c8fa"),l=a("d718"),c=a("5eb0"),d=a("e505"),p=a("ed08"),u={name:"ReceivablesIndex",components:{ReceivablesCreate:r["a"],CRMAllDetail:i["a"],TransferHandle:l["a"],TeamsHandle:c["a"]},mixins:[d["a"]],data:function(){return{crmType:"receivables",createShow:!1,moneyData:null,transferDialogShow:!1,transferHandleProps:{},teamsHandleProps:{},teamsDialogShow:!1}},computed:{showBottomMoney:function(){return!this.config.isSelect},moneyPageData:function(){if(!this.showBottomMoney)return!1;if(!this.moneyData||"{}"==JSON.stringify(this.moneyData))return null;if(0==this.selectionList.length)return this.moneyData||{};for(var e=0,t=0;t<this.selectionList.length;t++){var a=this.selectionList[t];1!=a.checkStatus&&10!=a.checkStatus||(e=Object(p["n"])(e,parseFloat(a.money)))}return{receivablesMoney:e.toFixed(2)}},handleOperations:function(){return this.getOperations(["transfer","export","delete","add_user","delete_user"])}},mounted:function(){},deactivated:function(){},methods:{tableOperationsClick:function(e){var t=this;"transfer"===e?(this.transferHandleProps={request:s["o"],params:{ids:this.selectionList.map((function(e){return e[t.crmType+"Id"]}))},showRemoveType:!0,help:this.getHelpObj(this.crmType,"transfer")},this.transferDialogShow=!0):"export"===e?this.$wkExport.export(this.crmType,{params:{ids:this.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]}))},request:s["c"]}):"delete"===e?this.$confirm("确定删除选中的".concat(this.selectionList.length,"项吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(s["a"])(t.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]}))).then((function(a){t.loading=!1,t.$message({type:"success",message:"删除成功"}),t.handleHandle({type:e})})).catch((function(){t.handleHandle({type:e}),t.loading=!1}))})).catch((function(){})):"add_user"!==e&&"delete_user"!==e||(this.teamsHandleProps={type:{add_user:"add",delete_user:"delete"}[e],addRequest:s["m"],removeRequest:s["l"],params:{ids:this.selectionList.map((function(e){return e[t.crmType+"Id"]}))},readOnlyHelp:this.getHelpObj(this.crmType,"teamReadOnly"),readWriteHelp:this.getHelpObj(this.crmType,"teamReadWrite")},this.teamsDialogShow=!0)},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"number"===t.property?"can-visit--underline can-visit--bold":"customerName"===t.property||"contractNum"===t.property?"can-visit--underline":""},createClick:function(){this.createShow=!0}}},h=u,m=(a("5684"),a("2877")),f=Object(m["a"])(h,n,o,!1,null,"7611ee90",null);t["default"]=f.exports}}]);