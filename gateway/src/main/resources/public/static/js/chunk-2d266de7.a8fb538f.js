(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d266de7"],{17266:function(e,t,o){"use strict";var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-dialog",{attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,"before-close":e.handleClose,title:"客户查重","custom-class":"no-padding-dialog",width:"800px"},on:{"update:visible":function(t){e.visible=t}}},[o("span",{staticClass:"el-dialog__title",attrs:{slot:"title"},slot:"title"},[e._v("客户查重"),e.helpObj?o("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.helpObj.type,"data-id":e.helpObj.id}}):e._e()]),e._v(" "),o("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"duplicate-check",class:{"show-table":e.showTable}},[o("div",[o("el-input",{staticClass:"search-input",nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.getList(t)}},model:{value:e.searchContent,callback:function(t){e.searchContent=t},expression:"searchContent"}},[o("el-select",{attrs:{slot:"prepend"},slot:"prepend",model:{value:e.typeSelect,callback:function(t){e.typeSelect=t},expression:"typeSelect"}},[o("el-option",{attrs:{label:"按客户名称",value:"name"}}),e._v(" "),o("el-option",{attrs:{label:"按手机号/电话",value:"phone"}})],1),e._v(" "),o("el-button",{attrs:{slot:"append",type:"primary",icon:"wk wk-search"},nativeOn:{click:function(t){return e.getList(t)}},slot:"append"},[e._v("查重")])],1)],1),e._v(" "),e.showTable?o("el-table",{staticClass:"duplicate-check__content",staticStyle:{width:"100%"},attrs:{size:"small",data:e.tableData,"cell-class-name":e.cellClassName,border:"",height:"380"},on:{"row-click":e.handleRowClick}},[e._l(e.fieldList,(function(e,t){return o("el-table-column",{key:t,attrs:{prop:e.prop,label:e.label,"min-width":e.width,"show-overflow-tooltip":""}})})),e._v(" "),o("el-table-column",{attrs:{fixed:"right",width:"110px",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text",disabled:!t.row.poolAuthList||!t.row.poolAuthList.receive},on:{click:function(o){e.handleClick("receive",t.row)}}},[e._v("领取")]),e._v(" "),o("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text",disabled:!t.row.poolAuthList||!t.row.poolAuthList.distribute},on:{click:function(o){e.handleClick("distribute",t.row)}}},[e._v("分配")])]}}])})],2):e._e(),e._v(" "),e.showTable?o("div",{staticClass:"duplicate-check__footer"},[e._v("注：查重结果最多显示10条，如不完整，请完善查重条件")]):e._e(),e._v(" "),o("c-r-m-full-screen-detail",{attrs:{id:e.relationId,visible:e.showFullDetail,"crm-type":e.relationCrmType,"pool-id":e.poolId},on:{"update:visible":function(t){e.showFullDetail=t},handle:e.getList}}),e._v(" "),o("alloc-handle",{attrs:{"pool-id":e.poolId,"selection-list":[e.relationData],"dialog-visible":e.allocDialogShow,"crm-type":"customer"},on:{"update:dialogVisible":function(t){e.allocDialogShow=t},handle:e.getList}})],1)])},n=[],l=(o("99af"),o("d81d"),o("b0c0"),o("e9f5"),o("ab43"),o("d3b7"),o("3ca3"),o("ddb0"),o("ec3a")),i=o("5868"),s=o("6683"),r={name:"DuplicateCheck",components:{AllocHandle:i["a"],CRMFullScreenDetail:function(){return Promise.resolve().then(o.bind(null,"df3e"))}},props:{crmType:String,visible:{type:Boolean,default:!1}},data:function(){return{loading:!1,typeSelect:"name",searchContent:"",tableData:null,showFullDetail:!1,relationId:"",relationData:{},poolId:"",relationCrmType:"",allocDialogShow:!1}},computed:{showTable:function(){return this.tableData},isCustomerFilter:function(){return"name"==this.typeSelect},fieldList:function(){return this.isCustomerFilter?[{prop:"name",label:"客户名称",width:200},{prop:"createTime",label:"创建时间",width:160},{prop:"ownerUserName",label:"负责人",width:100},{prop:"lastTime",label:"最后跟进时间",width:160},{prop:"module",label:"模块",width:100}]:[{prop:"mobile",label:"手机号/电话",width:160},{prop:"contactsName",label:"联系人",width:160},{prop:"name",label:"客户名称",width:200},{prop:"ownerUserName",label:"负责人",width:100},{prop:"module",label:"模块",width:100}]},helpObj:function(){return{leads:{type:"7",id:"77"},customer:{type:"8",id:"78"},contacts:{type:"9",id:"79"}}[this.crmType]||null}},watch:{typeSelect:function(){this.searchContent="",this.tableData=null}},mounted:function(){},methods:{handleClose:function(){this.$emit("update:visible",!1),this.resetData()},resetData:function(){this.typeSelect="name",this.searchContent="",this.tableData=null},getList:function(){var e=this;if(this.searchContent){var t={};t[this.typeSelect]=this.searchContent,this.loading=!0,Object(l["e"])(t).then((function(t){e.loading=!1;var o=t.data||[];e.tableData=o.map((function(t){return t.module=s["a"].convertTypeToName(t.type),t.poolName&&(t.module="".concat(t.module,"（").concat(t.poolName,"）")),e.isCustomerFilter||(t.mobile=e.searchContent,t.type==s["a"].contacts&&(t.contactsName=t.name,t.contactsId=t.id,t.name=t.customerName,t.id=t.customerId,t.type=s["a"].customer)),t}))})).catch((function(){e.loading=!1}))}},handleRowClick:function(e,t,o){if("name"==t.property&&e.id){this.relationId=e.id,e.poolAuthList?this.poolId=e.poolAuthList.poolId:this.poolId="";var a=s["a"].convertTypeToKey(e.type);this.relationCrmType="pool"==a?"customer":a,this.showFullDetail=!0}else"contactsName"==t.property&&e.contactsId&&(this.relationId=e.contactsId,this.relationCrmType="contacts",this.showFullDetail=!0)},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"name"===t.property||"contactsName"===t.property?"can-visit--underline":""},handleClick:function(e,t){var o=this;"receive"===e?this.$confirm("确定要领取该客户吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(l["Z"])({ids:[t.id],poolId:t.poolAuthList.poolId}).then((function(e){o.$message.success("操作成功"),o.getList()})).catch((function(){}))})).catch((function(){})):(t["customerId"]=t.id,this.relationData=t,this.allocDialogShow=!0)}}},c=r,d=(o("abb0"),o("2877")),p=Object(d["a"])(c,a,n,!1,null,"68b61f31",null);t["a"]=p.exports},"2bbb":function(e,t,o){},"3f80":function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("wk-page-header",{attrs:{title:e.config.showModuleName?"线索管理":"",help:e.getHelpObj(e.crmType,"index"),dropdowns:e.getDefaultHeaderHandes()},on:{command:e.pageHeaderCommand}},[o("template",{slot:"right"},[e.saveAuth?o("el-button",{attrs:{type:"primary"},on:{click:e.createClick}},[e._v("新建线索")]):e._e(),e._v(" "),e.indexAuth?o("el-button",{on:{click:function(t){e.dupCheckShow=!0}}},[e._v("查重")]):e._e()],1)],2),e._v(" "),o("div",{directives:[{name:"empty",rawName:"v-empty",value:!e.indexAuth,expression:"!indexAuth"}],staticClass:"crm-container",attrs:{"xs-empty-icon":"nopermission","xs-empty-text":"暂无权限"}},[o("wk-table-header",{attrs:{search:e.search,tabs:e.sceneList,"active-tab":e.sceneId,"selection-list":e.tableSelectionList,operations:e.handleOperations,"condition-type-fun":void 0,fields:e.getFilterFields,props:e.tableHeaderProps.props,"filter-header-props":e.tableHeaderProps.filterHeaderProps,"filter-form-props":e.tableHeaderProps.filterFormProps,"scene-set-props":e.tableHeaderProps.sceneSetProps,"scene-create-props":e.tableHeaderProps.sceneCreateProps},on:{"update:search":function(t){e.search=t},"update:activeTab":function(t){e.sceneId=t},"tabs-change":e.sceneSelect,"operations-click":e.tableOperationsClick,"event-change":e.tableHeaderHandle,"filter-change":e.handleFilter}}),e._v(" "),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.crmTableClass,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,"row-key":e.crmType+"Id",stripe:e.tableStyleObj.stripe,"use-virtual":"","highlight-current-row":""},on:{"row-click":e.handleRowClick,"sort-change":e.sortChange,"header-dragend":e.handleHeaderDragend,"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{"show-overflow-tooltip":"","reserve-selection":"",type:"selection",fixed:"",align:"center",width:"55"}}),e._v(" "),e.isShow?o("el-table-column",{attrs:{resizable:!1,prop:"call",fixed:"",label:"",width:"55"},scopedSlots:e._u([{key:"header",fn:function(e){return[o("i",{staticClass:"el-icon-phone",staticStyle:{color:"#2486e4",cursor:"not-allowed",opacity:"0.5"}})]}},{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"right",width:"500","popper-class":"no-padding-popover",trigger:"click"},on:{show:function(o){e.showData(t.row.leadsId)},hiden:function(t){e.showCount=-1}}},[o("call-center",{attrs:{scope:t,show:t.row.leadsId===e.showCount,"crm-type":"leads"},on:{changeType:e.changeCRMType}}),e._v(" "),o("el-button",{staticClass:"wk-call-btn",style:{opacity:t.$index>=0?1:0},attrs:{slot:"reference",type:"primary",icon:"el-icon-phone",circle:""},on:{click:function(o){o.stopPropagation(),e.callCheckClick(o,t,t.$index)}},slot:"reference"})],1)]}}])}):e._e(),e._v(" "),o("el-table-column",{attrs:{fixed:"",width:"60"},scopedSlots:e._u([{key:"header",fn:function(e){return[o("i",{staticClass:"el-icon-star-off focus-icon is-disabled"})]}},{key:"default",fn:function(t){return[o("el-tooltip",{attrs:{content:0==t.row.star?"添加关注":"取消关注",effect:"dark",placement:"top"}},[0==t.row.star?o("i",{staticClass:"el-icon-star-off focus-icon",on:{click:function(o){e.toggleStar(t.row)}}}):o("i",{staticClass:"wk wk-focus-on focus-icon active",on:{click:function(o){e.toggleStar(t.row)}}})])]}}])}),e._v(" "),e._l(e.fieldList,(function(t,a){return o("el-table-column",{key:a,attrs:{fixed:1===t.isLock,prop:t.prop,label:t.label,width:t.width,"class-name":t.width>60?"column":"",sortable:"custom","show-overflow-tooltip":""},scopedSlots:e._u([{key:"otherHeader",fn:function(a){return t.width>60?[o("el-button",{staticClass:"el-lock-btn",attrs:{icon:1===t.isLock?"wk wk-unlock":"wk wk-lock",type:"text"},on:{click:function(o){o.stopPropagation(),e.fieldFixed(t)}}}),e._v(" "),e.showFilter(t)?o("el-button",{staticClass:"el-filter-btn",attrs:{type:"text",icon:"wk wk-screening"},on:{click:function(o){o.stopPropagation(),e.showFilterClick(t)}}}):e._e()]:void 0}},{key:"default",fn:function(a){var n=a.row,l=a.column;a.$index;return[o("wk-field-view",{attrs:{props:t,"form-type":t.formType,value:n[l.property]},scopedSlots:e._u([{key:"default",fn:function(o){o.data;return[e._v("\n              "+e._s(e.fieldFormatter(n,l,n[l.property],t))+"\n            ")]}}])})]}}])})})),e._v(" "),o("el-table-column"),e._v(" "),o("wk-empty",{attrs:{slot:"empty",props:{buttonTitle:"新建线索",showButton:e.saveAuth}},on:{click:e.createClick},slot:"empty"}),e._v(" "),o("field-set",{attrs:{slot:"other","crm-type":e.crmType},on:{change:e.setSave},slot:"other"})],2),e._v(" "),o("div",{staticClass:"pagination-container"},[o("el-dropdown",{attrs:{trigger:"click",placement:"top"}},[o("el-button",{staticClass:"dropdown-btn"},[o("i",{staticClass:"el-icon-s-fold"})]),e._v(" "),o("el-dropdown-menu",{staticClass:"wk-table-style-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[o("el-dropdown-item",[o("span",{on:{click:function(e){e.stopPropagation()}}},[o("el-switch",{model:{value:e.tableStyleObj.rightBorderShow,callback:function(t){e.$set(e.tableStyleObj,"rightBorderShow",t)},expression:"tableStyleObj.rightBorderShow"}}),e._v("显示竖向分割线")],1)]),e._v(" "),o("el-dropdown-item",[o("span",{on:{click:function(e){e.stopPropagation()}}},[o("el-switch",{model:{value:e.tableStyleObj.bottomBorderShow,callback:function(t){e.$set(e.tableStyleObj,"bottomBorderShow",t)},expression:"tableStyleObj.bottomBorderShow"}}),e._v("显示横向分割线")],1)]),e._v(" "),o("el-dropdown-item",[o("span",{on:{click:function(e){e.stopPropagation()}}},[o("el-switch",{model:{value:e.tableStyleObj.stripe,callback:function(t){e.$set(e.tableStyleObj,"stripe",t)},expression:"tableStyleObj.stripe"}}),e._v("显示斑马纹")],1)])],1)],1),e._v(" "),o("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.showDview?o("leads-detail",{staticClass:"d-view",attrs:{id:e.rowID,"page-list":e.crmType==e.rowType?e.list:[],"page-index":e.rowIndex,"model-data":e.modelData},on:{"update:id":function(t){e.rowID=t},"update:pageIndex":function(t){e.rowIndex=t},handle:e.handleHandle,"hide-view":function(t){e.showDview=!1}}}):e._e(),e._v(" "),e.createShow?o("leads-create",{on:{close:function(t){e.createShow=!1},"save-success":e.handleHandle}}):e._e(),e._v(" "),o("duplicate-check",{attrs:{"crm-type":e.crmType,visible:e.dupCheckShow},on:{"update:visible":function(t){e.dupCheckShow=t}}}),e._v(" "),e.transferDialogShow?o("transfer-handle",{attrs:{props:e.transferHandleProps,"dialog-visible":e.transferDialogShow},on:{"update:dialogVisible":function(t){e.transferDialogShow=t},handle:e.handleHandle}}):e._e()],1)},n=[],l=(o("d81d"),o("e9f5"),o("7d54"),o("ab43"),o("e9c4"),o("d3b7"),o("159b"),o("7360")),i=o("b7c5"),s=o("de88"),r=o("006f"),c=o("17266"),d=o("d718"),p=o("e505"),u={name:"LeadsIndex",components:{LeadsDetail:s["a"],CallCenter:r["a"],LeadsCreate:i["a"],DuplicateCheck:c["a"],TransferHandle:d["a"]},mixins:[p["a"]],data:function(){return{crmType:"leads",showCount:0,createShow:!1,modelData:{},dupCheckShow:!1,transferDialogShow:!1,transferHandleProps:{}}},computed:{isShow:function(){return this.$store.state.crm.isCall},handleOperations:function(){return this.getOperations(["transfer","transform","export","delete"])}},mounted:function(){},deactivated:function(){},methods:{tableOperationsClick:function(e){var t=this;"transfer"===e?(this.transferHandleProps={request:l["m"],params:{ids:this.selectionList.map((function(e){return e[t.crmType+"Id"]}))},help:this.getHelpObj(this.crmType,"transfer")},this.transferDialogShow=!0):"transform"===e?this.$confirm("确定将这些线索转换为客户吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(l["n"])(t.selectionList.map((function(e){return e.leadsId}))).then((function(o){t.loading=!1,t.$message({type:"success",message:"转化成功"}),t.$store.dispatch("GetMessageNum"),t.handleHandle({type:e})})).catch((function(){t.loading=!1}))})).catch((function(){})):"export"===e?this.$wkExport.export(this.crmType,{params:{ids:this.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]}))},request:l["d"]}):"delete"===e&&this.$confirm("确定删除选中的".concat(this.selectionList.length,"项吗？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(l["a"])(t.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]}))).then((function(o){t.loading=!1,t.$message({type:"success",message:"删除成功"}),t.handleHandle({type:e})})).catch((function(){t.handleHandle({type:e}),t.loading=!1}))})).catch((function(){}))},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"leadsName"===t.property?"can-visit--underline":""},showData:function(e){this.showCount=e},changeCRMType:function(e){this.rowType=e.type,this.rowID=e.id,this.modelData={modelId:e.id,model:e.type},this.showDview=!0;var t={modelId:e.id,model:e.type};t=JSON.stringify(t),localStorage.setItem("callOutData",t)},callCheckClick:function(e,t){var o=this;this.list.forEach((function(e){o.$set(e,"callShow",!1)})),this.$set(t.row,"callShow",!t.row.callShow);var a=e.target.parentNode;a.__vue__.showPopper=!t.row.callShow},createClick:function(){this.createShow=!0}}},h=u,f=(o("df12"),o("2877")),m=Object(f["a"])(h,a,n,!1,null,"3ef38076",null);t["default"]=m.exports},"7ebc":function(e,t,o){},abb0:function(e,t,o){"use strict";o("2bbb")},df12:function(e,t,o){"use strict";o("7ebc")}}]);