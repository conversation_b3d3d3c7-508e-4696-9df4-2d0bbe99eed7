(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d41a06c"],{"1a77":function(e,t,a){"use strict";a("5e07")},"225e":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("xr-header",{attrs:{label:"数据操作日志"}}),e._v(" "),a("div",{staticClass:"main-body"},[a("flexbox",{staticClass:"main-table-header"},[a("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间"},model:{value:e.dateTime,callback:function(t){e.dateTime=t},expression:"dateTime"}}),e._v(" "),a("wk-user-dialog-select",{attrs:{radio:!1,placeholder:"选择人员"},model:{value:e.userList,callback:function(t){e.userList=t},expression:"userList"}}),e._v(" "),a("el-select",{on:{change:e.modelChange},model:{value:e.model,callback:function(t){e.model=t},expression:"model"}},e._l(e.modelOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),a("el-select",{attrs:{multiple:"","collapse-tags":""},model:{value:e.subModelLabels,callback:function(t){e.subModelLabels=t},expression:"subModelLabels"}},e._l(e.subModelsOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.refreshList}},[e._v("查询")]),e._v(" "),a("el-button",{staticClass:"main-table-header-button",attrs:{size:"small",type:"primary"},on:{click:e.exportClick}},[e._v("导出")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-table",class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.list,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight,"highlight-current-row":""}},e._l(e.fieldList,(function(e,t){return a("el-table-column",{key:t,attrs:{prop:e.prop,label:e.label,"show-overflow-tooltip":""}})}))),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)},i=[],n=(a("7db0"),a("e9f5"),a("f665"),a("7d54"),a("d3b7"),a("159b"),a("c73d"),a("a2822")),s=a("f468"),o=a("8f81"),r=a("5c96"),u=a("e828"),c=a("ed08"),d={name:"DataHandleLog",components:{XrHeader:s["a"],WkUserDialogSelect:o["a"]},mixins:[u["a"]],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-270,dateTime:[],userList:[],model:"",subModelLabels:[],list:[],currentPage:1,pageSize:10,pageSizes:[10,20,30,40],total:0,postParams:{}}},computed:{subModelsOptions:function(){var e=this,t=this.modelOptions.find((function(t){return t.value===e.model}));return t?t.list:[]}},mounted:function(){window.onresize=function(){self.tableHeight=document.documentElement.clientHeight-270},this.getList()},methods:{refreshList:function(){this.currentPage=1,this.getList()},modelChange:function(){this.subModelLabels=[]},getList:function(){var e=this;this.loading=!0;var t={page:this.currentPage,limit:this.pageSize,model:this.model,type:1};this.userList&&this.userList.length&&(t.userIds=this.userList),this.dateTime&&this.dateTime.length&&(t.startTime=this.dateTime[0],t.endTime=this.dateTime[1]),t.subModelLabels=this.subModelLabels,this.postParams=t,Object(n["c"])(t).then((function(t){var a=t.data.list;a.forEach((function(t){t.model=e.getModelName(t.model)})),e.list=a,e.total=t.data.totalRow,e.loading=!1})).catch((function(){e.loading=!1}))},exportClick:function(){var e=r["Loading"].service({fullscreen:!0,text:"导出中..."});Object(n["d"])(this.postParams).then((function(t){Object(c["g"])(t),e.close()})).catch((function(){e.close()}))},handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()}}},p=d,b=(a("1a77"),a("2877")),h=Object(b["a"])(p,l,i,!1,null,"1e008158",null);t["default"]=h.exports},"5e07":function(e,t,a){},a2822:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return o}));var l=a("b775");function i(e){return Object(l["a"])({url:"adminSysLog/queryLoginLogPageList",method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"},data:e})}function n(e){return Object(l["a"])({url:"adminSysLog/exportLoginLog",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"},timeout:6e4})}function s(e){return Object(l["a"])({url:"adminSysLog/querySysLogPageList",method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"},data:e})}function o(e){return Object(l["a"])({url:"adminSysLog/exportSysLog",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"},timeout:6e4})}},c73d:function(e,t,a){"use strict";var l=a("23e7"),i=a("cfe9"),n=a("edd0"),s=a("83ab"),o=TypeError,r=Object.defineProperty,u=i.self!==i;try{if(s){var c=Object.getOwnPropertyDescriptor(i,"self");!u&&c&&c.get&&c.enumerable||n(i,"self",{get:function(){return i},set:function(e){if(this!==i)throw new o("Illegal invocation");r(i,"self",{value:e,writable:!0,configurable:!0,enumerable:!0})},configurable:!0,enumerable:!0})}else l({global:!0,simple:!0,forced:u},{self:i})}catch(d){}},e828:function(e,t,a){"use strict";t["a"]={data:function(){return{modelOptions:[{label:"客户管理",value:"crm",list:[{label:"线索",value:21},{label:"客户",value:22},{label:"联系人",value:23},{label:"项目",value:24},{label:"合同",value:25},{label:"回款",value:26},{label:"发票",value:27},{label:"回访",value:28},{label:"产品",value:29},{label:"市场活动",value:30}]},{label:"办公管理",value:"oa",list:[{label:"日历",value:41},{label:"日志",value:42}]}],sysOptions:[{label:"企业首页",value:1},{label:"应用管理",value:2},{label:"员工管理",value:3},{label:"部门管理",value:4},{label:"角色管理",value:5},{label:"客户管理",value:7},{label:"办公管理",value:11}],fieldList:[{prop:"realname",label:"用户",width:100},{prop:"createTime",label:"时间",width:150},{prop:"ipAddress",label:"IP地址",width:100},{prop:"model",label:"模块",width:150},{prop:"behavior",label:"行为",width:150},{prop:"object",label:"对象",width:150},{prop:"detail",label:"操作详情",width:100}]}},methods:{getModelName:function(e){return{crm:"客户管理",oa:"办公管理",admin:"系统管理"}[e]}}}}}]);