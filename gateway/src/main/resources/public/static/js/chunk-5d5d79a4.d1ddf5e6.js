(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d5d79a4"],{"061e":function(e,t,s){"use strict";s("5cc0")},"0ebd":function(e,t,s){"use strict";t["a"]={colorList:["#1890ff","#00A3BF","#DE350B","#5243AA","#00875A","#FF991F","#091E42"]}},"140a":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"main"},[s("flexbox",{staticClass:"main-header",attrs:{justify:"space-between"}},[s("div",{staticClass:"main-header__left"},[s("span",{staticClass:"title"},[e._v("日历")])]),e._v(" "),s("div",{staticClass:"main-header__right"},[s("el-button",{attrs:{type:"primary"},on:{click:e.createEvents}},[e._v("新建日程")])],1)]),e._v(" "),s("div",{staticClass:"search-bar"},[e.showUser?s("wk-user-select",e._b({ref:"wkUserSelect",staticClass:"left-user",staticStyle:{height:"auto !important"},attrs:{value:e.checkedUser&&e.checkedUser.length>0?e.checkedUser[0].userId:"",request:e.subUserListIndex,radio:!0,props:{isList:!0}},on:{change:e.selectUser}},"wk-user-select",e.$attrs,!1),[s("el-input",{staticClass:"type-select",class:["type-select--no-border",{"is-show":e.$refs.wkUserSelect&&e.$refs.wkUserSelect.visible}],staticStyle:{width:"180px"},attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:e.filterName,callback:function(t){e.filterName=t},expression:"filterName"}},[s("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1):s("span",{staticClass:"username"},[e._v("日历")])],1),e._v(" "),s("flexbox",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"calendar-box",style:{height:e.contentHeight+"px"}},[s("div",{staticClass:"box-left"},[s("el-checkbox-group",{staticClass:"left-scroll",model:{value:e.checkCusList,callback:function(t){e.checkCusList=t},expression:"checkCusList"}},[s("schedule",{directives:[{name:"loading",rawName:"v-loading",value:e.scheduleLoading,expression:"scheduleLoading"}],ref:"schedule",attrs:{"list-data-type":e.listDataType,"active-time":e.activeTime,"calendar-arr":e.calendarArr},on:{choseDay:e.gotoPast,changeMonth:e.changeMonth}}),e._v(" "),s("div",{staticClass:"left-main"},[s("div",{staticClass:"main-title",on:{click:function(t){e.showSys=!e.showSys}}},[e.showSys?s("i",{staticClass:"el-icon-arrow-down",staticStyle:{"margin-right":"0"}}):s("i",{staticClass:"el-icon-arrow-right",staticStyle:{"margin-right":"0"}}),e._v(" "),s("span",{staticClass:"main-text"},[e._v("系统类型")])]),e._v(" "),s("el-collapse-transition",[s("div",{directives:[{name:"show",rawName:"v-show",value:e.showGroup&&e.showSys,expression:"showGroup && showSys"}]},e._l(e.sysTypeList,(function(t){return s("el-checkbox",{key:t.typeId,class:t.class,attrs:{label:t.typeId}},[e._v("\n                "+e._s(t.typeName)+"\n              ")])})))])],1),e._v(" "),s("div",{staticClass:"left-bottom"},[s("div",{staticClass:"bottom-title",on:{click:function(t){e.showCustom=!e.showCustom}}},[e.showCustom?s("i",{staticClass:"el-icon-arrow-down",staticStyle:{"margin-right":"0"}}):s("i",{staticClass:"el-icon-arrow-right",staticStyle:{"margin-right":"0"}}),e._v(" "),s("span",{staticClass:"main-text"},[e._v("自定义类型")])]),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.showGroup&&e.showCustom,expression:"showGroup && showCustom"}]},e._l(e.customTypeList,(function(t){return s("el-checkbox",{key:t.typeId,class:t.class,attrs:{label:t.typeId}},[e._v(e._s(t.typeName))])})))])],1),e._v(" "),s("div",{staticClass:"left-bottom-text"},[s("span",{staticClass:"text-span"},[e._v("自定义类型可在后台配置")])])],1),e._v(" "),s("div",{staticClass:"box-right"},[s("FullCalendar",{ref:"fullCalendar",staticClass:"calendar-main",attrs:{"button-text":e.buttonText,header:{left:"",center:"prevYear,prev, title, next,nextYear",right:"today, listDay, timeGridWeek, dayGridMonth"},plugins:e.calendarPlugins,weekends:e.calendarWeekends,"first-day":e.firstDay,"event-time-format":e.evnetTime,"all-day-slot":!0,"event-limit":!0,events:e.calendarEvents,"event-limit-text":e.eventLimiTtext,"now-indicator":!0,"display-event-end":!1,"slot-label-format":{hour:"numeric",minute:"2-digit",omitZeroMinute:!1,meridiem:"short",hour12:!1},"column-format":{day:"dddd M/d"},"list-day-format":e.listDayFormat,"all-day-text":"全天","no-events-message":"暂无日程",locale:"zh-cn","week-number-calculation":"ISO","default-view":"dayGridMonth"},on:{eventClick:e.eventClick,datesRender:e.datesRender,dateClick:e.handleDateClick}})],1),e._v(" "),s("create-event",{attrs:{"show-create":e.showCreate,"select-div":e.selectDiv,"color-list":e.colorList,"cus-check":e.cusCheck},on:{createSuccess:e.createSuccess,close:function(t){e.showCreate=!1}}}),e._v(" "),e.showTodayDetail?s("today-list-detail",{attrs:{id:e.eventId,"show-today-detail":e.showTodayDetail,"cus-check":e.cusCheck,"today-detail-data":e.todayDetailData},on:{deleteSuccess:e.handleSuccess,createSuccess:e.handleSuccess,close:function(t){e.showTodayDetail=!1}}}):e._e(),e._v(" "),s("c-r-m-full-screen-detail",{attrs:{id:e.relationID,visible:e.showFullDetail,"crm-type":e.relationCrmType},on:{"update:visible":function(t){e.showFullDetail=t}}})],1)],1)},a=[],c=s("2909"),n=(s("99af"),s("4de4"),s("7db0"),s("caad"),s("a15b"),s("d81d"),s("14d9"),s("b0c0"),s("e9f5"),s("910d"),s("f665"),s("7d54"),s("ab43"),s("d3b7"),s("ac1f"),s("2532"),s("3ca3"),s("5319"),s("159b"),s("ddb0"),s("dc09")),o=s("88e1"),r=s("a7c0"),l=s("19bc"),d=s("aafe"),h=s("f88c"),u=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"schedule-calendar"},[s("div",[s("Calendar",e._g(e._b({ref:"calendar",attrs:{"mark-date-more":e.scheduleList},on:{changeMonth:e.changeMonth}},"Calendar",e.$attrs,!1),e.$listeners))],1)])},m=[],v=s("be6e"),f=s.n(v),y=s("bce8"),p={name:"Schedule",components:{Calendar:f.a},props:{activeTime:{type:Object,default:function(){return{}}},listDataType:{type:String,default:""}},data:function(){return{scheduleList:[],currentMonthDate:new Date}},watch:{},mounted:function(){this.selectDay(new Date,!0)},methods:{changeMonth:function(e){this.currentMonthDate=new Date(e),this.$emit("changeMonth",this.currentMonthDate)},selectDay:function(e){this.$refs.calendar.ChoseMonth(e,!0)},selectMouth:function(e){this.$refs.calendar.ChoseMonth(e)},getDateList:function(e){var t=this;this.scheduleList=[],Object(y["f"])(e).then((function(e){var s=e.data||[];s.forEach((function(e){t.scheduleList.push({date:e,className:"mark1"})}))})).catch((function(){}))}}},T=p,C=(s("061e"),s("2877")),g=Object(C["a"])(T,u,m,!1,null,"a43af13e",null),w=g.exports,k=s("3335"),b=s("0ebd"),D=s("5c0a"),I=s("c1df"),L=s.n(I),S=s("be86"),x=(s("795d"),s("a435"),s("1599"),s("124e"),s("ed08")),_={name:"Calender",components:{FullCalendar:n["a"],Schedule:w,CreateEvent:S["a"],TodayListDetail:k["a"],WkUserSelect:D["a"],CRMFullScreenDetail:function(){return Promise.resolve().then(s.bind(null,"df3e"))}},data:function(){return{loading:!1,eventId:"",contentHeight:document.documentElement.clientHeight-210,calendarPlugins:[o["d"],r["a"],d["a"],h["a"],l["a"]],calendarWeekends:!0,calendarEvents:[],calendarList:[],colorList:b["a"].colorList,buttonText:{month:"月",week:"周",day:"日",today:"今天"},evnetTime:{hour:"numeric",minute:"2-digit",hour12:!1},firstDay:1,scheduleLoading:!1,calendarArr:[],isFirstToDay:!0,currentTime:"",currentActiveTime:"",checkSysList:[],sysCheck:[{label:"分配的任务"},{label:"需联系的客户"},{label:"即将到期的合同"},{label:"计划回款"}],checkCusList:[],dayEventList:[],cusCheck:[],showGroup:!1,showSys:!0,showCustom:!0,showCreate:!1,choseTitle:"",showTodayDetail:!1,todayDetailData:{},selectDiv:null,activeTime:{},listDataType:"",todaySchedule:[],needData:{leadsTimeList:[],customerTimeList:[],endContractTimeList:[],receiveContractTimeList:[],businessTimeList:[],dealBusinessTimeList:[]},checkedUser:[],copyCheckCusList:[],showUser:!0,showpover:!1,taskList:[],showFullDetail:!1,relationCrmType:"task",relationID:"",selectSysList:[],sysTypeId:[],firstEnter:!0}},computed:{subUserListIndex:function(){return y["p"]},showUserPover:function(){return this.$refs.wkUserSelect&&this.$refs.wkUserSelect.visible},sysTypeList:function(){return this.cusCheck.filter((function(e){return 1===e.type}))},customTypeList:function(){return this.cusCheck.filter((function(e){return 2===e.type}))},filterName:function(){return this.checkedUser.length>0?this.checkedUser[0].realname+"的日程":"我的日程"}},watch:{checkCusList:{handler:function(e){1===e.length?(this.copyCheckCusList=e,this.customFifter(e)):0===e.length?1===this.copyCheckCusList.length&&(this.checkCusList=this.copyCheckCusList,this.$message.error("请至少选中一个类型")):this.customFifter(e)},deep:!0,immediate:!0}},mounted:function(){var e=this;window.onresize=function(){e.contentHeight=document.documentElement.clientHeight-210},this.showUserSelect(),this.addBus()},destroyed:function(){this.$bus.off("handleSuccess")},beforeRouteLeave:function(e,t,s){s()},methods:{addBus:function(){var e=this;this.$bus.on("handleSuccess",(function(){e.getCusCheck()}))},getList:function(){var e=this;this.loading=!0,this.activeTime.typeIds=null,this.$refs.schedule.getDateList(this.activeTime),Object(y["e"])(this.activeTime).then((function(t){e.calendarEvents=[];var s=t.data||[];s.forEach((function(e){e.startTime=L()(parseInt(e.startTime)).format("YYYY-MM-DD HH:mm:ss"),e.endTime=L()(parseInt(e.endTime)).format("YYYY-MM-DD HH:mm:ss")})),e.dayEventList=s,e.handleShowData(),e.loading=!1})).catch((function(){e.loading=!1}))},getCusCheck:function(){var e=this;this.loading=!0,this.checkSysList=[],this.calendarEvents=[],this.showGroup=!1,this.sysTypeId=[];var t=[],s={1:"task",2:"customer",3:"contract",4:"receiveContract",5:"leads",6:"business",7:"dealBusiness"};Object(y["g"])({userId:this.activeTime.userId}).then((function(i){var a=i.data||[];e.todaySchedule=[],a.forEach((function(i){i.select&&t.push(i.typeId),1===i.type?(e.sysTypeId.push({typeId:i.typeId,name:i.typeName,crmType:s[i.color]}),"1"===i.color?i.class="color_8":"2"===i.color?i.class="color_1":"3"===i.color?i.class="color_5":"4"===i.color?i.class="color_11":"5"===i.color?i.class="color_3":"6"===i.color?i.class="color_6":"7"===i.color&&(i.class="color_7")):e.colorList.forEach((function(e,t){i.color===e&&(i.class="color_".concat(t+1),i.color=e)}))})),0===e.checkCusList.length&&(e.checkCusList=t),e.activeTime.typeIds=e.checkCusList,e.cusCheck=a,e.getTodayTypeList()})).catch((function(){e.loading=!1}))},updateList:function(){this.activeTime.typeIds=this.checkCusList,0!==this.checkCusList.length&&Object(y["o"])({typeIds:this.checkCusList,userId:this.activeTime.userId}).then((function(e){})).catch((function(){}))},listDayFormat:function(e){var t=new Date(e.date.marker).getTime(),s=L()(t).format("ll"),i=L()(t).format("dddd").replace("星期","周"),a=i+"  "+s;return a},toggleWeekends:function(){this.calendarWeekends=!this.calendarWeekends},gotoPast:function(e,t){if(this.isFirstToDay)this.isFirstToDay=!1;else{var s=new Date(e).getTime(),i=L()(s).format("YYYY-MM-DD");this.selectDiv=i;var a=this.$refs.fullCalendar.getApi();a&&(t||a.changeView("listDay"),a.gotoDate(i))}},handleDateClick:function(e){this.selectDiv===e.dateStr?this.showCreate=!0:this.selectDiv=e.dateStr;var t=document.getElementsByClassName("select-day");t&&t.length&&t[0].classList.remove("select-day"),e.dayEl.classList.add("select-day")},eventLimiTtext:function(e){return"剩余".concat(e,"条")},datesRender:function(e){if("listDay"===e.view.type){if("暂无日程"===e.el.textContent){var t=document.createElement("img");t.id="emityImg",t.src=s("b45c");var i=document.getElementsByClassName("fc-list-empty-wrap1")[0];i&&i.insertBefore(t,i.children[0])}}else if("dayGridMonth"===e.view.type){if(this.currentTime===e.view.title?this.selectDiv&&document.querySelector('td[data-date="'.concat(this.selectDiv,'"]'))&&document.querySelector('td[data-date="'.concat(this.selectDiv,'"]')).classList.add("select-day"):this.currentTime=e.view.title,this.activeTime.startTime!==new Date(e.view.activeStart).getTime()){this.activeTime.startTime=new Date(e.view.activeStart).getTime(),this.activeTime.endTime=new Date(e.view.activeEnd).getTime();var a=this.activeTime.endTime-this.activeTime.startTime;a>864e5&&this.getCusCheck()}}else e.view.type;this.listDataType=e.view.type},eventClick:function(e){var t=this;if(e.event.extendedProps&&-2==e.event.extendedProps.typeId)return this.relationID=e.event.id,this.relationCrmType="task",void setTimeout((function(){t.showFullDetail=!0}),200);this.eventId=e.event.id,this.todayDetailData={startTime:e.event.start||"",endTime:e.event.end||e.event.start,id:e.event.id,title:e.event.title,userId:this.activeTime.userId,groupId:e.event.groupId,backgroundColor:e.event.textColor},e.event.extendedProps&&(this.todayDetailData.name=e.event.extendedProps.name,this.todayDetailData.createTime=e.event.extendedProps.createTime,this.todayDetailData.headTitle=e.event.title,this.todayDetailData.crmType=e.event.extendedProps.crmType,this.todayDetailData.typeId=e.event.extendedProps.typeId||3),this.showTodayDetail=!0},clickDay:function(e){},changeMonth:function(e,t){this.gotoPast(e,!0)},customFifter:function(e){this.updateEvent(this.checkCusList)},updateEvent:function(e){var t=[];this.calendarList.forEach((function(s){e.forEach((function(e){e===s.groupId&&t.push(s)}))})),this.updateList(),this.calendarEvents=t},createEvents:function(){this.selectDiv="",this.showCreate=!0},handleSure:function(e,t){this.calendarEvents.push({title:e.title,crmType:e.crmType,start:e.startTime,id:e.eventId,color:t,textColor:t,backgroundColor:Object(x["e"])(t,.1),typeId:e.groupId,groupId:e.typeId,end:e.endTime})},createSuccess:function(){this.showCreate=!1,this.getCusCheck()},handleSuccess:function(){this.showTodayDetail=!1,this.getCusCheck()},selectUser:function(e,t){this.checkedUser=t,this.copyCheckCusList=[],t.length?(this.activeTime.userId=t.map((function(e){return e.userId})).join(","),this.getCusCheck()):this.activeTime.userId=""},showUserSelect:function(){var e=this;Object(y["p"])().then((function(t){0===t.data.length?e.showUser=!1:e.showUser=!0})).catch((function(){}))},getTodayTypeList:function(){var e=this;this.loading=!0;var t={startTime:this.activeTime.startTime,endTime:this.activeTime.endTime,userId:this.activeTime.userId};Object(y["b"])(t).then((function(t){var s=t.data||{};e.needData={leadsTimeList:s.leadsTimeList||[],customerTimeList:s.customerTimeList||[],endContractTimeList:s.endContractTimeList||[],receiveContractTimeList:s.receiveContractTimeList||[],businessTimeList:s.businessTimeList||[],dealBusinessTimeList:s.dealBusinessTimeList||[]},e.todaySchedule=e.handleData(e.cusCheck),e.selectSysList.includes("1")?e.getTask():(e.taskList=[],e.getList())})).catch((function(){}))},getTask:function(){var e=this;this.taskList=[];var t={startTime:this.activeTime.startTime,endTime:this.activeTime.endTime,userId:this.activeTime.userId};Object(y["c"])(t).then((function(t){var s=t.data||[],i="#1890ff";e.taskList=s.map((function(t){return{title:t.name,startTime:"".concat(L()(parseInt(t.startTime)).format("YYYY-MM-DD")," 00:00:00"),id:t.taskId,eventId:t.taskId,color:i,textColor:i,backgroundColor:Object(x["e"])(i,.1),groupId:-2,typeId:e.sysTypeId[0].typeId,endTime:"".concat(L()(parseInt(t.endTime)).format("YYYY-MM-DD")," 23:59:59")}})),e.getList()})).catch((function(){}))},handleData:function(e){var t=this;this.selectSysList=[];var s=[];e.forEach((function(e){1===e.type&&t.selectSysList.push(e.color)}));var i=this.sysTypeId.find((function(e){return"leads"===e.crmType}))||{},a="#DE350B";this.needData.leadsTimeList.forEach((function(e){s.push({title:"需联系的线索",startTime:"".concat(e," 00:00:00"),eventId:-1,color:a,textColor:a,backgroundColor:Object(x["e"])(a,.1),crmType:i.crmType,typeId:i.typeId,groupId:i.typeId,endTime:"".concat(e," 23:59:59")})}));var c=this.sysTypeId.find((function(e){return"customer"===e.crmType}))||{},n="#1890ff";this.needData.customerTimeList.forEach((function(e){s.push({title:"需联系的客户",startTime:"".concat(e," 00:00:00"),eventId:-1,color:n,textColor:n,backgroundColor:Object(x["e"])(n,.1),crmType:c.crmType,typeId:c.typeId,groupId:c.typeId,endTime:"".concat(e," 23:59:59")})}));var o=this.sysTypeId.find((function(e){return"business"===e.crmType}))||{},r="#FF991F";this.needData.businessTimeList.forEach((function(e){s.push({title:"需联系的项目",startTime:"".concat(e," 00:00:00"),eventId:-1,color:r,textColor:r,backgroundColor:Object(x["e"])(r,.1),crmType:o.crmType,typeId:o.typeId,groupId:o.typeId,endTime:"".concat(e," 23:59:59")})}));var l=this.sysTypeId.find((function(e){return"dealBusiness"===e.crmType}))||{},d="#091E42";this.needData.dealBusinessTimeList.forEach((function(e){s.push({title:"预计成交的项目",startTime:"".concat(e," 00:00:00"),eventId:-1,color:d,textColor:d,backgroundColor:Object(x["e"])(d,.1),crmType:l.crmType,typeId:l.typeId,groupId:l.typeId,endTime:"".concat(e," 23:59:59")})}));var h=this.sysTypeId.find((function(e){return"contract"===e.crmType}))||{},u="#00875A";this.needData.endContractTimeList.forEach((function(e){s.push({title:"即将到期的合同",startTime:"".concat(e," 00:00:00"),eventId:-1,color:u,textColor:u,backgroundColor:Object(x["e"])(u,.1),crmType:h.crmType,typeId:h.typeId,groupId:h.typeId,endTime:"".concat(e," 23:59:59")})}));var m=this.sysTypeId.find((function(e){return"receiveContract"===e.crmType}))||{},v="#5243AA";return this.needData.receiveContractTimeList.forEach((function(e){s.push({title:"计划回款",startTime:"".concat(e," 00:00:00"),eventId:-1,color:v,textColor:v,backgroundColor:Object(x["e"])(v,.1),crmType:m.crmType,typeId:m.typeId,groupId:m.typeId,endTime:"".concat(e," 23:59:59")})})),s},handleShowData:function(){var e=this,t=[].concat(Object(c["a"])(this.dayEventList||[]),Object(c["a"])(this.todaySchedule||[]),Object(c["a"])(this.taskList||[]));t.forEach((function(t){e.handleSure(t,t.color)})),this.calendarList=this.calendarEvents,this.showGroup=!0,this.customFifter(this.checkCusList)}}},E=_,M=(s("db7b"),Object(C["a"])(E,i,a,!1,null,"6ddbf942",null));t["default"]=M.exports},"56be":function(e,t,s){},"5cc0":function(e,t,s){},db7b:function(e,t,s){"use strict";s("56be")}}]);