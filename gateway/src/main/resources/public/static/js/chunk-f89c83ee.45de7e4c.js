(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f89c83ee"],{3898:function(e,t,a){},"4b57":function(e,t,a){"use strict";a("6802")},6802:function(e,t,a){},"6be5":function(e,t,a){"use strict";a("dd95")},"9a6b":function(e,t,a){"use strict";a("3898")},b0a8:function(e,t,a){"use strict";var s=a("5579");t["a"]={components:{WkEmpty:s["a"]},props:{},data:function(){return{currentPage:1,pageSize:15,pageSizes:[15,30,60,100],total:0,rowHeight:44,otherTableHeight:265,tableHeight:200}},computed:{},watch:{},created:function(){},mounted:function(){var e=this;window.onresize=function(){e.updateTableHeight()}},beforeDestroy:function(){},methods:{handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},updateTableHeight:function(){var e=document.documentElement.clientHeight-this.otherTableHeight,t=this.rowHeight*this.list.length+41;this.tableHeight=t>e?e:0===this.list.length?200:t}}}},d9fe:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("flexbox",{staticClass:"main-header",attrs:{justify:"space-between"}},[a("div",{staticClass:"main-header__left"},[a("span",{staticClass:"title"},[e._v(e._s(e.title))])]),e._v(" "),a("div",{staticClass:"main-header__right"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.createClick}},[e._v("新建任务")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.exportClick}},[e._v("导出")])],1)]),e._v(" "),a("flexbox",{staticClass:"main-header is-filter-header",attrs:{justify:"space-between"}},[a("div",{staticClass:"main-header__left"},[a("el-input",{staticClass:"search-input",attrs:{placeholder:"请输入任务名称"},on:{input:e.debouncedRefreshList},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.debouncedRefreshList(t)}},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[a("el-button",{attrs:{slot:"suffix",type:"icon",icon:"wk wk-sousuo"},on:{click:e.debouncedRefreshList},slot:"suffix"})],1),e._v(" "),a("span",{staticClass:"tabs"},[a("span",{staticClass:"tabs-label"},[e._v("显示:")]),e._v(" "),e._l(e.tabs,(function(t,s){return a("el-button",{key:s,attrs:{type:t.name===e.tabsSelectValue?"selected":null},on:{click:function(a){e.tabsChange(t.name)}}},[e._v(e._s(t.label))])}))],2),e._v(" "),a("span",{staticClass:"progress"},[e._v("共"),a("span",[e._v(e._s(e.progress.allTask))]),e._v("个,已完成"),a("span",[e._v(e._s(e.progress.stopTask))]),e._v("个")]),e._v(" "),a("el-button",{staticClass:"refresh-btn",class:[{"is-rotate":e.refreshRotate}],attrs:{type:"icon",icon:"el-icon-refresh"},on:{click:e.refreshRotateClick}})],1),e._v(" "),a("div",{staticClass:"main-header__right"},[a("el-popover",{attrs:{"popper-class":"no-padding-popover",placement:"bottom",width:"300",trigger:"click"},model:{value:e.taskFilterShow,callback:function(t){e.taskFilterShow=t},expression:"taskFilterShow"}},[e.taskFilterShow?a("task-filter",{attrs:{"due-date":e.dueDate,priority:e.priority,done:e.showDone,users:e.userList},on:{close:function(t){e.taskFilterShow=!1},save:e.taskFilterSave}}):e._e(),e._v(" "),a("el-button",{attrs:{slot:"reference",type:e.taskFilterShow?"selected":"subtle",icon:"wk wk-screening"},on:{click:function(t){e.taskFilterShow=!0}},slot:"reference",model:{value:e.taskFilterShow,callback:function(t){e.taskFilterShow=t},expression:"taskFilterShow"}},[e._v("筛选")])],1)],1)]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],key:(e.tableHeight||"0")+"-"+e.pageSize,class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,stripe:e.WKConfig.tableStyle.stripe,"use-virtual":"","row-key":"taskId","highlight-current-row":""},on:{"row-click":e.handleRowClick,"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"",align:"center",fixed:"",width:"55"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("el-checkbox",{on:{change:function(t){e.taskOverClick(s)}},nativeOn:{click:function(e){e.stopPropagation()}},model:{value:s.checked,callback:function(t){e.$set(s,"checked",t)},expression:"row.checked"}})]}}])}),e._v(" "),e._l(e.tableHeaderFields,(function(t,s){return a("el-table-column",{key:s,attrs:{fixed:0==s,prop:t.field,label:t.name,"min-width":t.width,"show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(s){var i=s.row,n=s.column;s.$index;return["priority"===n.property?[a("span",{staticClass:"status-mark",style:{backgroundColor:e.getPriorityColor(i.priority).color}}),e._v(e._s(e.getPriorityColor(i.priority).label)+"\n        ")]:"labelId"===n.property?[a("tag-view",{attrs:{"item-bottom":0,value:(i.labelList||[]).map((function(e){return{name:e.labelName,value:e.color?e.color:"#ccc"}}))}})]:[t.iconClass&&""!==i[n.property]&&null!==i[n.property]?a("i",{class:t.iconClass}):e._e(),e._v(" "),a("span",{class:{"can-visit--underline":t.canVisite}},[e._v(e._s(e.fieldFormatter(i,n,i[n.property],t)))]),e._v(" "),"stopTime"===t.field&&e.getIsOverdue(i)?a("el-tag",{attrs:{type:"danger","disable-transitions":""}},[e._v("已逾期")]):e._e()]]}}])})})),e._v(" "),a("wk-empty",{attrs:{slot:"empty",props:{buttonTitle:"新建任务",showButton:!0}},on:{click:e.createClick},slot:"empty"})],2),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.taskDetailShow?a("task-detail",{ref:"particulars",attrs:{id:e.taskId,"detail-index":e.detailIndex,"no-listener-class":["el-table__body"]},on:{"on-handle":e.detailHandle,close:function(t){e.taskDetailShow=!1}}}):e._e(),e._v(" "),e.taskCreateShow?a("task-create",{on:{save:e.getList,close:function(t){e.taskCreateShow=!1}}}):e._e()],1)},i=[],n=a("b85c"),r=a("5530"),l=(a("a15b"),a("d81d"),a("a434"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c"),a("7c0f")),o=a("1658"),c=a("eaf0"),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"task-filter"},[e._m(0),e._v(" "),a("div",{staticClass:"task-filter__body"},[a("el-form",{ref:"form",attrs:{"label-width":"90px"}},[a("el-form-item",{attrs:{label:"负责人"}},[a("wk-user-dialog-select",{staticClass:"handle-item-content",attrs:{value:e.users.map((function(e){return e.userId}))},on:{change:e.userChage}})],1),e._v(" "),a("el-form-item",{attrs:{label:"截止时间"}},[a("el-date-picker",{directives:[{name:"elclickoutside",rawName:"v-elclickoutside",value:e.handleDateClose,expression:"handleDateClose"}],ref:"filterDatePicker",attrs:{type:"date",placeholder:"选择日期","value-format":"yyyy-MM-dd"},model:{value:e.dueDateValue,callback:function(t){e.dueDateValue=t},expression:"dueDateValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"优先级"}},[a("el-select",{model:{value:e.priorityValue,callback:function(t){e.priorityValue=t},expression:"priorityValue"}},e._l(e.priorityOptions,(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})))],1),e._v(" "),a("el-form-item",{attrs:{label:"显示已完成"}},[a("el-switch",{model:{value:e.doneValue,callback:function(t){e.doneValue=t},expression:"doneValue"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"task-filter__footer"},[a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.saveClick,expression:"saveClick"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),a("el-button",{nativeOn:{click:function(t){return e.closeClick(t)}}},[e._v("取消")])],1)])])},h=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"task-filter__header"},[a("span",{staticClass:"task-filter__title"},[e._v("筛选")])])}],d=a("8f81"),p=a("ed08"),f={name:"TaskFilter",components:{WkUserDialogSelect:d["a"]},props:{dueDate:String,priority:String,users:Array,done:Boolean},data:function(){return{dueDateValue:"",priorityValue:"",doneValue:!1,usersList:[],priorityOptions:[{label:"全部",key:""},{label:"高",key:"3"},{label:"中",key:"2"},{label:"低",key:"1"},{label:"无",key:"0"}]}},computed:{},watch:{},created:function(){this.dueDateValue=this.dueDate,this.priorityValue=this.priority,this.doneValue=this.done,this.usersList=Object(p["D"])(this.users)},beforeDestroy:function(){},methods:{closeClick:function(){this.$emit("close")},userChage:function(e,t){this.usersList=t},saveClick:function(){this.$emit("save",this.dueDateValue,this.priorityValue,this.doneValue,this.usersList),this.$emit("close")},handleDateClose:function(){this.$refs.filterDatePicker.pickerVisible=!1}}},k=f,m=(a("9a6b"),a("2877")),b=Object(m["a"])(k,u,h,!1,null,"4c0fe064",null),g=b.exports,v=a("05e9"),y=a("5feb"),w=a("2f62"),_=a("7a1a"),C=a("b0a8"),S=a("0886"),x=a("c1df"),T=a.n(x),D={name:"Index",components:{TaskDetail:c["a"],TaskFilter:g,TaskCreate:v["a"],TagView:y["a"]},mixins:[C["a"],S["a"]],props:{},data:function(){return{tabsSelectValue:"0",taskType:"",list:[],refreshRotate:!1,loading:!1,dueDate:"",search:"",priority:"",showDone:!0,userList:[],taskFilterShow:!1,progress:{stopTask:0,allTask:0},tableHeaderFields:[{name:"任务名称",field:"name",width:360,formType:"text"},{name:"描述",field:"description",width:120,formType:"textarea"},{name:"优先级",field:"priority",width:80,formType:"select"},{name:"负责人",field:"mainUserId",width:80,formType:"user"},{name:"开始时间",field:"startTime",width:120,iconClass:"wk wk-icon-datetime2",formType:"date"},{name:"结束时间",field:"stopTime",width:200,iconClass:"wk wk-icon-datetime2",formType:"date"},{name:"参与人",field:"ownerUserId",width:80,formType:"user"},{name:"标签",field:"labelId",width:200,formType:"tag"},{name:"子任务数",field:"childAllCount",width:80,iconClass:"wk wk-subtasks",formType:"text",canVisite:!0},{name:"附件数",field:"fileCount",width:80,iconClass:"wk wk-icon-file",formType:"text",canVisite:!0},{name:"评论数",field:"commentCount",width:80,iconClass:"wk wk-icon-message-line",formType:"text",canVisite:!0}],taskId:"",detailIndex:-1,taskDetailShow:!1,taskCreateShow:!1}},computed:Object(r["a"])(Object(r["a"])({},Object(w["b"])(["userInfo"])),{},{tabs:function(){return 1==this.taskType?[{label:"全部",name:"0"},{label:"我负责的",name:"1"},{label:"我参与的",name:"3"}]:[{label:"全部",name:"0"},{label:"下属负责的",name:"1"},{label:"下属参与的",name:"3"}]},title:function(){return 1==this.taskType?"我的任务":"下属的任务"}}),watch:{},created:function(){this.taskType=this.$route.params.type,this.debouncedRefreshList=Object(_["debounce"])(300,this.refreshList),this.refreshList()},mounted:function(){},beforeRouteUpdate:function(e,t,a){this.taskType=e.params.type,this.showDone=!0,this.progress={stopTask:0,allTask:0},this.tabsSelectValue="0",this.dueDate="",this.priority="",this.userList=[],this.search="",this.refreshList(),a()},beforeDestroy:function(){},methods:{createClick:function(){this.taskCreateShow=!0},cellClassName:function(e){var t=e.row,a=e.column;e.rowIndex,e.columnIndex;if("name"===a.property){var s="can-visit--underline";return t.checked&&(s+=" wk-task-name-finish"),s}return""},handleRowClick:function(e,t,a){this.taskId=e.taskId,this.detailIndex=this.getObjIndex(this.list,"taskId",e.taskId),this.taskDetailShow=!0},getObjIndex:function(e,t,a){for(var s=0;s<e.length;s++){var i=e[s];if(i[t]===a)return s}return null},handleSelectionChange:function(e){},refreshRotateClick:function(){var e=this;this.getList(),this.refreshRotate=!0,setTimeout((function(){e.refreshRotate=!1}),1e3)},refreshList:function(){this.currentPage=1,this.list=[],this.getList()},taskFilterSave:function(e,t,a,s){this.priority=t,this.dueDate=e,this.showDone=a,this.userList=s,this.refreshList()},getList:function(){var e=this;this.loading=!0;var t={page:this.currentPage,limit:this.pageSize,type:this.tabsSelectValue,priority:this.priority,dueDate:this.dueDate,status:this.showDone?"":"1",mainUserIds:this.userList&&this.userList.length>0?this.userList.map((function(e){return e.userId})):[]};this.search&&(t.search=this.search),1!=this.taskType&&(t.mold=1),Object(l["g"])(t).then((function(a){e.loading=!1;var s=a.data||{};if(e.tabsSelectValue==t.type){var i,r=s.page,l=r.list,o=Object(n["a"])(l);try{for(o.s();!(i=o.n()).done;){var c=i.value;5==c.status&&(c.checked=!0)}}catch(u){o.e(u)}finally{o.f()}e.total=r.totalRow,e.list=l,e.progress=s,e.updateTableHeight()}else e.refreshList()})).catch((function(){e.updateTableHeight(),e.loading=!1}))},fieldFormatter:function(e,t,a,s){return"mainUserId"===t.property?e.mainUser?e.mainUser.realname:"":"ownerUserId"===t.property?e.ownerUserList?e.ownerUserList.map((function(e){return e.realname})).join("、"):"":a},tabsChange:function(e){this.tabsSelectValue=e,this.refreshList()},taskCellHandle:function(e,t,a){"view"==e?(this.taskId=t.taskId,this.detailIndex=a,this.taskDetailShow=!0):"complete"==e&&(this.progress.stopTask=t.checked?++this.progress.stopTask:--this.progress.stopTask)},detailHandle:function(e){var t=this;if("delete"==e.type)this.list.splice(e.index,1),this.updateTableHeight();else{var a=1;e.index>0&&(a=Math.ceil(e.index/5));var s={page:a,limit:5,type:this.tabsSelectValue,priority:this.priority,dueDate:this.dueDate,status:this.showDone?"":"1"};1!=this.taskType&&(s.mold=1),Object(l["g"])(s).then((function(a){for(var s=t.list[e.index],i=0;i<a.data.page.list.length;i++){var n=a.data.page.list[i];if(n.taskId==s.taskId){5==n.status&&(n.checked=!0),t.list.splice(e.index,1,n);break}}t.progress=a.data})).catch((function(){}))}},exportClick:function(){var e=this;this.loading=!0;var t={type:this.tabsSelectValue,priority:this.priority,dueDate:this.dueDate,status:this.showDone?"":"1"};1!=this.taskType&&(t.mold=1),Object(l["h"])(t).then((function(t){Object(p["g"])(t),e.loading=!1})).catch((function(){e.loading=!1}))},getIsOverdue:function(e){var t=e.stopTime,a=e.status;return!(5==a||!t)&&T()().isAfter(-1!==t.indexOf(" ")?t:"".concat(t," 23:59:59"))},taskOverClick:function(e){var t=e.checked?5:1;Object(o["l"])({taskId:e.taskId,status:t}).then((function(a){e.status=t})).catch((function(){e.checked=!1}))}}},I=D,L=(a("6be5"),a("4b57"),Object(m["a"])(I,s,i,!1,null,"aa260366",null));t["default"]=L.exports},dd95:function(e,t,a){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}}}]);