(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-38507286"],{"6de2":function(e,t,i){"use strict";i("8bb1")},8917:function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));var s=i("b775");function r(e){return Object(s["a"])({url:"jxcProductType/queryJxcProductType",method:"post",data:e})}},"8bb1":function(e,t,i){},e505:function(e,t,i){"use strict";var s=i("2909"),r=i("5530"),o=(i("99af"),i("4de4"),i("7db0"),i("caad"),i("d81d"),i("14d9"),i("b0c0"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("ab43"),i("b64b"),i("d3b7"),i("ac1f"),i("25f0"),i("2532"),i("841c"),i("159b"),i("ddb0"),i("e170")),a=i("ec3a"),n=i("b775");function c(e){return Object(n["a"])({url:"CrmWeixinLeads/queryPage",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(n["a"])({url:"CrmWeixinLeads/exportLeads",method:"post",responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"},data:e})}var h=i("7360"),p=i("0007"),d=i("1112"),u=i("77dc"),f=i("6382"),m=i("d43a"),g=i("e737"),w=i("9532"),y=i("ce1c"),b=i("55c3"),v=i("dea6"),T=i("5579"),I=i("fdca"),S=i("5d2e"),k=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-popover",{staticClass:"field-set-wrap",attrs:{"popper-class":"no-padding-popover",placement:"bottom-end",width:"240",trigger:"click"},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[i("div",{staticClass:"field-set"},[i("el-input",{staticClass:"field-set__search",attrs:{placeholder:"搜索字段（拖拽字段进行排序）"},on:{input:e.searchClick},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}}),e._v(" "),i("div",{staticClass:"field-set__title"},[e._v("显示这些字段")]),e._v(" "),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"field-set__content"},[e._l(e.disabledFields,(function(t,s){return i("flexbox",{directives:[{name:"show",rawName:"v-show",value:1!=e.poolConfig&&"poolDay"!==t.fieldName||1==e.poolConfig,expression:"(poolConfig != 1 && item.fieldName !== 'poolDay') || poolConfig == 1"}],key:s,staticClass:"field-set__content--item "},[i("el-checkbox",{model:{value:t.check,callback:function(i){e.$set(t,"check",i)},expression:"item.check"}},[t.center?i("span",[e._v(e._s(t.left)),i("span",{staticClass:"input-word"},[e._v(e._s(t.center))]),e._v(e._s(t.right))]):i("span",[e._v(e._s(t.name))])])],1)})),e._v(" "),i("draggable",{attrs:{options:{filter:".el-checkbox",handle:".drag-handle"}},model:{value:e.dragFields,callback:function(t){e.dragFields=t},expression:"dragFields"}},e._l(e.dragFields,(function(t,s){return i("flexbox",{directives:[{name:"show",rawName:"v-show",value:1!=e.poolConfig&&"poolDay"!==t.fieldName||1==e.poolConfig,expression:"(poolConfig != 1 && item.fieldName !== 'poolDay') || poolConfig == 1"}],key:s,staticClass:"field-set__content--item "},[i("el-checkbox",{model:{value:t.check,callback:function(i){e.$set(t,"check",i)},expression:"item.check"}},[t.center?i("span",[e._v(e._s(t.left)),i("span",{staticClass:"input-word"},[e._v(e._s(t.center))]),e._v(e._s(t.right))]):i("span",[e._v(e._s(t.name))])]),e._v(" "),i("div",{staticClass:"drag-handle"},[e._v("⋮⋮")])],1)})))],2),e._v(" "),i("div",{staticClass:"field-set__ft"},[i("el-button",{attrs:{type:"primary",size:"medium"},on:{click:e.save}},[e._v("保存")]),e._v(" "),i("el-button",{attrs:{size:"medium",type:"text"},on:{click:e.reSet}},[e._v("重置")])],1)],1),e._v(" "),e.$slots.reference?e._t("default",null,{slot:"reference"}):i("el-button",{staticClass:"set-btn",attrs:{slot:"reference",type:e.show?"selected":"",icon:"wk wk-icon-config-line"},on:{click:function(t){e.show=!e.show}},slot:"reference"})],2)},D=[],x=(i("a9e3"),i("6683")),C=i("ed08"),L=i("b76a"),_=i.n(L),O={name:"FieldSet",components:{Draggable:_.a},props:{crmType:String,isSeas:{type:Boolean,default:!1},poolId:[String,Number]},data:function(){return{loading:!1,show:!1,poolConfig:1,fields:[],disabledFields:[],dragFields:[],copyfields:[],search:""}},computed:{},watch:{show:function(e){e&&this.getList()},poolId:function(){this.fields=[],this.show&&this.getList()}},mounted:function(){},beforeDestroy:function(){},methods:{getList:function(){var e=this;this.loading=0==this.fields.length;var t=null,i={};this.isSeas?(t=o["u"],i.poolId=this.poolId):(t=o["i"],i.label=x["a"][this.crmType]),t(i).then((function(t){var i=t.data||{};e.poolConfig=i.poolConfig;var s=(i.value||[]).map((function(e){return e.left="",e.center="",e.right="",e.check=!0,e})),r=(i.hideValue||[]).map((function(e){return e.left="",e.center="",e.right="",e.check=!1,e}));e.fields=s.concat(r),e.getShowFields(e.fields),e.copyfields=Object(C["D"])(e.fields),e.loading=!1})).catch((function(){e.loading=!1}))},getShowFields:function(e){var t=!1,i=[],s=[];e.forEach((function(e){1!==e.isLock&&(t=!0),t?s.push(e):i.push(e)})),this.dragFields=s,this.disabledFields=i},searchClick:function(){var e=this;this.fields=this.fields.map((function(t){var i=t.name.indexOf(e.search);if(-1!=i){t.left=t.name.substr(0,i),t.center=t.name.substr(i,e.search.length);var s=i+e.search.length;t.right=t.name.substr(s,t.name.length-s)}else t.left="",t.center="",t.right="";return t}))},save:function(){var e=this,t=this.disabledFields.concat(this.dragFields),i=t.filter((function(e){return e.check}));if(i.length<2)this.$message.error("至少要显示两列");else{var s=t.filter((function(e){return!e.check}));this.loading=!0;var r=null,a={noHideFields:i.map((function(e){return{id:e.id,fieldName:e.fieldName,style:e.style}})),hideFields:s.map((function(e){return{id:e.id,fieldName:e.fieldName,style:e.style}}))};this.isSeas?(r=o["t"],a.poolId=this.poolId):(r=o["h"],a.label=x["a"][this.crmType]),r(a).then((function(t){e.$message.success("操作成功"),e.show=!1,e.loading=!1,e.$emit("change")})).catch((function(){e.loading=!1}))}},reSet:function(){this.fields=Object(C["D"])(this.copyfields),this.getShowFields(this.fields)}}},j=O,N=(i("6de2"),i("2877")),F=Object(N["a"])(j,k,D,!1,null,"3b590be8",null),P=F.exports,H=i("8967"),$=i("2f62"),R=i("e297"),E=i.n(R),q=i("7403"),z=i("4042"),A=i("9bbe"),B=i("8ed6"),M=i("6bfe"),W=i("5667"),U=i("7a1a"),J={transfer:{name:"转移",type:"transfer",icon:"wk wk-icon-transfer2"},transform:{name:"转化为客户",type:"transform",icon:"wk wk-customer-line"},transformLead:{name:"转化为线索",type:"transformLead",icon:"wk wk-leads-line"},export:{name:"导出选中",type:"export",icon:"wk wk-icon-export2"},delete:{name:"删除",type:"delete",icon:"wk wk-icon-delete-line"},put_seas:{name:"放入公海",type:"put_seas",icon:"wk wk-seas"},lock:{name:"锁定",type:"lock",icon:"wk wk-icon-lock2"},unlock:{name:"解锁",type:"unlock",icon:"wk wk-icon-unlock2"},add_user:{name:"添加团队成员",type:"add_user",icon:"wk wk-icon-add-line"},delete_user:{name:"移除团队成员",type:"delete_user",icon:"wk wk-icon-remove-line"},alloc:{name:"分配",type:"alloc",icon:"wk wk-icon-org"},get:{name:"领取",type:"get",icon:"wk wk-activity-line"},start:{name:"上架",type:"start",icon:"wk wk-icon-shelves-line"},disable:{name:"下架",type:"disable",icon:"wk wk-icon-sold-out-line"},state_start:{name:"启用",type:"state_start",icon:"wk wk-icon-success-line"},state_disable:{name:"停用",type:"state_disable",icon:"wk wk-icon-close-line"},deal_status:{name:"更改成交状态",type:"deal_status",icon:"wk wk-icon-success-line"},reset_invoice_status:{name:"重置开票信息",type:"reset_invoice_status",icon:"wk wk-icon-reset2"},update:{name:"编辑",type:"update",icon:"wk wk-icon-edit-line"}},V={isSelect:!1,showScene:!0,ignoreFilterFields:[],showSearch:!0,showModuleName:!0,selectionHandle:!0,radio:!1,request:null,searchList:null,params:null},G={createUserName:"createUserId",ownerUserName:"ownerUserId",ownerDeptName:"ownerDeptId",customerName:"customerId",superiorCustomerName:"superiorCustomerId",businessName:"businessId",contactsName:"contactsId",parentContactsName:"parentContactsId",companyUserName:"companyUserId",contractNum:"contractId",planNum:"receivablesPlanId",categoryName:"categoryId"};t["a"]={components:{WkPageHeader:I["a"],WkTableHeader:S["a"],FieldSet:P,WkEmpty:T["a"],WkFieldView:H["default"]},props:{props:{type:Object,default:null},selectedData:Array},data:function(){return{loading:!1,tableHeight:200,tableStyleObj:{init:!1,stripe:!1,bottomBorderShow:!0,rightBorderShow:!0},tableHeaderProps:{},rowHeight:44,list:[],fieldList:[],sortData:{},currentPage:1,pageSize:E.a.get("crmPageSizes")||15,pageSizes:[15,30,60,100],total:0,search:"",rowID:"",rowType:"",showDview:!1,filterObj:[],sceneId:"",sceneList:[],sceneData:{},selectionList:[],isRequested:!1,rowIndex:0,ignoreSelectedChange:!1,hasExaSelctedData:!1,filterFieldList:[],customConfig:{}}},mixins:[q["a"],z["a"]],computed:Object(r["a"])(Object(r["a"])({},Object($["b"])(["crm","userInfo"])),{},{config:function(){return Object(B["a"])(Object(r["a"])({},V),this.props||{})},saveAuth:function(){return this.$auth("crm.".concat(this.crmType,".save"))},indexAuth:function(){var e=this.isSeas?"pool":this.crmType;return!!this.$auth("crm.".concat(e,".index"))},crmTableClass:function(){var e=[];return this.config&&this.config.radio&&e.push("no-all-selection"),this.tableStyleObj&&!1===this.tableStyleObj.rightBorderShow&&e.push("is-no-right-border-style"),this.tableStyleObj&&!1===this.tableStyleObj.bottomBorderShow&&e.push("is-no-bottom-border-style"),e.push("is-filter-table"),e},tableSelectionList:function(){return this.config.isSelect?[]:this.selectionList}}),watch:{showDview:function(){var e=JSON.parse(localStorage.getItem("callOutData"));e&&(this.modelData={modelId:e.id,model:e.type})},"config.params":{handler:function(){this.indexAuth&&this.refreshList()}},"config.otherHeight":{handler:function(){this.updateTableHeight()}},selectedData:function(e){var t=this;if(this.ignoreSelectedChange=!0,e){var i=!0;if(e.length!==this.selectionList.length)i=!1;else for(var s=0;s!==e.length;++s)if(e[s]["".concat(this.crmType,"Id")]!==this.selectionList[s]["".concat(this.crmType,"Id")]){i=!1;break}i||(this.hasExaSelctedData=!1,setTimeout((function(){t.setSelections(t.selectedData)}),100))}this.$nextTick((function(){t.ignoreSelectedChange=!1}))},tableStyleObj:{handler:function(e,t){t&&e&&t.init&&e.init&&this.updateTableStyle()},deep:!0}},created:function(){this.tableHeaderProps=this.getBaseTableHeaderProps(),this.debouncePostWidthChange=Object(U["debounce"])(500,this.postWidthChange)},mounted:function(){var e=this;window.onresize=function(){e.updateTableHeight()},this.indexAuth?("applet"===this.crmType?this.getFieldList():"marketing"===this.crmType?this.getList():this.isSeas?this.loading=!0:(this.loading=!0,this.config.showScene?this.getSceneList():this.refreshList()),this.getCustomConfig()):this.updateTableHeight()},methods:{getDefaultHeaderHandes:function(){var e=[];return this.$auth("crm.".concat(this.crmType,".excelimport"))&&e.push({command:"enter",name:"导入",icon:"wk wk-import"}),this.$auth("crm.".concat(this.crmType,".excelexport"))&&e.push({command:"out",name:"导出",icon:"wk wk-export"}),e},pageHeaderCommand:function(e){"out"==e?this.exportInfos():"enter"==e?this.importInfos():"seasSet"==e&&this.$router.push("/manage/customer/customer")},exportInfos:function(){var e,t=this.getBaseParams();e=this.isSeas?a["w"]:{customer:a["k"],leads:h["c"],contacts:p["c"],applet:l,business:d["b"],contract:u["c"],receivables:m["b"],product:f["c"],invoice:b["c"],receivablesPlan:g["c"]}[this.crmType],this.$wkExport.export(this.crmType,{params:{search:t},isSeas:this.isSeas,poolId:this.poolId,request:e})},importInfos:function(){this.$wkImport.import(this.crmType,{ownerSelectShow:!1,poolSelectShow:this.isSeas,userInfo:this.userInfo})},getBaseTableHeaderProps:function(){var e=this.getHelpObj(this.crmType),t=!1;t=this.props?this.props.showScene:!this.isSeas&&!["marketing","applet"].includes(this.crmType);var i=!1;return i=!["marketing","applet"].includes(this.crmType)&&(!this.isSeas||!!this.poolId),{props:{showFilterView:i,showExportFields:!1},filterHeaderProps:{maxTabCount:this.config.isSelect?2:5,tabSetShow:t,searchPlaceholder:{leads:"线索名称/手机/电话",customer:"客户名称/手机/电话",contacts:"联系人姓名/手机/电话",product:"产品名称",business:"项目名称",contract:"客户名称/合同编号/合同名称",receivables:"客户名称/回款编号",receivablesPlan:"客户名称/合同编号",visit:"回访编号",invoice:"发票号码/客户名称/合同编号",marketing:"活动名称"}[this.crmType]},filterFormProps:{showExport:!0,showSaveScene:!this.isSeas,help:e?{type:e.type,id:e.filterForm}:null,exportHelp:e?{type:e.type,id:e.filterFormExport}:null,saveRequest:this.isSeas?a["E"]:o["E"],saveParams:this.isSeas?{poolId:this.poolId}:{label:x["a"][this.crmType]}},sceneSetProps:{indexRequest:o["B"],indexParams:{type:x["a"][this.crmType]},sortRequest:o["C"],sortParams:{type:x["a"][this.crmType]},defaultsRequest:o["x"],deleteRequest:o["y"],fieldsIndexRequest:o["L"],fieldsIndexParams:{label:x["a"][this.crmType]},help:e?{type:e.type,id:e.sceneSet}:null},sceneCreateProps:{updateRequest:o["D"],updateParams:{type:x["a"][this.crmType]},saveRequest:o["A"],saveParams:{type:x["a"][this.crmType]},help:e?{type:e.type,id:e.sceneCreate}:null}}},getOperations:function(e){for(var t=[],i=0;i<e.length;i++){var s=e[i];this.getOperationsPermision(s)&&t.push(J[s])}return t},getOperationsPermision:function(e){if(!this.crm||!this.crm[this.crmType])return!1;var t=this.crm[this.crmType];if("transfer"==e)return"transform"!=this.sceneData.bydata&&t.transfer;if("transform"==e)return"transform"!=this.sceneData.bydata&&t.transform;if("export"==e)return this.isSeas?this.poolId?this.poolAuth.excelexport:this.crm.pool&&this.crm.pool.excelexport:t.excelexport;if("delete"==e)return this.isSeas?this.poolId?this.poolAuth.delete:this.crm.pool&&this.crm.pool.delete:t.delete;if("put_seas"==e)return t.putinpool;if("lock"==e||"unlock"==e)return t.lock;if("add_user"==e||"delete_user"==e)return t.teamsave;if("alloc"==e)return this.poolId?this.poolAuth.distribute:this.crm.pool&&this.crm.pool.distribute;if("get"==e)return this.poolId?this.poolAuth.receive:this.crm.pool&&this.crm.pool.receive;if("start"==e){for(var i=0;i<this.selectionList.length;i++){var s=this.selectionList[i];if(1==s.status)return!1}return t.status}if("disable"==e){for(var r=0;r<this.selectionList.length;r++){var o=this.selectionList[r];if(0==o.status)return!1}return t.status}return"deal_status"==e?t.dealStatus:"transformLead"===e||("state_start"==e||"state_disable"==e?t.updateStatus:"reset_invoice_status"==e?t.resetInvoiceStatus&&1==this.selectionList.length:"update"!=e||t.update&&1==this.selectionList.length)},getHelpObj:function(e,t){return this.config.showModuleName?Object(W["a"])(e,t,this.isSeas):null},getSceneList:function(){var e=this;Object(o["z"])({type:x["a"][this.crmType]}).then((function(t){var i=(t.data||[]).map((function(e){return e.label=e.name,e.value=null!==e.sceneId?e.sceneId.toString():"",e})),s=i.filter((function(e){return 1===e.isDefault})),r=null;s&&s.length>0?r=s[0]:i.length>0&&(r=i[0]),r&&(r.id=r.sceneId?r.sceneId.toString():"",r.bydata=r.bydata||"",e.sceneId=r.id,e.sceneData=r),e.sceneList=i,e.refreshList()})).catch((function(){}))},getList:function(){var e=this;this.loading=!0;var t=this.getIndexRequest(),i=this.getBaseParams();i.page=this.currentPage,i.limit=this.pageSize,t(i).then((function(t){e.isRequested=!0,"customer"===e.crmType?e.list=t.data.list.map((function(e){return e.show=!1,e})):("contract"!==e.crmType&&"receivables"!==e.crmType&&"receivablesPlan"!==e.crmType&&"business"!==e.crmType||(e.moneyData=t.data.extraData&&t.data.extraData.money||{}),e.list=t.data.list),t.data.totalRow&&Math.ceil(t.data.totalRow/e.pageSize)<e.currentPage&&e.currentPage>1?(e.currentPage=e.currentPage-1,e.getList()):(e.total=t.data.totalRow,e.loading=!1),e.$nextTick((function(){document.querySelector(".el-table__body-wrapper").scrollTop=1})),e.selectedData&&!e.hasExaSelctedData&&(e.hasExaSelctedData=!0,setTimeout((function(){e.setSelections(e.selectedData)}),100)),e.updateTableHeight()})).catch((function(){e.loading=!1}))},getBaseParams:function(){var e={search:this.search,type:this.isSeas?x["a"].pool:x["a"][this.crmType]};if(this.sortData.order&&(e.sortField=this.sortData.prop,e.order="ascending"==this.sortData.order?2:1),"applet"===this.crmType&&(e.type=this.appletType),this.sceneId&&(e.sceneId=this.sceneId),this.marketingCrmType&&(e.crmType=this.marketingCrmType),this.poolId&&(e.poolId=this.poolId),this.filterObj&&this.filterObj.length>0){var t=this.filterObj?this.filterObj.filter((function(e){return e.values&&e.values.length>0||[5,6].includes(e.type)})):[];t.length>0&&(e.searchList=t)}if(this.config.params){e=Object(r["a"])({},e);var i=e.searchList||[];for(var o in this.config.params){var a=this.config.params[o];"checkStatus"===o?i.push({formType:"checkStatus",name:"checkStatus",type:1,values:Object(M["a"])(a)?a:[a]}):i.push({formType:"text",name:o.includes("Id")?Object(C["J"])(o):o,type:1,values:Object(M["a"])(a)?a:[a]})}e.searchList=i}return this.config.searchList&&(e.searchList=[].concat(Object(s["a"])(e.searchList),Object(s["a"])(this.config.searchList))),e},getIndexRequest:function(){return"leads"===this.crmType?h["g"]:"applet"===this.crmType?c:"customer"===this.crmType?this.isSeas?a["z"]:a["p"]:"contacts"===this.crmType?p["h"]:"business"===this.crmType?d["f"]:"contract"===this.crmType?u["g"]:"product"===this.crmType?f["g"]:"receivables"===this.crmType?m["f"]:"marketing"===this.crmType?w["d"]:"visit"===this.crmType?y["c"]:"invoice"===this.crmType?b["e"]:"receivablesPlan"===this.crmType?g["f"]:void 0},getFieldList:function(e){var t=this;if("applet"===this.crmType)return this.fieldList=[{prop:"weixinName",label:"微信名称",width:"115px"},{prop:"weixinImg",label:"头像",width:"115px"},{prop:"mobile",label:"手机号",width:"115px"},{prop:"ownerUserName",label:"负责人",width:"115px"},{prop:"createTime",label:"创建时间",width:"115px"},{prop:"isTransform",label:"是否转化"}],void this.getList();if(0==this.fieldList.length||e){this.loading=!0;var i={};this.isSeas?this.poolId&&(i.poolId=this.poolId):i.label=x["a"][this.crmType];var s=this.isSeas?o["H"]:o["I"];s(i).then((function(e){for(var i=[],s=0;s<e.data.length;s++){var o=e.data[s],a=0;a=o.width?o.width:o.name&&o.name.length<=6?15*o.name.length+95:140,i.push(Object(r["a"])(Object(r["a"])({},o),{},{prop:o.fieldName,label:o.name,width:a}))}t.fieldList=i,t.getList()})).catch((function(){t.loading=!1}))}else this.getList()},getFilterFields:function(){var e=this;return new Promise((function(t){var i={};e.isSeas?i.poolId=e.poolId:i.label=x["a"][e.crmType];var s=e.isSeas?a["D"]:o["L"];s(i).then((function(i){var s=i.data||[];if(e.props&&e.props.ignoreFilterFields){var r=e.props.ignoreFilterFields;e.filterFieldList=s.filter((function(e){return!r.includes(e.fieldName)}))}else e.filterFieldList=s;t(e.filterFieldList)})).catch((function(){}))}))},showFilter:function(e){if(Object.keys(G).includes(e.prop))return!0;var t=this.filterFieldList.find((function(t){return t.fieldName===e.prop}));return!!t},fieldFormatter:function(e,t,i,s){return"isTransform"===t.property?["否","是"][e[t.property]]||"--":s?Object(A["a"])(s.formType,e[t.property],"--",s):""===e[t.property]||null===e[t.property]?"--":e[t.property]},tableHeaderHandle:function(e,t){"search"===e?this.refreshList():"export-fields-collapse"===e?(this.updateTableStyle({filterConfig:{showExportFields:t}}),this.updateTableHeight()):"scene-refresh"===e&&this.getSceneList()},handleRowClick:function(e,t,i){if("selection"!==t.type){if("leads"===this.crmType)"leadsName"===t.property?(this.rowID=e.leadsId,this.rowType="leads",this.showDview=!0):this.showDview=!1;else if("customer"===this.crmType){if("businessCheck"===t.property&&e.businessCount>0)return;"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):this.showDview=!1}else"contacts"===this.crmType?"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"name"===t.property?(this.rowID=e.contactsId,this.rowType="contacts",this.showDview=!0):this.showDview=!1:"business"===this.crmType?"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"businessName"===t.property?(this.rowID=e.businessId,this.rowType="business",this.showDview=!0):this.showDview=!1:"contract"===this.crmType?"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"businessName"===t.property?(this.rowID=e.businessId,this.rowType="business",this.showDview=!0):"contactsName"===t.property?(this.rowID=e.contactsId,this.rowType="contacts",this.showDview=!0):"num"===t.property?(this.rowID=e.contractId,this.rowType="contract",this.showDview=!0):this.showDview=!1:"product"===this.crmType?"name"===t.property?(this.rowID=e.productId,this.rowType="product",this.showDview=!0):this.showDview=!1:"receivables"===this.crmType?"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"contractNum"===t.property?(this.rowID=e.contractId,this.rowType="contract",this.showDview=!0):"number"===t.property?(this.rowID=e.receivablesId,this.rowType="receivables",this.showDview=!0):this.showDview=!1:"marketing"==this.crmType?"marketingName"===t.property?(this.rowID=e.marketingId,this.rowType="marketing",this.showDview=!0):this.showDview=!1:"visit"==this.crmType?"visitNumber"===t.property?(this.rowID=e.visitId,this.rowType="visit",this.showDview=!0):"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"contractNum"===t.property?(this.rowID=e.contractId,this.rowType="contract",this.showDview=!0):"contactsName"===t.property?(this.rowID=e.contactsId,this.rowType="contacts",this.showDview=!0):this.showDview=!1:"invoice"==this.crmType?"customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"contractNum"===t.property?(this.rowID=e.contractId,this.rowType="contract",this.showDview=!0):"invoiceApplyNumber"===t.property?(this.rowID=e.invoiceId,this.rowType="invoice",this.showDview=!0):this.showDview=!1:"receivablesPlan"==this.crmType&&("customerName"===t.property?(this.rowID=e.customerId,this.rowType="customer",this.showDview=!0):"contractNum"===t.property?(this.rowID=e.contractId,this.rowType="contract",this.showDview=!0):"num"===t.property?(this.rowID=e.receivablesPlanId,this.rowType="receivablesPlan",this.showDview=!0):this.showDview=!1);this.rowIndex=this.getRowIndex()}},getRowIndex:function(){for(var e=0,t=0;t<this.list.length;t++){var i=this.list[t];if(i["".concat(this.rowType,"Id")]===this.rowID){e=t;break}}return e},handleFilter:function(e){this.filterObj=e,this.refreshList()},sceneSelect:function(e){this.sceneData=e,this.sceneId=e.sceneId,this.refreshList()},refreshList:function(){this.currentPage=1,this.getFieldList()},handleHandle:function(e){["edit"].includes(e.type)||("customer"!=this.crmType&&"contract"!=this.crmType&&"leads"!=this.crmType||"transfer"!==e.type||this.$store.dispatch("GetMessageNum"),["alloc","get","transfer","transform","delete","put_seas","exit-team"].includes(e.type)&&(this.showDview=!1),"clear-sort"==e.type?(this.getMainTable().clearSort(),this.sortChange()):(this.config.isSelect||this.getMainTable().clearSelection(),this.getList()))},getMainTable:function(){var e=null;return this.$children.forEach((function(t){t.$options&&"ElTable"===t.$options.name&&(e=t)})),e},getTableHead:function(){var e=null;return this.$children.forEach((function(t){t.$options&&"WkTableHeader"===t.$options.name&&(e=t)})),e},setSave:function(){this.getFieldList(!0)},listHeadHandle:function(e){"save-success"!==e.type&&"import-crm"!==e.type||this.refreshList()},sortChange:function(e,t,i){this.sortData=e||{},this.refreshList()},handleSelectionChange:function(e){var t=this;if(!this.ignoreSelectedChange){if(this.config.radio&&e.length>1){var i=this.getMainTable(),s=e[e.length-1];return this.ignoreSelectedChange=!0,i.clearSelection(),void this.$nextTick((function(){t.ignoreSelectedChange=!1,i.toggleRowSelection(s)}))}this.selectionList=e,this.$emit("selection-change",e,this.crmType)}},handleHeaderDragend:function(e,t,i,s){this.debouncePostWidthChange(e,i)},postWidthChange:function(e,t){if(t.property){var i,s=null;if(this.fieldList.forEach((function(s){t.property===s.prop&&(i=s.id,s.width=e)})),!i)return;var r={id:i,width:e,field:t.property};if(this.isSeas){if(!this.poolId)return;s=o["s"],r.poolId=this.poolId}else s=o["g"];s(r).then((function(e){})).catch((function(){}))}},handleSizeChange:function(e){E.a.set("crmPageSizes",e),this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},getStatusStyle:function(e){return{backgroundColor:this.getStatusColor(e)}},toggleStar:function(e){var t=this;this.loading=!0;var i={leads:h["l"],customer:a["jb"],contacts:p["p"],business:d["q"]}[this.crmType],s={};s["id"]=e["".concat(this.crmType,"Id")],i(s).then((function(){t.loading=!1,t.$message.success(e.star>0?"取消关注成功":"关注成功"),e.star=e.star>0?0:1})).catch((function(){t.loading=!1}))},updateTableHeight:function(){var e=this,t=document.documentElement.clientHeight;this.config&&this.config.otherHeight?this.$nextTick((function(){var i=e.getTableHead();e.tableHeight=t-e.config.otherHeight-(i?i.$el.clientHeight:0)})):this.$nextTick((function(){var i=e.getTableHead(),s=t-i.$el.clientHeight-230,r=e.rowHeight*e.list.length+50;e.tableHeight=r>s?s:0===e.list.length?200:r}))},setSelections:function(e){var t=this.getMainTable();t.clearSelection(),this.$nextTick((function(){e.forEach((function(e){t.toggleRowSelection(e)}))}))},toggleRowSelection:function(e,t,i){var s=this;this.$nextTick((function(){var r=s.selectionList.find((function(i){return i[e]===t}));r&&s.getMainTable().toggleRowSelection(r,i)}))},showFilterClick:function(e){var t=this.filterFieldList.find((function(t){return t.fieldName===e.prop}));if(t||(t=this.filterFieldList.find((function(t){return t.fieldName===G[e.prop]}))),t){var i={isExport:!1};i.formType=t.formType,i.fieldName=t.fieldName,i.name=t.label,this.getAdvancedFilterDefaultItemByFormType(i,t,this.conditionTypeFun);var s=this.getTableHead();if(s){if(s.filterObj&&s.filterObj.form.length){var r=s.filterObj.form.find((function(e){return e.fieldName===t.fieldName}));r||s.filterObj.form.push(i)}else s.filterObj={form:[i]};s.showFilterClick()}}},fieldFixed:function(e){var t=this,i=1===e.isLock?0:1,s=this.isSeas?a["J"]:o["j"];s({id:e.id,isLock:i}).then((function(s){e.isLock=i,t.$message.success(1===i?"已将该列固定在列表前部":"已取消该列在前部固定")})).catch((function(){}))},getCustomConfig:function(){var e=this;Object(v["b"])(v["a"].pcCRMprefix+this.crmType).then((function(t){var i=t.data;if(Object(M["a"])(i)&&i.length>0){var s=i[0];Object(M["c"])(s)&&(s.tableStyleObj&&(s.tableStyleObj.init=!0,e.tableStyleObj=s.tableStyleObj),e.customConfig=s,e.tableHeaderProps.props.showExportFields=s.filterConfig.showExportFields)}e.tableStyleObj.init||e.tableStyleObj.$set("init",!0)})).catch((function(){}))},updateTableStyle:function(e){Object(v["c"])(v["a"].pcCRMprefix+this.crmType,[Object(r["a"])(Object(r["a"])({},this.customConfig),{},{tableStyleObj:this.tableStyleObj},e||{})]).then((function(e){})).catch((function(){}))}},beforeDestroy:function(){}}}}]);