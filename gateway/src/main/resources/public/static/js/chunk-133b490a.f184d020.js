(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-133b490a"],{"0030":function(t,e,a){"use strict";a("783e")},"100d":function(t,e,a){"use strict";a("45b6")},1277:function(t,e){t.exports="data:image/png;base64,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"},"129f5":function(t,e,a){"use strict";a("1593")},1593:function(t,e,a){},"1f66":function(t,e){t.exports="data:image/png;base64,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"},2602:function(t,e,a){"use strict";a.d(e,"r",(function(){return n})),a.d(e,"s",(function(){return s})),a.d(e,"t",(function(){return o})),a.d(e,"o",(function(){return r})),a.d(e,"m",(function(){return l})),a.d(e,"n",(function(){return c})),a.d(e,"c",(function(){return u})),a.d(e,"f",(function(){return d})),a.d(e,"g",(function(){return p})),a.d(e,"h",(function(){return h})),a.d(e,"u",(function(){return m})),a.d(e,"v",(function(){return f})),a.d(e,"x",(function(){return b})),a.d(e,"a",(function(){return v})),a.d(e,"b",(function(){return y})),a.d(e,"i",(function(){return g})),a.d(e,"j",(function(){return A})),a.d(e,"p",(function(){return T})),a.d(e,"q",(function(){return w})),a.d(e,"l",(function(){return C})),a.d(e,"k",(function(){return I})),a.d(e,"d",(function(){return D})),a.d(e,"w",(function(){return k})),a.d(e,"e",(function(){return M}));var i=a("b775");function n(t){return Object(i["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(i["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(i["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(i["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(i["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(i["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(i["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(i["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(i["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(i["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(i["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(i["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(i["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function A(t){return Object(i["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(i["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(t){return Object(i["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(i["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function I(t){return Object(i["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function D(t){return Object(i["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function k(t){return Object(i["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function M(t){return Object(i["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"28c9":function(t,e,a){t.exports=a.p+"static/img/sort-done.0944d8df.png"},3517:function(t,e,a){"use strict";a("7861")},"45b6":function(t,e,a){},4946:function(t,e,a){"use strict";a("fe21")},"4e82":function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),s=a("59ed"),o=a("7b0b"),r=a("07fa"),l=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),p=a("a640"),h=a("3f7e"),m=a("99f4"),f=a("1212"),b=a("ea83"),v=[],y=n(v.sort),g=n(v.push),A=u((function(){v.sort(void 0)})),T=u((function(){v.sort(null)})),w=p("sort"),C=!u((function(){if(f)return f<70;if(!(h&&h>3)){if(m)return!0;if(b)return b<603;var t,e,a,i,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(i=0;i<47;i++)v.push({k:e+i,v:a})}for(v.sort((function(t,e){return e.v-t.v})),i=0;i<v.length;i++)e=v[i].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),I=A||!T||!w||!C,D=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};i({target:"Array",proto:!0,forced:I},{sort:function(t){void 0!==t&&s(t);var e=o(this);if(C)return void 0===t?y(e):y(e,t);var a,i,n=[],c=r(e);for(i=0;i<c;i++)i in e&&g(n,e[i]);d(n,D(t)),a=r(n),i=0;while(i<a)e[i]=n[i++];while(i<c)l(e,i++);return e}})},"4f37":function(t,e,a){},5162:function(t,e,a){t.exports=a.p+"static/img/sort-chart.c4a6390f.png"},"640b":function(t,e,a){"use strict";a("f221")},"68e4":function(t,e,a){},6963:function(t,e,a){"use strict";a("de58")},"6a0f":function(t,e,a){"use strict";a("4f37")},"775a":function(t,e){t.exports="data:image/png;base64,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"},"783e":function(t,e,a){},7861:function(t,e,a){},"7ac2":function(t,e,a){"use strict";a("bc19")},"8e30":function(t,e){t.exports="data:image/png;base64,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"},"919c":function(t,e,a){"use strict";a.d(e,"x",(function(){return n})),a.d(e,"z",(function(){return s})),a.d(e,"q",(function(){return o})),a.d(e,"y",(function(){return r})),a.d(e,"l",(function(){return l})),a.d(e,"o",(function(){return c})),a.d(e,"s",(function(){return u})),a.d(e,"p",(function(){return d})),a.d(e,"m",(function(){return p})),a.d(e,"n",(function(){return h})),a.d(e,"e",(function(){return m})),a.d(e,"b",(function(){return f})),a.d(e,"c",(function(){return b})),a.d(e,"d",(function(){return v})),a.d(e,"a",(function(){return y})),a.d(e,"f",(function(){return g})),a.d(e,"u",(function(){return A})),a.d(e,"t",(function(){return T})),a.d(e,"v",(function(){return w})),a.d(e,"r",(function(){return C})),a.d(e,"h",(function(){return I})),a.d(e,"i",(function(){return D})),a.d(e,"g",(function(){return k})),a.d(e,"k",(function(){return M})),a.d(e,"j",(function(){return j})),a.d(e,"w",(function(){return O}));a("e9f5"),a("7d54"),a("b64b"),a("d3b7"),a("159b");var i=a("b775");function n(t){return Object(i["a"])({url:"biCrmInstrument/queryBulletin",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(i["a"])({url:"biCrmInstrument/queryDataInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"crmInstrument/queryBulletinInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"crmInstrument/queryRecordCount",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"biCrmInstrument/queryPerformance",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(i["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(i["a"])({url:"biCrmInstrument/salesTrend",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return t.label=t.crmType,Object(i["a"])({url:"crmInstrument/queryRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(i["a"])({url:"biCrmInstrument/forgottenCustomerCount",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(i["a"])({url:"crmInstrument/forgottenCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(i["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(i["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(i["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(i["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(i["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(i["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function A(t){return Object(i["a"])({url:"crmInstrument/queryModelSort",method:"post",data:t})}function T(t){return Object(i["a"])({url:"crmInstrument/setModelSort",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(t){return Object(i["a"])({url:"crmInstrument/unContactCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(i["a"])({url:"crmInstrument/queryNoRecordCustomerList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function I(t){return Object(i["a"])({url:"crmActivity/exportRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"})}function D(t){var e=new FormData;return Object.keys(t).forEach((function(a){e.append(a,t[a])})),Object(i["a"])({url:"crmActivity/importRecordList",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function k(t){return Object(i["a"])({url:"crmActivity/downloadRecordExcel",method:"post",data:t,responseType:"blob"})}function M(t){return Object(i["a"])({url:"crmSearchDefault/save",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(i["a"])({url:"crmSearchDefault/queryByType",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(i["a"])({url:"crmBiSearch/searchReceivablesPlanPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},a041:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"workbench",staticClass:"crm-workbench"},[a("flexbox",{staticClass:"crm-workbench__hd"},[a("div",{staticClass:"title"},[t._v("CRM仪表盘"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"7"}})]),t._v(" "),a("el-button",{staticClass:"dropdown-btn",attrs:{icon:"el-icon-more"},on:{click:function(e){t.setSortShow=!0}}})],1),t._v(" "),a("div",{staticClass:"head"},[a("flexbox",{staticClass:"head__body"},[a("xr-radio-menu",{attrs:{options:t.rangeOptions,"is-default":t.dataTypeDefault,"user-checked-data":t.filterValue.users,"dep-checked-data":t.filterValue.strucs,width:250},on:{"update:isDefault":function(e){t.dataTypeDefault=e},select:t.radioMenuSelect},model:{value:t.filterDataType,callback:function(e){t.filterDataType=e},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.avatarData.realname,callback:function(e){t.$set(t.avatarData,"realname",e)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),a("wk-time-type-Select",{ref:"timeTypeSelect",staticStyle:{"margin-left":"8px"},attrs:{mode:"no-border",width:200,"is-default":t.timeDefault,"default-type":t.filterTime},on:{"update:isDefault":function(e){t.timeDefault=e},change:t.timeTypeChange}})],1)],1),t._v(" "),a("div",{staticClass:"crm-workbench__body"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"brief-box"},[t._m(0),t._v(" "),a("div",{staticClass:"brief"},t._l(t.briefList,(function(e,i){return a("brief-item",{key:i,class:{"is-current":0===i},attrs:{"is-hover":0===i,data:e,"rate-text":t.rateText},nativeOn:{click:function(a){t.reportClick(e)}}})})))]),t._v(" "),a("flexbox",{staticClass:"section",attrs:{align:"stretch"}},[a("draggable",{staticClass:"left",attrs:{group:{name:"sort"},options:{forceFallback:!1,handle:".filter-handle"}},on:{end:t.dragEnd},model:{value:t.sortLeft,callback:function(e){t.sortLeft=e},expression:"sortLeft"}},[t._l(t.sortLeft,(function(e,i){return[1===e.isHidden?a("div",{key:i,staticStyle:{display:"none"}}):a(e.component,{key:i,tag:"component",staticClass:"left-content component-item",attrs:{"filter-value":t.filterValue},on:{"chart-click":t.chartClick}})]}))],2),t._v(" "),a("draggable",{staticClass:"right",attrs:{group:{name:"sort"},options:{forceFallback:!1,handle:".filter-handle"}},on:{end:t.dragEnd},model:{value:t.sortRight,callback:function(e){t.sortRight=e},expression:"sortRight"}},[t._l(t.sortRight,(function(e,i){return[1===e.isHidden?a("div",{key:i,staticStyle:{display:"none"}}):a(e.component,{key:i,tag:"component",staticClass:"right-content component-item",attrs:{"filter-value":t.filterValue},on:{"chart-click":t.chartClick}})]}))],2)],1)],1),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,"record-request":t.reportData.recordRequest,params:t.reportData.params,"field-list":t.fieldReportList,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}}),t._v(" "),t.setSortShow?a("set-sort",{attrs:{visible:t.setSortShow},on:{"update:visible":function(e){t.setSortShow=e},save:t.getModelSort}}):t._e()],1)},n=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"brief-title"},[a("span",{staticClass:"icon wk wk-icon-briefing"}),t._v(" "),a("span",{staticClass:"text"},[t._v("销售简报"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"8"}})])])}],s=a("c14f"),o=a("1da1"),r=a("5530"),l=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("b680"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("159b"),a("919c")),c=a("f2ec"),u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"brief-item"},[a("flexbox",{staticClass:"brief-item__body"},[a("div",{staticClass:"info"},[a("div",{staticClass:"title"},[t._v("\n        "+t._s(t.data.label)),t.data.helpType?a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":t.data.helpType,"data-id":t.data.helpId},on:{click:function(t){t.stopPropagation()}}}):t._e()]),t._v(" "),a("div",{directives:[{name:"fit-text",rawName:"v-fit-text",value:{fontSize:24},expression:"{ fontSize: 24 }"}],staticClass:"number"},[t._v("\n        "+t._s(t.data.num)),a("span",{staticClass:"unit"},[t._v(t._s(t.data.unit))])]),t._v(" "),a("div",{staticClass:"des"},[""!==t.rateText?[t._v(t._s(t.rateText)),a("span",{staticClass:"rate",class:t.data.status},[t._v(t._s(t.data.rate)+"%"),a("span",{staticClass:"rate__icon",class:t.statusIcon})])]:t._e()],2)])]),t._v(" "),a("div",{staticClass:"brief-item__others"},[a("el-tooltip",{attrs:{effect:"dark",placement:"bottom",content:"近6个月数据变化情况"}},[a("div",{ref:"echartsLine",staticClass:"echarts-line"})])],1)],1)},d=[],p=a("313e"),h=a("7a1a"),m="FitText",f=function(){var t=this[m],e=t.el,a=t.option,i=a.fontSize;e.style.fontSize=i+"px";var n=e.scrollWidth;while(e.scrollWidth>e.clientWidth){if(n=e.scrollWidth,i-=.2,e.style.fontSize=i+"px",n<=e.clientWidth){e.style.overflowX="visible";break}if(i<=12){e.style.overflowX="hidden",e.style.overflowY="hidden",e.style.textOverflow="ellipsis";break}}},b={name:"FitText",bind:function(t,e,a){var i=e.value||{fontSize:14},n=Object(h["debounce"])(300,f.bind(t));t[m]={el:t,option:i,debouncedResize:n},window.addEventListener("resize",n)},inserted:function(t,e,a){var i=t[m].debouncedResize;i()},update:function(t,e,a){var i=t[m].debouncedResize;i()},unbind:function(t){var e=t[m].debouncedResize;window.removeEventListener("resize",e)}},v={name:"BriefItem",directives:{FitText:b},components:{},props:{data:Object,rateText:String,isHover:Boolean},data:function(){return{hovering:!0,chartObj:null,chartOption:{color:["#1890ff"],grid:{left:0,top:5,right:0,bottom:30},xAxis:{type:"category",data:["1","2","3","4","5","6"],axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!1}},yAxis:{type:"value",splitLine:{show:!1}},series:[{data:[],symbol:"none",smooth:!0,type:"line",areaStyle:{color:new p["a"].LinearGradient(0,0,0,1,[{offset:0,color:"rgba(0, 82, 204, 0.2)"},{offset:1,color:"rgba(0, 82, 204, 0)"}])}}]}}},computed:{statusIcon:function(){return this.data.status?"top"===this.data.status?"wk wk-icon-top2":"wk wk-icon-bottom":""}},watch:{"data.chartData":{handler:function(){var t=this;this.data.chartData&&(this.chartOption.series[0].data=this.data.chartData,this.$nextTick((function(){t.chartObj.setOption(t.chartOption,!0)})))},immediate:!0,deep:!0}},created:function(){},mounted:function(){var t=this;window.onresize=function(){t.chartObj&&t.chartObj.resize()},this.initChart()},beforeDestroy:function(){window.onresize=null},methods:{initChart:function(){this.chartObj=p["b"](this.$refs.echartsLine),this.chartObj.setOption(this.chartOption,!0),this.isHover?this.mouseEnter():this.mouseLeave()},mouseEnter:function(){this.hovering=!0,this.chartOption.color=["#DEEBFF"],this.chartOption.series[0].areaStyle.color=new p["a"].LinearGradient(0,0,0,1,[{offset:0,color:"rgba(222,235,255, 0.5)"},{offset:1,color:"rgba(222,235,255, 0)"}]),this.chartObj.setOption(this.chartOption,!0)},mouseLeave:function(){this.hovering=!1,this.chartOption.color=["#1890ff"],this.chartOption.series[0].areaStyle.color=new p["a"].LinearGradient(0,0,0,1,[{offset:0,color:"rgba(0, 82, 204, 0.2)"},{offset:1,color:"rgba(0, 82, 204, 0)"}]),this.chartObj.setOption(this.chartOption,!0)}}},y=v,g=(a("4946"),a("2877")),A=Object(g["a"])(y,u,d,!1,null,"ac5e7c68",null),T=A.exports,w=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("statistics-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{icon:"wk wk-target-solid"},on:{"unfold-change":t.handleUpdateUnfold}},[i("template",{slot:"title-left"},[t._v(t._s(t.typeName)+"金额目标及完成情况"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",staticStyle:{"margin-left":"4px"},attrs:{"data-type":"4","data-id":"14"}})]),t._v(" "),i("el-select",{attrs:{slot:"filter-left",mode:"no-border"},on:{change:t.handleCommand},slot:"filter-left",model:{value:t.optionValue,callback:function(e){t.optionValue=e},expression:"optionValue"}},t._l(t.options,(function(t){return i("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}))),t._v(" "),i("wk-toggle-button",{attrs:{slot:"title-right"},on:{change:t.getData},slot:"title-right",model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},t._l([{name:"折线图",value:"line"},{name:"柱状图",value:"bar"}],(function(t){return i("wk-toggle-item",{key:t.value,attrs:{label:t.name,value:t.value}})}))),t._v(" "),t.hasSetAuth?i("el-button",{staticStyle:{padding:"0"},attrs:{slot:"filter-right",type:"link"},on:{click:t.enterSetPage},slot:"filter-right"},[t._v("设置目标")]):t._e(),t._v(" "),i("el-empty",{directives:[{name:"show",rawName:"v-show",value:t.errorShow,expression:"errorShow"}],staticStyle:{"padding-top":"75px"},attrs:{image:a("f86a"),description:"不能同事选择部门和员工的目标及完成情况"}}),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.errorShow,expression:"!errorShow"}],attrs:{id:"sale-statistics"}})],2)},C=[],I=(a("caad"),a("2532"),a("8679")),D=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"statistics-card card",class:[{"is-close":!t.isUnfold}]},[a("flexbox",{staticClass:"card-title"},[t.move?a("span",{staticClass:"filter-handle"},[t._v("⋮⋮")]):t._e(),t._v(" "),a("div",{staticClass:"card-title-center text-one-ellipsis"},[t._t("title-left")],2),t._v(" "),a("div",{staticClass:"card-title-right"},[t._t("title-right"),t._v(" "),a("i",{class:["wk",t.isUnfold?"wk-shrink":"wk-full"],on:{click:t.handleUpdateUnfold}})],2)]),t._v(" "),t.isUnfold?[a("flexbox",{staticClass:"filter-bar"},[t.workbench?a("flexbox",{staticClass:"left"},[a("span",{staticClass:"filter-tag"},[t._v(t._s(t.workbench.avatarData.realname))]),t._v(" "),a("span",{staticClass:"filter-tag"},[t._v(t._s(t.fixedToday?"今天":t.workbench.$refs.timeTypeSelect.typeShowValue))]),t._v(" "),t._t("filter-left")],2):t._e(),t._v(" "),a("div",{staticClass:"right"},[t._t("filter-right")],2)],1),t._v(" "),t._t("default")]:t._e()],2)},k=[],M={name:"StatisticsCard",components:{},props:{icon:String,move:{type:Boolean,default:!0},fixedToday:{type:String,default:""}},inject:{workbench:{default:""}},data:function(){return{isUnfold:!0}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{handleUpdateUnfold:function(){this.isUnfold=!this.isUnfold,this.$emit("unfold-change",this.isUnfold)}}},j=M,O=(a("e82f"),Object(g["a"])(j,D,k,!1,null,"3cb8ed3e",null)),S=O.exports,x={components:{StatisticsCard:S},props:{filterValue:{type:Object,default:function(){}}},data:function(){return{echatsOptions:{color:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"]}}},watch:{filterValue:{handler:function(){this.getData&&this.getData()},deep:!0}},mounted:function(){var t=this;this.$bus.on("window-resize",(function(){t.chartObj&&t.chartObj.resize()})),this.getData&&this.getData()},beforeDestroy:function(){this.$bus.off("window-resize")},methods:{getBaseParams:function(){var t={};return"custom"!==this.filterValue.dataType?t.dataType=this.filterValue.dataType:(t.dataType=0,t.deptList=(this.filterValue.strucs||[]).map((function(t){return t.deptId})),t.userList=(this.filterValue.users||[]).map((function(t){return t.userId}))),this.filterValue.timeLine.type&&("custom"===this.filterValue.timeLine.type?(t.dateFilter="custom",t.startDate=this.filterValue.timeLine.startTime.replace(/\./g,"-"),t.endDate=this.filterValue.timeLine.endTime.replace(/\./g,"-")):t.dateFilter=this.filterValue.timeLine.value||""),t}}},L=a("2f62"),G=a("a347"),N=a.n(G),R=a("c1df"),Z=a.n(R),Y=a("6bfe"),W={name:"SaleStatistics",components:{WkToggleButton:I["a"],WkToggleItem:I["b"]},mixins:[x],data:function(){return{typeValue:"line",sortList:[{name:"天",list:["today","yesterday","tomorrow"]},{name:"周",list:["week","lastWeek","nextWeek"]},{name:"月",list:["month","lastMonth","nextMonth"]},{name:"季度",list:["quarter","lastQuarter","nextQuarter"]},{name:"年度",list:["year","lastYear","nextYear"]}],options:[{name:"合同金额",value:1},{name:"回款金额",value:2}],optionValue:1,chartOption:{tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{top:"16px",left:"8px",right:"8px",bottom:"46px",containLabel:!0},legend:{bottom:"10px",data:["当月目标金额","实际完成金额"],textStyle:{color:N.a.colorBlack,fontWeight:N.a.axisLabelFontWeight}},xAxis:[{type:"category",data:[],axisTick:{lineStyle:{width:0}},axisLabel:{fontWeight:N.a.axisLabelFontWeight},axisLine:{lineStyle:{}},splitLine:{show:!1}}],yAxis:[{type:"value",axisTick:{show:!1},axisLine:{lineStyle:{width:0}},axisLabel:{fontWeight:N.a.axisLabelFontWeight},splitLine:{lineStyle:{}}}],series:[]},chartObj:null,loading:!1,requestParams:null}},computed:Object(r["a"])(Object(r["a"])({},Object(L["b"])(["manage"])),{},{hasSetAuth:function(){return this.manage&&this.manage.crm&&this.manage.crm.achievement},typeName:function(){return{1:"合同",2:"回款"}[this.optionValue]},errorShow:function(){return!!this.requestParams&&(Object(Y["a"])(this.requestParams.deptList)&&this.requestParams.deptList.length>0&&Object(Y["a"])(this.requestParams.userList)&&this.requestParams.userList.length>0)}}),mounted:function(){this.initChart()},methods:{initChart:function(){this.chartObj=p["b"](document.getElementById("sale-statistics")),this.chartObj.setOption(this.chartOption,!0)},handleCommand:function(){this.getData()},handleUpdateUnfold:function(t){var e=this;t&&this.$nextTick((function(){e.initChart()}))},getData:function(){var t=this;this.loading=!0;var e=Object(r["a"])({type:this.optionValue},this.getBaseParams());this.requestParams=e,Object(l["s"])(e).then((function(a){for(var i=e.dateFilter,n=a.data||[],s=[],o=[],r=[],l=!0,c=t.getFilterDateMoment(i),u=0;u<n.length;u++){var d=n[u];d.achievement>0&&(l=!1);var p=t.getDataIsFilterTime(i,d.type,c);s.push({value:d.achievement,symbolSize:p?14:8,itemStyle:{color:"#42526E",shadowColor:p?"rgba(66,82,110, 0.5)":"transparent",shadowBlur:p?10:0,shadowOffsetY:3,borderColor:"white",borderWidth:p&&"line"===t.typeValue?2:0}}),o.push({value:d.money,symbolSize:p?14:8,itemStyle:{color:p?"#36B37E":"#1890ff",shadowColor:p?"rgba(54,179,126, 0.5)":"transparent",shadowBlur:p?10:0,shadowOffsetY:3,borderColor:"white",borderWidth:p&&"line"===t.typeValue?2:0}}),r.push(d.type)}t.chartOption.xAxis[0].data=r.map((function(t){return t.replace(" ","\n")}));var h="",m={};["today","yesterday","tomorrow"].includes(i)?(h="平均每天目标金额",m={itemStyle:{color:"#97A0AF"},lineStyle:{color:"#97A0AF",type:"dashed"}}):["week","lastWeek","nextWeek"].includes(i)?(h="平均每周目标金额",m={itemStyle:{color:"#97A0AF"},lineStyle:{color:"#97A0AF",type:"dashed"}}):h=["quarter","lastQuarter","nextQuarter"].includes(i)?"季度目标金额":["year","lastYear","nextYear"].includes(i)?"年度目标金额":"当月目标金额",t.chartOption.series=t.getChartSeries(s,o,h,m,l),t.chartOption.legend.data[0]=h,t.chartOption.legend.itemWidth="line"===t.typeValue?25:14,t.chartOption.color=["#42526E","#1890ff"],t.chartObj.setOption(t.chartOption,!0),t.loading=!1})).catch((function(){t.loading=!1}))},getDataIsFilterTime:function(t,e,a){if(["today","yesterday","tomorrow"].includes(t))return e===a;if(["week","lastWeek","nextWeek"].includes(t))return e.includes(a);if(["month","lastMonth","nextMonth"].includes(t)){var i=e.split("-")[1];return i===a}if(["quarter","lastQuarter","nextQuarter"].includes(t)){var n=a.format("YYYY")+a.quarters();return e.replace("年","").includes(n)}return["year","lastYear","nextYear"].includes(t)?e===a:void 0},getFilterDateMoment:function(t){return["today","yesterday","tomorrow"].includes(t)?{today:Z()().format("MM-DD"),yesterday:Z()().subtract(1,"days").format("MM-DD"),tomorrow:Z()().add(1,"days").format("MM-DD")}[t]:["week","lastWeek","nextWeek"].includes(t)?(Z()().startOf("week"),{week:Z()().startOf("week").format("MM-DD"),lastWeek:Z()().subtract(1,"weeks").startOf("week").format("MM-DD"),nextWeek:Z()().add(1,"weeks").startOf("week").format("MM-DD")}[t]):["month","lastMonth","nextMonth"].includes(t)?{month:Z()().format("MM"),lastMonth:Z()().subtract(1,"months").format("MM"),nextMonth:Z()().add(1,"months").format("MM")}[t]:["quarter","lastQuarter","nextQuarter"].includes(t)?{quarter:Z()(),lastQuarter:Z()().subtract(1,"quarters"),nextQuarter:Z()().add(1,"quarters")}[t]:["year","lastYear","nextYear"].includes(t)?{year:Z()().format("YYYY"),lastYear:Z()().subtract(1,"years").format("YYYY"),nextYear:Z()().add(1,"years").format("YYYY")}[t]:void 0},getChartSeries:function(t,e,a,i,n){var s=[];return n||s.push(Object(r["a"])({name:a,type:this.typeValue,barMaxWidth:15,symbol:"circle",symbolSize:8,stack:"one",smooth:!0,data:t},i)),s.push({name:"实际完成金额",type:this.typeValue,barMaxWidth:15,symbol:"circle",symbolSize:8,stack:"two",smooth:!0,data:e,itemStyle:{color:"#1890ff"},lineStyle:{color:"#1890ff"}}),s},enterSetPage:function(){this.$router.push({name:"crmBizGoals"})}}},B=W,F=(a("f705"),Object(g["a"])(B,w,C,!1,null,"352830ce",null)),V=F.exports,z=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("statistics-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"data-statistics card",attrs:{icon:"wk wk-icon-summary"}},[a("template",{slot:"title-left"},[t._v("数据汇总")]),t._v(" "),t.data?a("div",{staticClass:"content"},[t._l(t.sections,(function(e,i){return a("div",{key:i,staticClass:"section"},[a("div",{staticClass:"section__hd"},[a("el-divider",{attrs:{"content-position":"left"}},[t._v(t._s(e.label))])],1),t._v(" "),a("div",{staticClass:"section__bd"},[a("flexbox",t._l(e.list,(function(i,n){return a("div",{key:n,staticClass:"section-item-wrap"},[a("div",{staticClass:"section-item",on:{click:function(a){t.showDetailList(e.crmType,i)}}},[a("div",{staticClass:"title"},[t._v("\n                "+t._s(i.label)+"\n                "),i.tagType?a("el-tag",{attrs:{"disable-transitions":"",type:i.tagType}},[t._v("\n                  "+t._s(i.tagContent)+"\n                ")]):t._e(),t._v(" "),i.helpType?a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":i.helpType,"data-id":i.helpId},on:{click:function(t){t.stopPropagation()}}}):t._e()],1),t._v(" "),a("div",{directives:[{name:"fit-text",rawName:"v-fit-text",value:{fontSize:20},expression:"{ fontSize: 20 }"}],staticClass:"value"},[t._v("\n                "+t._s(t.data[i.field])+"\n                "),a("span",{staticClass:"value-unit"},[t._v("\n                  "+t._s(i.unit)+"\n                ")])])])])})))],1)])})),t._v(" "),t._l(t.otherSections,(function(e,i){return a("flexbox",{key:"s"+i},t._l(e,(function(e,i){return a("div",{key:i,staticClass:"section two-columns"},[a("div",{staticClass:"section__hd"},[a("el-divider",{attrs:{"content-position":"left"}},[t._v(t._s(e.label))])],1),t._v(" "),a("div",{staticClass:"section__bd"},[a("flexbox",t._l(e.list,(function(i,n){return a("div",{key:n,staticClass:"section-item-wrap"},[a("div",{staticClass:"section-item",on:{click:function(a){t.showDetailList(e.crmType,i)}}},[a("div",{staticClass:"title"},[t._v("\n                  "+t._s(i.label)),i.tagType?a("el-tag",{attrs:{"disable-transitions":"",type:i.tagType}},[t._v(t._s(i.tagContent))]):t._e(),i.helpType?a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":i.helpType,"data-id":i.helpId},on:{click:function(t){t.stopPropagation()}}}):t._e()],1),t._v(" "),a("div",{directives:[{name:"fit-text",rawName:"v-fit-text",value:{fontSize:20},expression:"{ fontSize: 20 }"}],staticClass:"value"},[t._v(t._s(t.data[i.field])),a("span",{staticClass:"value-unit"},[t._v("\n                  "+t._s(i.unit)+"\n                ")])])])])})))],1)])})))}))],2):t._e(),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],2)},E=[],U=a("ec3a"),Q=a("77dc"),J=a("1112"),P=a("bf0a"),X=a("2602"),_=a("45af"),H=a("ef89"),q=a("2a64"),K=a("8c73"),$={name:"DataStatistics",components:{ReportList:q["a"]},directives:{FitText:b},filters:{},mixins:[x],data:function(){return{data:null,sections:[{label:"客户汇总",crmType:"customer",list:[{label:"新增客户",field:"allCustomer",unit:"个",query:["createTime"]},{label:"转成交客户",field:"dealCustomer",unit:"个",helpType:"4",helpId:"15",query:["createTime","dealStatus"]},{label:"放入公海客户",field:"putInPoolNum",unit:"个",helpType:"4",helpId:"16",query:["createTime"]},{label:"公海池领取",field:"receiveNum",unit:"个",query:["receiveTime","isReceive"]}]},{label:"项目汇总",crmType:"business",list:[{label:"新增项目",field:"allBusiness",unit:"个",query:["createTime"]},{label:"赢单项目",field:"endBusiness",unit:"个",query:["createTime","isEnd"]},{label:"输单项目",field:"loseBusiness",unit:"个",query:["createTime","isEnd"]},{label:"项目总金额",field:"businessMoney",unit:"元",query:["createTime"]}]},{label:"合同汇总",crmType:"contract",list:[{label:"签约合同",field:"allContract",unit:"个",query:["orderDate","checkStatus"]},{label:"即将到期",field:"expireContract",unit:"个",tagType:"warning",tagContent:"警告",helpType:"4",helpId:"17",query:["checkStatus"]},{label:"已到期",field:"endContract",unit:"个",tagType:"danger",tagContent:"已到期",query:["endTime","checkStatus"]},{label:"合同金额",field:"contractMoney",unit:"元",query:["orderDate","checkStatus"]}]}],otherSections:[[{label:"跟进汇总",crmType:"customer",list:[{label:"跟进客户数",field:"activityNum",unit:"个",query:["createTime"]},{label:"新增未跟进客户数",field:"activityRealNum",unit:"个",query:["createTime"]}]},{label:"回款金额",crmType:"receivables",list:[{label:"回款",field:"receivablesMoney",unit:"元",helpType:"4",helpId:"18",query:["returnTime","checkStatus"]},{label:"预计回款",field:"planMoney",unit:"元",helpType:"4",helpId:"19",query:["returnDate"]}]}]],loading:!1,reportListShow:!1,fieldReportList:null,reportData:{title:"",placeholder:"",request:null,params:null}}},computed:Object(r["a"])({},Object(L["b"])(["userInfo"])),methods:{getData:function(){var t=this;this.loading=!0,Object(l["z"])(this.getBaseParams()).then((function(e){t.loading=!1;var a=e.data||{};a.noActivityNum=a.allCustomer-a.activityRealNum,a.businessMoney=Object(K["h"])(a.businessMoney),a.contractMoney=Object(K["h"])(a.contractMoney),a.receivablesMoney=Object(K["h"])(a.receivablesMoney),a.planMoney=Object(K["h"])(a.planMoney),t.data=a})).catch((function(){t.loading=!1}))},getDateFilter:function(){var t=this.filterValue.timeLine,e=t.type,a=t.value,i=t.startTime,n=t.endTime,s={};return s.dateFilter="default"===e?a:e,"custom"===e&&(s.startDate=i.replace(/\./g,"-"),s.endDate=n.replace(/\./g,"-")),s},showDetailList:function(t,e){if("putInPoolNum"!==e.field){var a={receivables:"planMoney"===e.field?8:7,customer:2,contract:6,business:5}[t];this.reportData.title=e.label,this.reportData.type=a,this.reportData.crmType="planMoney"===e.field?"receivablesPlan":t;var i=this.filterValue.timeLine,n=i.type,s=i.value,o=i.startTime,u=i.endTime,d={search:"",type:a,searchList:[]},p=null;"custom"!==this.filterValue.dataType&&(p=d.dataType=this.filterValue.dataType);var h="custom"===n?["".concat(o),"".concat(u)]:[s],m=e.query,f=function(t){return{formType:["orderDate","returnTime","returnDate","endTime"].includes(t)?"date":"datetime",name:t,type:14,values:h}};if(m.includes("endTime")&&d.searchList.push(f("endTime")),m.includes("createTime")&&d.searchList.push(f("createTime")),m.includes("receiveTime")&&d.searchList.push(f("receiveTime")),m.includes("returnTime")&&d.searchList.push(f("returnTime")),m.includes("returnDate")&&d.searchList.push(f("returnDate")),m.includes("orderDate")&&d.searchList.push(f("orderDate")),m.includes("lastTime")&&d.searchList.push(f("lastTime")),m.includes("dealStatus")&&d.searchList.push({formType:"dealStatus",name:"dealStatus",type:1,values:[1]}),m.includes("checkStatus")){var b=["receivablesMoney","planMoney","contractMoney","allContract","endContract"].includes(e.field)?[1,10]:[1];d.searchList.push({formType:"checkStatus",name:"checkStatus",type:1,values:b})}m.includes("isEnd")&&d.searchList.push({formType:"text",name:"isEnd",type:1,values:["endBusiness"===e.field?1:2]}),m.includes("isReceive")&&d.searchList.push({formType:"text",name:"isReceive",type:1,values:[2]});var v=this.getDateFilter();"activityRealNum"===e.field?(d.dateFilter=this.filterValue.timeLine.value,this.reportData.request=l["r"]):"activityNum"===e.field?(d=Object(r["a"])({search:"",type:2,category:1,dataType:p},v),this.reportData.request=X["e"]):"putInPoolNum"===e.field?(this.reportData.request=X["w"],d.type=9):["expireContract","endContract"].includes(e.field)?(this.reportData.request=_["b"],"expireContract"==e.field&&(d=Object(r["a"])({search:"",type:2,dataType:p,searchList:[{formType:"checkStatus",name:"checkStatus",type:1,values:[1,10]}]},v)),d.data=d.searchList,delete d.searchList,d.type="expireContract"===e.field?1:2):["endBusiness","loseBusiness"].includes(e.field)?(d=Object(r["a"])({search:"",type:"endBusiness"===e.field?1:2},v),p&&(d.dataType=p),this.reportData.request=c["d"]):"planMoney"===e.field?this.reportData.request=l["w"]:"allBusiness"===e.field?this.reportData.request=c["a"]:["allContract","contractMoney"].includes(e.field)?this.reportData.request=H["d"]:["allCustomer","dealCustomer","receiveNum"].includes(e.field)?this.reportData.request=X["d"]:this.reportData.request={receivables:P["h"],customer:U["p"],contract:Q["g"],business:J["f"]}[t],(this.filterValue.users.length||this.filterValue.strucs.length)&&(d.userList=this.filterValue.users.map((function(t){return t.userId})),d.deptList=this.filterValue.strucs.map((function(t){return t.deptId}))),"businessMoney"==e.field&&(this.reportData.request=c["a"]),this.reportData=Object(r["a"])(Object(r["a"])({},this.reportData),{},{params:d}),this.reportListShow=!0}}}},tt=$,et=(a("640b"),Object(g["a"])(tt,z,E,!1,null,"b8baca7e",null)),at=et.exports,it=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("statistics-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{icon:"wk wk-funnel-solid"},on:{"unfold-change":t.handleUpdateUnfold}},[a("template",{slot:"title-left"},[t._v("销售漏斗"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"20"}})]),t._v(" "),a("template",{slot:"filter-left"},[a("el-select",{attrs:{mode:"no-border"},on:{change:t.handleCommand},model:{value:t.flowId,callback:function(e){t.flowId=e},expression:"flowId"}},t._l(t.flowList,(function(t){return a("el-option",{key:t.flowId,attrs:{label:t.flowName,value:t.flowId}})})))],1),t._v(" "),a("wk-toggle-button",{attrs:{slot:"title-right"},on:{change:t.dataTypeChange},slot:"title-right",model:{value:t.dataType,callback:function(e){t.dataType=e},expression:"dataType"}},t._l(t.dataOptions,(function(t){return a("wk-toggle-item",{key:t.value,attrs:{label:t.name,value:t.value}})}))),t._v(" "),a("div",{attrs:{id:"sales-funnel"}})],2)},nt=[],st={name:"SalesFunnel",components:{WkToggleButton:I["a"],WkToggleItem:I["b"]},mixins:[x],data:function(){return{chartOption:{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){var e=t[0]||{},a=e.data||{};return"".concat(e.name,'<br/> 项目个数：<span style="font-weight: 500;">').concat(a.count,'</span><br/> 项目金额：<span style="font-weight: 500;">').concat(a.money," 元</span>")}},color:["#1890ff "],legend:{data:[],formatter:function(t){return t.split("(")[0]},textStyle:{color:N.a.colorBlack,fontWeight:N.a.axisLabelFontWeight}},grid:{top:"20px",left:"10px",right:"30px",bottom:"20px",containLabel:!0},xAxis:{max:"dataMax"},yAxis:{type:"category",axisTick:{show:!1},axisLine:{show:!0},axisLabel:{fontWeight:N.a.axisLabelFontWeight},splitLine:{lineStyle:{}},data:[]},series:[{type:"bar",label:{color:N.a.colorBlack,fontWeight:N.a.axisLabelFontWeight},barMaxWidth:20,data:[]}]},chartObj:null,loading:!1,dataList:[],flowList:[],flowId:null,flowName:"",moneyList:[],countList:[],dataType:1,dataOptions:[{name:"金额",value:1},{name:"数量",value:2}]}},computed:{echartList:function(){return 1===this.dataType?this.moneyList:this.countList}},mounted:function(){this.initChart(),this.getBusinessStatusList()},methods:{initChart:function(){var t=this;this.chartObj=p["b"](document.getElementById("sales-funnel")),this.chartObj.setOption(this.chartOption,!0),this.chartObj.on("click",(function(e){var a=t.dataList[e.dataIndex]||{};a.flowId=t.flowId,a.flowName=t.flowName,t.$emit("chart-click",e,a)}))},handleUpdateUnfold:function(t){var e=this;t&&this.$nextTick((function(){e.initChart()}))},dataTypeChange:function(){this.chartOption.series[0].data=this.echartList,this.chartObj.setOption(this.chartOption,!0)},getData:function(){var t=this;this.flowId&&(this.loading=!0,Object(l["o"])(Object(r["a"])(Object(r["a"])({},this.getBaseParams()),{},{typeId:this.flowId})).then((function(e){t.loading=!1,t.dataList=(e.data||[]).reverse();for(var a=[],i=[],n=[],s=0;s<t.dataList.length;s++){var o=t.dataList[s];a.push(o.settingName),i.push({name:o.settingName,value:o.businessMoney||0,money:o.businessMoney||0,count:o.businessNum||0}),n.push({name:o.settingName,value:o.businessNum||0,money:o.businessMoney||0,count:o.businessNum||0})}t.moneyList=i,t.countList=n,t.chartOption.series[0].data=t.echartList,t.chartOption.yAxis.data=a,t.chartOption.legend.data=a,t.chartObj.setOption(t.chartOption,!0)})).catch((function(){t.loading=!1})))},getBusinessStatusList:function(){var t=this;this.loading=!0,Object(J["r"])().then((function(e){var a=e.data||[];t.flowList=a,t.flowList.length>0?(t.flowId=t.flowList[0].flowId,t.flowName=t.flowList[0].flowName,t.handleCommand()):t.loading=!1})).catch((function(){t.loading=!1}))},handleCommand:function(){var t=this;this.flowId&&(this.flowList.map((function(e){e.flowId==t.flowId&&(t.flowName=e.flowName)})),this.getData())}}},ot=st,rt=(a("100d"),Object(g["a"])(ot,it,nt,!1,null,"df0a3344",null)),lt=rt.exports,ct=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("statistics-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"performance-chart",attrs:{icon:"wk wk-icon-Completion-rate"},on:{"unfold-change":t.handleUpdateUnfold}},[a("template",{slot:"title-left"},[t._v("业绩指标完成率"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"21"}})]),t._v(" "),a("template",{slot:"filter-left"},[a("el-select",{attrs:{mode:"no-border"},on:{change:t.handleCommand},model:{value:t.optionValue,callback:function(e){t.optionValue=e},expression:"optionValue"}},t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})))],1),t._v(" "),a("div",{attrs:{id:"performance-chart"}}),t._v(" "),a("flexbox",{staticClass:"info-box",attrs:{justify:"center"}},[a("div",{staticClass:"info-item",attrs:{direction:"column"}},[t.noAchievement?a("span",{staticClass:"label mark"},[t._v("暂无目标"),t.noAchievement&&t.$auth("manage.crm.achievement")?a("el-button",{staticStyle:{padding:"0","margin-left":"12px"},attrs:{type:"link"},on:{click:t.enterSet}},[t._v("设置目标")]):t._e()],1):[a("span",{staticClass:"label mark"},[t._v("目标金额")]),t._v(" "),a("span",{staticClass:"value"},[t._v(t._s(t.data.achievementMoneys))])]],2),t._v(" "),1===t.optionValue?a("div",{staticClass:"info-item",attrs:{direction:"column"}},[a("span",{staticClass:"label mark is-light"},[t._v("完成金额")]),t._v(" "),a("span",{staticClass:"value"},[t._v(t._s(t._f("separator")(t.data.money)))])]):t._e(),t._v(" "),2===t.optionValue?a("div",{staticClass:"info-item",attrs:{direction:"column"}},[a("span",{staticClass:"label mark is-light"},[t._v("完成金额")]),t._v(" "),a("span",{staticClass:"value"},[t._v(t._s(t._f("separator")(t.data.money)))])]):t._e()])],2)},ut=[],dt=(a("7db0"),a("f665"),{name:"PerformanceChart",mixins:[x],data:function(){return{chartOption:{color:["#1890ff"],tooltip:{textStyle:{fontWeight:500,color:"#172B4D"},formatter:function(t){return"".concat(t.seriesName,"<br/>").concat(t.name," : ").concat(t.value,"%")}},series:[{type:"gauge",name:"",radius:"85%",title:{fontSize:14,color:"#42526e",offsetCenter:[0,"60%"]},detail:{formatter:"{value}%",fontSize:20,color:"#20253A"},splitNumber:4,data:[{value:0,name:"完成率",detail:{formatter:function(t){return-1===t?"":"".concat(t,"%")}}}],progress:{roundCap:!0,show:!0,width:6},axisLine:{roundCap:!0,lineStyle:{color:[[1,"#A0A5BA"]],width:4}},axisTick:{show:!1},splitLine:{distance:4,length:8,lineStyle:{width:2}},axisLabel:{color:"#172B4D",fontWeight:500,fontSize:14},pointer:{width:6,length:"34%",itemStyle:{color:"#1890ff"}},anchor:{show:!0,showAbove:!0,size:18,itemStyle:{borderColor:"#1890ff",borderWidth:5}}}]},loading:!1,data:{achievementMoneys:0,contractMoneys:0,receivablesMoneys:0},options:[{name:"回款金额",value:2},{name:"合同金额",value:1}],optionValue:2}},computed:{noAchievement:function(){return!!this.data&&0===this.data.achievementMoneys}},mounted:function(){this.initChart()},methods:{initChart:function(){this.chartObj=p["b"](document.getElementById("performance-chart")),this.chartObj.setOption(this.chartOption,!0)},handleUpdateUnfold:function(t){var e=this;t&&this.$nextTick((function(){e.initChart()}))},getData:function(){var t=this;this.loading=!0,Object(l["l"])(Object(r["a"])(Object(r["a"])({},this.getBaseParams()),{},{type:this.optionValue})).then((function(e){if(t.loading=!1,e.data){var a=e.data||{};t.data=a;var i=t.chartOption.series[0].data[0];i.name="完成率",i.value=t.noAchievement?0:a.proportion||"0";var n=t.options.find((function(e){return e.value===t.optionValue}))||{};t.chartOption.series[0].name=n.name||"",t.chartOption.series[0].splitLine.lineStyle.color=t.noAchievement?"#A0A5BA":"#1890ff",t.chartObj.setOption(t.chartOption,!0)}})).catch((function(){t.loading=!1}))},handleCommand:function(){this.getData()},enterSet:function(){this.$router.push({name:"crmBizGoals"})}}}),pt=dt,ht=(a("3517"),Object(g["a"])(pt,ct,ut,!1,null,"f69ad2e8",null)),mt=ht.exports,ft=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("statistics-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{icon:"wk wk-target-solid"}},[a("template",{slot:"title-left"},[t._v("排行榜"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"22"}})]),t._v(" "),a("template",{slot:"filter-left"},[a("el-select",{attrs:{mode:"no-border"},on:{change:t.handleCommand},model:{value:t.optionValue,callback:function(e){t.optionValue=e},expression:"optionValue"}},t._l(t.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})))],1),t._v(" "),a("div",{staticClass:"content"},[a("el-table",{staticClass:"table-list",staticStyle:{width:"100%"},attrs:{size:"small",data:t.data,height:335,"cell-class-name":t.cellClassName,"row-class-name":t.rowClassName,stripe:!1}},[a("el-table-column",{attrs:{resizable:!1,prop:"index",label:"排名"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.sort))])]}}])}),t._v(" "),a("el-table-column",{attrs:{resizable:!1,prop:"name",label:"姓名"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("xr-avatar",{staticClass:"user-img",attrs:{name:e.row.realname,size:30}}),t._v(" "),a("span",{staticClass:"user-name"},[t._v(t._s(e.row.realname))])]}}])}),t._v(" "),a("el-table-column",{attrs:{resizable:!1,label:t.optionName+"（"+t.optionInfo.unit+"）",prop:"value"}}),t._v(" "),t.rateShow?a("el-table-column",{attrs:{resizable:!1,label:"目标完成率（%）",prop:"rate"}}):t._e()],1),t._v(" "),a("div",{staticClass:"my-ranking"},[a("div",{staticClass:"row value"},[a("span",[t._v(t._s(t.myData.sort))])]),t._v(" "),a("div",{staticClass:"row"},[a("xr-avatar",{staticClass:"user-img",attrs:{name:t.myData.realname,size:30}}),t._v(" "),a("span",{staticClass:"user-name"},[t._v(t._s(t.myData.realname))])],1),t._v(" "),a("div",{staticClass:"row value"},[t._v(t._s(t.myData.value))]),t._v(" "),t.rateShow?a("div",{staticClass:"row value"},[t._v(t._s(t.myData.rate))]):t._e()])],1)],2)},bt=[],vt=(a("fb6a"),a("4e82"),{name:"RankingStatistics",filters:{filterRankImage:function(t){return[a("775a"),a("8e30"),a("1f66")][t-1]}},mixins:[x],data:function(){return{data:[],myData:Object(r["a"])(Object(r["a"])({},this.userInfo),{},{value:"--",sort:"--"}),loading:!1,options:[{name:"回款金额",value:2,requst:l["e"]},{name:"合同金额",value:1,requst:l["b"]},{name:"合同数",value:3,requst:l["c"]},{name:"新增客户数",value:4,requst:l["d"]},{name:"新增联系人",value:5,requst:l["a"]},{name:"新增跟进记录数",value:8,requst:l["f"]}],optionValue:2}},computed:Object(r["a"])(Object(r["a"])({},Object(L["b"])(["userInfo","bi"])),{},{optionName:function(){var t=this,e=this.options.find((function(e){return e.value===t.optionValue}));return e?e.name:""},optionInfo:function(){return 1==this.optionValue||2==this.optionValue||7==this.optionValue?{key:"money",unit:"元"}:8==this.optionValue?{key:"count",unit:"条"}:{key:"count",unit:"个"}},rateShow:function(){return 1==this.optionValue||2==this.optionValue}}),created:function(){},methods:{getData:function(){var t=this;if(this.$auth("bi.ranking.read")){this.loading=!0;var e=this.options.find((function(e){return e.value===t.optionValue})).requst;e(this.getBaseParams()).then((function(e){t.loading=!1;var a=e.data||[],i=a.slice(0,10),n=a.find((function(e){return e.userId===t.userInfo.userId}));t.data=i.map((function(e,a){var i=a+1;return e.sort=i<=9?"0".concat(i):i,e.value=e[t.optionInfo.key],e})),t.myData=n?Object(r["a"])(Object(r["a"])({},n),{},{value:n[t.optionInfo.key]}):Object(r["a"])(Object(r["a"])({},t.userInfo),{},{sort:"--",value:"--",rate:"--"})})).catch((function(){t.loading=!1}))}},handleCommand:function(t){this.getData()},cellClassName:function(t){var e=t.row,a=(t.column,t.rowIndex,t.columnIndex);return 0===a&&e.sort<=3?"is-top":""},rowClassName:function(t){t.row;var e=t.rowIndex;return e<3?"linear-row":""}}}),yt=vt,gt=(a("6963"),Object(g["a"])(yt,ft,bt,!1,null,"754821ec",null)),At=gt.exports,Tt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("statistics-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"fixed-today":"true",icon:"wk wk-icon-remind"}},[a("template",{slot:"title-left"},[t._v("客户遗忘提醒"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"23"}})]),t._v(" "),a("div",{staticClass:"content"},[a("flexbox",{attrs:{gutter:0,wrap:"wrap",align:"stretch"}},t._l(t.showData,(function(e,i){return a("div",{key:i,staticClass:"brief-wrap"},[a("div",{staticClass:"brief",on:{click:function(a){t.reportClick(e)}}},[a("div",{staticClass:"title"},[t._v("\n            "+t._s(e.label)),e.tagType?a("el-tag",{attrs:{"disable-transitions":"",type:e.tagType}},[t._v(t._s(e.tagContent))]):t._e()],1),t._v(" "),a("div",{directives:[{name:"fit-text",rawName:"v-fit-text",value:{fontSize:20},expression:"{ fontSize: 20 }"}],staticClass:"value"},[t._v(t._s(e.value)),a("span",{staticClass:"value-unit"},[t._v("\n            个\n          ")])])])])})))],1),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],2)},wt=[],Ct=(a("4de4"),a("910d"),{name:"RankingStatistics",directives:{FitText:b},components:{ReportList:q["a"]},mixins:[x],data:function(){return{data:[{label:"超过7天未联系",value:0,key:"sevenDays",hidden:!1},{label:"超过15天未联系",value:0,key:"fifteenDays",hidden:!1},{label:"超过30天未联系",value:0,key:"oneMonth",hidden:!1},{label:"超过3个月未联系",value:0,key:"threeMonth",hidden:!1,tagType:"warning",tagContent:"警告"},{label:"超过6个月未联系",value:0,key:"sixMonth",hidden:!1,tagType:"warning",tagContent:"警告"},{label:"逾期未联系",value:0,key:"unContactCustomerCount",hidden:!1,tagType:"danger",tagContent:"已逾期"}],loading:!1,reportListShow:!1,reportData:{title:"",placeholder:"",crmType:"",request:null,params:null,paging:!0,sortable:"custom"}}},computed:{showData:function(){return this.data.filter((function(t){return!t.hidden}))}},methods:{getData:function(){var t=this;this.loading=!0;var e=this.getBaseParams();e.dateFilter="today",Object(l["m"])(e).then((function(e){t.loading=!1,t.data=t.data.map((function(t){return t.hidden=!e.data.hasOwnProperty(t.key),t.value=e.data[t.key],t}))})).catch((function(){t.loading=!1}))},reportClick:function(t){if(this.reportData.title=t.label,this.reportData.placeholder="客户名称/手机/电话",this.reportData.crmType="customer","unContactCustomerCount"==t.key)this.reportData.params=Object(r["a"])(Object(r["a"])({},this.getBaseParams()),{},{isSub:1}),this.reportData.request=l["v"];else{var e={sevenDays:7,fifteenDays:15,oneMonth:30,threeMonth:90,sixMonth:180}[t.key];this.reportData.params=Object(r["a"])(Object(r["a"])({},this.getBaseParams()),{},{type:e}),this.reportData.request=l["n"]}this.reportData.params.dateFilter="today",this.reportData.paging=!0,this.reportData.sortable="custom",this.reportListShow=!0}}}),It=Ct,Dt=(a("6a0f"),Object(g["a"])(It,Tt,wt,!1,null,"217bbec8",null)),kt=Dt.exports,Mt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-popover",{staticClass:"wk-time-type-select",attrs:{width:350,placement:"bottom-start","popper-class":"no-padding-popover",trigger:"click"},model:{value:t.showTypePopover,callback:function(e){t.showTypePopover=e},expression:"showTypePopover"}},[a("div",{staticClass:"type-popper"},[a("flexbox",{staticClass:"type-popper-common"},[a("flexbox",{staticClass:"type-sort",attrs:{direction:"column",justify:"space-around"}},t._l(t.sortList,(function(e,i){return a("div",{key:i,staticClass:"type-sort-item"},[t._v(t._s(e.name))])}))),t._v(" "),a("flexbox",{staticClass:"type-content",attrs:{direction:"column",justify:"space-around"}},t._l(t.sortList,(function(e,i){return a("flexbox",{key:i,staticClass:"type-content-box",attrs:{wrap:"wrap"}},[t._l(t.options,(function(i,n){return[e.list.includes(i.value)?a("div",{key:n,staticClass:"type-content-item",class:{selected:t.selectItem.value==i.value&&!t.showCustomContent},on:{click:function(e){t.typeSelectClick(i)}}},[t._v("\n              "+t._s(i.label)+"\n            ")]):t._e()]}))],2)})))],1),t._v(" "),a("div",[a("div",{staticClass:"type-content-item custom"},[t._v("\n        自定义\n      ")]),t._v(" "),a("flexbox",{staticClass:"type-content-custom",attrs:{justify:"space-between"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy.MM.dd"},on:{change:function(e){t.updateDefaultStatus(0)}},model:{value:t.editCustomTimes,callback:function(e){t.editCustomTimes=e},expression:"editCustomTimes"}})],1),t._v(" "),a("div",{staticClass:"confirm"},[a("div",[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:function(e){t.$emit("update:isDefault",t.isChecked)}},model:{value:t.isChecked,callback:function(e){t.isChecked=e},expression:"isChecked"}},[t._v("保存为默认值")]),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"4","data-id":"6"}})],1),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.sureClick}},[t._v("确定")])],1)],1)],1),t._v(" "),a("el-input",{staticClass:"type-select",class:[t.mode?"type-select--"+t.mode:"",{"is-show":t.showTypePopover}],style:{width:t.width+"px"},attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.typeShowValue,callback:function(e){t.typeShowValue=e},expression:"typeShowValue"}},[a("i",{class:["el-icon-arrow-up"],attrs:{slot:"suffix"},slot:"suffix"})])],1)},jt=[],Ot=(a("a9e3"),{name:"WkTimeTypeSelect",props:{defaultType:[String,Object],width:{type:[String,Number],default:200},isDefault:{type:[String,Number],default:0},mode:String,options:{type:Array,default:function(){return[{label:"今天",value:"today"},{label:"昨天",value:"yesterday"},{label:"明天",value:"tomorrow"},{label:"本周",value:"week"},{label:"上周",value:"lastWeek"},{label:"下周",value:"nextWeek"},{label:"本月",value:"month"},{label:"上月",value:"lastMonth"},{label:"下月",value:"nextMonth"},{label:"本季度",value:"quarter"},{label:"上一季度",value:"lastQuarter"},{label:"下一季度",value:"nextQuarter"},{label:"本年度",value:"year"},{label:"上一年度",value:"lastYear"},{label:"下一年度",value:"nextYear"}]}}},data:function(){return{selectType:{label:"本月",value:"month"},selectItem:{},showTypePopover:!1,sureCustomContent:!1,isChecked:0,customTimes:[],editCustomTimes:[],sortList:[{name:"天",list:["today","yesterday","tomorrow"]},{name:"周",list:["week","lastWeek","nextWeek"]},{name:"月",list:["month","lastMonth","nextMonth"]},{name:"季度",list:["quarter","lastQuarter","nextQuarter"]},{name:"年度",list:["year","lastYear","nextYear"]}]}},computed:{typeShowValue:function(){return this.sureCustomContent?this.customTimes.join("-"):this.selectType.label},showCustomContent:function(){return Object(Y["a"])(this.editCustomTimes)&&this.editCustomTimes.length>0}},watch:{defaultType:{handler:function(){void 0!==this.defaultType&&("string"===typeof this.defaultType?(this.selectType=this.getDefaultTypeValue(this.defaultType),this.selectItem=this.getDefaultTypeValue(this.defaultType)):Object(Y["c"])(this.defaultType)&&(this.defaultType.label?(this.selectType=this.defaultType,this.selectItem=this.defaultType):"default"==this.defaultType.type?(this.selectType=this.getDefaultTypeValue(this.defaultType.value),this.selectItem=this.getDefaultTypeValue(this.defaultType.value)):"custom"==this.defaultType.type&&(this.sureCustomContent=!0,this.customTimes=[this.defaultType.startTime,this.defaultType.endTime],this.editCustomTimes=[this.defaultType.startTime,this.defaultType.endTime])))},immediate:!0},showTypePopover:function(t){t&&(this.isChecked=this.isDefault)}},mounted:function(){},methods:{getDefaultTypeValue:function(t){for(var e=0;e<this.options.length;e++){var a=this.options[e];if(a.value==t)return a}return{label:"本月",value:"month"}},updateDefaultStatus:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.isChecked=t,this.$emit("update:isDefault",this.isChecked)},typeSelectClick:function(t){this.editCustomTimes=[],this.updateDefaultStatus(),this.selectItem=t},sureClick:function(){if(this.showCustomContent){var t=Z()(this.editCustomTimes[1].replace(/\./g,"-")).diff(Z()(this.editCustomTimes[0].replace(/\./g,"-")),"days",!0);if(t>90)return void this.$message.error("筛选天数不能大于90天");this.customTimes=[this.editCustomTimes[0],this.editCustomTimes[1]],this.sureCustomContent=!0,this.showTypePopover=!1,this.$emit("change",{type:"custom",startTime:this.editCustomTimes[0],endTime:this.editCustomTimes[1]})}else this.selectType=this.selectItem,this.sureCustomContent=!1,this.showTypePopover=!1,this.$emit("change",{type:"default",value:this.selectType.value,label:this.selectType.label})}}}),St=Ot,xt=(a("0030"),Object(g["a"])(St,Mt,jt,!1,null,"e2a74c0a",null)),Lt=xt.exports,Gt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{ref:"wkDialog",attrs:{visible:t.visible,"append-to-body":!0,"close-on-click-modal":!1,title:"仪表盘模块设置","custom-class":"no-padding-dialog",width:"650px"},on:{close:t.handleCancel}},[a("div",{staticClass:"handle-box"},[a("reminder",{attrs:{content:"拖动以下模块可设置模块位置，点击开启/关闭按钮可设置模块是否在仪表盘显示，点击保存按钮即可生效。"}}),t._v(" "),a("flexbox",{staticClass:"section",attrs:{align:"stretch"}},[a("div",{staticClass:"left"},[a("draggable",{staticClass:"draggable-box",attrs:{group:{name:"sort"},options:{forceFallback:!1}},model:{value:t.sortLeft,callback:function(e){t.sortLeft=e},expression:"sortLeft"}},t._l(t.sortLeft,(function(e,i){return a("flexbox",{key:i,staticClass:"sort-item"},[a("div",{staticClass:"label"},[t._v(t._s(e.title))]),a("el-switch",{model:{value:e.show,callback:function(a){t.$set(e,"show",a)},expression:"item.show"}})],1)})))],1),t._v(" "),a("div",{staticClass:"right"},[a("draggable",{staticClass:"draggable-box",attrs:{group:{name:"sort"},options:{forceFallback:!1}},model:{value:t.sortRight,callback:function(e){t.sortRight=e},expression:"sortRight"}},t._l(t.sortRight,(function(e,i){return a("flexbox",{key:i,staticClass:"sort-item"},[a("div",{staticClass:"label"},[t._v(t._s(e.title))]),a("el-switch",{model:{value:e.show,callback:function(a){t.$set(e,"show",a)},expression:"item.show"}})],1)})))],1)])],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.handleConfirm(e)}}},[t._v("保存")]),t._v(" "),a("el-button",{nativeOn:{click:function(e){return t.handleCancel(e)}}},[t._v("取消")])],1)])},Nt=[],Rt=a("b76a"),Zt=a.n(Rt),Yt=a("8f37"),Wt=a("9dba"),Bt={name:"SetSort",components:{Draggable:Zt.a,Reminder:Yt["a"]},mixins:[Wt["a"]],props:{visible:{type:Boolean,required:!0,default:!1}},data:function(){return{loading:!1,sortLeft:[],sortRight:[],data:{1:{title:"合同/回款金额目标及完成情况",icon:"wk wk-target",iconColor:"#4983EF",img:a("5162")},2:{title:"数据汇总",icon:"wk wk-data",iconColor:"#BF80FF",img:a("aad9")},3:{title:"回款金额目标及完成情况",icon:"wk wk-icon-receivable",iconColor:"#FFD144",img:a("5162")},4:{title:"业绩指标完成率",icon:"wk wk-performance",iconColor:"#4983EF",img:a("28c9")},5:{title:"销售漏斗",icon:"wk wk-funnel",iconColor:"#50CF9E",img:a("a0ad"),id:5},6:{title:"遗忘提醒",icon:"wk wk-clock",iconColor:"#ff8400",img:a("1277")},7:{title:"排行榜",icon:"wk wk-hollow-results",iconColor:"#4a5bfd",img:a("eda56")}}}},computed:{},watch:{},created:function(){this.getModelSort()},methods:{getModelSort:function(){var t=this;this.loading=!0,Object(l["u"])().then((function(e){var a=e.data.left||[],i=e.data.right||[];t.sortLeft=a.map((function(e){return e.show=0==e.isHidden,Object(r["a"])(Object(r["a"])({},e),t.data[e.modelId])})),t.sortRight=i.map((function(e){return e.show=0==e.isHidden,Object(r["a"])(Object(r["a"])({},e),t.data[e.modelId])})),t.loading=!1})).catch(this.loading=!1)},handleCancel:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var t=this;this.loading=!0;var e={};e.left=this.sortLeft.map((function(t){var e={};return e.isHidden=t.show?0:1,e.modelId=t.modelId,e})),e.right=this.sortRight.map((function(t){var e={};return e.isHidden=t.show?0:1,e.modelId=t.modelId,e})),Object(l["t"])(e).then((function(e){t.$message({type:"success",message:"操作成功"}),t.$emit("save"),t.handleCancel(),t.loading=!1})).catch((function(){t.loading=!1}))}}},Ft=Bt,Vt=(a("129f5"),Object(g["a"])(Ft,Gt,Nt,!1,null,"8ead3312",null)),zt=Vt.exports,Et=a("83f1"),Ut=a("da92"),Qt={name:"Workbench",components:{BriefItem:T,SaleStatistics:V,DataStatistics:at,SalesFunnel:lt,PerformanceChart:mt,RankingStatistics:At,ForgetRemind:kt,WkTimeTypeSelect:Lt,ReportList:q["a"],XrRadioMenu:Et["a"],SetSort:zt,draggable:Zt.a},data:function(){return{briefList:[{label:"新增客户",unit:"人",title:"新增客户",type:"customer",labelValue:2,field:"customerCount",icon:"wk-customer",num:"",rate:"",status:"",color:"#2362FB"},{label:"新增联系人",unit:"人",title:"新增联系人",type:"contacts",labelValue:3,field:"contactsCount",icon:"wk-contacts",num:"",rate:"",status:"",color:"#27BA4A"},{label:"新增项目",unit:"个",title:"新增项目",type:"business",labelValue:5,field:"businessCount",icon:"wk-business",num:"",rate:"",status:"",color:"#FB9323"},{label:"新增合同",unit:"个",title:"新增合同",type:"contract",labelValue:6,field:"contractCount",icon:"wk-contract",num:"",rate:"",status:"",color:"#4A5BFD",helpType:"4",helpId:"9"},{label:"合同金额",unit:"元",title:"合同金额",type:"contract",labelValue:6,field:"contractMoney",icon:"wk-receivables",num:"",rate:"",status:"",color:"#19B5F6",helpType:"4",helpId:"10"},{label:"项目金额",unit:"元",title:"项目金额",type:"business",labelValue:5,field:"businessMoney",icon:"wk-icon-opportunities",num:"",rate:"",status:"",color:"#AD5CFF",helpType:"4",helpId:"11"},{label:"回款金额",unit:"元",title:"回款金额",type:"receivables",labelValue:7,field:"receivablesMoney",icon:"wk-receivables",num:"",rate:"",status:"",color:"#FFB940",helpType:"4",helpId:"12"},{label:"新增跟进记录",unit:"条",title:"新增跟进记录",type:"record",labelValue:"",field:"recordCount",icon:"wk-record",num:"",rate:"",status:"",color:"#4A5BFD",helpType:"4",helpId:"13"}],rangeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],filterDataType:2,filterTime:"month",filterValue:{dataType:2,users:[],strucs:[],timeLine:{type:"default",value:"month"}},loading:!1,reportListShow:!1,fieldReportList:null,reportData:{title:"",placeholder:"",crmType:"",request:null,recordRequest:l["p"],params:null,paging:!0,sortable:!1},sortLeft:[],sortRight:[],setSortShow:!1,dataTypeDefault:0,timeDefault:0,defaultValue:{}}},computed:Object(r["a"])(Object(r["a"])({},Object(L["b"])(["userInfo","collapse"])),{},{avatarData:function(){if("custom"===this.filterValue.dataType){var t=(this.filterValue.users||[]).map((function(t){return t.realname})),e=(this.filterValue.strucs||[]).map((function(t){return t.name}));return{realname:t.concat(e).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}},rateText:function(){if("custom"===this.filterValue.timeLine.type)return"";var t=this.filterValue.timeLine.value||"month";return{today:"较昨天",yesterday:"较前天",week:"较上周",lastWeek:"较前周",month:"较上月",lastMonth:"较前月",quarter:"较上季度",lastQuarter:"较上上季度",year:"较去年",lastYear:"较前年"}[t]||""}}),provide:function(){return{workbench:this}},watch:{collapse:function(){var t=this;setTimeout((function(){t.$bus.emit("window-resize")}),300)},filterValue:{handler:function(){this.getBriefData()},deep:!0}},created:function(){var t=this;return Object(o["a"])(Object(s["a"])().m((function e(){return Object(s["a"])().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getDataTypeDefault();case 1:return e.n=2,t.getTimeDefault();case 2:t.getModelSort();case 3:return e.a(2)}}),e)})))()},mounted:function(){var t=this;this.$nextTick((function(){window.addEventListener("resize",t.resizeFn)}))},beforeDestroy:function(){window.removeEventListener("resize",this.resizeFn),this.$bus.off("window-resize")},methods:{resizeFn:function(){var t=this;this.timer||(this.timer=setTimeout((function(){clearTimeout(t.timer),t.timer=null}),30),this.$bus.emit("window-resize"))},timeTypeChange:function(t){this.filterValue.timeLine=t,this.savefilter("time")},radioMenuSelect:function(t,e){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.users=[],this.filterValue.strucs=[],this.savefilter("dataType")):(this.filterValue.users=e.users,this.filterValue.strucs=e.strucs,this.savefilter("dataType"))},getDataTypeDefault:function(t){var e=this;return new Promise((function(t,a){var i={defaultType:1};Object(l["j"])(i).then((function(a){if(a.data&&a.data.defaultValue){var i=JSON.parse(a.data.defaultValue);e.dataTypeDefault=1,e.filterDataType=i.dataType,e.defaultValue=i}else e.dataTypeDefault=0,e.filterDataType=2,e.defaultValue={dataType:2,users:[],strucs:[],timeLine:{type:"default",value:"month"}};t()})).catch((function(){a()}))}))},getTimeDefault:function(t){var e=this;return new Promise((function(t,a){var i={defaultType:2};Object(l["j"])(i).then((function(a){if(a.data&&a.data.defaultValue){var i=JSON.parse(a.data.defaultValue);e.timeDefault=1,e.filterTime=i,e.defaultValue.timeLine=i}else e.timeDefault=0,e.filterTime="month",e.defaultValue.timeLine={type:"default",value:"month"};e.filterValue=e.defaultValue,t()})).catch((function(){a()}))}))},savefilter:function(t){var e={defaultType:"dataType"===t?"1":"2"};e.defaultValue="dataType"===t?this.dataTypeDefault?JSON.stringify(this.filterValue):"":this.timeDefault?JSON.stringify(this.filterValue.timeLine):"",Object(l["k"])(e).then((function(t){})).catch((function(){}))},getBaseParams:function(){var t={};return"custom"!=this.filterDataType?t.dataType=this.filterDataType:(t.deptList=(this.filterValue.strucs||[]).map((function(t){return t.deptId})),t.userList=(this.filterValue.users||[]).map((function(t){return t.userId}))),this.filterValue.timeLine.type&&("custom"===this.filterValue.timeLine.type?(t.dateFilter="custom",t.startDate=this.filterValue.timeLine.startTime.replace(/\./g,"-"),t.endDate=this.filterValue.timeLine.endTime.replace(/\./g,"-")):t.dateFilter=this.filterValue.timeLine.value||""),t},getBriefData:function(){var t=this;this.loading=!0,Object(l["x"])(this.getBaseParams()).then((function(e){t.loading=!1;var a=e.data||[],i=7===a.length?a[6]:null;if(i){var n=a[5];t.briefList.forEach((function(e){for(var s=[],o=1;o<=6;o++){var r=a[o];s.push(r[e.field]||0)}t.$set(e,"chartData",s);var l=i[e.field]||0;"contractMoney"==e.field||"businessMoney"==e.field||"receivablesMoney"==e.field?e.num=Object(K["h"])(l||0):e.num=l;var c=n[e.field]||0,u=l-c;0!==u?(e.status=u>0?"top":"bottom",e.rate=0===l||0===u?0:Ut["a"].times(Ut["a"].divide(u,l),100).toFixed(2)):(e.status="",e.rate=0)}))}})).catch((function(){t.loading=!1}))},reportClick:function(t){t.type&&(this.reportData.title="销售简报-".concat(t.title),this.reportData.placeholder={customer:"客户名称/手机/电话",contacts:"联系人姓名/手机/电话",business:"项目名称",business_status:"项目名称",contract:"合同名称",receivables:"回款编号",record:""}[t.type],this.reportData.crmType=t.type,this.reportData.params=this.getBaseParams(),"record"==t.type?(this.fieldReportList=[{label:"模块",prop:"crmType",width:300},{label:"新增跟进记录数",prop:"count"}],this.reportData.request=l["y"],this.reportData.paging=!1,this.reportData.sortable=!1):(this.fieldReportList=null,this.reportData.request=l["q"],this.reportData.paging=!0,this.reportData.sortable="custom","receivablesMoney"!==t.field&&"contractMoney"!==t.field&&"contractCount"!==t.field||(this.reportData.params.checkStatus=1,this.reportData.params.moneyType={contractMoney:1,contractCount:1,receivablesMoney:2}[t.field])),this.reportData.params.label=t.labelValue,this.reportListShow=!0)},getModelSort:function(){var t=this;Object(l["u"])().then((function(e){var a=e.data.left||[],i=e.data.right||[],n=["SaleStatistics","DataStatistics","","PerformanceChart","SalesFunnel","ForgetRemind","RankingStatistics"];t.sortLeft=a.map((function(t){return t.component=n[t.modelId-1],t})),t.sortRight=i.map((function(t){return t.component=n[t.modelId-1],t}))})).catch()},chartClick:function(t,e){if(this.reportData.title="销售漏斗-".concat(e.settingName),this.reportData.placeholder="项目名称",this.reportData.crmType="business",this.reportData.params=this.getBaseParams(),e.isEnd)this.reportData.params.categoryId=e.flowId,this.reportData.params.type=e.isEnd,this.reportData.request=c["d"];else{var a=this.reportData.params,i=a.dateFilter,n=a.endDate,s=a.startDate;this.reportData.params.searchList=[{formType:"datetime",name:"createTime",type:14,values:"custom"==i?[s,n]:[i]},{formType:"business_cause",group:!0,name:"flowName",type:1,values:[e.flowName]},{formType:"business_cause",name:"settingName",stage:!0,type:1,values:[e.settingName]}],this.reportData.request=c["a"],this.reportData.params.type=5}this.fieldReportList=null,this.reportData.paging=!0,this.reportData.sortable="custom",this.reportData.params.label=5,this.reportListShow=!0},dragEnd:function(){this.handleDragSave()},handleDragSave:function(){var t={};t.left=this.sortLeft.map((function(t){return{isHidden:t.isHidden,modelId:t.modelId}})),t.right=this.sortRight.map((function(t){return{isHidden:t.isHidden,modelId:t.modelId}})),Object(l["t"])(t).then((function(){})).catch((function(){}))}}},Jt=Qt,Pt=(a("7ac2"),Object(g["a"])(Jt,i,n,!1,null,"712ac27a",null));e["default"]=Pt.exports},a0ad:function(t,e,a){t.exports=a.p+"static/img/sort-funnel.fa5c47ba.png"},aad9:function(t,e){t.exports="data:image/png;base64,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"},bc19:function(t,e,a){},bf0a:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return s})),a.d(e,"e",(function(){return o})),a.d(e,"f",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"d",(function(){return c})),a.d(e,"g",(function(){return u})),a.d(e,"h",(function(){return d}));a("d3b7");var i=a("b775");function n(t){var e=t.tableType;if(!e)return Promise.reject();delete t.tableType;var a={count:"biEmployee/contractNumStats",money:"biEmployee/contractMoneyStats",back:"biEmployee/receivablesMoneyStats"}[e];return a?Object(i["a"])({url:a,method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}}):Promise.reject()}function s(t){var e=t.tableType;if(!e)return Promise.reject();delete t.tableType;var a={count:"biEmployee/contractNumStatsExport",money:"biEmployee/contractMoneyStatsExport",back:"biEmployee/receivablesMoneyStatsExport"}[e];return a?Object(i["a"])({url:a,method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}}):Promise.reject()}function o(t){return Object(i["a"])({url:"biEmployee/totalContract",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"biEmployee/totalContractExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"biEmployee/invoiceStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(i["a"])({url:"biEmployee/invoiceStatsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(i["a"])({url:"crmBiSearch/searchInvoicePageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(i["a"])({url:"crmBiSearch/searchReceivablesPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},d33c:function(t,e,a){},da92:function(t,e,a){"use strict";function i(t,e){return void 0===e&&(e=15),+parseFloat(Number(t).toPrecision(e))}function n(t){var e=t.toString().split(/[eE]/),a=(e[0].split(".")[1]||"").length-+(e[1]||0);return a>0?a:0}function s(t){if(-1===t.toString().indexOf("e"))return Number(t.toString().replace(".",""));var e=n(t);return e>0?i(Number(t)*Math.pow(10,e)):Number(t)}function o(t){h&&(t>Number.MAX_SAFE_INTEGER||Number.MIN_SAFE_INTEGER)}function r(t){return function(){for(var e=[],a=0;a<arguments.length;a++)e[a]=arguments[a];var i=e[0],n=e.slice(1);return n.reduce((function(e,a){return t(e,a)}),i)}}var l=r((function(t,e){var a=s(t),i=s(e),r=n(t)+n(e),l=a*i;return o(l),l/Math.pow(10,r)})),c=r((function(t,e){var a=Math.pow(10,Math.max(n(t),n(e)));return(l(t,a)+l(e,a))/a})),u=r((function(t,e){var a=Math.pow(10,Math.max(n(t),n(e)));return(l(t,a)-l(e,a))/a})),d=r((function(t,e){var a=s(t),r=s(e);return o(a),o(r),l(a/r,i(Math.pow(10,n(e)-n(t))))}));function p(t,e){var a=Math.pow(10,e),i=d(Math.round(Math.abs(l(t,a))),a);return t<0&&0!==i&&(i=l(i,-1)),i}var h=!0;function m(t){void 0===t&&(t=!0),h=t}var f={strip:i,plus:c,minus:u,times:l,divide:d,round:p,digitLength:n,float2Fixed:s,enableBoundaryChecking:m};e["a"]=f},de58:function(t,e,a){},e82f:function(t,e,a){"use strict";a("68e4")},eda56:function(t,e,a){t.exports=a.p+"static/img/sort-ranking.5bcc4e1c.png"},ef89:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return s})),a.d(e,"g",(function(){return o})),a.d(e,"h",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"e",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"d",(function(){return d})),a.d(e,"i",(function(){return p}));var i=a("b775");function n(t){return Object(i["a"])({url:"biAchievement/taskCompleteStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(i["a"])({url:"biAchievement/taskCompleteStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"biProduct/productStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"biProduct/productStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(i["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(i["a"])({url:"crmBiSearch/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(i["a"])({url:"crmBiSearch/searchContractPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(i["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f221:function(t,e,a){},f2ec:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return o})),a.d(e,"d",(function(){return r}));var i=a("b775");function n(t){return Object(i["a"])({url:"biFunnel/addBusinessAnalyze",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(i["a"])({url:"biFunnel/win",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"crmBiSearch/searchBusinessPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"crmInstrument/queryContendBusinessList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f705:function(t,e,a){"use strict";a("d33c")},fe21:function(t,e,a){}}]);