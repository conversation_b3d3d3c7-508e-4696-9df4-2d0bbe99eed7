(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-60a32760"],{"0ad2":function(e,t,i){"use strict";i("7354")},"38dd":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"crm-relative-table"},[e.config.showHeader?i("flexbox",{staticClass:"header",attrs:{justify:"center"}},[i("el-input",{attrs:{placeholder:e.config.placeholder},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchInput(t)}},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[i("el-button",{attrs:{slot:"append",type:"primary"},nativeOn:{click:function(t){return e.searchInput(t)}},slot:"append"},[e._v("搜索")])],1)],1):e._e(),e._v(" "),i("div",{staticClass:"body"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{size:"small",id:"crm-table","row-height":40,data:e.list,height:e.tableHeight+e.reduceHeaderHeight,"row-key":e.config.rowKey,"use-virtual":"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{"show-overflow-tooltip":"","reserve-selection":"",type:"selection",align:"center",width:"55"}}),e._v(" "),e._l(e.fieldList,(function(t,n){return i("el-table-column",{key:n,attrs:{fixed:0==n,prop:t.fieldName,label:t.name,formatter:e.config.tableFormatter,"show-overflow-tooltip":""}})}))],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)},a=[],o=i("5530"),l=(i("b0c0"),i("e9f5"),i("7d54"),i("d3b7"),i("ac1f"),i("841c"),i("159b"),i("8ed6")),s=i("6bfe"),c={fieldList:null,request:null,params:null,placeholder:"",isPaging:!0,showHeader:!0,tableFormatter:null,rowKey:""},r={name:"CrmRelativeTable",components:{},props:{props:Object},data:function(){return{search:"",copySearch:"",tableHeight:document.documentElement.clientHeight-290,loading:!1,list:[],fieldList:[],currentPage:1,pageSize:15,pageSizes:[15,30,60,100],total:0,selectionList:[]}},computed:{config:function(){return Object(l["a"])(Object(o["a"])({},c),this.props||{})},reduceHeaderHeight:function(){return this.config.showHeader?0:32}},watch:{"config.params":{handler:function(){this.getList()}}},created:function(){this.config.fieldList&&(this.fieldList=this.config.fieldList),this.getList()},mounted:function(){var e=this;this.updateTableHeight(),window.onresize=function(){e.updateTableHeight()}},beforeDestroy:function(){},methods:{searchInput:function(){this.copySearch=this.search,this.handleCurrentChange(1)},handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},getList:function(){var e=this,t=this.config.params||{};this.config.isPaging&&(t.page=this.currentPage,t.limit=this.pageSize),this.config.request(t).then((function(t){var i=t.data;Object(s["a"])(i)?(e.total=i.length,e.list=i):(e.list=i.list,e.total=i.totalRow)})).catch((function(){}))},updateTableHeight:function(){this.tableHeight=document.documentElement.clientHeight-290},getMainTable:function(){var e=null;return this.$children.forEach((function(t){t.$options&&"ElTable"===t.$options.name&&(e=t)})),e},handleSelectionChange:function(e){if(this.config.radio&&e.length>1){this.getMainTable().clearSelection();var t=e[e.length-1];this.getMainTable().toggleRowSelection(t),this.selectionList=[t]}else this.selectionList=e;this.$emit("selection-change",e)}}},h=r,u=(i("0ad2"),i("2877")),g=Object(u["a"])(h,n,a,!1,null,"db1ec1c2",null);t["default"]=g.exports},7354:function(e,t,i){}}]);