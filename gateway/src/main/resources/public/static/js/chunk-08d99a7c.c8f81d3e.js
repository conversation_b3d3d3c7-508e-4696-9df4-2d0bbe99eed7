(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-08d99a7c"],{"43ed":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"main",attrs:{direction:"column",align:"stretch"}},[a("xr-header",{attrs:{label:"自定义打印模板"}},[a("el-button",{attrs:{slot:"ft",type:"primary"},on:{click:t.addClick},slot:"ft"},[t._v("新建打印模板")])],1),t._v(" "),a("div",{staticClass:"main-body"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-table",class:t.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",id:"examine-table",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:t.tableHeight,"cell-class-name":t.cellClassName,"highlight-current-row":""},on:{"row-click":t.handleRowClick}},[t._l(t.fieldList,(function(e,i){return a("el-table-column",{key:i,attrs:{formatter:t.fieldFormatter,prop:e.prop,"min-width":e.width,label:e.label,"show-overflow-tooltip":""}})})),t._v(" "),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"250"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){t.handleClick("edit",e)}}},[t._v("编辑名称")]),t._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){t.handleClick("copy",e)}}},[t._v("复制")]),t._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){t.handleClick("delete",e)}}},[t._v("删除")])]}}])})],2),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":t.currentPage,"page-sizes":t.pageSizes,"page-size":t.pageSize,total:t.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("template-type-add",{attrs:{visible:t.templateAddShow,detail:t.editData},on:{"update:visible":function(e){t.templateAddShow=e},save:t.refreshList,next:t.createNext}})],1)],1)},n=[],l=a("5530"),o=(a("14d9"),a("a434"),a("f468")),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{visible:t.visible,"append-to-body":!0,"close-on-click-modal":!1,title:t.title,width:"400px"},on:{close:t.handleCancel}},[a("el-form",{ref:"form",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"模板名称"}},[a("el-input",{model:{value:t.dataForm.templateName,callback:function(e){t.$set(t.dataForm,"templateName",e)},expression:"dataForm.templateName"}})],1),t._v(" "),t.isEdit?t._e():a("el-form-item",{attrs:{label:"关联对象"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.dataForm.type,callback:function(e){t.$set(t.dataForm,"type",e)},expression:"dataForm.type"}},t._l(t.options,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})))],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:t.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[t._v(t._s(t.confirmText))]),t._v(" "),a("el-button",{nativeOn:{click:function(e){return t.handleCancel(e)}}},[t._v("取消")])],1)],1)},r=[],d=a("ea20"),c={name:"TemplateTypeAdd",components:{},mixins:[],props:{detail:Object,visible:{type:Boolean,required:!0,default:!1}},data:function(){return{loading:!0,dataForm:{},options:[{label:"项目",value:5},{label:"合同",value:6},{label:"回款",value:7}]}},computed:{isEdit:function(){return!!this.detail},title:function(){return this.isEdit?"编辑打印模板":"新建打印模板"},confirmText:function(){return this.isEdit?"保存":"下一步"}},watch:{visible:{handler:function(t){t&&(this.dataForm={templateName:"",type:5},this.isEdit&&(this.dataForm.templateName=this.detail.templateName))},immediate:!0}},mounted:function(){},methods:{handleCancel:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var t=this;if(this.dataForm.templateName)if(this.isEdit){this.loading=!0;var e=this.isEdit?d["R"]:d["G"],a=this.isEdit?{templateName:this.dataForm.templateName,templateId:this.detail.templateId}:this.dataForm;e(a).then((function(e){t.$message({type:"success",message:"编辑成功"}),t.loading=!1,t.handleCancel(),t.$emit("save")})).catch((function(){t.loading=!1}))}else this.$emit("next",this.dataForm)}}},p=c,m=a("2877"),u=Object(m["a"])(p,s,r,!1,null,"80b4d08e",null),h=u.exports,f={components:{XrHeader:o["a"],TemplateTypeAdd:h},data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-220,list:[],fieldList:[{prop:"templateName",label:"模板名称",width:150},{prop:"type",label:"关联对象",width:150},{prop:"createTime",label:"创建时间",width:150},{prop:"createUserName",label:"创建人",width:150},{prop:"updateTime",label:"更新时间",width:150}],currentPage:1,pageSize:10,pageSizes:[10,20,30,40],total:0,editData:null,templateAddShow:!1}},mounted:function(){var t=this;window.onresize=function(){t.tableHeight=document.documentElement.clientHeight-220},this.getList()},methods:{handleSizeChange:function(t){this.pageSize=t,this.getList()},handleCurrentChange:function(t){this.currentPage=t,this.getList()},refreshList:function(){this.handleCurrentChange(1)},getList:function(){var t=this;this.loading=!0,Object(d["Q"])({page:this.currentPage,limit:this.pageSize}).then((function(e){t.list=e.data.list,t.total=e.data.totalRow,t.loading=!1})).catch((function(){t.loading=!1}))},fieldFormatter:function(t,e){return"type"===e.property?{5:"项目",6:"合同",7:"回款"}[t[e.property]]:t[e.property]},cellClassName:function(t){t.row;var e=t.column;t.rowIndex,t.columnIndex;return"templateName"===e.property?"can-visit--underline":""},handleRowClick:function(t,e,a){"templateName"===e.property&&this.$router.push({name:"crmPrintDetail",query:{handle:"detail",templateName:t.templateName,templateId:t.templateId,type:t.type}})},addClick:function(){this.editData=null,this.templateAddShow=!0},createNext:function(t){this.$router.push({name:"crmPrintDetail",query:Object(l["a"])({handle:"create"},t)})},handleClick:function(t,e){var a=this;"edit"===t?(this.editData=e.row,this.templateAddShow=!0):"delete"===t?this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.loading=!0,Object(d["I"])({templateId:e.row["templateId"]}).then((function(t){a.list.splice(e.$index,1),0==a.list.length&&(a.currentPage=a.currentPage-1>0?a.currentPage-1:1),a.getList(),a.$message({type:"success",message:"操作成功"}),a.loading=!1})).catch((function(){a.loading=!1}))})).catch((function(){})):"copy"===t&&(this.loading=!0,Object(d["H"])({templateId:e.row["templateId"]}).then((function(t){a.getList(),a.$message({type:"success",message:"操作成功"}),a.loading=!1})).catch((function(){a.loading=!1})))}}},g=f,b=(a("c90d"),Object(m["a"])(g,i,n,!1,null,"24251796",null));e["default"]=b.exports},7332:function(t,e,a){},c90d:function(t,e,a){"use strict";a("7332")}}]);