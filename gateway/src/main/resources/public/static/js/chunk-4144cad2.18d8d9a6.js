(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4144cad2"],{"4c68":function(t,e,n){},"612a":function(t,e,n){"use strict";n.d(e,"k",(function(){return a})),n.d(e,"l",(function(){return o})),n.d(e,"m",(function(){return i})),n.d(e,"p",(function(){return s})),n.d(e,"o",(function(){return c})),n.d(e,"n",(function(){return l})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return d})),n.d(e,"b",(function(){return p})),n.d(e,"d",(function(){return m})),n.d(e,"h",(function(){return f})),n.d(e,"i",(function(){return h})),n.d(e,"g",(function(){return b})),n.d(e,"t",(function(){return y})),n.d(e,"s",(function(){return v})),n.d(e,"r",(function(){return T})),n.d(e,"q",(function(){return g})),n.d(e,"j",(function(){return j})),n.d(e,"f",(function(){return C})),n.d(e,"e",(function(){return O}));n("e9f5"),n("7d54"),n("b64b"),n("d3b7"),n("159b");var r=n("b775");function a(t){return Object(r["a"])({url:"adminDept/deleteDept/"+t.id,method:"post"})}function o(t){return Object(r["a"])({url:"adminDept/setDept",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(r["a"])({url:"adminDept/addDept",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(r["a"])({url:"adminUser/setUser",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(r["a"])({url:"adminUser/addUser",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(r["a"])({url:"adminRole/getAllRoleList",method:"post",data:t})}function u(){return Object(r["a"])({url:"adminRole/queryDefaultRole",method:"post"})}function d(t){return Object(r["a"])({url:"adminRole/getRoleList",method:"post",data:t})}function p(t){return Object(r["a"])({url:"adminRole/queryAuthRole/".concat(t),method:"post"})}function m(t,e){return Object(r["a"])({url:"adminRole/updateAuthRole/".concat(t),method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(r["a"])({url:"adminUser/resetPassword",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(r["a"])({url:"adminUser/usernameEdit",method:"post",data:t})}function b(t){return Object(r["a"])({url:"adminUser/usernameEditByManager",method:"post",data:t})}function y(t){return Object(r["a"])({url:"adminUser/setUserStatus",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(r["a"])({url:"adminUser/downloadExcel",method:"post",data:t,responseType:"blob"})}function T(t){var e=new FormData;return Object.keys(t).forEach((function(n){e.append(n,t[n])})),Object(r["a"])({url:"adminUser/excelImport",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"},timeout:6e4})}function g(t){return Object(r["a"])({url:"adminUser/downExcel",method:"post",data:t,responseType:"blob"})}function j(t){return Object(r["a"])({url:"crmCall/authorize",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(r["a"])({url:"adminUser/setUserDept",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(){return Object(r["a"])({url:"adminUser/countNumOfUser",method:"post"})}},"88e3":function(t,e,n){"use strict";n("4c68")},"919c":function(t,e,n){"use strict";n.d(e,"x",(function(){return a})),n.d(e,"z",(function(){return o})),n.d(e,"q",(function(){return i})),n.d(e,"y",(function(){return s})),n.d(e,"l",(function(){return c})),n.d(e,"o",(function(){return l})),n.d(e,"s",(function(){return u})),n.d(e,"p",(function(){return d})),n.d(e,"m",(function(){return p})),n.d(e,"n",(function(){return m})),n.d(e,"e",(function(){return f})),n.d(e,"b",(function(){return h})),n.d(e,"c",(function(){return b})),n.d(e,"d",(function(){return y})),n.d(e,"a",(function(){return v})),n.d(e,"f",(function(){return T})),n.d(e,"u",(function(){return g})),n.d(e,"t",(function(){return j})),n.d(e,"v",(function(){return C})),n.d(e,"r",(function(){return O})),n.d(e,"h",(function(){return _})),n.d(e,"i",(function(){return w})),n.d(e,"g",(function(){return F})),n.d(e,"k",(function(){return k})),n.d(e,"j",(function(){return U})),n.d(e,"w",(function(){return S}));n("e9f5"),n("7d54"),n("b64b"),n("d3b7"),n("159b");var r=n("b775");function a(t){return Object(r["a"])({url:"biCrmInstrument/queryBulletin",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(r["a"])({url:"biCrmInstrument/queryDataInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(r["a"])({url:"crmInstrument/queryBulletinInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(r["a"])({url:"crmInstrument/queryRecordCount",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(r["a"])({url:"biCrmInstrument/queryPerformance",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(r["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(r["a"])({url:"biCrmInstrument/salesTrend",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return t.label=t.crmType,Object(r["a"])({url:"crmInstrument/queryRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(r["a"])({url:"biCrmInstrument/forgottenCustomerCount",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(r["a"])({url:"crmInstrument/forgottenCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(r["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(r["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(r["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(r["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(r["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(r["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(r["a"])({url:"crmInstrument/queryModelSort",method:"post",data:t})}function j(t){return Object(r["a"])({url:"crmInstrument/setModelSort",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(r["a"])({url:"crmInstrument/unContactCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(r["a"])({url:"crmInstrument/queryNoRecordCustomerList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function _(t){return Object(r["a"])({url:"crmActivity/exportRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"},responseType:"blob"})}function w(t){var e=new FormData;return Object.keys(t).forEach((function(n){e.append(n,t[n])})),Object(r["a"])({url:"crmActivity/importRecordList",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}function F(t){return Object(r["a"])({url:"crmActivity/downloadRecordExcel",method:"post",data:t,responseType:"blob"})}function k(t){return Object(r["a"])({url:"crmSearchDefault/save",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function U(t){return Object(r["a"])({url:"crmSearchDefault/queryByType",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(t){return Object(r["a"])({url:"crmBiSearch/searchReceivablesPlanPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},b904:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"main"},[n("flexbox",{staticClass:"main-header",attrs:{justify:"space-between"}},[n("div",{staticClass:"main-header__left"},[n("span",{staticClass:"title"},[t._v("跟进记录")])])]),t._v(" "),n("flexbox",{staticClass:"main-header is-filter-header",attrs:{justify:"space-between"}},[n("div",{staticClass:"main-header__left"},[n("el-input",{staticClass:"search-input",attrs:{placeholder:"关键字搜索"},model:{value:t.filterForm.search,callback:function(e){t.$set(t.filterForm,"search",e)},expression:"filterForm.search"}},[n("el-button",{attrs:{slot:"suffix",type:"icon",icon:"wk wk-sousuo"},on:{click:t.refreshList},slot:"suffix"})],1),t._v(" "),n("span",{staticClass:"tabs"},[n("span",{staticClass:"tabs-label"},[t._v("显示:")]),t._v(" "),t._l(t.tabs,(function(e,r){return n("el-button",{key:r,attrs:{type:e.name===t.tabsSelectValue?"selected":null},on:{click:function(n){t.tabsChange(e.name)}}},[t._v(t._s(e.label))])}))],2)],1),t._v(" "),n("div",{staticClass:"main-header__right"},[n("el-popover",{attrs:{"popper-class":"no-padding-popover",placement:"bottom",width:"300",trigger:"click"},model:{value:t.filterShow,callback:function(e){t.filterShow=e},expression:"filterShow"}},[t.filterShow?n("record-filter",{attrs:{"crm-type-options":t.options,"crm-type":t.filterForm.crmType,"time-select":t.timeSelect,"user-ids":t.filterForm.userIds,type:t.filterForm.recordType},on:{close:function(e){t.filterShow=!1},save:t.filterSave}}):t._e(),t._v(" "),n("el-button",{attrs:{slot:"reference",type:t.filterShow?"selected":"subtle",icon:"wk wk-screening"},on:{click:function(e){t.filterShow=!0}},slot:"reference",model:{value:t.filterShow,callback:function(e){t.filterShow=e},expression:"filterShow"}},[t._v("筛选")])],1),t._v(" "),t.moreTypes.length>0?n("el-dropdown",{staticStyle:{"margin-left":"8px"},attrs:{trigger:"click"},on:{command:t.handleTypeDrop}},[n("el-button",{staticClass:"dropdown-btn",attrs:{icon:"el-icon-more"}}),t._v(" "),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.moreTypes,(function(e,r){return n("el-dropdown-item",{key:r,attrs:{icon:e.icon,command:e.type}},[t._v(t._s(e.name))])})))],1):t._e()],1)]),t._v(" "),n("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.getList,expression:"getList"}],key:""+t.scrollKey+t.tabsSelectValue,staticClass:"cell-section",attrs:{"infinite-scroll-distance":"100","infinite-scroll-disabled":"scrollDisabled"}},[t._l(t.list,(function(e,r){return n("log-cell",{key:r,staticClass:"log-cell",attrs:{index:r,item:e,"show-control":1===e.type},on:{delete:t.logCellDelete,edit:t.logCellEdit,"crm-detail":t.checkRelationDetail}},[t.isObject(e.content)&&4!=e.type?n("activity-item",{attrs:{slot:"content","item-data":e},on:{detail:t.checkRelationDetail},slot:"content"}):t._e(),t._v(" "),n("div",{staticClass:"relate-cell",on:{click:function(n){t.checkRelationDetail(e.activityType,e.activityTypeId,!0)}}},[n("i",{staticClass:"relate-cell-icon",class:t.getActivityIcon(e.activityType)}),t._v(" "),n("span",{staticClass:"relate-cell-type"},[t._v(t._s(t.getActivityTypeName(e.activityType))+"-")]),t._v(" "),n("span",{staticClass:"relate-cell-name"},[t._v(t._s(e.activityTypeName))])])],1)})),t._v(" "),t.loading?n("p",{staticClass:"scroll-bottom-tips"},[t._v("加载中...")]):t._e(),t._v(" "),t.noMore?n("p",{staticClass:"scroll-bottom-tips"},[t._v("没有更多了")]):t._e()],2),t._v(" "),n("c-r-m-full-screen-detail",{attrs:{id:t.relationID,visible:t.showFullDetail,"crm-type":t.relationCrmType},on:{"update:visible":function(e){t.showFullDetail=e},handle:t.detailHandle}}),t._v(" "),n("log-edit-dialog",{attrs:{visible:t.logEditDialogVisible,data:t.logEditData},on:{"update:visible":function(e){t.logEditDialogVisible=e},save:t.logEditSave}})],1)},a=[],o=n("5530"),i=(n("99af"),n("7db0"),n("14d9"),n("a434"),n("e9f5"),n("f665"),n("7d54"),n("b64b"),n("d3b7"),n("ac1f"),n("3ca3"),n("5319"),n("841c"),n("159b"),n("ddb0"),n("919c")),s=n("e170"),c=n("612a"),l=n("3bc5"),u=n("9ffb"),d=n("fc6d"),p=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"record-filter"},[t._m(0),t._v(" "),n("div",{staticClass:"record-filter__body"},[n("el-form",{ref:"form",staticClass:"task-filter__body",attrs:{"label-width":"90px"}},[n("el-form-item",{attrs:{label:"创建人"}},[n("wk-user-dialog-select",{attrs:{placeholder:"选择人员"},model:{value:t.usersIdsValue,callback:function(e){t.usersIdsValue=e},expression:"usersIdsValue"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"创建时间"}},[t.timeSelectValue?n("time-type-select",{attrs:{width:"100%","default-type":t.timeSelectValue},on:{change:t.timeTypeChange}}):t._e()],1),t._v(" "),n("el-form-item",{attrs:{label:"模块"}},[n("el-select",{model:{value:t.crmTypeValue,callback:function(e){t.crmTypeValue=e},expression:"crmTypeValue"}},t._l(t.crmTypeOptions,(function(t,e){return n("el-option",{key:e,attrs:{label:t.label,value:t.value}})})))],1),t._v(" "),n("el-form-item",{attrs:{label:"类型"}},[n("el-select",{model:{value:t.typeValue,callback:function(e){t.typeValue=e},expression:"typeValue"}},t._l(t.typeOptions,(function(t,e){return n("el-option",{key:e,attrs:{label:t.label,value:t.value}})})))],1)],1)],1),t._v(" "),n("div",{staticClass:"record-filter__footer"},[n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:t.saveClick,expression:"saveClick"}],attrs:{type:"primary"}},[t._v("保存")]),t._v(" "),n("el-button",{nativeOn:{click:function(e){return t.closeClick(e)}}},[t._v("取消")])],1)])])},m=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"record-filter__header"},[n("span",{staticClass:"record-filter__title"},[t._v("筛选")])])}],f=(n("a9e3"),n("8f81")),h=n("657f"),b=n("ed08"),y={name:"RecordFilter",components:{WkUserDialogSelect:f["a"],TimeTypeSelect:h["a"]},props:{crmTypeOptions:Array,crmType:[String,Number],timeSelect:Object,userIds:Array,type:[String,Number]},data:function(){return{usersIdsValue:[],crmTypeValue:"",timeSelectValue:null,typeValue:"",typeOptions:[{label:"全部",value:""},{label:"跟进记录",value:1},{label:"外勤签到",value:4}]}},computed:{},watch:{},created:function(){this.crmTypeValue=this.crmType,this.timeSelectValue=Object(b["D"])(this.timeSelect),this.typeValue=this.type,this.usersIdsValue=Object(b["D"])(this.userIds)},beforeDestroy:function(){},methods:{closeClick:function(){this.$emit("close")},timeTypeChange:function(t){this.timeSelectValue=t},saveClick:function(){this.$emit("save",this.crmTypeValue,this.timeSelectValue,this.typeValue,this.usersIdsValue)},handleDateClose:function(){this.$refs.filterDatePicker.pickerVisible=!1}}},v=y,T=(n("dc5b"),n("2877")),g=Object(T["a"])(v,p,m,!1,null,"c2c8e998",null),j=g.exports,C=n("67e1"),O=n("befc"),_=n("6683"),w=n("2f62"),F=n("6bfe"),k={name:"FollowIndex",components:{LogCell:l["a"],ActivityItem:u["a"],LogEditDialog:d["a"],RecordFilter:j,CRMFullScreenDetail:function(){return Promise.resolve().then(n.bind(null,"df3e"))}},mixins:[C["a"],O["a"]],props:{},data:function(){return{tabsSelectValue:"all",tabs:[{label:"全部",name:"all"},{label:"我创建的",name:"0"},{label:"我下属创建的",name:"1"}],options:[{label:"全部",value:""},{label:"线索",value:1},{label:"客户",value:2},{label:"联系人",value:3},{label:"项目",value:5},{label:"合同",value:6}],filterForm:{crmType:"",isUser:1,userIds:[],subUser:"",recordType:""},filterShow:!1,timeSelect:{type:"default",value:"month"},list:[],loading:!1,noMore:!1,page:1,scrollKey:Date.now(),showFullDetail:!1,relationID:"",relationCrmType:"",logEditData:null,logEditPosition:{seciton:0,index:0},logEditDialogVisible:!1,requestParams:{}}},computed:Object(o["a"])(Object(o["a"])({},Object(w["b"])(["crm","userInfo"])),{},{moreTypes:function(){var t=[];return this.$auth("crm.followRecord.excelimport")&&t.push({type:"enter",name:"导入",icon:"wk wk-import"}),this.$auth("crm.followRecord.excelexport")&&t.push({type:"out",name:"导出",icon:"wk wk-export"}),t},scrollDisabled:function(){return this.loading||this.noMore},userSelectShow:function(){return"0"!==this.filterForm.subUser}}),watch:{filterForm:{handler:function(){this.refreshList()},deep:!0}},mounted:function(){},created:function(){var t=this;this.$bus.on("import-crm-done-bus",(function(e){"crmFollowLog"===e&&t.refreshList()}))},beforeDestroy:function(){this.$bus.off("import-crm-done-bus")},methods:{isObject:function(t){return Object(F["c"])(t)},tabsChange:function(t){this.tabsSelectValue=t,this.filterForm.subUser="all"===t?"":t},filterSave:function(t,e,n,r){var a=e.type,o=e.startTime,i=e.endTime;if("custom"===a){var s=this.$moment(i.replace(/\./g,"-")).diff(this.$moment(o.replace(/\./g,"-")),"days",!0);if(s>90)return void this.$message.error("筛选天数不能大于90天")}this.filterShow=!1,this.filterForm.crmType=t,this.timeSelect=e,this.filterForm.recordType=n,this.filterForm.userIds=r},refreshList:function(){this.page=1,this.list=[],this.noMore=!1,this.scrollKey=Date.now()},getFilterParams:function(){var t={page:this.page,limit:15,isOa:!0,content:this.filterForm.search,subUser:this.filterForm.subUser,queryType:1,recordType:this.filterForm.recordType,userList:this.filterForm.userIds,activityType:this.filterForm.crmType};return 0==t.recordType&&delete t.recordType,this.timeSelect.type&&("custom"===this.timeSelect.type?(t.dateFilter="custom",t.startDate=this.timeSelect.startTime.replace(/\./g,"-"),t.endDate=this.timeSelect.endTime.replace(/\./g,"-")):t.dateFilter=this.timeSelect.value||""),t.subUser&&(t.dataType={0:1,1:2}[t.subUser]),Object.keys(t).forEach((function(e){t[e]||0===t[e]||delete t[e]})),t},getList:function(){var t=this;this.loading=!0,this.requestParams=this.getFilterParams(),Object(s["c"])(this.requestParams).then((function(e){t.loading=!1,t.noMore||(t.page++,t.list=t.list.concat(e.data.list||[])),t.noMore=e.data.lastPage})).catch((function(){t.noMore=!0,t.loading=!1}))},checkRelationDetail:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.relationID=e,this.relationCrmType=n?_["a"].convertTypeToKey(t):t,this.showFullDetail=!0},getActivityIcon:function(t){return this.getXrIcon(this.getActivityType(t))},getActivityTypeColor:function(t){return this.getXrIconColor(this.getActivityType(t))},detailHandle:function(t){"delete"==t.type&&this.refreshList()},logCellDelete:function(t,e,n){this.list.splice(e,1),this.scrollKey=Date.now()},logCellEdit:function(t,e,n){this.logEditData=t,this.logEditPosition={seciton:n,index:e},this.logEditDialogVisible=!0},logEditSave:function(t){t?this.logEditPosition.index>=0&&this.list.splice(this.logEditPosition.index,1,t):this.refreshList()},handleTypeDrop:function(t){var e=this;if(this.requestParams.activityType){if("out"==t)Object(i["h"])(this.requestParams).then((function(t){Object(b["g"])(t)})).catch((function(){}));else if("enter"==t){var n=this.options.find((function(t){return t.value===e.requestParams.activityType}));this.$wkImport.import("crmFollowLog",{typeName:"".concat(n.label,"跟进记录"),ownerSelectShow:!1,repeatHandleShow:!1,historyShow:!1,noImportProcess:!0,importRequest:i["i"],importParams:{crmType:this.requestParams.activityType},templateRequest:i["g"],templateParams:{crmType:this.requestParams.activityType},downloadErrFuc:this.getImportError,userInfo:this.userInfo})}}else this.$message.error("请先选择一个模块导入/导出")},getImportError:function(t){return new Promise((function(e,n){Object(c["q"])({token:t.token}).then((function(t){e(t)})).catch((function(){n()}))}))}}},U=k,S=(n("88e3"),Object(T["a"])(U,r,a,!1,null,"e226f180",null));e["default"]=S.exports},dc5b:function(t,e,n){"use strict";n("e51b")},e51b:function(t,e,n){}}]);