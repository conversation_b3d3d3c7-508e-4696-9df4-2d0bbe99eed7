(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54bc726a"],{"29da":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("wk-page-header",{attrs:{title:e.config.showModuleName?"发票管理":"",help:e.getHelpObj(e.crmType,"index"),dropdowns:e.getDefaultHeaderHandes()},on:{command:e.pageHeaderCommand}},[a("template",{slot:"right"},[e.saveAuth?a("el-button",{attrs:{type:"primary"},on:{click:e.createClick}},[e._v("新建发票")]):e._e()],1)],2),e._v(" "),a("div",{directives:[{name:"empty",rawName:"v-empty",value:!e.indexAuth,expression:"!indexAuth"}],staticClass:"crm-container",attrs:{"xs-empty-icon":"nopermission","xs-empty-text":"暂无权限"}},[a("wk-table-header",{attrs:{search:e.search,tabs:e.sceneList,"active-tab":e.sceneId,"selection-list":e.tableSelectionList,operations:e.handleOperations,"condition-type-fun":void 0,fields:e.getFilterFields,props:e.tableHeaderProps.props,"filter-header-props":e.tableHeaderProps.filterHeaderProps,"filter-form-props":e.tableHeaderProps.filterFormProps,"scene-set-props":e.tableHeaderProps.sceneSetProps,"scene-create-props":e.tableHeaderProps.sceneCreateProps},on:{"update:search":function(t){e.search=t},"update:activeTab":function(t){e.sceneId=t},"tabs-change":e.sceneSelect,"operations-click":e.tableOperationsClick,"event-change":e.tableHeaderHandle,"filter-change":e.handleFilter}}),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.crmTableClass,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,"row-key":e.crmType+"Id",stripe:e.tableStyleObj.stripe,"use-virtual":"","highlight-current-row":""},on:{"row-click":e.handleRowClick,"sort-change":e.sortChange,"header-dragend":e.handleHeaderDragend,"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"","reserve-selection":"",type:"selection",fixed:"",align:"center",width:"55"}}),e._v(" "),e._l(e.showfieldList,(function(t,n){return a("el-table-column",{key:n,attrs:{fixed:1===t.isLock,prop:t.prop,label:t.label,width:t.width,"class-name":t.width>60?"column":"",sortable:"custom","show-overflow-tooltip":""},scopedSlots:e._u([{key:"otherHeader",fn:function(n){return t.width>60?[a("el-button",{staticClass:"el-lock-btn",attrs:{icon:1===t.isLock?"wk wk-unlock":"wk wk-lock",type:"text"},on:{click:function(a){a.stopPropagation(),e.fieldFixed(t)}}}),e._v(" "),e.showFilter(t)?a("el-button",{staticClass:"el-filter-btn",attrs:{type:"text",icon:"wk wk-screening"},on:{click:function(a){a.stopPropagation(),e.showFilterClick(t)}}}):e._e()]:void 0}},{key:"default",fn:function(n){var o=n.row,i=n.column;return["checkStatus"==t.prop?[a("span",{staticClass:"status-mark",style:e.getStatusStyle(o.checkStatus)}),e._v(" "),a("span",[e._v(e._s(e.getStatusName(o.checkStatus)))])]:"invoiceType"==t.prop?[e._v("\n            "+e._s(e.fieldFormatter(o,i,o[i.property],t))+"\n          ")]:a("wk-field-view",{attrs:{props:t,"form-type":t.formType,value:o[i.property]}},[[e._v("\n              "+e._s(e.fieldFormatter(o,i,o[i.property],t))+"\n            ")]],2)]}}])})})),e._v(" "),a("el-table-column"),e._v(" "),e.canUpdateStatus?a("el-table-column",{attrs:{resizable:!1,label:"操作",fixed:"right",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:1==t.row.invoiceStatus,type:"primary-text"},nativeOn:{click:function(a){e.markReceivables(t)}}},[e._v(e._s(1==t.row.invoiceStatus?"已开票":"标记为开票"))])]}}])}):e._e(),e._v(" "),a("wk-empty",{attrs:{slot:"empty",props:{buttonTitle:"新建发票",showButton:e.saveAuth}},on:{click:e.createClick},slot:"empty"}),e._v(" "),a("field-set",{attrs:{slot:"other","crm-type":e.crmType},on:{change:e.setSave},slot:"other"})],2),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-dropdown",{attrs:{trigger:"click",placement:"top"}},[a("el-button",{staticClass:"dropdown-btn"},[a("i",{staticClass:"el-icon-s-fold"})]),e._v(" "),a("el-dropdown-menu",{staticClass:"wk-table-style-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.rightBorderShow,callback:function(t){e.$set(e.tableStyleObj,"rightBorderShow",t)},expression:"tableStyleObj.rightBorderShow"}}),e._v("显示竖向分割线")],1)]),e._v(" "),a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.bottomBorderShow,callback:function(t){e.$set(e.tableStyleObj,"bottomBorderShow",t)},expression:"tableStyleObj.bottomBorderShow"}}),e._v("显示横向分割线")],1)]),e._v(" "),a("el-dropdown-item",[a("span",{on:{click:function(e){e.stopPropagation()}}},[a("el-switch",{model:{value:e.tableStyleObj.stripe,callback:function(t){e.$set(e.tableStyleObj,"stripe",t)},expression:"tableStyleObj.stripe"}}),e._v("显示斑马纹")],1)])],1)],1),e._v(" "),a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.isCreate?a("create",{on:{"save-success":e.refreshList,close:function(t){e.isCreate=!1}}}):e._e(),e._v(" "),e.markShow?a("mark-invoice",{attrs:{visible:e.markShow,reset:e.isResetInvoice,detail:e.rowDetail},on:{"update:visible":function(t){e.markShow=t},change:function(t){e.handleHandle({type:"reset_invoice_status"})}}}):e._e(),e._v(" "),a("c-r-m-all-detail",{staticClass:"d-view",attrs:{id:e.rowID,visible:e.showDview,"crm-type":e.rowType,"page-list":e.crmType==e.rowType?e.list:[],"page-index":e.rowIndex},on:{"update:id":function(t){e.rowID=t},"update:visible":function(t){e.showDview=t},"update:pageIndex":function(t){e.rowIndex=t},handle:e.handleHandle}}),e._v(" "),e.transferDialogShow?a("transfer-handle",{attrs:{props:e.transferHandleProps,"dialog-visible":e.transferDialogShow},on:{"update:dialogVisible":function(t){e.transferDialogShow=t},handle:e.handleHandle}}):e._e()],1)},o=[],i=(a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("55c3")),r=a("44e4"),s=a("c12d"),l=a("d718"),c=a("c8fa"),p=a("e505"),d=a("9bbe"),u={name:"Invoice",components:{Create:r["a"],MarkInvoice:s["a"],TransferHandle:l["a"],CRMAllDetail:c["a"]},mixins:[p["a"]],props:{},data:function(){return{crmType:"invoice",list:[],selectionList:[],isCreate:!1,rowDetail:{},markShow:!1,isResetInvoice:!1,transferHandleProps:{},transferDialogShow:!1,showDview:!1}},computed:{canUpdateStatus:function(){return this.crm&&this.crm[this.crmType]&&this.crm[this.crmType].resetInvoiceStatus},showfieldList:function(){return this.fieldList.filter((function(e){return"invoiceStatus"!==e.prop}))},handleOperations:function(){return this.getOperations(["delete","reset_invoice_status","transfer"])}},watch:{},created:function(){},beforeDestroy:function(){},methods:{tableOperationsClick:function(e){var t=this;"transfer"===e?(this.transferHandleProps={request:i["k"],params:{ids:this.selectionList.map((function(e){return e[t.crmType+"Id"]}))},showRemoveType:!1,help:this.getHelpObj(this.crmType,"transfer")},this.transferDialogShow=!0):"delete"==e?this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(i["a"])(t.selectionList.map((function(e){return e.invoiceId}))).then((function(e){t.loading=!1,t.$message({type:"success",message:"删除成功"}),t.handleHandle({type:"delete"})})).catch((function(){t.loading=!1}))})).catch((function(){})):"reset_invoice_status"==e&&(this.rowDetail=this.selectionList[0],this.isResetInvoice=!0,this.markShow=!0)},createClick:function(){this.isCreate=!0},fieldFormatter:function(e,t,a,n){return"invoiceType"==t.property?{1:"增值税专用发票",2:"增值税普通发票",3:"国税通用机打发票",4:"地税通用机打发票",5:"收据"}[e[t.property]]:n?Object(d["a"])(n.formType,e[t.property]):""===e[t.property]||null===e[t.property]?"--":e[t.property]},markReceivables:function(e){this.rowDetail=e.row,this.isResetInvoice=!1,this.markShow=!0},changeUserCell:function(e){this.filterParams.ownerUserId=e.value,this.refreshList()},handleCommand:function(e){},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"invoiceApplyNumber"===t.property?"can-visit--underline can-visit--bold":"customerName"===t.property||"contractNum"===t.property?"can-visit--underline":""}}},h=u,f=(a("c583"),a("2877")),m=Object(f["a"])(h,n,o,!1,null,"c4a8e77e",null);t["default"]=m.exports},a6ec:function(e,t,a){},c583:function(e,t,a){"use strict";a("a6ec")}}]);