(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5683aaaa"],{"0519":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"map-box"},[s("div",{staticClass:"map-title"},[s("flexbox",{staticClass:"map-title-content"},[t._v("\n      附近的客户"),s("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"8","data-id":"121"}})])],1),t._v(" "),s("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"map-content"},[s("flexbox",{staticClass:"map-content--title",attrs:{align:"stretch"}},[s("flexbox-item",{staticClass:"map-filter"},[s("el-select",{attrs:{mode:"no-border"},on:{change:t.getMapInfo},model:{value:t.mapData.type,callback:function(e){t.$set(t.mapData,"type",e)},expression:"mapData.type"}},t._l(t.typeOptions,(function(t){return s("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))),t._v(" "),s("span",{staticClass:"place-text"},[t._v("附近 ")]),t._v(" "),s("el-popover",{attrs:{width:t.radiusSelectWidth,placement:"bottom","popper-class":"no-padding-popover",trigger:"click"},model:{value:t.showTypePopover,callback:function(e){t.showTypePopover=e},expression:"showTypePopover"}},[s("div",{staticClass:"type-popper"},[s("div",{staticClass:"type-content"},[t._l(t.memterOptions,(function(e,a){return s("div",{key:a,staticClass:"type-content-item",class:{selected:t.selectType.value==e.value&&!t.showCustomContent},on:{click:function(s){t.radiusChange(e)}}},[t._v("\n                "+t._s(e.label)+"\n              ")])})),t._v(" "),s("div",{staticClass:"type-content-item",class:{selected:t.showCustomContent},on:{click:function(e){t.showCustomContent=!0}}},[t._v("\n                自定义\n              ")])],2),t._v(" "),t.showCustomContent?s("div",{staticClass:"type-content-custom"},[s("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number",value:"positiveInt",expression:"'positiveInt'"}],model:{value:t.mapData.radius,callback:function(e){t.$set(t.mapData,"radius",e)},expression:"mapData.radius"}},[s("template",{slot:"append"},[t._v("米")])],2),t._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(e){t.radiusChange(null)}}},[t._v("确定")])],1):t._e()]),t._v(" "),s("el-input",{staticClass:"type-select",class:["type-select--no-border",{"is-show":t.showTypePopover}],style:{width:t.radiusSelectWidth+"px"},attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.mapData.radius,callback:function(e){t.$set(t.mapData,"radius",e)},expression:"mapData.radius"}},[s("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),s("span",{staticClass:"place-text"},[t._v(" 米的客户")])],1),t._v(" "),s("div",{staticClass:"title--right"},[s("div",{staticClass:"title--position"},[s("span",{staticClass:"wk wk-icon-position"}),t._v(" "),s("el-tooltip",{staticClass:"item",attrs:{content:t.address,effect:"dark",placement:"top-start"}},[s("span",{staticClass:"title--address"},[t._v(t._s(t.address))])])],1),t._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(e){t.changeAddressShow=!0}}},[t._v("重新选择地址")])],1)],1),t._v(" "),s("flexbox",{staticClass:"box-content",attrs:{align:"stretch"}},[s("div",{directives:[{name:"empty",rawName:"v-empty",value:t.mapList,expression:"mapList"}],staticClass:"map-content--left",attrs:{"xs-empty-icon":"none"}},[s("div",{attrs:{id:"map-scroll"}},[s("ul",{ref:"nearbyList",style:{height:t.mapHeight+"px"}},t._l(t.mapList,(function(e,a){return s("li",{key:a,staticClass:"map-info--box",on:{click:function(s){t.selectAddress(e)}}},[s("span",{staticClass:"wk wk-icon-location"}),t._v(" "),s("div",{staticClass:"map-box--content"},[s("div",{staticClass:"map-info--name"},[s("el-tooltip",{staticClass:"item",attrs:{content:e.customerName,effect:"dark",placement:"top-start"}},[s("div",{staticClass:"map-customer-name can-visit--underline",on:{click:function(s){s.stopPropagation(),t.checkCustomerDetail(e.customerId)}}},[t._v(t._s(e.customerName))])])],1),t._v(" "),s("div",{staticClass:"map-info--content"},[s("span",{staticClass:"content-title"},[t._v("负责人：")]),t._v(" "),s("span",{attrs:{title:e.ownerUserName}},[t._v(t._s(e.ownerUserName))])]),t._v(" "),s("div",{staticClass:"map-info--content"},[s("span",{staticClass:"content-title"},[t._v("地址：")]),t._v(" "),s("span",{attrs:{title:e.detailAddress}},[t._v(t._s(e.detailAddress))])]),t._v(" "),s("div",{staticClass:"map-info--content"},[s("span",{staticClass:"content-title"},[t._v("距离选择地址：")]),t._v(" "+t._s(e.distance/1e3)+" km")])])])})))])]),t._v(" "),s("flexbox-item",{staticClass:"map-primity",style:{height:t.mapHeight+"px"}},[s("div",{ref:"nearbyMap",staticClass:"map"})])],1)],1),t._v(" "),s("change-address",{attrs:{show:t.changeAddressShow,value:t.centerPoint},on:{select:t.handleSelect,close:function(e){t.changeAddressShow=!1}}}),t._v(" "),t.showDview?s("customer-detail",{staticClass:"d-view",attrs:{id:t.rowID,"no-listener-class":["map-customer-name"]},on:{"hide-view":function(e){t.showDview=!1}}}):t._e()],1)},i=[],n=s("5530"),o=(s("d81d"),s("14d9"),s("a434"),s("b0c0"),s("e9f5"),s("7d54"),s("ab43"),s("d3b7"),s("3ca3"),s("159b"),s("ddb0"),s("b775"));function l(t){return Object(o["a"])({url:"crmCustomer/nearbyCustomer",method:"post",data:t})}var c=s("d5b3"),r=s("ed08"),p=s("2f62"),d={name:"NearbyIndex",components:{ChangeAddress:function(){return s.e("chunk-ba525f2a").then(s.bind(null,"a155"))},CustomerDetail:c["a"]},props:{},data:function(){return{map:null,radiusSelectWidth:100,showTypePopover:!1,showCustomContent:!1,selectType:{},currentId:-1,changeAddressShow:!1,address:"",mapList:[],markerArr:[],loading:!0,circle:null,memterOptions:[{label:"1千米",value:"1000"},{label:"3千米",value:"3000"},{label:"5千米",value:"5000"},{label:"10千米",value:"10000"}],centerPoint:{},mapData:{radius:"1000",type:""},mapHeight:document.documentElement.clientHeight-225,typeOptions:[{value:"",label:"全部"},{value:2,label:"客户"},{value:9,label:"公海"}],showDview:!1,rowID:null}},computed:Object(n["a"])(Object(n["a"])({},Object(p["b"])(["crm"])),{},{menuItems:function(){var t=[];return this.crm&&this.crm.customer&&t.push({title:"客户",path:"customer",icon:s("70b9")}),this.crm&&this.crm.pool&&t.push({title:"公海",path:"seas",icon:s("8283")}),this.crm&&this.crm.customer&&this.crm.customer.nearbyCustomer&&t.push({title:"附近客户",path:"nearby",icon:s("b780")}),t}}),watch:{},mounted:function(){var t=this;Object(r["q"])().then((function(){var e=new BMap.Map(t.$refs.nearbyMap),s=new BMap.Point(116.404,39.915);e.centerAndZoom(s,14),e.enableScrollWheelZoom(!0),t.map=e,t.centerPoint=s,t.getMyPosition()}))},deactivated:function(){this.$refs.elMenu.activeIndex="nearby"},methods:{menuSelect:function(t,e){this.$emit("menu-select",t,e)},getMyPosition:function(){var t=new BMap.LocalCity;t.get(this.getFirstPosition)},getFirstPosition:function(t){this.centerPoint=t.center,this.address=t.name,this.getMapInfo()},radiusChange:function(t){t?(this.mapData.radius=t.value,this.selectType=t,this.showCustomContent=!1):(this.mapData.radius||(this.mapData.radius=1e3),this.showCustomContent=!0),this.mapData.radius>1e5&&this.$set(this.mapData,"radius",1e5),this.getMapInfo(),this.showTypePopover=!1},getMapInfo:function(){var t=this;this.loading=!0,this.map.clearOverlays();var e=Object(n["a"])({},this.mapData);e.lat=this.centerPoint.lat,e.lng=this.centerPoint.lng,l(e).then((function(e){t.mapList=e.data,t.addMarkerLabel(),t.setCircle(),t.loading=!1})).catch((function(){t.loading=!1}))},selectAddress:function(t){var e=this.map.getOverlays();this.currentId=t.customerId;for(var s=0;s<e.length;s++){var a=e[s];"ComplexCustomOverlay"===a._type&&(a._customerId===t.customerId?(this.map.centerAndZoom(a._point,this.map.getZoom()),a._changeOverStyle()):a._changeOutStyle())}},setCircle:function(){this.circle&&this.removeOverlay(this.circle);var t=new BMap.Circle(this.centerPoint,this.mapData.radius,{strokeColor:"#2362FB",fillColor:"#2362FB",strokeWeight:2,fillOpacity:.05,strokeOpacity:.5,strokeStyle:"solid"});t.setCenter(this.centerPoint),this.circle=t,this.circle.type="circle",this.map.addOverlay(t),this.map.setCenter(this.centerPoint),this.map.panTo(this.centerPoint),this.map.setViewport(this.markerArr)},addMarkerLabel:function(){function t(t,e,s){this._type="ComplexCustomOverlay",this._point=t,this._name=e,this._customerId=s}this.markerArr=[],t.prototype=new BMap.Overlay,t.prototype.initialize=function(t){var a=this;this._map=t;var i=this._div=document.createElement("div"),n=this._span=document.createElement("span");i.className="map-marker--custom "+"marker--".concat(this._customerId),n.className="map-custom--text",i.appendChild(n),i.style.position="absolute",i.style.zIndex=this._zIndex=BMap.Overlay.getZIndex(this._point.lat),i.style.color="white",i.style.padding="2px",i.style.whiteSpace="nowrap",i.style.MozUserSelect="none",i.style.fontSize="12px",n.innerHTML=this._name,i.appendChild(n);var o=this._arrow=document.createElement("div");o.style.background="url(".concat(s("14ce"),") no-repeat"),o.style.position="absolute",o.style.width="20px",o.style.height="15px",o.style.transform="scale(0.5)",o.style.top="24px",o.style.left="75px",o.style.opacity="0.95",o.style.overflow="hidden",i.appendChild(o),this._changeOverStyle=function(){this._div.style.backgroundColor="#fba019",this._div.style.whiteSpace="normal",this._div.style.zIndex="1",this._arrow.style.backgroundPosition="0px -20px"},this._changeOutStyle=function(){e.currentId!==this._customerId&&(this._div.style.backgroundColor="#2362FB",this._div.style.whiteSpace="nowrap",this._div.style.zIndex=this._zIndex,this._arrow.style.backgroundPosition="0px 0px")};var l=this;return i.onmouseover=function(){l._changeOverStyle()},i.onmouseout=function(){l._changeOutStyle()},i.onclick=function(){for(var t=null,s=0;s<e.mapList.length;s++)if(a._customerId===e.mapList[s].customerId){t=e.mapList.splice(s,1),e.mapList.unshift(t[0]),e.$refs.nearbyList.scrollTop=0,e.selectAddress(t[0]);break}},e.map.getPanes().labelPane.appendChild(i),i},t.prototype.draw=function(){var t=this._map,e=t.pointToOverlayPixel(this._point);this._div.style.left=e.x-parseInt(this._arrow.style.left)-10+"px",this._div.style.top=e.y-30+"px"};var e=this;this.mapList.forEach((function(s){var a=new BMap.Point(s.lng,s.lat),i=new t(a,s.customerName,s.customerId);e.markerArr.push(a),e.map.addOverlay(i)}))},removeOverlay:function(t){for(var e=this.map.getOverlays(),s=0;s<e.length;s++)e[s].type==t.type&&this.map.removeOverlay(e[s])},handleSelect:function(t){this.centerPoint=t.point,this.address=t.address+t.title,this.getMapInfo()},checkCustomerDetail:function(t){this.rowID=t,this.showDview=!0}}},m=d,u=(s("187d"),s("2877")),h=Object(u["a"])(m,a,i,!1,null,"6c544f3e",null);e["default"]=h.exports},"14ce":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAtCAYAAACqCZtVAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo4MzAzQzVFRjIyMDkxMUVBOTNFNkI3ODQ5MzVCRUIyNyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo4MzAzQzVGMDIyMDkxMUVBOTNFNkI3ODQ5MzVCRUIyNyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjgzMDNDNUVEMjIwOTExRUE5M0U2Qjc4NDkzNUJFQjI3IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjgzMDNDNUVFMjIwOTExRUE5M0U2Qjc4NDkzNUJFQjI3Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+6GqSmAAAAP5JREFUeNpi/P//PwMtAItK8h+aGMzEQCPAAqU7gbiMBH0gb37GIz8dZnAVEGsAsR8JDhLEIbcJiOtgQfEXiCOB+CSFIXASas5f5DD+BsS+QHyHTEPvQPV/wxZ5r4HYE4jfkGgoTN9rfKkCZHM4EP8i0lCsPsWV3PYBcRIQE8o9OOMGXzpeCk0t+EAuNBWQnEE6QGmSDDmicl4BNGhI8g0xBoMiMQiIL5MS/sSWFR+B2AuIt0Mt+UVsWUEMeAI1fGBLN0ZaFfQ0c/HQM5jlz0Kp0ToPXOcx/l4gCWIwA/E6Euo8fABU2gWN1nmjdd5onTda543WeaMGjxSDAQIMANHDYvwVeewXAAAAAElFTkSuQmCC"},"187d":function(t,e,s){"use strict";s("f670")},f670:function(t,e,s){}}]);