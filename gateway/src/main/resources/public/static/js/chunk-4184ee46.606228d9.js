(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4184ee46"],{2178:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[n("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-user-select":!1,title:"新增客户数排行","module-type":"ranking"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),n("div",{staticClass:"content"},[n("div",{staticClass:"content-title"},[t._v("新增客户数排行（按负责人、创建时间统计）")]),t._v(" "),n("div",{directives:[{name:"empty",rawName:"v-empty",value:0===t.list.length,expression:"list.length === 0"}],staticClass:"axis-content",attrs:{"xs-empty-text":"暂无排行"}},[n("div",{attrs:{id:"axismain"}})]),t._v(" "),n("div",{staticClass:"table-content"},[n("div",{staticClass:"handle-bar"},[n("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),n("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,"cell-class-name":t.cellClassName,stripe:t.WKConfig.tableStyle.stripe,height:"400","highlight-current-row":""},on:{"row-click":t.handleRowClick}},[n("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"公司总排名"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.$index+1)+"\n          ")]}}])}),t._v(" "),t._l(t.fieldList,(function(t,e){return n("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,"show-overflow-tooltip":""}})}))],2)],1)]),t._v(" "),n("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},o=[],r=(n("a434"),n("b0c0"),n("26b0")),i=n("b80b"),s=n("313e"),c=n("a475"),u=n("2602"),p={name:"RankingAddCustomerStatistics",mixins:[r["a"],i["a"]],data:function(){return{postParams:{},detailFields:[{name:"count",fieldType:"customerRank",list:[{formType:"user",name:"ownerUserId",type:3,values:[]}],request:u["d"],params:null}]}},computed:{},mounted:function(){this.fieldList=[{field:"realname",name:"创建人"},{field:"deptName",name:"部门"},{field:"count",name:"新增客户数（个）"}],this.initAxis()},methods:{getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,Object(c["d"])(t).then((function(t){e.loading=!1,e.list=t.data||[];for(var n=[],a=[],o=t.data.length>10?10:t.data.length,r=0;r<o;r++){var i=t.data[r];n.splice(0,0,parseFloat(i.count)),a.splice(0,0,i.realname)}e.axisOption.yAxis[0].data=a,e.axisOption.series[0].data=n,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},initAxis:function(){this.chartObj=s["b"](document.getElementById("axismain")),this.axisOption.tooltip.formatter="{b} : {c}个",this.axisOption.xAxis[0].name="（个）",this.chartObj.setOption(this.axisOption,!0)},exportClick:function(){this.requestExportInfo(c["e"],this.postParams)}}},d=p,l=(n("ac14"),n("2877")),h=Object(l["a"])(d,a,o,!1,null,"108f36aa",null);e["default"]=h.exports},2602:function(t,e,n){"use strict";n.d(e,"r",(function(){return o})),n.d(e,"s",(function(){return r})),n.d(e,"t",(function(){return i})),n.d(e,"o",(function(){return s})),n.d(e,"m",(function(){return c})),n.d(e,"n",(function(){return u})),n.d(e,"c",(function(){return p})),n.d(e,"f",(function(){return d})),n.d(e,"g",(function(){return l})),n.d(e,"h",(function(){return h})),n.d(e,"u",(function(){return b})),n.d(e,"v",(function(){return f})),n.d(e,"x",(function(){return m})),n.d(e,"a",(function(){return T})),n.d(e,"b",(function(){return C})),n.d(e,"i",(function(){return j})),n.d(e,"j",(function(){return y})),n.d(e,"p",(function(){return g})),n.d(e,"q",(function(){return O})),n.d(e,"l",(function(){return x})),n.d(e,"k",(function(){return R})),n.d(e,"d",(function(){return F})),n.d(e,"w",(function(){return U})),n.d(e,"e",(function(){return v}));var a=n("b775");function o(t){return Object(a["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(a["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(a["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(a["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(a["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(a["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(a["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(a["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(a["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(a["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(a["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(a["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(a["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(a["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function R(t){return Object(a["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function F(t){return Object(a["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function U(t){return Object(a["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(a["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"26b0":function(t,e,n){"use strict";var a=n("5530"),o=n("df55"),r=n("a347"),i=n.n(r);e["a"]={data:function(){return{loading:!1,axisOption:{color:["#1890ff"],toolbox:{showTitle:!1,feature:{saveAsImage:{pixelRatio:2}}},tooltip:{textStyle:{color:i.a.colorBlack,fontWeight:i.a.axisLabelFontWeight},trigger:"axis",formatter:"{b} : {c}元",axisPointer:{type:"shadow"}}},postParams:{},list:[],fieldList:[]}},mixins:[o["a"]],components:{},props:{},computed:{},watch:{},mounted:function(){this.axisOption=Object(a["a"])(Object(a["a"])({},this.axisOption),{},{grid:o["a"].data().chartDefaultOptions.grid,xAxis:[Object(a["a"])(Object(a["a"])({},o["a"].data().chartXAxisStyle),{},{name:"（元）"})],yAxis:[Object(a["a"])(Object(a["a"])({},o["a"].data().chartYAxisStyle),{},{axisLine:{show:!0},type:"category"})],series:[{type:"bar",label:o["a"].data().chartDefaultBase.label,barMaxWidth:20,data:[]}]})},methods:{},deactivated:function(){}}},a09f:function(t,e,n){},a475:function(t,e,n){"use strict";n.d(e,"f",(function(){return o})),n.d(e,"g",(function(){return r})),n.d(e,"l",(function(){return i})),n.d(e,"m",(function(){return s})),n.d(e,"r",(function(){return c})),n.d(e,"s",(function(){return u})),n.d(e,"j",(function(){return p})),n.d(e,"k",(function(){return d})),n.d(e,"d",(function(){return l})),n.d(e,"e",(function(){return h})),n.d(e,"b",(function(){return b})),n.d(e,"c",(function(){return f})),n.d(e,"p",(function(){return m})),n.d(e,"q",(function(){return T})),n.d(e,"n",(function(){return C})),n.d(e,"o",(function(){return j})),n.d(e,"h",(function(){return y})),n.d(e,"i",(function(){return g})),n.d(e,"a",(function(){return O}));var a=n("b775");function o(t){return Object(a["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(a["a"])({url:"biRanking/contractRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biRanking/receivablesRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"biRanking/contractCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(a["a"])({url:"biRanking/productCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"biRanking/productCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(a["a"])({url:"biRanking/customerCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(a["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(a["a"])({url:"biRanking/contactsCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(a["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(a["a"])({url:"biRanking/recordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(a["a"])({url:"biRanking/customerRecordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(a["a"])({url:"biRanking/customerRecordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(a["a"])({url:"biRanking/travelCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(a["a"])({url:"biRanking/travelCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(a["a"])({url:"crmBiSearch/searchContactsPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},ac14:function(t,e,n){"use strict";n("a09f")}}]);