(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f032a66"],{"59e4":function(t,e,a){},"9d36":function(t,e,a){"use strict";a("59e4")},ec36:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:t.filterTitle,"module-type":"portrait"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),a("div",{staticClass:"content"},[t._m(0),t._v(" "),a("div",{staticClass:"table-content"},[a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:"400","highlight-current-row":"","cell-class-name":t.cellClassName},on:{"row-click":t.handleRowClick}},t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{"min-width":0==e?180:100,prop:t.field,label:t.name,"show-overflow-tooltip":""}})})))],1)]),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,"crm-type":"customer",placeholder:t.reportData.placeholder,request:t.reportData.request,params:t.reportData.params},on:{"update:show":function(e){t.reportListShow=e}}})],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"axis-content"},[a("div",{attrs:{id:"axismain"}})])}],r=a("3835"),l=a("5530"),o=(a("14d9"),a("b0c0"),a("a9e3"),a("2602")),n=a("df55"),p=a("313e"),c=a("27c4"),u=a("2a64"),h={name:"CustomerPortrayalStatistics",components:{ReportList:u["a"]},mixins:[n["a"]],props:{},data:function(){return{loading:!1,axisOption:null,list:[],type:"",postParams:{},fieldList:[],reportListShow:!1,reportData:{title:"",placeholder:"",request:null,params:null}}},computed:{filterTitle:function(){return{source:"客户来源分析",industry:"客户行业分析",level:"客户级别分析"}[this.type]||""}},created:function(){this.type=this.$route.params.type},mounted:function(){this.initAxis()},beforeRouteUpdate:function(t,e,a){this.type=t.params.type,this.getDataList(this.postParams),a()},methods:{getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,t.type_analyse=this.type,Object(c["b"])(t).then((function(t){e.loading=!1;for(var a=[],s=[],i=[],r=[{name:"所有客户（个）",deal:!1},{name:"成交客户（个）",deal:!0}],l=[{field:"name",name:{source:"客户来源",industry:"客户行业",level:"客户级别"}[e.type]}],o=0;o<t.data.length;o++){var n=t.data[o];a.push({name:n.type,value:n.allCustomer}),s.push({name:n.type,value:n.dealCustomer}),i.push(n[e.type]);var p="value"+o;l.length<=t.data.length&&l.push({field:p,name:n.type});for(var c=["allCustomer","dealCustomer"],u=0;u<c.length;u++){var h=c[u];r[u][p]=n[h]}}e.axisOption.legend.data=i,e.axisOption.series[0].data=a,e.axisOption.series[1].data=s,e.chartObj.setOption(e.axisOption,!0),e.fieldList=l,e.list=r})).catch((function(){e.loading=!1}))},initAxis:function(){this.chartObj=p["b"](document.getElementById("axismain")),this.axisOption=this.getChartOptione(),this.chartObj.setOption(this.axisOption,!0)},getChartOptione:function(){return{title:[{text:"全部客户",x:"20%",bottom:"25"},{text:"成交客户",x:"70%",bottom:"25"}],color:this.chartColors,toolbox:this.toolbox,tooltip:{trigger:"item",formatter:"{b} : {c}"},legend:Object(l["a"])(Object(l["a"])({},this.chartDefaultOptions.legend),{},{x:"center",y:"bottom",type:"scroll",data:[]}),series:[{name:"全部客户",type:"pie",label:this.chartDefaultBase.label,radius:["35%","50%"],center:["25%","50%"],data:[]},{name:"成交客户",type:"pie",label:this.chartDefaultBase.label,radius:["35%","50%"],center:["75%","50%"],data:[]}]}},cellClassName:function(t){t.row,t.column,t.rowIndex;var e=t.columnIndex;return e?"can-visit--underline":""},handleRowClick:function(t,e,a){if("name"!=e.property){this.reportData.title="成交客户（个）"===t.name?"".concat(e.label,"（成交客户）"):"".concat(e.label,"（所有客户）"),this.reportData.request=o["d"];var s="";if(this.postParams.padDate){var i=this.postParams.startDate.split("-"),l=Object(r["a"])(i,1),n=l[0];s="".concat(Number(n)+1,"-01-01")}var p="custom"==this.postParams.dateFilter?["".concat(this.postParams.startDate),this.postParams.endDate||s]:[this.postParams.dateFilter],c={search:"",dataType:this.postParams.dataType,type:2};0==this.postParams.dataType&&(c.userList=this.postParams.userList,c.deptList=this.postParams.deptList),c.searchList=[{formType:"datetime",name:"createTime",type:14,values:p}],t.deal&&c.searchList.push({formType:"dealStatus",name:"dealStatus",type:1,values:[1]});var u="其他"==e.label?"":e.label;"industry"===this.type&&c.searchList.push({formType:"select",name:"industry",type:1,values:[u]}),"source"===this.type&&c.searchList.push({formType:"select",name:"source",type:1,values:[u]}),"level"===this.type&&c.searchList.push({formType:"select",name:"level",type:1,values:[u]}),this.reportData.params=c,this.reportListShow=!0}}}},d=h,m=(a("9d36"),a("2877")),f=Object(m["a"])(d,s,i,!1,null,"68960360",null);e["default"]=f.exports}}]);