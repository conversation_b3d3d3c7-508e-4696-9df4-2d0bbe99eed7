(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f1dfbea6"],{"08c2":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),t._v(" "),a("span",{staticClass:"text"},[t._v(t._s(t.title))])]),t._v(" "),t.showFilterView?[t.showYearSelect?t._e():a("time-type-select",{on:{change:t.timeTypeChange}}),t._v(" "),t.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":t.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.yearValue,callback:function(e){t.yearValue=e},expression:"yearValue"}}):t._e(),t._v(" "),t._t("after-time"),t._v(" "),t.showSimpleChoose?[t.showUserSelect&&t.showDeptSelect?a("el-select",{model:{value:t.simpleChooseType,callback:function(e){t.simpleChooseType=e},expression:"simpleChooseType"}},t._l(t.simpleOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))):t._e(),t._v(" "),1===t.simpleChooseType&&t.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:t.structuresSelectValue,callback:function(e){t.structuresSelectValue=e},expression:"structuresSelectValue"}}):t._e(),t._v(" "),2===t.simpleChooseType&&t.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:t.userSelectValue,callback:function(e){t.userSelectValue=e},expression:"userSelectValue"}}):t._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:t.dataTypeOptions,"user-checked-data":t.filterValue.userList,"dep-checked-data":t.filterValue.deptList,width:250},on:{select:t.radioMenuSelect},model:{value:t.filterDataType,callback:function(e){t.filterDataType=e},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.avatarData.realname,callback:function(e){t.$set(t.avatarData,"realname",e)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),t.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:t.businessStatusValue,callback:function(e){t.businessStatusValue=e},expression:"businessStatusValue"}},t._l(t.businessOptions,(function(t){return a("el-option",{key:t.flowId,attrs:{label:t.flowName,value:t.flowId}})}))):t._e(),t._v(" "),t.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:t.productValue,callback:function(e){t.productValue=e},expression:"productValue"}}):t._e(),t._v(" "),t.showCustomSelect?a("el-select",{on:{change:t.customSelectChange},model:{value:t.customValue,callback:function(e){t.customValue=e},expression:"customValue"}},t._l(t.customOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}))):t._e(),t._v(" "),t._t("append"),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(e){return t.emitFilter(e)}}},[t._v("查询")]),t._v(" "),t._t("default")]:t._e()],2)},i=[],s=a("5530"),o=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),l=a("ea20"),r=a("657f"),c=a("bfba"),u=a("8f81"),p=a("83f1"),d=a("2f62"),h={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:p["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(s["a"])(Object(s["a"])({},Object(d["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var t=(this.filterValue.userList||[]).map((function(t){return t.realname})),e=(this.filterValue.deptList||[]).map((function(t){return t.name}));return{realname:t.concat(e).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var t=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){t.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(t){var e=this;Object(o["r"])().then((function(a){e.businessOptions=a.data||[],e.businessOptions.length>0&&(e.businessStatusValue=e.businessOptions[0].flowId),t(!0)})).catch((function(){e.$emit("error")}))},getProductCategoryIndex:function(){var t=this;Object(l["T"])({type:"tree"}).then((function(e){t.productOptions=e.data})).catch((function(){}))},radioMenuSelect:function(t,e){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=e.users,this.filterValue.deptList=e.strucs)},timeTypeChange:function(t){this.timeTypeValue=t},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var t=this,e={};this.showSimpleChoose?1===this.simpleChooseType?e.deptList=(this.structuresSelectValue||"").split(",").filter((function(t){return!!t})):e.userList=(this.userSelectValue||"").split(",").filter((function(t){return!!t})):"custom"!==this.filterValue.dataType?e.dataType=this.filterValue.dataType:(e.dataType=0,e.deptList=(this.filterValue.deptList||[]).map((function(t){return t.deptId})),e.userList=(this.filterValue.userList||[]).map((function(t){return t.userId}))),this.showYearSelect?(e.dateFilter="custom",e.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(e.startDate=this.timeTypeValue.startTime,e.endDate=this.timeTypeValue.endTime,e.dateFilter="custom"):e.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(e.typeId=this.businessStatusValue,e.businessItem=this.businessOptions.map((function(e){if(e.flowId===t.businessStatusValue)return e}))),this.showProductSelect&&(e.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",e)}}},f=h,m=(a("965d"),a("2877")),b=Object(m["a"])(f,n,i,!1,null,"6d7c8f9a",null);e["a"]=b.exports},1845:function(t,e,a){"use strict";a("a5fa")},"26b0":function(t,e,a){"use strict";var n=a("5530"),i=a("df55"),s=a("a347"),o=a.n(s);e["a"]={data:function(){return{loading:!1,axisOption:{color:["#1890ff"],toolbox:{showTitle:!1,feature:{saveAsImage:{pixelRatio:2}}},tooltip:{textStyle:{color:o.a.colorBlack,fontWeight:o.a.axisLabelFontWeight},trigger:"axis",formatter:"{b} : {c}元",axisPointer:{type:"shadow"}}},postParams:{},list:[],fieldList:[]}},mixins:[i["a"]],components:{},props:{},computed:{},watch:{},mounted:function(){this.axisOption=Object(n["a"])(Object(n["a"])({},this.axisOption),{},{grid:i["a"].data().chartDefaultOptions.grid,xAxis:[Object(n["a"])(Object(n["a"])({},i["a"].data().chartXAxisStyle),{},{name:"（元）"})],yAxis:[Object(n["a"])(Object(n["a"])({},i["a"].data().chartYAxisStyle),{},{axisLine:{show:!0},type:"category"})],series:[{type:"bar",label:i["a"].data().chartDefaultBase.label,barMaxWidth:20,data:[]}]})},methods:{},deactivated:function(){}}},"965d":function(t,e,a){"use strict";a("c558")},a475:function(t,e,a){"use strict";a.d(e,"f",(function(){return i})),a.d(e,"g",(function(){return s})),a.d(e,"l",(function(){return o})),a.d(e,"m",(function(){return l})),a.d(e,"r",(function(){return r})),a.d(e,"s",(function(){return c})),a.d(e,"j",(function(){return u})),a.d(e,"k",(function(){return p})),a.d(e,"d",(function(){return d})),a.d(e,"e",(function(){return h})),a.d(e,"b",(function(){return f})),a.d(e,"c",(function(){return m})),a.d(e,"p",(function(){return b})),a.d(e,"q",(function(){return g})),a.d(e,"n",(function(){return y})),a.d(e,"o",(function(){return x})),a.d(e,"h",(function(){return v})),a.d(e,"i",(function(){return T})),a.d(e,"a",(function(){return C}));var n=a("b775");function i(t){return Object(n["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biRanking/contractRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"biRanking/receivablesRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(n["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biRanking/contractCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"biRanking/productCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"biRanking/productCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(n["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(n["a"])({url:"biRanking/customerCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(n["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(n["a"])({url:"biRanking/contactsCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(n["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(n["a"])({url:"biRanking/recordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(n["a"])({url:"biRanking/customerRecordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(n["a"])({url:"biRanking/customerRecordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(n["a"])({url:"biRanking/travelCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(n["a"])({url:"biRanking/travelCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(n["a"])({url:"crmBiSearch/searchContactsPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},a5fa:function(t,e,a){},c558:function(t,e,a){},d517:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-user-select":!1,title:"产品销量排行","module-type":"ranking"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),a("div",{staticClass:"content"},[a("div",{staticClass:"content-title"},[t._v("产品销量排行（按签订的审核通过的合同关联产品统计）")]),t._v(" "),a("div",{directives:[{name:"empty",rawName:"v-empty",value:0===t.list.length,expression:"list.length === 0"}],staticClass:"axis-content",attrs:{"xs-empty-text":"暂无排行"}},[a("div",{attrs:{id:"axismain"}})]),t._v(" "),a("div",{staticClass:"table-content"},[a("div",{staticClass:"handle-bar"},[a("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:"400","highlight-current-row":""}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"公司总排名"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.$index+1)+"\n          ")]}}])}),t._v(" "),t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,"show-overflow-tooltip":""}})}))],2)],1)])],1)},i=[],s=(a("a434"),a("b0c0"),a("26b0")),o=a("313e"),l=a("a475"),r={name:"RankingProductStatistics",mixins:[s["a"]],data:function(){return{}},computed:{},mounted:function(){this.fieldList=[{field:"realname",name:"员工"},{field:"deptName",name:"部门"},{field:"productCount",name:"产品销量"}],this.initAxis()},methods:{getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,Object(l["j"])(t).then((function(t){e.loading=!1,e.list=t.data||[];for(var a=[],n=[],i=t.data.length>10?10:t.data.length,s=0;s<i;s++){var o=t.data[s];a.splice(0,0,parseFloat(o.productCount)),n.splice(0,0,o.realname)}e.axisOption.yAxis[0].data=n,e.axisOption.series[0].data=a,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},initAxis:function(){this.chartObj=o["b"](document.getElementById("axismain")),this.axisOption.tooltip.formatter="{b} : {c}",this.axisOption.xAxis[0].name="",this.chartObj.setOption(this.axisOption,!0)},exportClick:function(){this.requestExportInfo(l["k"],this.postParams)}}},c=r,u=(a("1845"),a("2877")),p=Object(u["a"])(c,n,i,!1,null,"3ef7f3de",null);e["default"]=p.exports},df55:function(t,e,a){"use strict";var n=a("5530"),i=(a("d3b7"),a("08c2")),s=a("7a1a"),o=a("ed08"),l=a("a347"),r=a.n(l);e["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},textColor:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{lineStyle:{color:r.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:r.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:i["a"]},props:{},computed:{},watch:{},mounted:function(){var t=this;this.debouncedResize=Object(s["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",t.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(t){this.pageData.limit=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(t){this.pageData.page=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(t,e){return new Promise((function(a,n){t(e).then((function(t){Object(o["g"])(t),a&&a(t)})).catch((function(t){n&&n(t)}))}))},getChartYAxisStyle:function(t){var e=Object(o["D"])(this.chartYAxisStyle);if(!t)return e;for(var a in t){var i=e[a],s=t[a];e[a]=i?Object(n["a"])(Object(n["a"])({},i),s):s}return e}},deactivated:function(){}}}}]);