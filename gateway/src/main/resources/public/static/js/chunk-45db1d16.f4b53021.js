(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-45db1d16"],{"09db":function(t,e,a){"use strict";a("1779")},"0f1c":function(t,e,a){"use strict";a("f5e8")},1779:function(t,e,a){},"253f":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"system-customer main"},[a("xr-header",{attrs:{label:"业绩目标设置"}},[a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{slot:"otherLabel","data-type":"24","data-id":"226"},slot:"otherLabel"})]),t._v(" "),a("div",{staticClass:"customer-content"},[a("div",{staticClass:"main-body"},[a("task-set-statistics")],1)])],1)},s=[],l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("div",{staticClass:"tabs-bar"},[a("el-tabs",{on:{"tab-click":t.tabTypeClick},model:{value:t.tabType,callback:function(e){t.tabType=e},expression:"tabType"}},[a("el-tab-pane",{attrs:{label:"部门目标设置",name:"department"}}),t._v(" "),a("el-tab-pane",{attrs:{label:"员工目标设置",name:"user"}})],1)],1),t._v(" "),a("flexbox",{staticClass:"handle-bar",attrs:{justify:"space-between"}},[a("flexbox",[a("el-date-picker",{attrs:{clearable:!1,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.dateSelect,callback:function(e){t.dateSelect=e},expression:"dateSelect"}}),t._v(" "),a("el-select",{model:{value:t.typeSelect,callback:function(e){t.typeSelect=e},expression:"typeSelect"}},t._l([{label:"合同金额",value:1},{label:"回款金额",value:2}],(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))),t._v(" "),a("wk-dept-dialog-select",{attrs:{radio:""},on:{change:t.structuresValueChange},model:{value:t.structuresSelectValue,callback:function(e){t.structuresSelectValue=e},expression:"structuresSelectValue"}}),t._v(" "),"user"===t.tabType?a("wk-user-dialog-select",{attrs:{disabled:!t.userOptions,props:{showDisableUser:!!t.userOptions,disableUserList:t.userOptions,disableUserLabel:"员工"},radio:""},model:{value:t.userSelectValue,callback:function(e){t.userSelectValue=e},expression:"userSelectValue"}}):t._e(),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.handleClick("search")}}},[t._v("搜索")])],1),t._v(" "),t.isEdit&&0!==t.list.length?t._e():a("div",{staticStyle:{"flex-shrink":"0"}},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.addViewShow=!0}}},[t._v("设置目标")]),t._v(" "),t.list.length?a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.handleClick("edit")}}},[t._v("编辑")]):t._e(),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary"},nativeOn:{click:function(e){t.handleClick("import")}}},[t._v("导入")]),t._v(" "),t.list.length?a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.handleClick("export")}}},[t._v("导出")]):t._e()],1),t._v(" "),t.isEdit&&t.list.length>0?a("div",{staticStyle:{display:"flex",float:"right"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.handleClick("save")}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.handleClick("cancel")}}},[t._v("取消")])],1):t._e()],1),t._v(" "),a("div",{staticClass:"content"},[a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",id:"task-set-table",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:t.tableHeight,"cell-style":t.cellStyle,"highlight-current-row":""}},t._l(t.fieldList,(function(e,i){return a("el-table-column",{key:i,attrs:{fixed:0==i,prop:e.field,label:e.name,"show-overflow-tooltip":"",width:"150"},scopedSlots:t._u([{key:"default",fn:function(s){return["name"===e.field&&s.$index!==t.list.length-1?a("div",{staticClass:"table-show-item"},[t.isEdit?a("i",{staticClass:"wk wk-delete",class:{"is-show":t.isEdit},on:{click:function(e){t.deleteAchievement(s.row,s.$index)}}}):t._e(),t._v(t._s(s.row[e.field])+"\n          ")]):"name"!==e.field&&"yeartarget"!==e.field&&"first"!==e.field&&"second"!==e.field&&"third"!==e.field&&"fourth"!==e.field&&t.isEdit&&s.$index!==t.list.length-1?a("el-input",{attrs:{type:"number"},on:{input:function(e){t.handleInputEdit("change",s)},blur:function(e){t.handleInputEdit("blur",s)}},model:{value:s.row[e.field],callback:function(a){t.$set(s.row,e.field,a)},expression:"scope.row[item.field]"}}):a("div",{staticClass:"table-show-item"},[0===i?[t._v("\n              "+t._s(s.row[e.field])+"\n            ")]:[t._v("\n              "+t._s(t._f("separator")(s.row[e.field]))+"\n            ")]],2)]}}])})})))],1),t._v(" "),a("add-goal",{attrs:{visible:t.addViewShow,type:t.tabType},on:{"update:visible":function(e){t.addViewShow=e},success:t.tabTypeClick}}),t._v(" "),a("import-goal",{attrs:{show:t.importGoalShow},on:{close:function(e){t.importGoalShow=!1},success:t.tabTypeClick}})],1)},n=[],r=a("5530"),o=(a("99af"),a("4de4"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("b680"),a("d3b7"),a("ac1f"),a("25f0"),a("5319"),a("2934")),c=a("ea20"),u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{visible:t.visible,"append-to-body":!0,"close-on-click-modal":!1,width:"800px","custom-class":"no-padding-dialog"},on:{close:t.closeClick}},[a("span",{staticClass:"el-dialog__title",attrs:{slot:"title"},slot:"title"},[t._v("设置目标"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"227"}})]),t._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"add-goal"},[a("flexbox",{staticClass:"select-wrapper"},[a("flexbox-item",{staticClass:"select-item"},[a("flexbox",[a("span",{staticClass:"select-label"},[t._v(t._s(t.rangeLabel))]),t._v(" "),"user"==t.type?a("wk-user-dialog-select",{staticClass:"select-condition",attrs:{radio:!1,placeholder:"选择人员"},model:{value:t.selectDepOrUser,callback:function(e){t.selectDepOrUser=e},expression:"selectDepOrUser"}}):a("wk-dept-dialog-select",{staticClass:"select-condition",attrs:{radio:!1},model:{value:t.selectDepOrUser,callback:function(e){t.selectDepOrUser=e},expression:"selectDepOrUser"}})],1)],1),t._v(" "),a("flexbox-item",{staticClass:"select-item"},[a("span",{staticClass:"select-label"},[t._v("考核指标")]),t._v(" "),a("el-select",{staticClass:"select-condition",model:{value:t.typeSelect,callback:function(e){t.typeSelect=e},expression:"typeSelect"}},t._l([{label:"合同金额",value:1},{label:"回款金额",value:2}],(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})))],1)],1),t._v(" "),a("div",{staticClass:"add-goal__handle"},[a("el-date-picker",{attrs:{clearable:!1,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.dateSelect,callback:function(e){t.dateSelect=e},expression:"dateSelect"}}),t._v(" "),a("span",{staticClass:"handle-label"},[t._v("年度业务目标是 ¥ ")]),t._v(" "),a("el-input",{staticClass:"total-input",attrs:{type:"number"},on:{input:function(e){t.inputChange("total")}},model:{value:t.totalGoal,callback:function(e){t.totalGoal=e},expression:"totalGoal"}}),t._v(" "),a("span",{staticClass:"handle-label"},[t._v("元")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.averageClick}},[t._v("平均分配到每月")])],1),t._v(" "),a("flexbox",{staticClass:"add-goal__set"},t._l(t.quarterList,(function(e,i){return a("flexbox-item",{key:i,staticClass:"set-item"},[a("div",{staticClass:"set-item__hd"},[a("p",[t._v(t._s(e.title))]),t._v(" "),a("el-input",{attrs:{disabled:"",type:"number"},model:{value:e.all,callback:function(a){t.$set(e,"all",a)},expression:"item.all"}})],1),t._v(" "),a("div",{staticClass:"set-item__bd"},[a("div",{staticClass:"set-item-wrapper"},[a("span",{staticClass:"set-item__label"},[t._v(t._s(t.getSetLabe(i,0)))]),t._v(" "),a("el-input",{staticClass:"set-item__input",attrs:{type:"number"},on:{input:function(a){t.inputChange("sub",e,"first")},blur:function(e){t.inputBlur(i)}},model:{value:e.first,callback:function(a){t.$set(e,"first",a)},expression:"item.first"}})],1),t._v(" "),a("div",{staticClass:"set-item-wrapper"},[a("span",{staticClass:"set-item__label"},[t._v(t._s(t.getSetLabe(i,1)))]),t._v(" "),a("el-input",{staticClass:"set-item__input",attrs:{type:"number"},on:{input:function(a){t.inputChange("sub",e,"second")},blur:function(e){t.inputBlur(i)}},model:{value:e.second,callback:function(a){t.$set(e,"second",a)},expression:"item.second"}})],1),t._v(" "),a("div",{staticClass:"set-item-wrapper"},[a("span",{staticClass:"set-item__label"},[t._v(t._s(t.getSetLabe(i,2)))]),t._v(" "),a("el-input",{staticClass:"set-item__input",attrs:{type:"number"},on:{input:function(a){t.inputChange("sub",e,"third")},blur:function(e){t.inputBlur(i)}},model:{value:e.third,callback:function(a){t.$set(e,"third",a)},expression:"item.third"}})],1)])])})))],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.sureClick}},[t._v("确定")]),t._v(" "),a("el-button",{on:{click:t.closeClick}},[t._v("取消")])],1)])},d=[],p=a("8f81"),h=a("bfba"),f=a("c1df"),m=a.n(f),v=a("da92"),b={name:"AddGoal",components:{WkDeptDialogSelect:h["a"],WkUserDialogSelect:p["a"]},props:{visible:{type:Boolean,default:!1},type:String},data:function(){return{loading:!1,typeSelect:1,dateSelect:"",selectDepOrUser:[],totalGoal:"0",quarterList:[]}},computed:{rangeLabel:function(){return"user"===this.type?"考核人员":"考核部门"}},watch:{visible:function(t){t&&this.resetData()}},mounted:function(){},beforeDestroy:function(){},methods:{sureClick:function(){var t=this;if(this.selectDepOrUser.length){for(var e={type:"user"==this.type?3:2,year:this.dateSelect,status:this.typeSelect,yeartarget:this.totalGoal},a=0;a<this.quarterList.length;a++){var i=this.quarterList[a];e[this.getUploadKey(a,0)]=i.first,e[this.getUploadKey(a,1)]=i.second,e[this.getUploadKey(a,2)]=i.third}e.objIds=this.selectDepOrUser,this.loading=!0,Object(c["d"])(e).then((function(e){t.loading=!1,t.$message.success("操作成功"),t.$emit("success"),t.closeClick()})).catch((function(){t.loading=!1}))}else this.$message.error("请选择考核".concat("user"==this.type?"员工":"部门"))},inputChange:function(t,e,a){"total"==t?this.totalGoal?this.totalGoal=this.totalGoal.replace(/^(\-)*(\d+)\.(\d\d).*$/,"$1$2.$3"):this.totalGoal="0":e[a]?e[a]=e[a].replace(/^(\-)*(\d+)\.(\d\d).*$/,"$1$2.$3"):e[a]="0"},inputBlur:function(t){var e=this.quarterList[t];e.all=(parseFloat(e.first)+parseFloat(e.second)+parseFloat(e.third)).toFixed(2);for(var a=0,i=0;i<this.quarterList.length;i++){var s=this.quarterList[i];a+=parseFloat(s.all)}this.totalGoal=a?a.toFixed(2):0},averageClick:function(){for(var t=this.totalGoal?v["a"].divide(this.totalGoal,4).toFixed(2):0,e=v["a"].times(t,4),a=0;a<this.quarterList.length;a++){var i=this.quarterList[a],s=v["a"].divide(t,3).toFixed(2);if(i.first=s,i.second=s,i.third=v["a"].minus(t,v["a"].plus(s,s)).toFixed(2),i.all=t,a==this.quarterList.length-1&&e!=this.totalGoal){var l=v["a"].minus(this.totalGoal,v["a"].times(t,3),v["a"].times(s,2)).toFixed(2);i.third=l,i.all=v["a"].plus(i.first,i.second,i.third)}}},getSetLabe:function(t,e){return[["1月份","2月份","3月份"],["4月份","5月份","6月份"],["7月份","8月份","9月份"],["10月份","11月份","12月份"]][t][e]},getUploadKey:function(t,e){return[["january","february","march"],["april","may","june"],["july","august","september"],["october","november","december"]][t][e]},closeClick:function(){this.$emit("update:visible",!1),this.$emit("close")},resetData:function(){this.typeSelect=1,this.dateSelect=m()().year().toString(),this.selectDepOrUser=[],this.totalGoal="0",this.quarterList=[{title:"第一季度",all:0,first:0,second:0,third:0},{title:"第二季度",all:0,first:0,second:0,third:0},{title:"第三季度",all:0,first:0,second:0,third:0},{title:"第四季度",all:0,first:0,second:0,third:0}]}}},g=b,y=(a("ff5d"),a("2877")),_=Object(y["a"])(g,u,d,!1,null,"6bb8d32c",null),w=_.exports,k=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{visible:t.showDialog,"append-to-body":!0,"show-close":t.showCancel,"close-on-click-modal":!1,title:"导入业绩目标",width:"750px"},on:{"update:visible":function(e){t.showDialog=e},close:t.closeView}},[a("div",{staticClass:"dialog-body"},[a("el-steps",{attrs:{active:t.stepsActive,simple:""}},t._l(t.stepList,(function(t,e){return a("el-step",{key:e,attrs:{title:t.title,icon:t.icon,status:t.status}})}))),t._v(" "),1==t.stepsActive?a("div",[a("div",{staticClass:"sections"},[a("div",{staticClass:"sections__title"},[t._v("一、请选择目标对象（必填）")]),t._v(" "),a("div",{staticClass:"content"},[a("div",{staticClass:"user-cell"},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.achievementType,callback:function(e){t.achievementType=e},expression:"achievementType"}},t._l([{name:"部门",value:1},{name:"员工",value:2}],(function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})})))],1)])]),t._v(" "),a("div",{staticClass:"sections"},[a("div",{staticClass:"sections__title"},[t._v("二、请选择需要导入的文件")]),t._v(" "),a("div",{staticClass:"content"},[a("flexbox",{staticClass:"file-select"},[a("el-input",{attrs:{disabled:!0},model:{value:t.file.name,callback:function(e){t.$set(t.file,"name",e)},expression:"file.name"}}),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.selectFile}},[t._v("选择文件")])],1)],1),t._v(" "),a("div",{staticClass:"download",on:{click:t.download}},[t._v("\n          点击下载《业绩目标导入模板》")])])]):2==t.stepsActive?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"sections",attrs:{"element-loading-text":"数据导入中","element-loading-spinner":"el-icon-loading"}}):3==t.stepsActive?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"sections"},[a("div",{staticClass:"result-info"},[a("i",{staticClass:"wk wk-success result-info__icon"}),t._v(" "),a("p",{staticClass:"result-info__des"},[t._v("数据导入完成")]),t._v(" "),a("p",{staticClass:"result-info__detail"},[t._v("导入总数据"),a("span",{staticClass:"result-info__detail--all"},[t._v(t._s(t.resultData.totalSize))]),t._v("条，导入成功"),a("span",{staticClass:"result-info__detail--suc"},[t.resultData?[t._v(t._s(t.resultData.totalSize-(t.resultData.errSize||0)))]:t._e()],2),t._v("条，导入失败"),a("span",{staticClass:"result-info__detail--err"},[t._v(t._s(t.resultData.errSize||0))]),t._v("条")]),t._v(" "),t.resultData&&t.resultData.errSize>0?a("el-button",{staticClass:"result-info__btn--err",staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:t.downloadErrData}},[t._v("下载错误数据")]):t._e()],1)]):t._e(),t._v(" "),a("input",{ref:"userFileInput",attrs:{id:"import-input-file",type:"file"},on:{change:t.uploadFile}})],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.sureTitle?a("el-button",{attrs:{type:"primary"},on:{click:t.sureClick}},[t._v(t._s(t.sureTitle))]):t._e(),t._v(" "),a("el-button",{class:{"is-hidden":!t.showCancel},on:{click:t.closeView}},[t._v("取消")])],1)])},S=[],C=a("ed08"),x={name:"ImportGoal",components:{},props:{show:{type:Boolean,default:!1}},data:function(){return{loading:!1,showDialog:!1,achievementType:1,file:{name:""},stepsActive:1,stepList:[{icon:"wk wk-upload",title:"上传文件",status:"wait"},{icon:"wk wk-data-import",title:"导入数据",status:"wait"},{icon:"wk wk-success",title:"导入完成",status:"wait"}],resultData:null}},computed:{sureTitle:function(){return{1:"立即导入",2:"",3:"确定"}[this.stepsActive]},showCancel:function(){return 2!=this.stepsActive}},watch:{show:function(t){this.showDialog=t,this.resetData()}},mounted:function(){},methods:{sureClick:function(){var t=this;1==this.stepsActive?"finish"==this.stepList[0].status?(this.stepList[1].status="process",this.stepsActive=2,this.updateFile((function(e){t.stepList[1].status="finish",t.stepsActive=3,e.data&&(t.resultData=e.data,e.data.errSize>0?t.stepList[2].status="error":t.stepList[2].status="finish")}))):this.$message.error("请选择导入文件"):3==this.stepsActive&&this.closeView()},updateFile:function(t){var e=this;this.file.name?(this.loading=!0,Object(c["c"])({file:this.file,type:this.achievementType}).then((function(a){e.loading=!1,t&&t(a),e.$emit("success")})).catch((function(){t&&t(!1),e.loading=!1}))):this.$message.error("请选择导入文件")},downloadErrData:function(){this.getImportError(this.resultData.token)},getImportError:function(t){var e=this;this.loading=!0,Object(c["a"])({token:t}).then((function(t){Object(C["g"])(t),e.loading=!1})).catch((function(){e.loading=!1}))},download:function(){Object(c["b"])({type:this.achievementType}).then((function(t){Object(C["g"])(t)})).catch((function(){}))},selectFile:function(){this.$refs.userFileInput.click()},uploadFile:function(t){var e=t.target.files,a=e[0];Object(C["K"])(a.name)&&(this.file=a,this.stepList[0].status="finish"),t.target.value=""},closeView:function(){this.$emit("close")},resetData:function(){this.file={name:""},this.stepList=[{icon:"wk wk-upload",title:"上传文件",status:"wait"},{icon:"wk wk-data-import",title:"导入数据",status:"wait"},{icon:"wk wk-success",title:"导入完成",status:"wait"}],this.stepsActive=1,this.resultData=null}}},F=x,L=(a("0f1c"),Object(y["a"])(F,k,S,!1,null,"2a1d9092",null)),T=L.exports,D=a("2f62"),j={name:"TaskSetStatistics",components:{AddGoal:w,ImportGoal:T,WkDeptDialogSelect:h["a"],WkUserDialogSelect:p["a"]},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},loading:!1,tableHeight:document.documentElement.clientHeight-280,tabType:"department",dateSelect:"",typeSelect:1,structuresProps:{children:"children",label:"label",value:"id"},deptList:[],structuresSelectValue:"",userOptions:[],userSelectValue:"",isEdit:!1,list:[],fieldList:[{field:"name",name:""},{field:"yeartarget",name:"全年"},{field:"first",name:"第一季度"},{field:"january",name:"1月"},{field:"february",name:"2月"},{field:"march",name:"3月"},{field:"second",name:"第二季度"},{field:"april",name:"4月"},{field:"may",name:"5月"},{field:"june",name:"6月"},{field:"third",name:"第三季度"},{field:"july",name:"7月"},{field:"august",name:"8月"},{field:"september",name:"9月"},{field:"fourth",name:"第四季度"},{field:"october",name:"10月"},{field:"november",name:"11月"},{field:"december",name:"12月"}],addViewShow:!1,importGoalShow:!1}},computed:Object(r["a"])({},Object(D["b"])(["userInfo"])),mounted:function(){var t=this;window.onresize=function(){t.tableHeight=document.documentElement.clientHeight-280},this.dateSelect=m()().year().toString(),this.getDeptList()},methods:{tabTypeClick:function(){this.isEdit=!1,"department"===this.tabType?this.getAhievementList():"user"===this.tabType&&(this.getUserList(),this.getAhievementListForUser())},getAhievementList:function(){var t=this;this.loading=!0,Object(c["f"])({year:this.dateSelect,type:2,status:this.typeSelect,deptId:this.structuresSelectValue}).then((function(e){t.list=e.data.map((function(e){return e.name=e.objName,t.getShowItem(e)})),t.list.length&&t.getSubTotalModel(),t.loading=!1})).catch((function(){t.loading=!1}))},getShowItem:function(t){return t["first"]=this.calculateFirst(t),t["second"]=this.calculateSecond(t),t["third"]=this.calculateThird(t),t["fourth"]=this.calculateFourth(t),t["yeartarget"]=this.calculateAll(t),t},getSubTotalModel:function(){for(var t={name:"目标合计",january:"0.00",february:"0.00",march:"0.00",april:"0.00",may:"0.00",june:"0.00",july:"0.00",august:"0.00",september:"0.00",october:"0.00",november:"0.00",december:"0.00",yeartarget:"0.00",first:"0.00",second:"0.00",third:"0.00",fourth:"0.00",ignore:!0},e=0;e<this.list.length;e++)for(var a=this.list[e],i=0;i<this.fieldList.length;i++){var s=this.fieldList[i];"name"!==s.field&&(t[s.field]=(parseFloat(t[s.field])+parseFloat(a[s.field])).toFixed(2).toString())}this.list.push(t)},handleInputEdit:function(t,e){if("change"===t){var a=e.row[e.column.property]?e.row[e.column.property]:"0.00",i=a.replace(/^(\-)*(\d+)\.(\d\d).*$/,"$1$2.$3");a!==i&&(e.row[e.column.property]=i,this.$set(this.list,e.$index,e.row))}else"blur"===t&&("january"===e.column.property||"february"===e.column.property||"march"===e.column.property?(e.row["first"]=this.calculateFirst(e.row),e.row["yeartarget"]=this.calculateAll(e.row)):"april"===e.column.property||"may"===e.column.property||"june"===e.column.property?(e.row["second"]=this.calculateSecond(e.row),e.row["yeartarget"]=this.calculateAll(e.row)):"july"===e.column.property||"august"===e.column.property||"september"===e.column.property?(e.row["third"]=this.calculateThird(e.row),e.row["yeartarget"]=this.calculateAll(e.row)):"october"!==e.column.property&&"november"!==e.column.property&&"december"!==e.column.property||(e.row["fourth"]=this.calculateFourth(e.row),e.row["yeartarget"]=this.calculateAll(e.row)),"department"===this.tabType&&this.list.length>=2&&this.calculateSubTotal(e))},calculateSubTotal:function(t){for(var e=this.list[this.list.length-1],a="0",i=0;i<this.list.length-1;i++){var s=this.list[i];a=(parseFloat(a)+parseFloat(s[t.column.property])).toFixed(2).toString()}e[t.column.property]=a,"january"===t.column.property||"february"===t.column.property||"march"===t.column.property?(e["first"]=this.calculateFirst(e),e["yeartarget"]=this.calculateAll(e)):"april"===t.column.property||"may"===t.column.property||"june"===t.column.property?(e["second"]=this.calculateSecond(e),e["yeartarget"]=this.calculateAll(e)):"july"===t.column.property||"august"===t.column.property||"september"===t.column.property?(e["third"]=this.calculateThird(e),e["yeartarget"]=this.calculateAll(e)):"october"!==t.column.property&&"november"!==t.column.property&&"december"!==t.column.property||(e["fourth"]=this.calculateFourth(e),e["yeartarget"]=this.calculateAll(e))},calculateFirst:function(t){return(parseFloat(t.january)+parseFloat(t.february)+parseFloat(t.march)).toFixed(2).toString()},calculateSecond:function(t){return(parseFloat(t.april)+parseFloat(t.may)+parseFloat(t.june)).toFixed(2).toString()},calculateThird:function(t){return(parseFloat(t.july)+parseFloat(t.august)+parseFloat(t.september)).toFixed(2).toString()},calculateFourth:function(t){return(parseFloat(t.october)+parseFloat(t.november)+parseFloat(t.december)).toFixed(2).toString()},calculateAll:function(t){return(parseFloat(t.first)+parseFloat(t.second)+parseFloat(t.third)+parseFloat(t.fourth)).toFixed(2).toString()},getDeptList:function(){var t=this;Object(o["m"])({type:"tree"}).then((function(e){t.deptList=e.data,e.data.length>0&&(t.structuresSelectValue=e.data[0].id,t.tabTypeClick())}))},structuresValueChange:function(){"department"===this.tabType?(this.userSelectValue&&(this.userSelectValue="",this.userOptions=null),this.getUserList()):"user"===this.tabType&&(this.userSelectValue="",this.userOptions=null,this.getUserList())},getUserList:function(){var t=this,e={pageType:0};this.structuresSelectValue?(e.deptId=this.structuresSelectValue,Object(o["w"])(e).then((function(e){t.userOptions=e.data.list})).catch((function(){}))):(this.userSelectValue="",this.userOptions=null)},getAhievementListForUser:function(){var t=this;this.loading=!0;var e=this.structuresSelectValue;Object(c["f"])({year:this.dateSelect,type:3,status:this.typeSelect,deptId:e,userId:this.userSelectValue}).then((function(e){t.list=e.data.map((function(e){return e.name=e.objName,t.getShowItem(e)})),t.list.length&&t.getSubTotalModel(),t.loading=!1})).catch((function(){t.loading=!1}))},handleClick:function(t){var e=this;if("search"==t)this.updateAhievementList();else if("edit"==t)this.isEdit=!0;else if("import"==t)this.importGoalShow=!0;else if("export"==t){var a={department:"部门目标",user:"员工目标"}[this.tabType],i="".concat(this.dateSelect," 年").concat(a,".xlsx");Object(C["k"])(i,"task-set-table")}else if("save"==t){this.loading=!0;var s=this.list.filter((function(t,e,a){return!t.ignore}));Object(c["g"])(s).then((function(t){e.$message.success("操作成功"),e.loading=!1,e.isEdit=!1,e.updateAhievementList()})).catch((function(){e.loading=!1}))}else"cancel"==t&&(this.updateAhievementList(),this.isEdit=!1)},updateAhievementList:function(){"department"===this.tabType?this.getAhievementList():"user"===this.tabType&&this.getAhievementListForUser()},cellStyle:function(t){t.row,t.column;var e=t.rowIndex,a=t.columnIndex;return e===this.list.length-1?{backgroundColor:"#FAF9F6"}:1==a||2==a||6==a||10==a||14==a?{backgroundColor:"#E5F4FE",textAlign:"center"}:{textAlign:"center"}},deleteAchievement:function(t,e){var a=this;this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.loading=!0,Object(c["e"])({achievementId:t.achievementId}).then((function(t){a.loading=!1,a.$message.success("操作成功"),a.list.splice(e,1),a.list.pop(),a.list.length&&a.getSubTotalModel()})).catch((function(){a.loading=!1}))})).catch((function(){}))}}},A=j,O=(a("09db"),Object(y["a"])(A,l,n,!1,null,"1bce3186",null)),$=O.exports,E=a("f468"),V={name:"BizGoals",components:{TaskSetStatistics:$,XrHeader:E["a"]},data:function(){return{}},created:function(){},methods:{}},G=V,U=(a("dca4"),Object(y["a"])(G,i,s,!1,null,"4d4b89b3",null));e["default"]=U.exports},"52ad":function(t,e,a){},"53dd":function(t,e,a){},da92:function(t,e,a){"use strict";function i(t,e){return void 0===e&&(e=15),+parseFloat(Number(t).toPrecision(e))}function s(t){var e=t.toString().split(/[eE]/),a=(e[0].split(".")[1]||"").length-+(e[1]||0);return a>0?a:0}function l(t){if(-1===t.toString().indexOf("e"))return Number(t.toString().replace(".",""));var e=s(t);return e>0?i(Number(t)*Math.pow(10,e)):Number(t)}function n(t){h&&(t>Number.MAX_SAFE_INTEGER||Number.MIN_SAFE_INTEGER)}function r(t){return function(){for(var e=[],a=0;a<arguments.length;a++)e[a]=arguments[a];var i=e[0],s=e.slice(1);return s.reduce((function(e,a){return t(e,a)}),i)}}var o=r((function(t,e){var a=l(t),i=l(e),r=s(t)+s(e),o=a*i;return n(o),o/Math.pow(10,r)})),c=r((function(t,e){var a=Math.pow(10,Math.max(s(t),s(e)));return(o(t,a)+o(e,a))/a})),u=r((function(t,e){var a=Math.pow(10,Math.max(s(t),s(e)));return(o(t,a)-o(e,a))/a})),d=r((function(t,e){var a=l(t),r=l(e);return n(a),n(r),o(a/r,i(Math.pow(10,s(e)-s(t))))}));function p(t,e){var a=Math.pow(10,e),i=d(Math.round(Math.abs(o(t,a))),a);return t<0&&0!==i&&(i=o(i,-1)),i}var h=!0;function f(t){void 0===t&&(t=!0),h=t}var m={strip:i,plus:c,minus:u,times:o,divide:d,round:p,digitLength:s,float2Fixed:l,enableBoundaryChecking:f};e["a"]=m},dca4:function(t,e,a){"use strict";a("53dd")},f5e8:function(t,e,a){},ff5d:function(t,e,a){"use strict";a("52ad")}}]);