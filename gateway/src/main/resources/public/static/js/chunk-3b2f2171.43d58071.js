(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b2f2171"],{"0232":function(e,t,a){},"0845":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"system-customer main"},[a("xr-header",{attrs:{label:"业务参数设置"}}),e._v(" "),a("div",{staticClass:"main-content-wrap"},[a("div",{staticClass:"main-nav"},[a("div",{staticClass:"main-nav__content"},[a("div",{staticClass:"nav-sections-wrap"},e._l(e.menuList,(function(t,s){return a("div",{key:s,staticClass:"menu-item",class:{"is-select":t.key==e.menuIndex},on:{click:function(a){e.menuSelect(t.key)}}},[e._v("\n            "+e._s(t.label)+"\n          ")])})))])]),e._v(" "),a("keep-alive",[a(e.menuIndex,{tag:"component",staticClass:"main-content"})],1)],1)],1)},o=[],n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",{staticClass:"content-header"},[a("span",[e._v("日志欢迎语")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1),e._v(" "),a("div",{staticClass:"content-body"},[a("reminder",{staticClass:"reminder",attrs:{content:"以下内容为系统默认欢迎语，在日志随机展示，可自定义更改欢迎语。"}}),e._v(" "),e._l(e.list,(function(t,s){return a("div",{key:s,staticClass:"input-item"},[a("el-input",{attrs:{maxlength:100},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}}),e._v(" "),a("i",{staticClass:"el-icon-remove",on:{click:function(a){e.deleteItem(t,s)}}})],1)})),e._v(" "),a("el-button",{staticStyle:{"padding-left":"0"},attrs:{type:"primary-text"},on:{click:e.addItem}},[e._v("+添加欢迎语")])],2)])},i=[],l=(a("14d9"),a("a434"),a("b775"));function r(e){return Object(l["a"])({url:"adminConfig/setLogWelcomeSpeech",method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"},data:e})}function c(e){return Object(l["a"])({url:"adminConfig/getLogWelcomeSpeechList",method:"post",data:e})}function m(e){return Object(l["a"])({url:"oaCalendar/addOrUpdate",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(e){return Object(l["a"])({url:"oaCalendar/queryTypeList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(e){return Object(l["a"])({url:"oaCalendar/delete/".concat(e),method:"post"})}function v(e){return Object(l["a"])({url:"oaLog/queryOaLogRuleList",method:"post",data:e})}function f(e){return Object(l["a"])({url:"oaLog/setOaLogRule",method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"},data:e})}var p=a("8f37"),h={name:"LogWelcome",components:{Reminder:p["a"]},data:function(){return{loading:!1,list:[]}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,c().then((function(t){e.loading=!1,e.list=t.data||[]})).catch((function(){e.loading=!1}))},addItem:function(){this.list.push({value:""})},deleteItem:function(e,t){this.list.splice(t,1)},save:function(){for(var e=this,t=[],a=0;a<this.list.length;a++){var s=this.list[a];s.value&&t.push(s.value)}0!=t.length?(this.loading=!0,r(t).then((function(t){e.loading=!1,e.getDetail(),e.$message.success("操作成功")})).catch((function(){e.loading=!1}))):this.$message.error("请输入欢迎语")}}},b=h,y=(a("5e24"),a("2877")),g=Object(y["a"])(b,n,i,!1,null,"0c257a89",null),k=g.exports,_=a("f468"),w=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e._m(0),e._v(" "),a("div",{staticClass:"content-body"},[e._l(e.list,(function(t,s){return a("flexbox",{key:s,staticClass:"input-item"},[a("div",{staticClass:"block_box"},[a("div",{staticClass:"block",style:{backgroundColor:t.color}}),e._v(" "),a("span",[e._v(e._s(t.typeName))])]),e._v(" "),a("i",{staticClass:"wk wk-edit",on:{click:function(a){e.editItem(t)}}}),e._v(" "),a("i",{staticClass:"el-icon-delete-solid",on:{click:function(a){e.deleteItem(t)}}})])})),e._v(" "),a("el-button",{staticStyle:{"padding-left":"0"},attrs:{type:"primary-text"},on:{click:e.addItem}},[e._v("+添加日程类型")]),e._v(" "),a("el-dialog",{attrs:{title:e.title,visible:e.showCreate,"append-to-body":"",width:"580px"},on:{"update:visible":function(t){e.showCreate=t},close:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rule,"label-position":"left","label-width":"80px"}},[a("el-form-item",{attrs:{label:"类型名称",prop:"typeName"}},[a("el-input",{attrs:{placeholder:"请输入类型名称"},model:{value:e.form.typeName,callback:function(t){e.$set(e.form,"typeName",t)},expression:"form.typeName"}}),e._v(" "),a("div",{staticClass:"block-circle color-active",style:{backgroundColor:e.selectColor}})],1)],1),e._v(" "),a("flexbox",[a("label",{staticClass:"label-color"},[e._v("图标颜色")]),e._v(" "),e._l(e.colorList,(function(t){return a("div",{key:t,staticClass:"block-circle",style:{backgroundColor:t},on:{click:function(a){e.changeColor(t)}}})}))],2),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.save,expression:"save"}],attrs:{type:"primary"}},[e._v("确定")]),e._v(" "),a("el-button",{on:{click:function(t){e.showCreate=!1}}},[e._v("取消")])],1)],1)],2)])},C=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-header"},[a("span",[e._v("日程类型"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"23","data-id":"258"}})])])}],F=a("0ebd"),D=a("ed08"),x={name:"CalendarType",components:{},data:function(){return{loading:!1,title:"新建日程类型",showCreate:!1,colorList:F["a"].colorList,selectColor:F["a"].colorList[0],form:{typeName:"",color:F["a"].colorList[0],typeId:""},rule:{typeName:[{required:!0,message:"请输入类型名称",trigger:"blur"},{min:0,max:10,message:"请输入小于10个字符",trigger:"blur"}]},list:[]}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,u().then((function(t){e.loading=!1,e.list=t.data||[]})).catch((function(){e.loading=!1}))},addItem:function(){this.title="新建日程类型",this.form={typeName:""},this.showCreate=!0},editItem:function(e){this.title="编辑日程类型",this.form=Object(D["D"])(e),this.selectColor=e.color,this.showCreate=!0},deleteItem:function(e){var t=this;this.$confirm("此操作将永久删除类型, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d(e.typeId).then((function(e){t.$message({type:"success",message:"删除成功!"}),t.getDetail()})).catch((function(){}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},save:function(){var e=this;this.form.color=this.selectColor,this.$refs["form"].validate((function(t){t&&m(e.form).then((function(t){e.getDetail(),e.showCreate=!1,e.$message.success("操作成功")})).catch((function(){}))}))},changeColor:function(e){this.selectColor=e},close:function(){this.form={typeName:"",color:F["a"].colorList[0],typeId:""},this.$refs["form"].resetFields()}}},I=x,$=(a("1f8c"),Object(y["a"])(I,w,C,!1,null,"f3b80794",null)),O=$.exports,T=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",{staticClass:"content-header"},[e._m(0),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1),e._v(" "),a("div",{staticClass:"content-body"},[a("create-sections",{attrs:{title:"日报规则"}},[a("div",{staticClass:"rule-set",class:{"is-inactive":0==e.dayForm.status},attrs:{slot:"header"},slot:"header"},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.dayForm.status,callback:function(t){e.$set(e.dayForm,"status",t)},expression:"dayForm.status"}}),e._v(" "),a("span",{staticClass:"label"},[e._v("已启用规则")])],1),e._v(" "),a("el-form",{ref:"dayForm",attrs:{model:e.dayForm,"label-width":"120px","label-position":"top"}},[a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("谁需要提交")]),a("span",{staticClass:"label-tips"},[e._v("（默认全公司）")])]),e._v(" "),a("wk-user-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:!1},model:{value:e.dayForm.memberUserId,callback:function(t){e.$set(e.dayForm,"memberUserId",t)},expression:"dayForm.memberUserId"}})],2),e._v(" "),a("el-form-item",{staticClass:"wk-form-item",attrs:{label:"需要统计的日志"}},[a("el-checkbox-group",{model:{value:e.dayForm.effectiveDay,callback:function(t){e.$set(e.dayForm,"effectiveDay",t)},expression:"dayForm.effectiveDay"}},e._l(e.weekDaysOptions,(function(t){return a("el-checkbox",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})))],1),e._v(" "),a("el-form-item",{staticClass:"wk-form-item",attrs:{label:"开始提交时间"}},[a("el-select",{model:{value:e.dayForm.startTime,callback:function(t){e.$set(e.dayForm,"startTime",t)},expression:"dayForm.startTime"}},e._l(e.dayTimeOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})))],1),e._v(" "),a("el-form-item",{staticClass:"wk-form-item",attrs:{label:"结束提交时间"}},[a("el-select",{model:{value:e.dayForm.endTime,callback:function(t){e.$set(e.dayForm,"endTime",t)},expression:"dayForm.endTime"}},e._l(e.dayTimeOptions,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})))],1)],1)],1),e._v(" "),a("create-sections",{attrs:{title:"周报规则"}},[a("div",{staticClass:"rule-set",class:{"is-inactive":0==e.weekForm.status},attrs:{slot:"header"},slot:"header"},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.weekForm.status,callback:function(t){e.$set(e.weekForm,"status",t)},expression:"weekForm.status"}}),e._v(" "),a("span",{staticClass:"label"},[e._v("已启用规则")])],1),e._v(" "),a("el-form",{ref:"weekForm",attrs:{model:e.weekForm,"label-width":"120px","label-position":"top"}},[a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("谁需要提交")]),a("span",{staticClass:"label-tips"},[e._v("（默认全公司）")])]),e._v(" "),a("wk-user-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:!1},model:{value:e.weekForm.memberUserId,callback:function(t){e.$set(e.weekForm,"memberUserId",t)},expression:"weekForm.memberUserId"}})],2),e._v(" "),a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("开始提交时间")])]),e._v(" "),a("el-select",{model:{value:e.weekForm.startDay,callback:function(t){e.$set(e.weekForm,"startDay",t)},expression:"weekForm.startDay"}},e._l(e.weekDaysOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:"每"+e.label,value:parseInt(e.value)}})})))],2),e._v(" "),a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("结束提交时间")])]),e._v(" "),a("el-select",{model:{value:e.weekForm.endDay,callback:function(t){e.$set(e.weekForm,"endDay",t)},expression:"weekForm.endDay"}},e._l(e.weekDaysOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:"每"+e.label,value:parseInt(e.value)}})})))],2)],1)],1),e._v(" "),a("create-sections",{attrs:{title:"月报规则"}},[a("div",{staticClass:"rule-set",class:{"is-inactive":0==e.monthForm.status},attrs:{slot:"header"},slot:"header"},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.monthForm.status,callback:function(t){e.$set(e.monthForm,"status",t)},expression:"monthForm.status"}}),e._v(" "),a("span",{staticClass:"label"},[e._v("已启用规则")])],1),e._v(" "),a("el-form",{ref:"monthForm",attrs:{model:e.monthForm,"label-width":"120px","label-position":"top"}},[a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("谁需要提交")]),a("span",{staticClass:"label-tips"},[e._v("（默认全公司）")])]),e._v(" "),a("wk-user-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:!1},model:{value:e.monthForm.memberUserId,callback:function(t){e.$set(e.monthForm,"memberUserId",t)},expression:"monthForm.memberUserId"}})],2),e._v(" "),a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("开始提交时间")])]),e._v(" "),a("el-select",{model:{value:e.monthForm.startDay,callback:function(t){e.$set(e.monthForm,"startDay",t)},expression:"monthForm.startDay"}},e._l(e.monthTimeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:""+e.label,value:e.value}})})))],2),e._v(" "),a("el-form-item",{staticClass:"wk-form-item"},[a("template",{slot:"label"},[a("span",[e._v("结束提交时间")])]),e._v(" "),a("el-select",{model:{value:e.monthForm.endDay,callback:function(t){e.$set(e.monthForm,"endDay",t)},expression:"monthForm.endDay"}},e._l(e.monthTimeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:""+e.label,value:e.value}})})))],2)],1)],1)],1)])},U=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("span",[e._v("日志规则设置"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"23","data-id":"257"}})])}],j=(a("a15b"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("10ff")),L=a("8f81"),S={name:"LogRuleSet",components:{CreateSections:j["a"],WkUserDialogSelect:L["a"]},data:function(){return{loading:!1,weekDaysOptions:[{label:"周一",value:"1"},{label:"周二",value:"2"},{label:"周三",value:"3"},{label:"周四",value:"4"},{label:"周五",value:"5"},{label:"周六",value:"6"},{label:"周日",value:"7"}],dayTimeOptions:[],monthTimeOptions:[],dayForm:{effectiveDay:["1","2","3","4","5"],memberUserId:[]},weekForm:{memberUserId:[]},monthForm:{memberUserId:[]}}},created:function(){for(var e=7;e<=23;e++)this.dayTimeOptions.push(e<10?"0".concat(e,":00"):"".concat(e,":00"));for(var t=1;t<=31;t++)this.monthTimeOptions.push({label:"每月".concat(t,"号"),value:t});this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,v().then((function(t){e.loading=!1;var a=t.data[0]||{};e.dayForm={type:a.type,status:a.status,ruleId:a.ruleId,memberUserId:(a.memberUser||[]).map((function(e){return e.userId})),effectiveDay:a.effectiveDay?a.effectiveDay.split(","):[],startTime:a.startTime,endTime:a.endTime};var s=t.data[1]||{};e.weekForm={type:s.type,status:s.status,ruleId:s.ruleId,memberUserId:(s.memberUser||[]).map((function(e){return e.userId})),startDay:s.startDay,endDay:s.endDay};var o=t.data[2]||{};e.monthForm={type:o.type,status:o.status,ruleId:o.ruleId,memberUserId:(o.memberUser||[]).map((function(e){return e.userId})),startDay:o.startDay,endDay:o.endDay}})).catch((function(){e.loading=!1}))},save:function(){var e=this;if(1==this.dayForm.status)for(var t in this.dayForm){var a=this.dayForm[t];if("weekDaysOptions"==t&&0==a.length)return void this.$message.error("请选择需要统计的日志");if(("startTime"==t||"endTime"==t)&&!a)return void this.$message.error("请选择日报规则时间")}if(1==this.weekForm.status)for(var s in this.weekForm){var o=this.weekForm[s];if(("startDay"==s||"endDay"==s)&&!o)return void this.$message.error("请选择周报规则时间")}if(1==this.monthForm.status)for(var n in this.monthForm){var i=this.monthForm[n];if(("startDay"==n||"endDay"==n)&&!i)return void this.$message.error("请选择月报规则时间")}this.loading=!0;var l=Object(D["D"])(this.dayForm);l.memberUserId=l.memberUserId.join(","),l.effectiveDay=l.effectiveDay.join(",");var r=Object(D["D"])(this.weekForm);r.memberUserId=r.memberUserId.join(",");var c=Object(D["D"])(this.monthForm);c.memberUserId=c.memberUserId.join(","),f([l,r,c]).then((function(t){e.loading=!1,e.getDetail(),e.$message.success("操作成功")})).catch((function(){e.loading=!1}))}}},N=S,E=(a("2f0a"),Object(y["a"])(N,T,U,!1,null,"4e5f17b4",null)),R=E.exports,W=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e._m(0),e._v(" "),a("div",{staticClass:"content-body"},[e._v("\n    是否启用CRM活动资讯"),a("el-switch",{staticStyle:{"margin-left":"8px"},on:{change:e.changeStatus},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)])},M=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-header"},[a("span",[e._v("活动资讯")])])}];function A(e){return Object(l["a"])({url:"adminConfig/setMarketing",method:"post",data:e})}var B={name:"MarketingSet",components:{},props:{},data:function(){return{loading:!1,value:!1}},computed:{},watch:{},created:function(){var e=this;this.$store.dispatch("QueryMarketing").then((function(t){e.value=1===t.data}))},beforeDestroy:function(){},methods:{changeStatus:function(){var e=this;this.loading=!0,A({status:this.value?1:0}).then((function(t){e.$message.success("操作成功"),e.$store.dispatch("QueryMarketing"),e.loading=!1})).catch((function(){e.loading=!1}))}}},q=B,J=(a("cbfc"),Object(y["a"])(q,W,M,!1,null,"bdc334fe",null)),Q=J.exports,H={name:"OtherSystem",components:{LogWelcome:k,CalendarType:O,XrHeader:_["a"],LogRuleSet:R,MarketingSet:Q},data:function(){return{menuIndex:"LogWelcome",types:""}},computed:{Show:function(){return this.$store.state.crm.isCall},menuList:function(){var e=[{label:"日志欢迎语",key:"LogWelcome"},{label:"日志规则设置",key:"LogRuleSet"},{label:"日历类型设置",key:"CalendarType"}];return e}},methods:{menuSelect:function(e){this.menuIndex=e}}},X=H,z=(a("b5d8"),Object(y["a"])(X,s,o,!1,null,"3f608ced",null));t["default"]=z.exports},"0ebd":function(e,t,a){"use strict";t["a"]={colorList:["#1890ff","#00A3BF","#DE350B","#5243AA","#00875A","#FF991F","#091E42"]}},"1f8c":function(e,t,a){"use strict";a("2b27")},"2b27":function(e,t,a){},"2f0a":function(e,t,a){"use strict";a("9cd0")},"5e24":function(e,t,a){"use strict";a("c7a3")},"9cd0":function(e,t,a){},b5d8:function(e,t,a){"use strict";a("f1b4")},c7a3:function(e,t,a){},cbfc:function(e,t,a){"use strict";a("0232")},f1b4:function(e,t,a){}}]);