(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3a2b1cbd"],{"12a1":function(e,t,a){"use strict";a("c757")},"3e72":function(e,t,a){},"4ff9":function(e,t,a){},"8a85":function(e,t,a){"use strict";a("c6e9")},a47a:function(e,t,a){"use strict";a("bfdb")},a528:function(e,t,a){"use strict";a("4ff9")},a6de:function(e,t,a){"use strict";a("3e72")},aa99:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("xr-header",{attrs:{label:"客户公海规则设置"}},[a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{slot:"otherLabel","data-type":"24","data-id":"211"},slot:"otherLabel"}),e._v(" "),a("el-button",{attrs:{slot:"ft",type:"primary"},on:{click:e.addClick},slot:"ft"},[e._v("新建公海")])],1),e._v(" "),a("div",{staticClass:"main-body"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-table",class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",id:"examine-table",data:e.list,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight,"cell-class-name":e.cellClassName,"highlight-current-row":""},on:{"row-click":e.handleRowClick}},[a("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"poolName",width:"150",label:"公海名称"}}),e._v(" "),a("el-table-column",{attrs:{formatter:e.fieldFormatter,"show-overflow-tooltip":"",prop:"adminUser",label:"公海管理员"}}),e._v(" "),a("el-table-column",{attrs:{formatter:e.fieldFormatter,"show-overflow-tooltip":"",prop:"memberUser",label:"公海成员"}}),e._v(" "),a("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"customerNum",width:"100",label:"客户数量"}}),e._v(" "),a("el-table-column",{attrs:{formatter:e.fieldFormatter,"show-overflow-tooltip":"",prop:"status",width:"100",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){e.handleClick("status",t)}}},[e._v(e._s(0===t.row["status"]?"启用":"停用"))]),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){e.handleClick("transfer",t)}}},[e._v("转移")]),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){e.handleClick("edit",t)}}},[e._v("编辑")]),e._v(" "),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){e.handleClick("delete",t)}}},[e._v("删除")])]}}])})],1),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.createShow?a("pool-add",{attrs:{action:e.createAction},on:{"hiden-view":function(t){e.createShow=!1},save:e.getList}}):e._e(),e._v(" "),e.detailShow?a("pool-detail",{attrs:{id:e.detailId},on:{hide:function(t){e.detailShow=!1}}}):e._e(),e._v(" "),a("pool-transfer",{attrs:{id:e.detailId,visible:e.transferShow},on:{"update:visible":function(t){e.transferShow=t},transfer:e.getList}})],1)},s=[],l=(a("a15b"),a("d81d"),a("a434"),a("b0c0"),a("e9f5"),a("ab43"),a("d3b7"),a("ec3a")),n=a("8f37"),r=a("f468"),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("create-view",{attrs:{loading:e.loading,"body-style":{height:"100%"}}},[a("flexbox",{staticClass:"pool-add-container",attrs:{direction:"column",align:"stretch"}},[a("flexbox",{staticClass:"pool-add-header"},[a("div",{staticStyle:{flex:"1","font-size":"20px","font-weight":"bold"}},[e._v(e._s(e.title))]),e._v(" "),a("i",{staticClass:"el-icon-close close",on:{click:e.hidenView}})]),e._v(" "),a("div",{staticClass:"pool-add-content"},[e.baseFrom?a("create-sections",{attrs:{title:"基本信息"}},[a("flexbox",{staticStyle:{"margin-top":"8px"},attrs:{direction:"column",align:"stretch"}},[a("el-form",{ref:"ruleForm",staticClass:"pool-add-items",attrs:{model:e.baseFrom,rules:e.baseRules,"label-position":"top"}},[a("el-form-item",{staticClass:"pool-add-item pool-add-item__left",attrs:{prop:"poolName"}},[a("div",{staticStyle:{display:"inline-block"},attrs:{slot:"label"},slot:"label"},[a("div",{staticClass:"xr-form-label"},[e._v("\n                  公海名称\n                ")])]),e._v(" "),a("el-input",{attrs:{maxlength:100},model:{value:e.baseFrom.poolName,callback:function(t){e.$set(e.baseFrom,"poolName",t)},expression:"baseFrom.poolName"}})],1),e._v(" "),a("el-form-item",{staticClass:"pool-add-item pool-add-item__right",attrs:{prop:"adminUsers"}},[a("div",{staticStyle:{display:"inline-block"},attrs:{slot:"label"},slot:"label"},[a("div",{staticClass:"xr-form-label"},[e._v("\n                  公海管理员"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"212"}})])]),e._v(" "),a("wk-user-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:!1},model:{value:e.baseFrom.adminUsers,callback:function(t){e.$set(e.baseFrom,"adminUsers",t)},expression:"baseFrom.adminUsers"}})],1),e._v(" "),a("el-form-item",{staticClass:"pool-add-item pool-add-item__left",attrs:{prop:"memberUsers"}},[a("div",{staticStyle:{display:"inline-block"},attrs:{slot:"label"},slot:"label"},[a("div",{staticClass:"xr-form-label"},[e._v("\n                  公海成员"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"213"}})])]),e._v(" "),a("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":e.baseFrom.memberUsers.users,"dep-value":e.baseFrom.memberUsers.strucs},on:{"update:userValue":function(t){e.$set(e.baseFrom.memberUsers,"users",t)},"update:depValue":function(t){e.$set(e.baseFrom.memberUsers,"strucs",t)}}})],1)],1)],1)],1):e._e(),e._v(" "),e.recycleRuleData?a("create-sections",{attrs:{title:"规则设置"}},[a("flexbox",{staticClass:"row",attrs:{align:"stretch"}},[a("div",{staticClass:"row-label"},[e._v("前负责人领取规则")]),e._v(" "),a("div",{staticClass:"row-content"},[a("el-radio-group",{model:{value:e.baseFrom.preOwnerSetting,callback:function(t){e.$set(e.baseFrom,"preOwnerSetting",t)},expression:"baseFrom.preOwnerSetting"}},[a("el-radio",{attrs:{label:0}},[e._v("不限制")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("限制")])],1),e._v(" "),1===e.baseFrom.preOwnerSetting?a("div",{staticClass:"xr-input"},[a("span",[e._v("前负责人")]),e._v(" "),a("el-input",{nativeOn:{keyup:function(t){e.inputLimit("preOwnerSettingDay")}},model:{value:e.baseFrom.preOwnerSettingDay,callback:function(t){e.$set(e.baseFrom,"preOwnerSettingDay",t)},expression:"baseFrom.preOwnerSettingDay"}}),e._v(" "),a("span",[e._v("天内不允许领取该客户")])],1):e._e()],1)]),e._v(" "),a("flexbox",{staticClass:"row",attrs:{align:"stretch"}},[a("div",{staticClass:"row-label"},[e._v("领取频率规则")]),e._v(" "),a("div",{staticClass:"row-content"},[a("el-radio-group",{model:{value:e.baseFrom.receiveSetting,callback:function(t){e.$set(e.baseFrom,"receiveSetting",t)},expression:"baseFrom.receiveSetting"}},[a("el-radio",{attrs:{label:0}},[e._v("不限制")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("限制")])],1),e._v(" "),1===e.baseFrom.receiveSetting?a("div",{staticClass:"xr-input"},[a("span",[e._v("每天最多领取")]),e._v(" "),a("el-input",{nativeOn:{keyup:function(t){e.inputLimit("receiveNum")}},model:{value:e.baseFrom.receiveNum,callback:function(t){e.$set(e.baseFrom,"receiveNum",t)},expression:"baseFrom.receiveNum"}}),e._v(" "),a("span",[e._v("个公海客户")])],1):e._e()],1)]),e._v(" "),a("flexbox",{staticClass:"row",attrs:{align:"stretch"}},[a("div",{staticClass:"row-label"},[e._v("提醒规则")]),e._v(" "),a("div",{staticClass:"row-content"},[a("el-radio-group",{model:{value:e.baseFrom.remindSetting,callback:function(t){e.$set(e.baseFrom,"remindSetting",t)},expression:"baseFrom.remindSetting"}},[a("el-radio",{attrs:{label:0}},[e._v("不提醒")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("提醒")])],1),e._v(" "),1===e.baseFrom.remindSetting?a("div",{staticClass:"xr-input"},[a("span",[e._v("提前")]),e._v(" "),a("el-input",{nativeOn:{keyup:function(t){e.inputLimit("remindDay")}},model:{value:e.baseFrom.remindDay,callback:function(t){e.$set(e.baseFrom,"remindDay",t)},expression:"baseFrom.remindDay"}}),e._v(" "),a("span",[e._v("天提醒负责人")])],1):e._e()],1)]),e._v(" "),a("flexbox",{staticClass:"row",attrs:{align:"stretch"}},[a("div",{staticClass:"row-label"},[e._v("收回规则"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"273"}})]),e._v(" "),a("div",{staticClass:"row-content"},[a("el-radio-group",{model:{value:e.baseFrom.putInRule,callback:function(t){e.$set(e.baseFrom,"putInRule",t)},expression:"baseFrom.putInRule"}},[a("el-radio",{attrs:{label:1}},[e._v("自动回收")]),e._v(" "),a("el-radio",{attrs:{label:0}},[e._v("不自动回收")])],1)],1)]),e._v(" "),1==e.baseFrom.putInRule?e._l(e.recycleRuleData,(function(t,i){return a("recycle-rule",{key:i,attrs:{data:t,"level-customer":e.levelCustomerName,"true-label":i+1,"is-edit":e.isEdit}})})):e._e(),e._v(" "),a("flexbox",{staticClass:"row",attrs:{align:"stretch"}},[a("div",{staticClass:"row-label"},[e._v("公海字段设置")]),e._v(" "),a("div",{staticClass:"row-content"},[a("div",{staticClass:"field-set"},[a("div",{staticClass:"field-set__name"},[e._v("\n                选择公海字段\n              ")]),e._v(" "),a("flexbox",{attrs:{wrap:"wrap"}},e._l(e.customerPoolFields,(function(t,i){return a("el-checkbox",{key:i,attrs:{"true-label":0,"false-label":1},model:{value:t.isHidden,callback:function(a){e.$set(t,"isHidden",a)},expression:"item.isHidden"}},[e._v(e._s(t.name))])})))],1)])])],2):e._e()],1),e._v(" "),a("div",{staticClass:"handle-bar"},[a("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.saveClick,expression:"saveClick"}],staticClass:"handle-button",attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),a("el-button",{staticClass:"handle-button",nativeOn:{click:function(t){return e.hidenView(t)}}},[e._v("取消")])],1)],1)],1)},c=[],d=a("5530"),u=(a("d9e2"),a("4de4"),a("14d9"),a("910d"),a("ac1f"),a("5319"),a("5620")),m=a("10ff"),v=a("b592"),p=a("8f81"),f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"recycle-rule"},[a("el-checkbox",{attrs:{"true-label":e.trueLabel},model:{value:e.data.type,callback:function(t){e.$set(e.data,"type",t)},expression:"data.type"}},[e._v(e._s(e.typeName)),e.helpTypeId?a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":e.helpTypeId},on:{click:function(e){e.preventDefault(),e.stopPropagation()}}}):e._e()]),e._v(" "),e.data.type==e.trueLabel?a("div",{staticClass:"recycle-rule__content"},[a("div",[a("span",{staticClass:"check-label"},[e._v("选择不进入公海客户")]),e._v(" "),e.dealHandleShow?a("el-checkbox",{attrs:{"true-label":0,"false-label":1},model:{value:e.data.dealHandle,callback:function(t){e.$set(e.data,"dealHandle",t)},expression:"data.dealHandle"}},[e._v("已成交客户"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"262"},on:{click:function(e){e.preventDefault(),e.stopPropagation()}}})]):e._e(),e._v(" "),e.businessHandleShow?a("el-checkbox",{attrs:{"true-label":0,"false-label":1},model:{value:e.data.businessHandle,callback:function(t){e.$set(e.data,"businessHandle",t)},expression:"data.businessHandle"}},[e._v("有项目客户")]):e._e()],1),e._v(" "),a("div",[a("el-radio",{attrs:{label:1},model:{value:e.data.customerLevelSetting,callback:function(t){e.$set(e.data,"customerLevelSetting",t)},expression:"data.customerLevelSetting"}},[e._v("所有客户统一设置")]),e._v(" "),a("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.allCustomerData,stripe:e.WKConfig.tableStyle.stripe}},[a("el-table-column",{attrs:{prop:"level",label:"客户",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:e.limitDayName,prop:"limitDay"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v("超过")]),e._v(" "),a("el-input",{staticClass:"value-input",nativeOn:{keyup:function(a){e.inputLimit(t.row)}},model:{value:t.row.limitDay,callback:function(a){e.$set(t.row,"limitDay",a)},expression:"scope.row.limitDay"}}),e._v(" "),a("span",[e._v("天"+e._s(e.limitDayUnit)+"，进入公海")])]}}])})],1)],1),e._v(" "),a("div",[a("el-radio",{attrs:{label:2},model:{value:e.data.customerLevelSetting,callback:function(t){e.$set(e.data,"customerLevelSetting",t)},expression:"data.customerLevelSetting"}},[e._v("根据客户级别分别设置")]),e._v(" "),a("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{data:e.levelCustomerData,stripe:e.WKConfig.tableStyle.stripe}},[a("el-table-column",{attrs:{prop:"level",label:"客户",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:e.limitDayName,prop:"limitDay"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v("超过")]),e._v(" "),a("el-input",{staticClass:"value-input",nativeOn:{keyup:function(a){e.inputLimit(t.row)}},model:{value:t.row.limitDay,callback:function(a){e.$set(t.row,"limitDay",a)},expression:"scope.row.limitDay"}}),e._v(" "),a("span",[e._v("天"+e._s(e.limitDayUnit)+"，进入公海")])]}}])})],1)],1)]):e._e()],1)},h=[],b=(a("a9e3"),{name:"RecycleRule",components:{},props:{trueLabel:[String,Number],isEdit:Boolean,data:{type:Object,default:function(){return{type:"",dealHandle:1,businessHandle:1,customerLevelSetting:null,level:[]}}},levelCustomer:{type:Array,default:function(){return[]}}},data:function(){return{allCustomerData:[{level:"所有客户",limitDay:""}],levelCustomerData:[]}},computed:{typeName:function(){return{1:"超过N天“无新建跟进（跟进记录）”的客户，由系统定时退回公海客户池",2:"超过N天“无新建项目”的客户，由系统定时退回公海客户池",3:"超过N天“未成交”的客户，由系统定时退回公海客户池"}[parseInt(this.trueLabel)]},helpTypeId:function(){return{1:"214",2:"215",3:""}[parseInt(this.trueLabel)]},limitDayName:function(){return{1:"未跟进天数",2:"未新建天数",3:"未成交天数"}[parseInt(this.trueLabel)]},limitDayUnit:function(){return{1:"未跟进",2:"未新建项目",3:"未成交"}[parseInt(this.trueLabel)]},dealHandleShow:function(){return 1==this.trueLabel||2==this.trueLabel},businessHandleShow:function(){return 1==this.trueLabel||3==this.trueLabel}},watch:{"data.customerLevelSetting":{handler:function(e,t){1==e?this.isEdit&&null==t?this.data.level=this.getEditData(this.allCustomerData,this.data.level):this.data.level=this.allCustomerData:this.isEdit&&null==t?this.levelCustomerData&&this.levelCustomerData.length&&(this.data.level=this.getEditData(this.levelCustomerData,this.data.level)):this.data.level=this.levelCustomerData},deep:!0,immediate:!0},levelCustomer:{handler:function(e){e&&e.length&&(this.levelCustomerData=e.map((function(e){var t={limitDay:""};return t.level=e,t})),2==this.data.customerLevelSetting&&this.isEdit&&this.levelCustomerData&&this.levelCustomerData.length&&(this.data.level=this.getEditData(this.levelCustomerData,this.data.level)))},immediate:!0}},created:function(){},beforeDestroy:function(){},methods:{getEditData:function(e,t){for(var a=0;a<e.length;a++)for(var i=e[a],s=0;s<t.length;s++){var l=t[s];l.level==i.level&&(i.limitDay=l.limitDay)}return e},inputLimit:function(e){e.limitDay=e.limitDay.replace(/[^0-9]/g,"")}}}),g=b,_=(a("a6de"),a("2877")),y=Object(_["a"])(g,f,h,!1,null,"30c9bef1",null),w=y.exports,C={name:"PoolAdd",components:{CreateView:u["a"],CreateSections:m["a"],WkUserDialogSelect:p["a"],WkUserDepDialogSelect:v["a"],RecycleRule:w},props:{action:{type:Object,default:function(){return{type:"save",id:"",data:{}}}}},data:function(){var e=function(e,t,a){t&&(t.users&&t.users.length||t.strucs&&t.strucs.length)?a():a(new Error("请选择公海成员"))};return{loading:!1,baseFrom:null,baseRules:{poolName:[{required:!0,message:"请输入公海名称",trigger:"blur"}],adminUsers:[{required:!0,message:"请选择公海管理员",trigger:["blur","change"]}],memberUsers:[{required:!0,validator:e,trigger:["blur","change"]}]},recycleRuleData:null,levelCustomerName:[],customerPoolFields:[],requestFields:{preOwnerSettingDay:"前负责人限制领取天数需大于0",receiveNum:"领取频率限制个数需大于0",remindDay:"提醒规则天数需大于0"}}},computed:{title:function(){return this.isEdit?"编辑公海":"新建公海"},isEdit:function(){return this.action&&"update"==this.action.type}},watch:{},created:function(){this.isEdit?this.getDetail():this.getCreateInfo(),this.getLevelCustomerData()},beforeDestroy:function(){},methods:{getLevelCustomerData:function(){var e=this;Object(l["C"])().then((function(t){e.levelCustomerName=t.data||[]})).catch((function(){}))},getDetail:function(){var e=this;this.loading=!0,Object(l["I"])({poolId:this.action.id}).then((function(t){e.getEditInfo(t.data),e.loading=!1})).catch((function(){e.loading=!1}))},getEditInfo:function(e){var t=this;this.baseFrom={poolName:e.poolName,adminUsers:(e.adminUser||[]).map((function(e){return e.userId})),memberUsers:{users:(e.memberUser||[]).map((function(e){return e.userId})),strucs:(e.memberDept||[]).map((function(e){return e.id}))},preOwnerSetting:e.preOwnerSetting,preOwnerSettingDay:e.preOwnerSettingDay,receiveSetting:e.receiveSetting,remindSetting:e.remindSetting,receiveNum:e.receiveNum,remindDay:e.remindDay,putInRule:e.putInRule},this.recycleRuleData=this.getEditRule(e.rule),this.getCustomerPoolFields(e.field||[]).then((function(e){t.customerPoolFields=e}))},getEditRule:function(e){var t=[{type:"",dealHandle:1,businessHandle:1,customerLevelSetting:1,level:[]},{type:"",dealHandle:1,businessHandle:1,customerLevelSetting:1,level:[]},{type:"",dealHandle:1,businessHandle:1,customerLevelSetting:1,level:[]}];if(e)for(var a=0;a<e.length;a++){var i=e[a];i.level=i.levelSetting,t.splice(i.type-1,1,i)}return t},getCreateInfo:function(){var e=this;this.baseFrom={poolName:"",adminUsers:[],memberUsers:{users:[],strucs:[]},preOwnerSetting:0,preOwnerSettingDay:"",receiveSetting:0,receiveNum:"",remindSetting:0,remindDay:"",putInRule:1},this.recycleRuleData=this.getEditRule(),this.getCustomerPoolFields().then((function(t){e.customerPoolFields=t}))},getCustomerPoolFields:function(e){var t=this;return new Promise((function(a,i){Object(l["D"])().then((function(i){var s=i.data||[],l=s.map((function(t){return t.isHidden=t.isHidden=e?1:0,t}));a(e?t.getEditFields(l,e):l)})).catch((function(){i()}))}))},getEditFields:function(e,t){for(var a=0;a<e.length;a++)for(var i=e[a],s=0;s<t.length;s++){var l=t[s];i.fieldId?i.fieldId===l.fieldId&&(i.settingId=l.settingId,i.isHidden=l.isHidden):i.fieldName===l.fieldName&&(i.settingId=l.settingId,i.isHidden=l.isHidden)}return e},saveClick:function(){var e=this;this.$refs.ruleForm.validate((function(t){if(!t){if(e.$refs.ruleForm.fields)for(var a=0;a<e.$refs.ruleForm.fields.length;a++){var i=e.$refs.ruleForm.fields[a];if("error"==i.validateState){e.$message.error(i.validateMessage);break}}return!1}var s=e.getRequestParams();s&&e.uploadPoolSet(s)}))},uploadPoolSet:function(e){var t=this;this.isEdit&&(e.poolId=this.action.id),this.loading=!0,Object(l["F"])(e).then((function(e){t.$emit("save"),t.$message.success(t.isEdit?"编辑成功":"新建成功"),t.loading=!1,t.hidenView()})).catch((function(){t.loading=!1}))},requestFieldsVerify:function(e){return!("preOwnerSettingDay"==e&&1==this.baseFrom.preOwnerSetting&&(!this.baseFrom[e]||this.baseFrom[e]<=0))&&(!("receiveNum"==e&&1==this.baseFrom.receiveSetting&&(!this.baseFrom[e]||this.baseFrom[e]<=0))&&!("remindDay"==e&&1==this.baseFrom.remindSetting&&(!this.baseFrom[e]||this.baseFrom[e]<=0)))},getRequestParams:function(){var e={};for(var t in this.baseFrom){if(!this.requestFieldsVerify(t))return void this.$message.error(this.requestFields[t]);if("adminUsers"==t){var a=this.baseFrom.adminUsers||[];e.adminUserId=a.join(",")}else if("memberUsers"==t){var i=this.baseFrom.memberUsers||{},s=i.users||[],l=i.strucs||[];e.memberUserId=s.join(","),e.memberDeptId=l.join(",")}else e[t]=this.baseFrom[t]}if(1==this.baseFrom.putInRule){var n=this.recycleRuleData.filter((function(e){return e.type}));if(!n.length)return void this.$message.error("请勾选自动回收规则");for(var r=[],o=0;o<this.recycleRuleData.length;o++){var c=this.recycleRuleData[o];if(c=Object(d["a"])({},c),c.type){for(var u=!1,m=[],v=0;v<c.level.length;v++){var p=c.level[v];p.limitDay&&p.limitDay>0&&(u=!0,m.push(p))}if(c.level=m,r.push(c),!u)return void this.$message.error("收回规则超过天数需大于0")}}e.rule=r}else e.rule=[];var f=this.customerPoolFields.filter((function(e){return 0==e.isHidden}));if(!(f.length<2))return e.field=this.customerPoolFields,e;this.$message.error("公海字段至少要显示两个")},hidenView:function(){this.$emit("hiden-view")},inputLimit:function(e){this.baseFrom[e]=this.baseFrom[e].replace(/[^0-9]/g,"")}}},S=C,x=(a("12a1"),Object(_["a"])(S,o,c,!1,null,"553acb81",null)),D=x.exports,k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("slide-view",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"d-view",attrs:{"no-listener-class":e.noListenerClass},on:{close:e.hideView}},[e.detail?a("flexbox",{staticClass:"detail-main",attrs:{orient:"vertical",align:"stretch"}},[a("flexbox",{staticClass:"detail-header"},[a("div",{staticClass:"header-icon"},[a("i",{staticClass:"wk wk-s-seas"})]),e._v(" "),a("div",[a("div",{staticClass:"header-title"},[e._v("公海名称")]),e._v(" "),a("div",{staticClass:"header-name"},[e._v(e._s(e.detail.poolName))])]),e._v(" "),a("span",{staticClass:"customer-num"},[e._v("\n        客户数量："),a("span",{staticClass:"customer-num__value"},[e._v(e._s(e.detail.customerNum||0)+"个")])])]),e._v(" "),a("flexbox",{staticClass:"member",attrs:{align:"stretch"}},[a("flexbox-item",{staticClass:"member-item"},[a("div",{staticClass:"label"},[e._v("公海管理员")]),e._v(" "),a("div",{staticClass:"value"},e._l(e.detail.adminUser,(function(e,t){return a("xr-avatar",{key:"admin"+t,attrs:{name:e.realname,size:32,src:e.img}})})))]),e._v(" "),a("flexbox-item",{staticClass:"member-item"},[a("div",{staticClass:"label"},[e._v("公海成员")]),e._v(" "),a("div",{staticClass:"value"},[e._l(e.detail.memberUser,(function(e,t){return a("xr-avatar",{key:"member"+t,attrs:{name:e.realname,size:32,src:e.img}})})),e._v(" "),e._l(e.detail.memberDept,(function(e,t){return a("xr-avatar",{key:"dept"+t,attrs:{title:"dept"+t,name:e.name,size:32,background:"#FB6523"}})}))],2)])],1),e._v(" "),a("create-sections",{attrs:{title:"规则设置"}},[a("div",{staticClass:"rule"},[a("flexbox",{staticClass:"rule-item",attrs:{align:"stretch"}},[a("div",{staticClass:"label"},[e._v("前负责人领取规则")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.detail.preOwnerSetting?"前负责人"+e.detail.preOwnerSettingDay+"天内不允许领取该客户":"不限制"))])]),e._v(" "),a("flexbox",{staticClass:"rule-item",attrs:{align:"stretch"}},[a("div",{staticClass:"label"},[e._v("领取频率规则")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.detail.receiveSetting?"每天最多领取"+e.detail.receiveNum+"个公海客户":"不限制"))])]),e._v(" "),a("flexbox",{staticClass:"rule-item",attrs:{align:"stretch"}},[a("div",{staticClass:"label"},[e._v("提醒规则")]),e._v(" "),a("div",{staticClass:"value"},[e._v(e._s(1==e.detail.remindSetting?"提前"+e.detail.remindDay+"天提醒负责人":"不提醒"))])]),e._v(" "),a("flexbox",{staticClass:"rule-item",attrs:{align:"stretch"}},[a("div",{staticClass:"label"},[e._v("收回规则")]),e._v(" "),0==e.detail.putInRule?a("div",{staticClass:"value"},[e._v("不自动回收")]):a("div",{staticClass:"value rule-value"},e._l(e.detail.rule,(function(e,t){return a("detail-recycle-rule",{key:t,attrs:{data:e}})})))]),e._v(" "),a("flexbox",{staticClass:"rule-item",attrs:{align:"stretch"}},[a("div",{staticClass:"label"},[e._v("公海字段")]),e._v(" "),a("div",{staticClass:"value field-value"},[e._v(e._s(e._f("fieldNameFilter")(e.detail.field)))])])],1)])],1):e._e()],1)},F=[],I=a("130f"),N=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"detail-recycle-rule"},[a("div",[e._v(e._s(e.typeName))]),e._v(" "),a("div",{staticClass:"detail-recycle-rule__content"},[a("div",{staticClass:"range-rule"},[a("span",[e._v("选择不进入公海客户")]),e._v(" "),e.dealHandleShow&&0===e.data.dealHandle?a("span",[e._v("已成交客户")]):e._e(),e._v(" "),e.businessHandleShow&&0===e.data.businessHandle?a("span",[e._v("有项目客户")]):e._e()]),e._v(" "),a("div",[1===e.data.customerLevelSetting?a("div",{staticClass:"type-rule"},[e._v("所有客户统一设置")]):2===e.data.customerLevelSetting?a("div",{staticClass:"type-rule"},[e._v("根据客户级别分别设置")]):e._e(),e._v(" "),a("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.data.levelSetting,stripe:e.WKConfig.tableStyle.stripe}},[a("el-table-column",{attrs:{prop:"level",label:"客户",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:e.limitDayName,prop:"limitDay"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s("超过"+t.row.limitDay+"天"+e.getLimitDayUnit(e.data.type)+"，进入公海"))])]}}])})],1)],1)])])},L=[],$={name:"DetailRecycleRule",components:{},props:{data:null},data:function(){return{}},computed:{typeName:function(){return{1:"超过N天“无新建跟进（跟进记录）”的客户，由系统定时退回公海客户池",2:"超过N天“无新建项目”的客户，由系统定时退回公海客户池",3:"超过N天“未成交”的客户，由系统定时退回公海客户池"}[parseInt(this.data.type)]},limitDayName:function(){return{1:"未跟进天数",2:"未新建天数",3:"未成交天数"}[parseInt(this.data.type)]},dealHandleShow:function(){return 1==this.data.type||2==this.data.type},businessHandleShow:function(){return 1==this.data.type||3==this.data.type}},watch:{},mounted:function(){},beforeDestroy:function(){},methods:{getLimitDayUnit:function(e){return{1:"未跟进",2:"未新建项目",3:"未成交"}[e]}}},O=$,H=(a("8a85"),Object(_["a"])(O,N,L,!1,null,"93f3bb4e",null)),U=H.exports,R={name:"PoolDetail",components:{SlideView:I["a"],CreateSections:m["a"],DetailRecycleRule:U},filters:{fieldNameFilter:function(e){return e.filter((function(e){return 0===e.isHidden})).map((function(e){return e.name})).join("，")}},mixins:[],props:{id:[String,Number],noListenerClass:{type:Array,default:function(){return["el-table__body"]}}},data:function(){return{loading:!1,detail:null}},computed:{},watch:{id:function(e){this.detail=null,this.getDetail()}},mounted:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,Object(l["I"])({poolId:this.id}).then((function(t){e.detail=t.data,e.loading=!1})).catch((function(){e.loading=!1}))},hideView:function(){this.$emit("hide")}}},E=R,P=(a("e231"),Object(_["a"])(E,k,F,!1,null,"56d85e4d",null)),j=P.exports,z=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{ref:"wkDialog",attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,title:"转移",width:"450px"},on:{close:e.handleCancel}},[a("div",{staticClass:"handle-box"},[a("flexbox",{staticClass:"handle-item"},[a("div",{staticClass:"handle-item-name"},[e._v("转移到：")]),e._v(" "),a("el-select",{model:{value:e.selectId,callback:function(t){e.selectId=t},expression:"selectId"}},e._l(e.list,(function(e){return a("el-option",{key:e.poolId,attrs:{label:e.poolName,value:e.poolId}})})))],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),a("el-button",{nativeOn:{click:function(t){return e.handleCancel(t)}}},[e._v("取消")])],1)])},V=[],q=a("9dba"),W={name:"PoolTransfer",components:{},mixins:[q["a"]],props:{visible:{type:Boolean,required:!0,default:!1},id:[String,Number]},data:function(){return{loading:!1,selectId:"",allList:[]}},computed:{list:function(){var e=this;return this.allList.filter((function(t){return t.poolId!=e.id}))}},watch:{list:{handler:function(){this.selectId=this.list&&this.list.length>0?this.list[0].poolId:""},immediate:!0},visible:function(e){e&&0===this.list.length&&this.getList()}},mounted:function(){},methods:{getList:function(){var e=this;this.loading=!0,Object(l["L"])().then((function(t){e.allList=t.data||[],e.loading=!1})).catch((function(){e.loading=!1}))},handleCancel:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this;this.selectId&&(this.loading=!0,Object(l["M"])({prePoolId:this.id,postPoolId:this.selectId}).then((function(t){e.$message({type:"success",message:"操作成功"}),e.loading=!1,e.$emit("transfer"),e.handleCancel()})).catch((function(){e.loading=!1})))}}},K=W,T=(a("a528"),Object(_["a"])(K,z,V,!1,null,"2e8fcc3a",null)),A=T.exports,B={name:"SystemPool",components:{Reminder:n["a"],XrHeader:r["a"],PoolAdd:D,PoolDetail:j,PoolTransfer:A},mixins:[],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-220,currentPage:1,pageSize:15,pageSizes:[15,30,45,60],total:0,list:[],createAction:{type:"save"},createShow:!1,detailId:"",detailShow:!1,transferShow:!1}},computed:{},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-220},this.getList()},methods:{handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},getList:function(){var e=this;this.loading=!0,Object(l["K"])({page:this.currentPage,limit:this.pageSize}).then((function(t){e.list=t.data.list,e.total=t.data.totalRow,e.loading=!1})).catch((function(){e.loading=!1}))},fieldFormatter:function(e,t){if("adminUser"===t.property){var a=e["adminUser"]||[];return a.map((function(e){return e.realname})).join("、")}if("memberUser"===t.property){var i=e["memberDept"]||[],s=i.map((function(e){return e.name})).join("、"),l=e["memberUser"]||[],n=l.map((function(e){return e.realname})).join("、");s&&n&&(s+="、");var r=s+n;return r}return"status"===t.property?0===e.status?"停用":"启用":e[t.property]},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"poolName"===t.property?"can-visit--underline":""},addClick:function(){this.createAction={type:"save"},this.createShow=!0},handleRowClick:function(e,t,a){"poolName"===t.property&&(this.detailId=e.poolId,this.detailShow=!0)},handleClick:function(e,t){var a=this;"edit"===e?(this.createAction={type:"update",id:t.row.poolId,data:t.row},this.createShow=!0):"transfer"===e?(this.detailId=t.row.poolId,this.transferShow=!0):"delete"===e?this.$confirm("您确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(l["H"])({poolId:t.row.poolId}).then((function(e){a.list.splice(t.$index,1),a.$message({type:"success",message:"操作成功"})})).catch((function(){}))})).catch((function(){a.$message({type:"info",message:"已取消删除"})})):"status"===e&&this.$confirm("您确定要".concat(0===t.row.status?"启用":"停用","该公海?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(l["G"])({poolId:t.row.poolId,status:0===t.row.status?1:0}).then((function(e){t.row.status=0===t.row.status?1:0,a.$message({type:"success",message:"操作成功"})})).catch((function(){}))})).catch((function(){a.$message({type:"info",message:"已取消删除"})}))}}},J=B,M=(a("a47a"),Object(_["a"])(J,i,s,!1,null,"18e7cdaa",null));t["default"]=M.exports},bfdb:function(e,t,a){},c6e9:function(e,t,a){},c757:function(e,t,a){},e231:function(e,t,a){"use strict";a("f6c6")},f6c6:function(e,t,a){}}]);