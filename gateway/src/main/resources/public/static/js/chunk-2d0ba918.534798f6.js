(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ba918"],{3885:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.disabled,clearable:""},on:{change:e.valueChange},model:{value:e.dataValue,callback:function(t){e.dataValue=t},expression:"dataValue"}},e._l(e.option,(function(e,t){return a("el-option",{key:t,attrs:{label:e.num,value:e.receivablesPlanId}})})))},i=[],l=(a("7db0"),a("e9f5"),a("f665"),a("a9e3"),a("d3b7"),{data:function(){return{dataValue:""}},watch:{value:function(e){this.dataValue=e}},props:{value:{type:[String,Number],default:""},index:Number,item:Object,disabled:{type:Boolean,default:!1}},mounted:function(){this.dataValue=this.value},methods:{valueChange:function(e){this.$emit("value-change",{index:this.index,value:e})}}}),o=a("77dc"),u={name:"XhReceivablesPlan",components:{},mixins:[l],props:{relation:{type:Object,default:function(){return{}}},receivablesId:[String,Number]},data:function(){return{option:[]}},computed:{},watch:{relation:function(e){e.moduleType?this.getPlanList():this.option=[]}},mounted:function(){this.relation.moduleType&&this.getPlanList()},methods:{getPlanList:function(){var e=this;this.loading=!0;var t={contractId:this.relation.contractId};this.receivablesId&&(t.receivablesId=this.receivablesId),Object(o["v"])(t).then((function(t){e.loading=!1,e.option=t.data})).catch((function(){e.loading=!1}))},valueChange:function(e){this.$emit("value-change",{index:this.index,value:e,data:this.option.find((function(t){return t.receivablesPlanId==e}))})}}},c=u,d=a("2877"),s=Object(d["a"])(c,n,i,!1,null,"276fd164",null);t["default"]=s.exports}}]);