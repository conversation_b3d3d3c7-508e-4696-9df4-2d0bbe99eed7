(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"17b4":function(e,t,i){},"1bfc":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-filter-header"},[i("flexbox",{directives:[{name:"show",rawName:"v-show",value:!e.selectionList||0===e.selectionList.length,expression:"!selectionList || selectionList.length === 0"}],staticClass:"filter-wrap"},[e.config.showSearch?i("el-input",{staticClass:"search-input",attrs:{placeholder:e.config.searchPlaceholder},on:{input:e.inputChange},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchInput(t)}},model:{value:e.inputContent,callback:function(t){e.inputContent="string"===typeof t?t.trim():t},expression:"inputContent"}},[i("el-button",{attrs:{slot:"suffix",type:"icon",icon:"wk wk-sousuo"},on:{click:e.searchInput},slot:"suffix"})],1):e._e(),e._v(" "),e._t("left-start"),e._v(" "),i("div",{ref:"filterTabs",staticClass:"tabs"},[e.showTabs&&e.showTabs.length>0?[i("span",{staticClass:"tabs-label"},[e._v("显示:")]),e._v(" "),e._l(e.showTabs,(function(t,n){return i("el-button",{key:n,attrs:{title:t.label,type:t.value===e.currentActiveTab?"selected":null,icon:t.icon},on:{click:function(i){e.tabsClick(t)}}},[e._v(e._s(t.label))])}))]:e._e(),e._v(" "),e.config.tabSetShow||e.otherTabs.length>0?i("el-dropdown",{attrs:{trigger:"click"},on:{command:e.otherClick}},[i("el-button",{staticClass:"dropdown-btn",attrs:{type:e.otherActiveTabName?"selected":null}},[i("span",[e._v(e._s(e.otherActiveTabName))]),e._v(" "),i("i",{staticClass:"el-icon-arrow-down",staticStyle:{"margin-left":"0"}})]),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e._l(e.otherTabs,(function(t,n){return i("el-dropdown-item",{key:n,class:{"is-select":e.currentActiveTab===t.value},attrs:{command:t.value}},[e._v("\n            "+e._s(t.label)+"\n          ")])})),e._v(" "),e.config.tabSetShow?i("el-dropdown-item",{attrs:{divided:e.otherTabs.length>0,command:e.config.tabSetCommand}},[i("span",{staticClass:"wk-filter-btn"},[i("i",{staticClass:"wk wk-manage"}),e._v(e._s(e.config.tabSetLabel)+"\n            ")])]):e._e()],2)],1):e._e(),e._v(" "),e._t("left-end")],2),e._v(" "),i("div",{staticClass:"filter-right"},[e._t("right")],2)],2),e._v(" "),e.selectionList&&e.selectionList.length>0?i("flexbox",{staticClass:"selection-bar"},[i("div",{staticClass:"selected—title"},[e._v("已选中 "),i("span",{staticClass:"selected—count"},[e._v(e._s(e.selectionList.length))]),e._v(" 项")]),e._v(" "),i("flexbox",{staticClass:"selection-items-box"},e._l(e.operations,(function(t,n){return i("el-button",{key:n,attrs:{icon:t.icon,size:"medium"},on:{click:function(i){e.selectionBarClick(t.type)}}},[e._v(e._s(t.name))])})))],1):e._e(),e._v(" "),e._t("default")],2)},a=[],s=i("5530"),o=(i("4de4"),i("7db0"),i("a630"),i("caad"),i("fb6a"),i("e9f5"),i("910d"),i("f665"),i("a9e3"),i("d3b7"),i("ac1f"),i("3ca3"),i("841c"),i("8ed6")),l=i("7a1a"),c={showSearch:!0,searchPlaceholder:"",maxTabCount:5,tabSetShow:!1,tabSetLabel:"设置",tabSetCommand:"set"},r={name:"WkFilterHeader",components:{},props:{props:Object,search:String,selectionList:Array,activeTab:[String,Number],tabs:Array,operations:{type:Array,default:function(){return[]}}},data:function(){return{inputContent:"",currentActiveTab:"",currentMaxTabCount:5}},computed:{config:function(){return Object(o["a"])(Object(s["a"])({},c),this.props||{})},showTabs:function(){return this.tabs&&this.tabs.length>this.currentMaxTabCount?this.tabs.slice(0,this.currentMaxTabCount):this.tabs},otherTabs:function(){return this.tabs&&this.tabs.length>this.currentMaxTabCount?this.tabs.slice(this.currentMaxTabCount):[]},otherActiveTabName:function(){var e=this;if(!this.tabs||!this.tabs.length||!this.otherTabs.length)return"";var t=this.otherTabs.find((function(t){return t.value===e.currentActiveTab}));return t?t.label:""}},watch:{activeTab:{handler:function(){void 0!==this.activeTab&&null!==this.activeTab&&this.currentActiveTab!==this.activeTab&&(this.currentActiveTab=this.activeTab)},immediate:!0},tabs:{handler:function(){this.changeTabsSize()},immediate:!0},search:{handler:function(){this.inputContent!==this.search&&(this.inputContent=this.search)},immediate:!0}},created:function(){this.debounceResizeWindow=Object(l["debounce"])(300,this.resizeWindow)},mounted:function(){this.debounceResizeWindow(),window.addEventListener("resize",this.debounceResizeWindow)},beforeDestroy:function(){window.removeEventListener("resize",this.debounceResizeWindow)},methods:{resizeWindow:function(){this.currentMaxTabCount=this.config.maxTabCount,this.changeTabsSize()},changeTabsSize:function(){var e=this;this.$nextTick((function(){var t=e.$refs.filterTabs;if(t)for(var i=t.getBoundingClientRect(),n=i.x+i.width,a=Array.from(t.childNodes||[]).filter((function(e){return["DIV","BUTTON","SPAN"].includes(e.tagName)})),s=a.length-1;s>=0;s--){var o=a[s],l=o.getBoundingClientRect(),c=l.x+l.width;if(!(c>n))break;e.currentMaxTabCount--}}))},searchInput:function(){this.$emit("event-change","search",this.inputContent)},inputChange:function(){this.$emit("update:search",this.inputContent),this.$emit("event-change","searchUpdate",this.inputContent)},otherClick:function(e){e===this.config.tabSetCommand?this.$emit("event-change",e):this.tabsClick(this.tabs.find((function(t){return t.value===e})))},tabsClick:function(e){this.currentActiveTab=e.value,this.$emit("update:activeTab",this.currentActiveTab),this.$emit("tabs-change",e)},selectionBarClick:function(e){this.$emit("operations-click",e)}}},d=r,u=(i("70b7"),i("2dce"),i("2877")),f=Object(u["a"])(d,n,a,!1,null,"9f7484f8",null);t["a"]=f.exports},"2dce":function(e,t,i){"use strict";i("7860")},"37af":function(e,t,i){"use strict";i("e5a7")},3813:function(e,t,i){"use strict";i("b2dd")},3932:function(e,t,i){"use strict";i("9373")},"43b6":function(e,t,i){"use strict";i.d(t,"a",(function(){return we})),i.d(t,"b",(function(){return f}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"add-node-btn"},[i("el-popover",{attrs:{"visible-arrow":!1,placement:"right",trigger:"click","popper-class":"no-padding-popover"},model:{value:e.popoverShow,callback:function(t){e.popoverShow=t},expression:"popoverShow"}},[i("div",{staticClass:"add-node-approve"},[i("div",{staticClass:"add-node-approve-btn",on:{click:function(t){e.selectClick("approve")}}},[i("i",{staticClass:"wk wk-associate is-orange"}),e._v("审批人\n      ")]),e._v(" "),i("div",{staticClass:"add-node-approve-btn",on:{click:function(t){e.selectClick("condition")}}},[i("i",{staticClass:"wk wk-approve is-green"}),e._v("条件分支\n      ")])]),e._v(" "),i("el-button",{attrs:{slot:"reference",type:"primary",icon:"el-icon-plus",circle:""},slot:"reference"})],1)],1)},a=[],s={name:"AddNodeBtn",components:{},props:{},data:function(){return{popoverShow:!1}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{selectClick:function(e){this.popoverShow=!1,this.$emit("command",e)}}},o=s,l=i("2877"),c=Object(l["a"])(o,n,a,!1,null,null,null),r=c.exports,d=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"condition-node"},[i("div",{staticClass:"condition-node-wrap"},[i("div",{staticClass:"condition-wrap",class:{"is-error":e.node.isError},on:{click:e.click}},[i("div",{staticClass:"header"},[e.isEdit?i("el-input",{ref:"wkFlowInput",attrs:{type:"text",size:"mini",maxlength:"20"},on:{blur:e.nameInputBlur},model:{value:e.node.conditionName,callback:function(t){e.$set(e.node,"conditionName",t)},expression:"node.conditionName"}}):i("span",{staticClass:"title",on:{click:function(t){return t.stopPropagation(),e.titleEditClick(t)}}},[e._v(e._s(e.node.conditionName))]),e._v(" "),i("span",{staticClass:"priority"},[e._v("优先级"+e._s(e.index+1))]),e._v(" "),i("i",{staticClass:"el-icon-close close",on:{click:function(t){return t.stopPropagation(),e.deleteClick(t)}}})],1),e._v(" "),i("div",{staticClass:"content"},[e.node.conditionDataList&&e.node.conditionDataList.length>0?[e._v("\n          "+e._s(e.getConditonContent())+"\n        ")]:[e._v("\n          无条件\n        ")]],2)]),e._v(" "),i("add-node-btn",{on:{command:e.handleCommand}})],1)])},u=[],f=(i("99af"),i("caad"),i("a15b"),i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("e9f5"),i("7d54"),i("ab43"),i("a9e3"),i("d3b7"),i("2532"),i("159b"),i("ddb0"),{examineType:1,name:"审批人",conditionList:[],deptList:[],examineErrorHandling:2,parentLevel:1,tempParentLevel:1,roleId:"",type:1,userList:[],chooseType:1,overType:1,rangeType:1,isError:!1}),p={examineType:0,name:"",conditionList:[{conditionName:"条件",conditionDataList:[],examineDataList:[],isError:!1},{conditionName:"条件",conditionDataList:[],examineDataList:[],isError:!1}]},h={conditionName:"条件",conditionDataList:[],examineDataList:[],isError:!1},m=i("ed08"),v=[{label:"属于",value:7}],b=[{label:"完全等于",value:11},{label:"包含任意",value:7}],y=[{label:"小于",value:3},{label:"大于",value:2},{label:"小于等于",value:5},{label:"等于",value:1},{label:"大于等于",value:4},{label:"介于(两个数之间)",value:6}],g=[{label:"<",value:1},{label:"≤",value:2}];function k(e){var t={};return e.forEach((function(e){t[e.value]=e.label})),t}function C(e){return{name:"发起人",conditionType:8,userList:[],deptList:[],roleList:[],values:{userList:[],deptList:[],roleList:[]},checked:e}}var _=i("6bfe"),x={name:"WkConditionNode",components:{AddNodeBtn:r},props:{index:Number,parent:Array,conditionParent:Array,node:{type:Object,default:function(){return{}}}},data:function(){return{selectOptionsObj:k(v),checkboxOptionsObj:k(b),numberOptionsObj:k(y),numberValueOptionsObj:k(g),isEdit:!1}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{nameInputBlur:function(){this.isEdit=!1,""===this.node.conditionName&&(this.node.conditionName="条件")},titleEditClick:function(){var e=this;this.disabled||(this.isEdit=!0,this.$nextTick((function(){e.$refs.wkFlowInput.focus()})))},deleteClick:function(){this.$emit("delete")},handleCommand:function(e){"approve"===e?this.parent.splice(0,0,Object(m["D"])(f)):"condition"===e&&this.parent.splice(0,0,Object(m["D"])(p))},click:function(){this.$emit("node-click",this.$props)},getConditonContent:function(){var e=this,t=this.node.conditionDataList;return t.map((function(t){if(3===t.type){var i=t.values;if(t.setting){var n=[];t.setting.forEach((function(e){void 0!=e.value&&t.values.includes(e.value)&&n.push(e.label)})),n.length>0&&(i=n)}return"".concat(t.name," ").concat(e.selectOptionsObj[t.conditionType]," ").concat(i.join("或"))}return 9===t.type?"".concat(t.name," ").concat(e.checkboxOptionsObj[t.conditionType]," ").concat(t.values.join("、")):6===t.conditionType?"".concat(Object(_["b"])(t.leftValue)?"":t.leftValue," ").concat(e.numberValueOptionsObj[t.leftCondition]," ").concat(t.name," ").concat(e.numberValueOptionsObj[t.rightCondition]," ").concat(Object(_["b"])(t.rightValue)?"":t.rightValue):8===t.conditionType?"".concat(t.name,"属于 ").concat(t.deptList.map((function(e){return e.name})).join("或")).concat(t.deptList.length>0&&t.userList.length>0?"或":"").concat(t.userList.map((function(e){return e.realname})).join("或")).concat(t.userList.length>0&&t.roleList.length>0?"或":"").concat(t.roleList.map((function(e){return e.roleName})).join("或")):"".concat(t.name," ").concat(e.numberOptionsObj[t.conditionType]," ").concat(t.values)})).join(" 并且 ")}}},w=x,T=Object(l["a"])(w,d,u,!1,null,"0b7fb9ca",null),N=T.exports,O=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-conditon-node"},[i("div",{staticClass:"wk-conditon-node-wrap"},[i("div",{staticClass:"conditon-wrap-body"},[i("el-button",{staticClass:"add-btn",attrs:{plain:""},on:{click:e.addClick}},[e._v("添加条件")]),e._v(" "),e._l(e.node.conditionList,(function(t,n){return i("div",{key:n,staticClass:"condition"},[0===n?[i("div",{staticClass:"cover-line is-top-left"}),e._v(" "),i("div",{staticClass:"cover-line is-bottom-left"})]:e._e(),e._v(" "),n===e.node.conditionList.length-1?[i("div",{staticClass:"cover-line is-top-right"}),e._v(" "),i("div",{staticClass:"cover-line is-bottom-right"})]:e._e(),e._v(" "),i("wk-condition-node",{attrs:{index:n,parent:t.examineDataList,"condition-parent":e.node.conditionList,node:t},on:{delete:function(t){e.conditionDelete(n)},"node-click":e.nodeClick}}),e._v(" "),t.examineDataList&&t.examineDataList.length>0?[e._l(t.examineDataList,(function(n,a){return[0===n.examineType?i("wk-condition-wrap",{key:a,attrs:{index:a,node:n,parent:t.examineDataList}}):i("wk-node",{key:a,attrs:{index:a,node:n,parent:t.examineDataList},on:{"node-click":e.nodeClick}})]}))]:e._e()],2)}))],2),e._v(" "),i("div",{staticClass:"add-node-btn-wrap"},[i("add-node-btn",{on:{command:e.handleCommand}})],1)])])},S=[],L=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-node"},[i("div",{staticClass:"wk-node-wrap",class:{"is-first":!e.visibleArrow,"is-error":e.node.isError,"is-disabled":e.disabled},on:{click:e.click}},[i("div",{staticClass:"header",style:{backgroundColor:e.headerColor}},[e.isEdit?i("el-input",{ref:"wkFlowInput",attrs:{type:"text",size:"mini"},on:{blur:function(t){e.isEdit=!1}},model:{value:e.node.name,callback:function(t){e.$set(e.node,"name",t)},expression:"node.name"}}):i("span",{staticClass:"title",on:{click:function(t){return t.stopPropagation(),e.titleEditClick(t)}}},[e._v(e._s(e.node.name))]),e._v(" "),e.disabled?e._e():i("i",{staticClass:"el-icon-close close",on:{click:function(t){return t.stopPropagation(),e.deleteClick(t)}}})],1),e._v(" "),i("div",{staticClass:"body"},[e.disabled?i("div",{staticClass:"content"},[e._v(e._s(e.node.content))]):i("div",{staticClass:"content"},[e._v(e._s(e.getContent()))]),e._v(" "),e.disabled?e._e():i("i",{staticClass:"el-icon-arrow-right"})])]),e._v(" "),i("div",{staticClass:"add-node-btn-wrap"},[i("add-node-btn",{on:{command:e.handleCommand}})],1)])},j=[],I=i("eecc"),D={name:"WkNode",components:{AddNodeBtn:r},mixins:[],props:{visibleArrow:{type:Boolean,default:!0},index:Number,disabled:{type:Boolean,default:!1},headerColor:{type:String,default:"#f78b22"},parent:Array,node:{type:Object,default:function(){return{}}}},data:function(){return{isEdit:!1,examineTypeObj:I["a"],sendLevelObj:{},topLevelObj:{},levelObj:{}}},computed:{},watch:{},created:function(){for(var e=1;e<=20;e++){var t="第".concat(e,"级上级");this.levelObj[e]=t,1===e?(this.sendLevelObj[e]="直属上级",this.topLevelObj[e]="最高级上级"):(this.sendLevelObj[e]=t,this.topLevelObj[e]=t)}},mounted:function(){},beforeDestroy:function(){},methods:{titleEditClick:function(){var e=this;this.disabled||(this.isEdit=!0,this.$nextTick((function(){e.$refs.wkFlowInput.focus()})))},deleteClick:function(){this.parent.splice(this.index,1)},handleCommand:function(e){"approve"===e?this.parent.splice(this.index+1,0,Object(m["D"])(f)):"condition"===e&&this.parent.splice(this.index+1,0,Object(m["D"])(p))},getContent:function(){var e=this.node,t=e.examineType,i=e.type,n=e.userList,a=e.parentLevel,s=e.roleId,o=e.roleObj,l=e.overType,c=e.tempParentLevel,r=I["a"][t];return 1===t?1===n.length?"".concat(r," 1人"):n.length>1?"".concat(n.length,"人").concat(I["b"][i]):"请选择审批人":2===t?"".concat(this.sendLevelObj[a]):3===t?s?"".concat(o.roleName).concat(I["b"][i]):"请选择审批人":4===t?s?"发起人从".concat(o.roleName,"中自选"):r:5===t?1===i?s?0===l?"从 直属上级 到 ".concat(o.roleName):1===l?"从 直属上级 到 发起人向上的".concat(this.levelObj[a]):"":"请选择审批人":"从 直属上级 到 组织架构中由上至下的".concat(this.topLevelObj[c]):""},click:function(){this.disabled||this.$emit("node-click",this.$props)}}},$=D,E=Object(l["a"])($,L,j,!1,null,"52949144",null),F=E.exports,V={name:"WkConditionWrap",components:{AddNodeBtn:r,WkNode:F,WkConditionNode:N},props:{index:Number,parent:Array,node:{type:Object,default:function(){return{}}}},data:function(){return{tree:null}},computed:{},watch:{},created:function(){"WkConditionWrap"===this.$parent.$options.name?this.tree=this.$parent.tree:this.tree=this},mounted:function(){},beforeDestroy:function(){},methods:{addClick:function(){this.node.conditionList.push(Object(m["D"])(h))},conditionDelete:function(e){this.node.conditionList.length>2?this.node.conditionList.splice(e,1):this.parent.splice(this.index,1)},handleCommand:function(e){"approve"===e?this.parent.splice(this.index+1,0,Object(m["D"])(f)):"condition"===e&&this.parent.splice(this.index+1,0,Object(m["D"])(p))},nodeClick:function(e){this.tree.$emit("node-click",e)}}},P=V,A=Object(l["a"])(P,O,S,!1,null,null,null),W=A.exports,B=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},R=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-end-node"},[i("div",{staticClass:"circle"}),e._v(" "),i("div",{staticClass:"text"},[e._v("流程结束")])])}],H={name:"WkEndNode",components:{},props:{},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{}},q=H,M=Object(l["a"])(q,B,R,!1,null,"100619a6",null),z=M.exports,U=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-approve-flow-wrap"},[i("el-button-group",[i("el-button",{staticClass:"group-btn",attrs:{disabled:e.scale<=.5,icon:"el-icon-minus"},on:{click:function(t){e.scaleClick("minus")}}}),e._v(" "),i("el-button",{staticClass:"group-btn",attrs:{disabled:e.scale>3,icon:"el-icon-plus"},on:{click:function(t){e.scaleClick("plus")}}})],1),e._v(" "),i("div",{staticClass:"wk-approve-flow",style:{transform:"scale("+e.scale+")"}},[e.sendNode?i("wk-node",{attrs:{"visible-arrow":!1,node:e.sendNode,parent:e.list,disabled:"","header-color":"#15388b"}}):e._e(),e._v(" "),e._l(e.list,(function(t,n){return[0===t.examineType?i("wk-condition-wrap",{key:n,attrs:{index:n,node:t,parent:e.list},on:{"node-click":e.nodeClick}}):i("wk-node",{key:n,attrs:{index:n,parent:e.list,node:t},on:{"node-click":e.nodeClick}})]})),e._v(" "),i("wk-end-node"),e._v(" "),i("wk-condition-set",{attrs:{visible:e.conditionSetVisible,node:e.editNode,"condition-parent":e.conditionParent,"condition-parent-index":e.conditionParentIndex,props:e.config},on:{"update:visible":function(t){e.conditionSetVisible=t}}}),e._v(" "),i("wk-node-set",{attrs:{visible:e.nodeSetVisible,node:e.editNode,props:e.config},on:{"update:visible":function(t){e.nodeSetVisible=t}}})],2)],1)},J=[],Y=i("5530"),Q=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-drawer",{attrs:{visible:e.visible,"with-header":!1,size:"500px",title:"我是标题","append-to-body":""},on:{close:e.close}},[e.editNode?i("flexbox",{staticClass:"drawer-header"},[i("div",{staticClass:"edit-title"},[e.isNameEdit?i("el-input",{ref:"conditionNameInput",attrs:{maxlength:"20"},on:{blur:e.nameInputBlur},model:{value:e.editNode.conditionName,callback:function(t){e.$set(e.editNode,"conditionName",t)},expression:"editNode.conditionName"}}):[i("span",{staticClass:"title",on:{click:e.titleEditClick}},[e._v(e._s(e.editNode.conditionName))]),e._v(" "),i("i",{staticClass:"wk wk-edit",on:{click:e.titleEditClick}})]],2),e._v(" "),i("el-popover",{attrs:{placement:"bottom",width:"200",trigger:"click"}},[i("draggable",{attrs:{options:{group:"list",forceFallback:!1,fallbackClass:"draggingStyle",filter:"drag-item__label",preventOnFilter:!1}},model:{value:e.dragList,callback:function(t){e.dragList=t},expression:"dragList"}},e._l(e.dragList,(function(t,n){return i("flexbox",{key:n,staticClass:"drag-item"},[i("div",{staticClass:"drag-item__label"},[e._v(e._s(t.conditionName))]),e._v(" "),i("div",{staticClass:"drag-item__handle"},[e._v("⋮⋮")])])}))),e._v(" "),i("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[e._v("优先级设置")])],1),e._v(" "),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"26","data-id":"236"}}),e._v(" "),i("i",{staticClass:"el-icon-close ",on:{click:e.close}})],1):e._e(),e._v(" "),e.editNode?i("div",{staticClass:"drawer-body"},[e._l(e.editNode.conditionDataList,(function(t,n){return i("el-row",{key:n,staticClass:"set-row",attrs:{gutter:20,type:"flex",align:"middle"}},[i("el-col",{staticClass:"set-row__title",attrs:{span:5}},[e._v(e._s(t.name))]),e._v(" "),i("el-col",{staticClass:"set-row__center",attrs:{span:17}},[8===t.conditionType?i("flexbox",[i("div",{staticStyle:{flex:"1"}},[i("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":t.values.userList,"dep-value":t.values.deptList,placeholder:"请选择员工或部门"},on:{"update:userValue":function(i){e.$set(t.values,"userList",i)},"update:depValue":function(i){e.$set(t.values,"deptList",i)},change:function(i){e.userDepSelectChange(arguments,t)}}}),e._v(" "),i("role-employee-select",{ref:"roleSelect",refInFor:!0,staticStyle:{width:"100%","margin-top":"8px"},attrs:{props:{onlyShowRole:!0},multiple:"",placeholder:"请选择角色","collapse-tags":"",clearable:""},on:{change:function(i){e.roleSelectChange(t)}},model:{value:t.values.roleList,callback:function(i){e.$set(t.values,"roleList",i)},expression:"item.values.roleList"}})],1),e._v(" "),i("div",{staticStyle:{"flex-shrink":"0","margin-left":"8px"}},[e._v("或")])]):[9,3].includes(t.type)?[3==t.type?i("el-select",{staticClass:"condition-select",model:{value:t.conditionType,callback:function(i){e.$set(t,"conditionType",i)},expression:"item.conditionType"}},e._l(e.selectOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):9==t.type?i("el-select",{staticClass:"condition-select",model:{value:t.conditionType,callback:function(i){e.$set(t,"conditionType",i)},expression:"item.conditionType"}},e._l(e.checkboxOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),i("el-select",{staticClass:"condition-value",attrs:{multiple:""},model:{value:t.values,callback:function(i){e.$set(t,"values",i)},expression:"item.values"}},e._l(t.setting,(function(e,t){return i("el-option",{key:t,attrs:{label:void 0!==e.value?e.label:e,value:void 0!==e.value?e.value:e}})})))]:[i("el-select",{class:["condition-select",{"is-block":6===t.conditionType}],model:{value:t.conditionType,callback:function(i){e.$set(t,"conditionType",i)},expression:"item.conditionType"}},e._l(e.numberOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),6===t.conditionType?[i("div",{staticStyle:{"margin-top":"10px"}},[i("el-input-number",{staticClass:"small",attrs:{controls:!1},model:{value:t.leftValue,callback:function(i){e.$set(t,"leftValue",i)},expression:"item.leftValue"}}),e._v(" "),i("el-select",{staticClass:"small is-condition",model:{value:t.leftCondition,callback:function(i){e.$set(t,"leftCondition",i)},expression:"item.leftCondition"}},e._l(e.numberValueOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),i("span",{staticClass:"small-select-label"},[e._v(e._s(t.name))]),e._v(" "),i("el-select",{staticClass:"small is-condition",model:{value:t.rightCondition,callback:function(i){e.$set(t,"rightCondition",i)},expression:"item.rightCondition"}},e._l(e.numberValueOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),i("el-input-number",{staticClass:"small",attrs:{controls:!1},model:{value:t.rightValue,callback:function(i){e.$set(t,"rightValue",i)},expression:"item.rightValue"}})],1)]:i("el-input-number",{staticClass:"condition-value",attrs:{controls:!1},model:{value:t.values,callback:function(i){e.$set(t,"values",i)},expression:"item.values"}})]],2),e._v(" "),i("el-col",{staticClass:"set-row__footer",attrs:{span:1}},[i("i",{staticClass:"wk wk-delete",on:{click:function(t){e.deleteItem(n)}}})])],1)})),e._v(" "),i("div",[i("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:e.conditionSelectClick}},[e._v("添加条件")]),e._v(" "),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"26","data-id":"237"}})],1)],2):e._e(),e._v(" "),i("div",{staticClass:"drawer-footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),i("el-button",{nativeOn:{click:function(t){return e.close(t)}}},[e._v("取消")])],1),e._v(" "),i("wk-condition-select",{attrs:{props:e.props,checks:e.editNode?e.editNode.conditionDataList:[],visible:e.conditionSelectVisible},on:{"update:visible":function(t){e.conditionSelectVisible=t},confirm:e.conditionSelectConfirm}})],1)},X=[],G=(i("4de4"),i("7db0"),i("910d"),i("f665"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,title:"选择条件",width:"600px","custom-class":"no-padding-dialog"},on:{close:e.handleCancel}},[i("div",{staticStyle:{padding:"20px"}},[i("div",[e._v("请选择用来区分审批流程的条件字段 ")]),e._v(" "),i("div",{staticStyle:{"margin-top":"10px"}},e._l(e.list,(function(t,n){return i("el-checkbox",{key:n,attrs:{label:t.name},model:{value:t.checked,callback:function(i){e.$set(t,"checked",i)},expression:"item.checked"}})})))]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("确定")]),e._v(" "),i("el-button",{nativeOn:{click:function(t){return e.handleCancel(t)}}},[e._v("取消")])],1)])}),K=[],Z={name:"WkConditionSelect",components:{},inheritAttrs:!1,props:{visible:{type:Boolean,required:!0,default:!1},checks:Array,props:{type:Object,default:function(){return{}}}},data:function(){return{list:[]}},computed:{},watch:{visible:function(e){e&&this.getList()}},methods:{handleConfirm:function(){this.$emit("confirm",this.list.filter((function(e){return e.checked}))),this.handleCancel()},getList:function(){var e=this,t=this.props,i=t.conditionSelectRequest,n=t.conditionSelectParams,a=t.conditionSelectList;a?this.handleList(Object(m["D"])(a)):i(n).then((function(t){var i=t.data||[];e.handleList(i)})).catch((function(){}))},handleList:function(e){var t=this,i=[],n=null;this.checks&&this.checks.forEach((function(e){8===e.conditionType?n=e:i.push(e.fieldName||e.name)}));var a=[];e.forEach((function(e,n){i.includes(e.fieldName||e.name)?(a.push(n),t.$set(e,"checked",!0)):t.$set(e,"checked",!1)})),a.forEach((function(i){var n=e[i],a=t.checks.find((function(e){return e.fieldName===n.fieldName||e.name===n.name}));t.$set(a,"checked",!0),e.splice(i,1,a)})),n?(this.$set(n,"checked",!0),e.splice(0,0,n),this.list=e):(e.splice(0,0,Object(m["D"])(C(!1))),this.list=e)},handleCancel:function(){this.$emit("update:visible",!1)}}},ee=Z,te=Object(l["a"])(ee,G,K,!1,null,"412745e0",null),ie=te.exports,ne=i("b76a"),ae=i.n(ne),se=i("b592"),oe=i("94d4"),le={name:"WkConditionSet",components:{WkConditionSelect:ie,Draggable:ae.a,WkUserDepDialogSelect:se["a"],RoleEmployeeSelect:oe["a"]},props:{visible:{type:Boolean,required:!0,default:!1},node:{type:Object,default:function(){return{}}},conditionParent:Array,conditionParentIndex:Number,props:{type:Object,default:function(){return{}}}},data:function(){return{editNode:null,isNameEdit:!1,selectOptions:v,checkboxOptions:b,numberOptions:y,numberValueOptions:g,conditionSelectVisible:!1,dragList:[]}},computed:{},watch:{visible:function(e){var t=this;if(e){var i=Object(m["D"])(this.conditionParent);this.editNode=i[this.conditionParentIndex],0===this.editNode.conditionDataList.length?this.editNode.conditionDataList.push(C(!0)):this.validateSetting(this.editNode.conditionDataList),i.forEach((function(e,t){e.index=t})),this.dragList=i}else{var n=Object(m["D"])(this.conditionParent);this.conditionParent.splice(0,this.conditionParent.length),this.dragList.forEach((function(e){t.conditionParent.push(n[e.index])})),this.editNode=null}}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{validateSetting:function(e){var t=this,i=[];if(e.forEach((function(e){3!==e.type&&9!==e.type||e.setting||i.push(e)})),i&&i.length>0){var n=this.props,a=n.conditionSelectRequest,s=n.conditionSelectParams,o=n.conditionSelectList;if(o){var l=Object(m["D"])(o);i.forEach((function(e){var i=l.find((function(t){return t.fieldName===e.fieldName}));i&&t.$set(e,"setting",i.setting)}))}else a(s).then((function(e){var n=e.data||[];i.forEach((function(e){var i=n.find((function(t){return t.fieldName===e.fieldName}));i&&t.$set(e,"setting",i.setting)}))})).catch((function(){}))}},nameInputBlur:function(){this.isNameEdit=!1,""===this.editNode.conditionName&&(this.editNode.conditionName="条件")},titleEditClick:function(){var e=this;this.isNameEdit=!0,this.$nextTick((function(){e.$refs.conditionNameInput.focus()}))},close:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this.editNode.conditionDataList||[];for(var t in this.editNode.conditionDataList=e.filter((function(e){return 3===e.type||9===e.type?e.values&&e.values.length>0:6===e.conditionType?!Object(_["b"])(e.leftValue)&&!Object(_["b"])(e.rightValue)&&e.leftValue<e.rightValue:8===e.conditionType?!Object(_["b"])(e.userList)||!Object(_["b"])(e.deptList)||!Object(_["b"])(e.roleList):!Object(_["b"])(e.values)})),this.editNode.isError=!1,this.editNode)"examineDataList"!==t&&(this.node[t]=this.editNode[t]);this.close()},conditionSelectClick:function(){this.conditionSelectVisible=!0},deleteItem:function(e){var t=this;this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.editNode.conditionDataList.splice(e,1)})).catch((function(){}))},conditionSelectConfirm:function(e){e.forEach((function(e){e.conditionType||(3===e.type||9===e.type?(e.conditionType=3===e.type?7:11,e.values=[]):5!==e.type&&6!==e.type||(e.conditionType=3,e.values=0,e.leftValue=0,e.leftCondition=1,e.rightCondition=1,e.rightValue=0))})),this.editNode.conditionDataList=Object(m["D"])(e)},userDepSelectChange:function(e,t){var i=e[2],n=e[3];t.userList=i,t.deptList=n},roleSelectChange:function(e){var t=this;this.$nextTick((function(){var i=t.$refs.roleSelect[0].select.selected;e.roleList=i.map((function(e){return{roleName:e.$props.label}}))}))}}},ce=le,re=(i("b1c0"),Object(l["a"])(ce,Q,X,!1,null,"e1d9c444",null)),de=re.exports,ue=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-drawer",{attrs:{visible:e.visible,"with-header":!1,size:"500px",title:"我是标题","append-to-body":""},on:{close:e.close}},[e.editNode?i("flexbox",{staticClass:"drawer-header"},[i("div",{staticClass:"edit-title"},[e.isNameEdit?i("el-input",{attrs:{maxlength:"20"},on:{blur:e.nameInputBlur},model:{value:e.editNode.name,callback:function(t){e.$set(e.editNode,"name",t)},expression:"editNode.name"}}):[i("span",{staticClass:"title",on:{click:function(t){e.isNameEdit=!0}}},[e._v(e._s(e.editNode.name))]),e._v(" "),i("i",{staticClass:"wk wk-edit",on:{click:function(t){e.isNameEdit=!0}}})]],2),e._v(" "),i("i",{staticClass:"el-icon-close ",on:{click:e.close}})]):e._e(),e._v(" "),e.editNode?i("div",{staticClass:"drawer-body"},[i("div",{staticClass:"section"},[i("el-radio-group",{on:{change:e.examineTypeChange},model:{value:e.editNode.examineType,callback:function(t){e.$set(e.editNode,"examineType",t)},expression:"editNode.examineType"}},e._l(e.examineTypeOptions,(function(t,n){return i("el-radio",{key:n,staticClass:"el-radio--type",attrs:{label:t.value}},[e._v(e._s(t.label))])}))),e._v(" "),1===e.editNode.examineType?i("div",{staticClass:"area-top"},[i("div",{staticClass:"section-handle"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.depUserViewDialogShow=!0}}},[e._v("添加员工")]),e._v(" "),i("span",{staticClass:"text-des"},[e._v("不能超过20人")])],1),e._v(" "),i("div",{staticClass:"user-list-wrap"},e._l(e.editNode.userList,(function(t,n){return i("span",{key:n,staticClass:"user-item"},[e._v("\n            "+e._s(t.realname)+"\n            "),i("i",{staticClass:"el-icon-close",on:{click:function(t){e.userDelete(n)}}})])})))]):e._e(),e._v(" "),2===e.editNode.examineType?i("div",{staticClass:"area-top"},[i("div",{staticClass:"section-handle"},[i("span",[e._v("发起人的")]),e._v(" "),i("el-select",{model:{value:e.editNode.parentLevel,callback:function(t){e.$set(e.editNode,"parentLevel",t)},expression:"editNode.parentLevel"}},e._l(e.sendLevelOption,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1),e._v(" "),i("div",{staticClass:"area-top wk-checkbox"},[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.editNode.type,callback:function(t){e.$set(e.editNode,"type",t)},expression:"editNode.type"}}),e._v(" "),i("span",{staticClass:"wk-checkbox__label"},[e._v("找不到主管时，由上级主管代审批")])],1)]):e._e(),e._v(" "),3===e.editNode.examineType?i("div",{staticClass:"area-top"},[i("role-employee-select",{ref:"roleSelect",attrs:{props:{onlyShowRole:!0},clearable:""},on:{change:e.roleSelectChange},model:{value:e.editNode.roleId,callback:function(t){e.$set(e.editNode,"roleId",t)},expression:"editNode.roleId"}})],1):e._e(),e._v(" "),4===e.editNode.examineType?i("div",{staticClass:"area-top"},[i("div",{staticClass:"section-handle"},[i("el-select",{model:{value:e.editNode.chooseType,callback:function(t){e.$set(e.editNode,"chooseType",t)},expression:"editNode.chooseType"}},[i("el-option",{attrs:{value:1,label:"自选一人"}}),e._v(" "),i("el-option",{attrs:{value:2,label:"自选多人"}})],1)],1),e._v(" "),i("div",{staticClass:"area-top"},[i("div",{staticClass:"section__title"},[e._v("选择范围")]),e._v(" "),i("div",{staticClass:"area-top"},[i("el-select",{on:{change:e.rangeTypeChange},model:{value:e.editNode.rangeType,callback:function(t){e.$set(e.editNode,"rangeType",t)},expression:"editNode.rangeType"}},[i("el-option",{attrs:{value:1,label:"全公司"}}),e._v(" "),i("el-option",{attrs:{value:2,label:"指定成员"}}),e._v(" "),i("el-option",{attrs:{value:3,label:"指定角色"}})],1),e._v(" "),2===e.editNode.rangeType?[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.depUserViewDialogShow=!0}}},[e._v("添加员工")])]:e._e(),e._v(" "),3===e.editNode.rangeType?i("role-employee-select",{ref:"roleSelect",attrs:{props:{onlyShowRole:!0},clearable:""},on:{change:e.roleSelectChange},model:{value:e.editNode.roleId,callback:function(t){e.$set(e.editNode,"roleId",t)},expression:"editNode.roleId"}}):e._e()],2),e._v(" "),2===e.editNode.rangeType&&e.editNode.userList.length>0?i("div",{staticClass:"user-list-wrap",staticStyle:{"margin-top":"8px"}},e._l(e.editNode.userList,(function(t,n){return i("span",{key:n,staticClass:"user-item"},[e._v("\n              "+e._s(t.realname)+"\n              "),i("i",{staticClass:"el-icon-close",on:{click:function(t){e.userDelete(n)}}})])}))):e._e()])]):e._e(),e._v(" "),5===e.editNode.examineType?i("div",{staticClass:"area-top"},[i("div",{staticClass:"section__title"},[e._v("审批终点")]),e._v(" "),i("el-radio-group",{staticClass:"el-radio-group--block",on:{change:e.endTypeChange},model:{value:e.editNode.type,callback:function(t){e.$set(e.editNode,"type",t)},expression:"editNode.type"}},[i("el-radio",{staticClass:"el-radio--block",attrs:{label:1}},[i("div",[e._v("指定角色（连续多级上级须包含该角色）"),i("role-employee-select",{ref:"roleSelect",attrs:{props:{onlyShowRole:!0},clearable:""},on:{change:e.roleSelectChange},model:{value:e.editNode.roleId,callback:function(t){e.$set(e.editNode,"roleId",t)},expression:"editNode.roleId"}})],1)]),e._v(" "),i("div",{staticClass:"area-top wk-checkbox",staticStyle:{"margin-left":"20px","line-height":"34px"}},[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.editNode.overType,callback:function(t){e.$set(e.editNode,"overType",t)},expression:"editNode.overType"}}),e._v(" "),i("span",{staticClass:"wk-checkbox__label",staticStyle:{"margin-right":"8px","font-size":"13px"}},[e._v("同时不超过发起人向上的")]),e._v(" "),i("el-select",{model:{value:e.editNode.parentLevel,callback:function(t){e.$set(e.editNode,"parentLevel",t)},expression:"editNode.parentLevel"}},e._l(e.levelOption,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1),e._v(" "),i("el-radio",{staticClass:"el-radio--block area-top",attrs:{label:2}},[i("span",[e._v("组织架构中由上至下的")]),i("el-select",{staticStyle:{"margin-left":"5px"},model:{value:e.editNode.tempParentLevel,callback:function(t){e.$set(e.editNode,"tempParentLevel",t)},expression:"editNode.tempParentLevel"}},e._l(e.topLevelOption,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1)],1)],1):e._e()],1),e._v(" "),e.waySectionShow?i("div",{staticClass:"section"},[i("div",{staticClass:"section__title"},[i("span",[e._v("多人审批时采用的审批方式")])]),e._v(" "),i("div",{staticClass:"section__content"},[i("el-radio-group",{model:{value:e.editNode.type,callback:function(t){e.$set(e.editNode,"type",t)},expression:"editNode.type"}},[3!==e.editNode.examineType?i("el-radio",{staticClass:"el-radio--block",attrs:{label:1}},[e._v("依次审批")]):e._e(),e._v(" "),i("el-radio",{staticClass:"el-radio--block",attrs:{label:2}},[e._v("会签（需所有审批人同意）")]),e._v(" "),i("el-radio",{staticClass:"el-radio--block",attrs:{label:3}},[e._v("或签（一名审批人同意或拒绝即可）")])],1)],1)]):e._e(),e._v(" "),i("div",{staticClass:"section"},[i("div",{staticClass:"section__title"},[i("span",[e._v("审批人为空时")]),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"26","data-id":"235"}})]),e._v(" "),i("div",{staticClass:"section__content"},[i("el-radio-group",{model:{value:e.editNode.examineErrorHandling,callback:function(t){e.$set(e.editNode,"examineErrorHandling",t)},expression:"editNode.examineErrorHandling"}},[i("el-radio",{staticClass:"el-radio--block",attrs:{label:2}},[e._v("自动转交管理员")])],1)],1)])]):e._e(),e._v(" "),i("div",{staticClass:"drawer-footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),i("el-button",{nativeOn:{click:function(t){return e.close(t)}}},[e._v("取消")])],1),e._v(" "),e.depUserViewDialogShow?i("wk-dep-user-dialog",{attrs:{"user-value":(e.editNode.userList||[]).map((function(e){return e.userId})),visible:e.depUserViewDialogShow,props:{showDisableUser:!1,showDept:!1}},on:{"update:visible":function(t){e.depUserViewDialogShow=t},change:e.selectUserChange}}):e._e()],1)},fe=[],pe=(i("fb6a"),i("2581")),he={methods:{getWkWayShowStatus:function(e){return!!e&&(1===e.examineType&&e.userList.length>1||3===e.examineType&&e.roleId||4===e.examineType&&2===e.chooseType&&(1===e.rangeType||2===e.rangeType&&e.userList.length>1||3===e.rangeType&&e.roleId))},getWkNodeErrorStatus:function(e){return 1===e.examineType?0===e.userList.length:2!==e.examineType&&(3===e.examineType?Object(_["b"])(e.roleId):4===e.examineType?2===e.rangeType?0===e.userList.length:3===e.rangeType&&Object(_["b"])(e.roleId):5===e.examineType?1===e.type?Object(_["b"])(e.roleId):2===e.type&&Object(_["b"])(e.tempParentLevel):void 0)}}},me={name:"WkNodeSet",components:{WkDepUserDialog:pe["default"],RoleEmployeeSelect:oe["a"]},mixins:[he],props:{visible:{type:Boolean,required:!0,default:!1},node:{type:Object,default:function(){return{}}},props:{type:Object,default:function(){return{}}}},data:function(){return{editNode:null,isNameEdit:!1,examineTypeOptions:[{label:"指定成员",value:1},{label:"上级",value:2},{label:"角色",value:3},{label:"发起人自选",value:4},{label:"连续多级上级",value:5}],sendLevelOption:[],topLevelOption:[],levelOption:[],depUserViewDialogShow:!1}},computed:{waySectionShow:function(){return this.getWkWayShowStatus(this.editNode)}},watch:{visible:function(e){this.editNode=e?Object(m["D"])(this.node):null}},created:function(){for(var e=1;e<=20;e++){var t="第".concat(e,"级上级");this.levelOption.push({label:t,value:e}),1===e?(this.sendLevelOption.push({label:"直属上级",value:e}),this.topLevelOption.push({label:"最高级上级",value:e})):(this.sendLevelOption.push({label:t,value:e}),this.topLevelOption.push({label:t,value:e}))}},mounted:function(){},beforeDestroy:function(){},methods:{nameInputBlur:function(){this.isNameEdit=!1,""===this.editNode.name&&(this.editNode.name="审批人")},close:function(){this.$emit("update:visible",!1)},handleConfirm:function(){for(var e in this.editNode.isError=this.getWkNodeErrorStatus(this.editNode),this.editNode)"conditionList"!==e&&(this.node[e]=this.editNode[e]);this.close()},examineTypeChange:function(){this.editNode.parentLevel=1,this.editNode.userList=[],2===this.editNode.examineType?this.editNode.type=1:2===this.editNode.examineType?this.editNode.type=0:3===this.editNode.examineType?this.editNode.type=2:4===this.editNode.examineType?(this.editNode.rangeType||(this.editNode.rangeType=1),this.editNode.chooseType=1,this.editNode.type=1):5===this.editNode.examineType&&(this.editNode.type=1),this.editNode.examineErrorHandling=2,this.editNode.roleId="",this.editNode.chooseType=1},selectUserChange:function(e,t,i){i.length>0?1===this.editNode.examineType?(this.editNode.userList=i.length>20?i.slice(0,20):i,this.editNode.type=1):this.editNode.userList=i:this.editNode.userList=[]},userDelete:function(e){this.editNode.userList.splice(e,1)},rangeTypeChange:function(){this.editNode.userList=[],this.editNode.roleId="",this.editNode.type=1},endTypeChange:function(){this.editNode.overType=0},roleSelectChange:function(){var e=this;this.$nextTick((function(){e.editNode.roleId?e.$set(e.editNode,"roleObj",{roleName:e.$refs.roleSelect.select.selectedLabel}):e.$set(e.editNode,"roleObj",null)}))}}},ve=me,be=(i("c96d"),i("b5d5"),Object(l["a"])(ve,ue,fe,!1,null,"f3c27842",null)),ye=be.exports,ge=i("8ed6"),ke={conditionSelectRequest:null,conditionSelectParams:null},Ce={name:"WkApproveFlow",components:{WkNode:F,WkConditionWrap:W,AddNodeBtn:r,WkEndNode:z,WkConditionSet:de,WkNodeSet:ye},mixins:[he],props:{props:{type:Object,default:function(){return{}}},sendNode:Object,list:Array},data:function(){return{editNode:null,editNodeIndex:null,conditionParent:[],conditionParentIndex:0,conditionSetVisible:!1,nodeSetVisible:!1,errorList:[],scale:1}},computed:{config:function(){return Object(ge["a"])(Object(Y["a"])({},ke),this.props||{})}},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{nodeClick:function(e){var t=e.node,i=e.index,n=e.conditionParent;this.editNode=t,t.examineType>0?this.nodeSetVisible=!0:(this.conditionParent=n,this.conditionParentIndex=i,this.conditionSetVisible=!0)},scaleClick:function(e){"minus"===e?this.scale=this.scale-.1:"plus"===e&&(this.scale=this.scale+.1)},getParams:function(){this.errorList=[];var e=[];return this.getListParams(this.list,e),this.errorList.length>0?{isError:!0,list:this.errorList}:{isError:!1,list:e}},getListParams:function(e,t){var i=this;e.forEach((function(e){0===e.examineType?t.push(i.getConditonWrapParams(e)):t.push(i.getNodeParams(e))}))},getNodeParams:function(e){e.isError=this.getWkNodeErrorStatus(e),e.isError&&this.errorList.push(e);var t={examineType:e.examineType,name:e.name,deptList:e.deptList,examineErrorHandling:e.examineErrorHandling,roleId:e.roleId,type:e.type,userList:e.userList.map((function(e){return e.userId})),chooseType:e.chooseType,rangeType:e.rangeType};return 5===e.examineType?1===e.type?t.parentLevel=1===e.overType?e.parentLevel:0:2===e.type&&(t.parentLevel=e.tempParentLevel):t.parentLevel=e.parentLevel,t},getConditionNodeParams:function(e,t){e.isError=!1,e.isError&&this.errorList.push(e);var i={conditionName:e.conditionName,sort:t+1,conditionDataList:[]};return e.conditionDataList.forEach((function(e){var t={name:e.name,fieldName:e.fieldName,type:e.type,fieldId:e.fieldId,conditionType:e.conditionType};3===e.type||9===e.type?t.values=e.values:6===e.conditionType?t.values=[e.leftValue,e.leftCondition,e.rightCondition,e.rightValue]:8===e.conditionType?t.values=e.values:t.values=[e.values],i.conditionDataList.push(t)})),i},getConditonWrapParams:function(e){var t=this,i={examineType:e.examineType,name:e.name,conditionList:[]};return e.conditionList.forEach((function(e,n){var a=t.getConditionNodeParams(e,n);a.examineDataList=[],i.conditionList.push(a),t.getListParams(e.examineDataList,a.examineDataList)})),i}}},_e=Ce,xe=(i("c377"),Object(l["a"])(_e,U,J,!1,null,null,null)),we=xe.exports},"45f9":function(e,t,i){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},4960:function(e,t,i){},"4e1a":function(e,t,i){},"4eee":function(e,t,i){},5579:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-empty"},[i("div",{staticClass:"wk-empty__title"},[e._v(e._s(e.config.emptyText||"暂无数据"))]),e._v(" "),e.config.showButton?i("el-button",{staticStyle:{"margin-top":"8px"},attrs:{icon:e.config.buttonIcon,type:"primary"},on:{click:e.btnClick}},[e._v(e._s(e.config.buttonTitle||"新建"))]):e._e()],1)},a=[],s=i("5530"),o=i("8ed6"),l={emptyText:"",showButton:!1,buttonIcon:"",buttonTitle:""},c={name:"WkEmpty",components:{},props:{props:Object},data:function(){return{}},computed:{config:function(){return Object(o["a"])(Object(s["a"])({},l),this.props||{})}},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{btnClick:function(){this.$emit("click")}}},r=c,d=(i("7442"),i("2877")),u=Object(d["a"])(r,n,a,!1,null,null,null);t["a"]=u.exports},"55f0":function(e,t,i){"use strict";i("4960")},"5be9":function(e,t,i){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},"5d2e":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wk-table-header"},[i("wk-filter-header",e._g(e._b({attrs:{props:e.filterHeaderConfig},on:{"event-change":e.filterHeaderHandle}},"wk-filter-header",e.$attrs,!1),e.$listeners),[i("template",{slot:"left-start"},[e._t("custom")],2),e._v(" "),i("template",{slot:"right"},[e.exportFields.length>0?i("el-button",{staticClass:"collapse-button",attrs:{type:e.exportFieldsShow?"selected":"text"},on:{click:function(t){e.collapseHandle()}}},[e._v("\n        "+e._s(e.exportFieldsShow?"收起筛选":"展开筛选")+"\n        "),i("i",{class:["el-icon-arrow-up",{"is-reverse":!e.exportFieldsShow}]})]):e._e(),e._v(" "),e.config.showFilterView?i("el-button",{staticClass:"filter-button",class:{"has-values":e.filterValidCount>0},attrs:{type:"subtle",icon:"wk wk-screening"},on:{click:e.showFilterClick}},[e._v("高级筛选"),e.filterValidCount>0?i("span",{staticClass:"values-span",attrs:{closable:"",type:"info"}},[e._v("\n          "+e._s(e.filterValidCount)),i("i",{staticClass:"el-icon-close",on:{click:function(t){t.stopPropagation(),e.clearFilterVal(e.filterObj.form)}}})]):e._e()]):e._e(),e._v(" "),e._t("default")],2)],2),e._v(" "),e.exportFields.length>0&&e.exportFieldsShow?i("filter-export-fields",{attrs:{"condition-type-fun":e.conditionTypeFun,form:e.exportFields,"field-list":e.fieldList},on:{filter:e.exportFilter,clear:e.clearFilterVal}}):e._e(),e._v(" "),i("scene-set",{attrs:{"dialog-visible":e.sceneSetShow,props:e.sceneSetProps,"create-props":e.sceneCreateProps,"condition-type-fun":e.conditionTypeFun},on:{"update:dialogVisible":function(t){e.sceneSetShow=t},"save-success":e.updateSceneList}}),e._v(" "),e.config.showFilterView?i("filter-form",{attrs:{"field-list":e.fieldList,"dialog-visible":e.filterShow,obj:e.filterObj,"condition-type-fun":e.conditionTypeFun,props:e.filterFormProps},on:{"update:dialogVisible":function(t){e.filterShow=t},filter:e.handleFilter}}):e._e()],1)},a=[],s=i("c14f"),o=i("1da1"),l=i("5530"),c=(i("4de4"),i("caad"),i("14d9"),i("e9f5"),i("910d"),i("7d54"),i("e9c4"),i("d3b7"),i("159b"),i("ddb0"),i("1bfc")),r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"filter-export-fields"},[i("div",{staticClass:"title"},[e._v("筛选条件")]),e._v(" "),i("filter-fields",{attrs:{"condition-type-fun":e.conditionTypeFun,form:e.form,"field-list":e.fieldList,"is-export":""}}),e._v(" "),i("div",{staticClass:"filter-export-fields__handle"},[i("el-button",{attrs:{type:"primary"},on:{click:e.filterClick}},[e._v("筛选")]),e._v(" "),i("el-button",{on:{click:e.clearClick}},[e._v("清空外露筛选值")])],1)],1)},d=[],u=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{ref:"filterFieldsForm",staticClass:"filter-fields"},e._l(e.form,(function(t,n){return i("el-row",{key:n},[i("el-col",{attrs:{span:6}},[i("el-select",{attrs:{disabled:e.isExport,placeholder:"请选择要筛选的字段名"},on:{change:function(i){e.fieldChange(t)},focus:e.fieldFocus},model:{value:t.fieldName,callback:function(i){e.$set(t,"fieldName",i)},expression:"formItem.fieldName"}},e._l(e.fieldList,(function(e){return i("el-option",{key:e.fieldName,attrs:{label:e.name,value:e.fieldName}})})))],1),e._v(" "),e.showCalCondition(t.formType,t.fieldName,t)?[i("el-col",{staticClass:"interval-base",attrs:{span:1}},[e._v(" ")]),e._v(" "),i("el-col",{attrs:{span:4}},[i("el-select",{attrs:{placeholder:"请选择范围"},on:{change:function(i){e.selectChange(i,t)}},model:{value:t.condition,callback:function(i){e.$set(t,"condition",i)},expression:"formItem.condition"}},e._l(e.getAdvancedFilterOptions(e.conditionTypeFun?e.conditionTypeFun(t):t.formType,t),(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1)]:e._e(),e._v(" "),"business_cause"==t.formType?i("el-col",{staticClass:"interval-base",attrs:{span:1}},[e._v(" ")]):e._e(),e._v(" "),"business_cause"==t.formType?i("el-col",{attrs:{span:4}},[i("el-select",{on:{change:function(i){e.typeOptionsChange(t)}},model:{value:t.flowName,callback:function(i){e.$set(t,"flowName",i)},expression:"formItem.flowName"}},e._l(t.typeOption,(function(e){return i("el-option",{key:e.flowId,attrs:{label:e.flowName,value:e.flowName}})})))],1):e._e(),e._v(" "),i("el-col",{staticClass:"interval-base",attrs:{span:1}},[e._v(" ")]),e._v(" "),i("el-col",{staticStyle:{position:"relative"},attrs:{span:e.getValueSpan(t.formType,t.fieldName,t)?e.isExport||!e.showExport?11:9:e.isExport?16:14}},["isNull"===t.condition||"isNotNull"===t.condition?[e._v("\n         \n      ")]:e.updateFlag?["checkStatus"===t.formType||"dealStatus"===t.formType||"invoiceStatus"===t.fieldName||"receivedStatus"===t.fieldName||"select"===t.formType&&"string"!=e.getSettingValueType(t.setting)?i("el-select",{attrs:{placeholder:"请选择筛选条件"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}},e._l(t.setting,(function(e){return i("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):["select","checkbox","field_tag"].includes(t.formType)?i("el-select",{attrs:{multiple:"field_tag"!==t.formType,placeholder:"请选择筛选条件"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}},e._l(t.setting,(function(e){return i("el-option",{key:e,attrs:{label:e,value:e}})}))):"field_attention"===t.formType?i("el-select",{attrs:{multiple:"",placeholder:"请选择筛选条件"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}},e._l(t.setting,(function(e){return i("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):"number"==t.formType||"floatnumber"==t.formType||"percent"==t.formType?[14===t.type?i("div",{staticClass:"date-range-value"},[i("el-input-number",{staticClass:"small",attrs:{controls:!1},model:{value:t.min,callback:function(i){e.$set(t,"min",i)},expression:"formItem.min"}}),e._v(" "),i("span",[e._v("-")]),e._v(" "),i("el-input-number",{staticClass:"small",attrs:{controls:!1},model:{value:t.max,callback:function(i){e.$set(t,"max",i)},expression:"formItem.max"}})],1):i("el-input-number",{staticClass:"small",staticStyle:{width:"100%"},attrs:{controls:!1},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}})]:"date"===t.formType||"datetime"===t.formType?[i("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:14===t.type,expression:"formItem.type === 14"}],ref:"wkDatePicker"+n,refInFor:!0,attrs:{"picker-options":e.getPickerOptions(t,n),type:"date"===t.formType?"daterange":"datetimerange","value-format":"date"===t.formType?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"right"},on:{change:function(i){e.datePickerChange(t)}},model:{value:t.range,callback:function(i){e.$set(t,"range",i)},expression:"formItem.range"}}),e._v(" "),t.timeType?i("div",{staticClass:"date-range-content",on:{click:function(i){e.dateRangeSelect(t,n)}}},[e._v(e._s(t.timeTypeName))]):e._e(),e._v(" "),i("el-date-picker",{directives:[{name:"show",rawName:"v-show",value:14!==t.type,expression:"formItem.type !== 14"}],attrs:{"value-format":"date"===t.formType?"yyyy-MM-dd":"yyyy-MM-dd HH:mm:ss",type:t.formType,placeholder:"选择日期"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}})]:"business_cause"===t.formType?i("el-select",{model:{value:t.settingName,callback:function(i){e.$set(t,"settingName",i)},expression:"formItem.settingName"}},e._l(t.settingList,(function(e){return i("el-option",{key:e.settingId,attrs:{label:e.settingName,value:e.settingName}})}))):"user"===t.formType||"single_user"===t.formType?i("wk-user-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:!1},on:{change:function(i){e.userDepChange(t,arguments[0],arguments[1])}},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}}):"structure"===t.formType?i("wk-dept-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:!1},on:{change:function(i){e.userDepChange(t,arguments[0],arguments[1])}},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}}):"boolean_value"==t.formType?i("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}}):"productTypeId"===t.fieldName?i("wk-product-category",{attrs:{item:t,value:t.value,type:"jxc"},on:{change:e.arrayValueChange}}):"category"===t.formType?i("xh-prouct-cate",{attrs:{item:t,value:t.value},on:{"value-change":e.arrayValueChange}}):"map_address"===t.formType?i("v-distpicker",{attrs:{province:t.address.state,city:t.address.city,area:t.address.area},on:{province:function(i){e.selectProvince(i,t)},city:function(i){e.selectCity(i,t)},area:function(i){e.selectArea(i,t)}}}):"position"==t.formType?i("wk-position",{attrs:{"show-detail":!1,props:{checkStrictly:!0}},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}}):i("el-input",{attrs:{placeholder:"多个条件请用；隔开"},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"formItem.value"}})]:e._e()],2),e._v(" "),e.isExport?e._e():i("el-col",{staticClass:"delete",attrs:{span:1}},[i("i",{staticClass:"el-icon-error delete-btn",on:{click:function(t){e.handleDelete(n)}}})]),e._v(" "),!e.isExport&&e.showExport?i("el-col",{attrs:{span:2}},[i("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:e.exportChange},model:{value:t.isOut,callback:function(i){e.$set(t,"isOut",i)},expression:"formItem.isOut"}},[e._v("外露")])],1):e._e()],2)})))},f=[],p=i("53ca"),h=(i("7db0"),i("b0c0"),i("f665"),i("f068")),m=i("8f81"),v=i("bfba"),b=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-cascader",e._b({ref:"elCascader",staticStyle:{width:"100%"},attrs:{options:e.options,"show-all-levels":!1,props:e.defaultProps},on:{change:e.valueChange},model:{value:e.dataValue,callback:function(t){e.dataValue=t},expression:"dataValue"}},"el-cascader",e.$attrs,!1))},y=[],g=(i("a9e3"),i("2532"),i("8917")),k=i("ea20"),C=i("6bfe"),_={name:"ProductType",components:{},props:{item:Object,value:{type:[String,Number,Object,Array],default:null},type:{type:String,default:"jxc"}},data:function(){return{options:[],dataValue:null,defaultProps:{},request:null}},watch:{value:{handler:function(){this.value?this.dataValue&&this.dataValue.includes(this.value)||(this.dataValue=this.value):this.dataValue=null},deep:!0,immediate:!0}},mounted:function(){this.setConfig(),this.getProductCategoryIndex()},methods:{setConfig:function(){"jxc"===this.type?(this.defaultProps={children:"childList",label:"productTypeName",value:"productTypeId",checkStrictly:!0},this.request=g["a"]):"crm"===this.type&&(this.defaultProps={children:"children",label:"label",value:"categoryId",checkStrictly:!0},this.request=k["T"])},getProductCategoryIndex:function(){var e=this;this.request({type:"tree"}).then((function(t){e.options=e.formatOptions(t.data,e.defaultProps.children)})).catch((function(){}))},formatOptions:function(e,t){var i=this;return e.forEach((function(e){e.hasOwnProperty(t)&&(Object(C["b"])(e[t])?delete e[t]:e[t]=i.formatOptions(e[t],t))})),e},valueChange:function(e){this.$emit("input",e[e.length-1]||""),this.$emit("change",{valArr:e,value:e,item:this.item,valueContent:e.length>0?this.$refs.elCascader.getCheckedNodes()[0].label:""})}}},x=_,w=i("2877"),T=Object(w["a"])(x,b,y,!1,null,"7f8dc4ee",null),N=T.exports,O=i("5a8c"),S=i("0a3e"),L=i("4042"),j={name:"FilterFields",components:{WkUserDialogSelect:m["a"],XhProuctCate:h["c"],WkProductCategory:N,VDistpicker:O["a"],WkDeptDialogSelect:v["a"],WkPosition:S["a"]},mixins:[L["a"]],props:{conditionTypeFun:Function,form:Array,fieldList:Array,disabled:{type:Boolean,default:!1},showExport:{type:Boolean,default:!0},isExport:{type:Boolean,default:!1}},data:function(){return{updateFlag:!0}},computed:{},watch:{form:function(){var e=this;this.$nextTick((function(){e.$el.scrollTop=e.$el.scrollHeight}))}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{showCalCondition:function(e,t,i){return this.getAdvancedFilterOptions(this.conditionTypeFun?this.conditionTypeFun(i):e,t).length>0},getValueSpan:function(e,t,i){return"business_cause"==e||this.showCalCondition(e,t,i)?8:13},typeOptionsChange:function(e){if(e.flowName){var t=e.typeOption.find((function(t){return t.flowName===e.flowName}));e.settingList=t.settingList||[]}else e.settingList=[];e.settingName=""},getSettingValueType:function(e){if(e&&e.length>0){var t=e[0];return Object(p["a"])(t)}return[]},selectProvince:function(e,t){t.address.state=e.value},selectCity:function(e,t){t.address.city=e.value},selectArea:function(e,t){t.address.area=e.value},arrayValueChange:function(e){e.value.length>0?(e.item.value=e.value,e.item.valueContent=e.valueContent):e.item.value=[]},userDepChange:function(e,t,i){e.valueContent=i},selectChange:function(e,t){this.getAdvancedFilterOptions(this.conditionTypeFun?this.conditionTypeFun(t):t.formType,t.fieldName).forEach((function(i){i.value===e&&(t.type=i.type)}))},fieldFocus:function(){this.$el.click()},fieldChange:function(e){var t=this,i=this.fieldList.find((function(t){return t.fieldName===e.fieldName}));i&&(e.formType=i.formType,e.name=i.name,this.getAdvancedFilterDefaultItemByFormType(e,i,this.conditionTypeFun)),this.$emit("on-field-change"),this.updateFlag=!1,this.$nextTick((function(){t.updateFlag=!0}))},dateRangeSelect:function(e,t){var i=this.$refs["wkDatePicker".concat(t)][0];this.$nextTick((function(){i.focus(),i.pickerVisible=!0}))},datePickerChange:function(e){e.timeTypeName="",e.timeType=""},getPickerOptions:function(e,t){for(var i=this,n=[{text:"本年度",value:"year"},{text:"上一年度",value:"lastYear"},{text:"下一年度",value:"nextYear"},{text:"上半年",value:"firstHalfYear"},{text:"下半年",value:"nextHalfYear"},{text:"本季度",value:"quarter"},{text:"上一季度",value:"lastQuarter"},{text:"下一季度",value:"nextQuarter"},{text:"本月",value:"month"},{text:"上月",value:"lastMonth"},{text:"下月",value:"nextMonth"},{text:"本周",value:"week"},{text:"上周",value:"lastWeek"},{text:"下周",value:"nextWeek"},{text:"今天",value:"today"},{text:"昨天",value:"yesterday"},{text:"明天",value:"tomorrow"},{text:"过去7天",value:"previous7day"},{text:"过去30天",value:"previous30day"},{text:"未来7天",value:"future7day"},{text:"未来30天",value:"future30day"}],a=[],s=function(){var t=n[o];a.push({text:t.text,onClick:function(n){n.$emit("pick",[],!1),i.$nextTick((function(){e.timeTypeName=t.text,e.timeType=t.value}))}})},o=0;o<n.length;o++)s();return{shortcuts:a}},handleDelete:function(e){this.$emit("on-field-delete",e)},exportChange:function(){this.$emit("on-field-export")}}},I=j,D=(i("c517"),Object(w["a"])(I,u,f,!1,null,"c69f2dd0",null)),$=D.exports,E=(i("a15b"),i("d81d"),i("ab43"),i("ac1f"),i("5319"),function(e){return[5,6].includes(e)});function F(e){var t=[];return e.forEach((function(e){var i=[];if("datetime"==e.formType||"date"==e.formType)14===e.type?i=Object(C["b"])(e.timeType)?e.range:[e.timeType]:e.value&&(i=Object(C["a"])(e.value)?e.value:[e.value]),E(e.type)&&(i=[]),t.push({formType:e.formType,name:e.fieldName,type:e.type,values:i});else if("number"==e.formType||"floatnumber"==e.formType||"percent"==e.formType)14===e.type?i=[Object(C["b"])(e.min)?0:e.min,Object(C["b"])(e.max)?0:e.max]:Object(C["b"])(e.value)||(i=[e.value]),E(e.type)&&(i=[]),t.push({formType:e.formType,name:e.fieldName,type:e.type,values:i});else if("business_cause"==e.formType)e.flowName&&t.push({formType:e.formType,name:"flowName",type:1,values:[e.flowName]}),e.settingName&&t.push({formType:e.formType,name:"settingName",type:1,values:[e.settingName]});else if("user"==e.formType||"single_user"==e.formType)i=e.value,E(e.type)&&(i=[]),t.push({type:e.type,values:i,formType:e.formType,name:e.fieldName});else if("position"==e.formType)i=e.value.filter((function(e){return!Object(C["b"])(e.name)})).map((function(e){return JSON.stringify(e)})),E(e.type)&&(i=[]),t.push({type:e.type,values:i,formType:e.formType,name:e.fieldName});else if("structure"==e.formType)i=e.value,E(e.type)&&(i=[]),t.push({type:e.type,values:i,formType:e.formType,name:e.fieldName});else if("checkbox"==e.formType)i="receivedStatus"!==e.fieldName||Object(C["a"])(e.value)?Object(C["a"])(e.value)?e.value:[]:""===e.value||null===e.value||void 0===e.value?[]:[e.value],E(e.type)&&(i=[]),t.push({type:e.type,values:i,formType:e.formType,name:e.fieldName});else if("category"==e.formType)i=Object(C["a"])(e.value)&&e.value.length>0?[e.value[e.value.length-1]]:[],E(e.type)&&(i=[]),t.push({type:1,values:i,formType:e.formType,name:e.fieldName});else if("select"===e.formType)i=e.value||[],"string"==V(e.setting)||!e.value&&0!==e.value||(i=[e.value]),E(e.type)&&(i=[]),t.push({type:e.type,values:i,formType:e.formType,name:e.fieldName});else if(["field_tag","field_attention"].includes(e.formType))i=e.value||[],Object(C["a"])(i)||(i=[i]),E(e.type)&&(i=[]),t.push({type:e.type,values:i,formType:e.formType,name:e.fieldName});else if("map_address"==e.formType){var n=[];for(var a in e.address){var s=e.address[a];s&&n.push(s)}t.push({values:[n.join(",")],type:1,formType:e.formType,name:e.fieldName})}else{var o=[];if("string"===typeof e.value){var l=e.value.replace(/；/g,";");o=l.split(";").filter((function(e){return""!==e&&null!==e}))}else o=[e.value];E(e.type)&&(o=[]),t.push({type:e.type,values:o,formType:e.formType,name:e.fieldName})}})),t}function V(e){if(e&&e.length>0){var t=e[0];return Object(p["a"])(t)}return[]}var P={name:"FilterExportFields",components:{FilterFields:$},props:{conditionTypeFun:Function,form:Array,fieldList:Array},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{filterClick:function(){this.$emit("filter",F(this.form))},clearClick:function(){this.$emit("clear",this.form)}}},A=P,W=(i("b167"),Object(w["a"])(A,r,d,!1,null,"7c694fac",null)),B=W.exports,R=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,width:"700px"},on:{"update:visible":function(t){e.visible=t},close:e.handleCancel}},[i("span",{staticClass:"el-dialog__title",attrs:{slot:"title"},slot:"title"},[e._v("场景管理"),e.config.help?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.config.help.type,"data-id":e.config.help.id}}):e._e()]),e._v(" "),i("div",{staticClass:"scene-name"},[e._v("您可通过拖拽管理场景")]),e._v(" "),i("flexbox",{staticClass:"scene-list"},[i("div",{staticClass:"scene-list-box"},[i("flexbox",{staticClass:"scene-list-head"},[i("el-checkbox",{attrs:{indeterminate:e.isleftIndeterminate},on:{change:e.handleleftCheckAllChange},model:{value:e.checkleftAll,callback:function(t){e.checkleftAll=t},expression:"checkleftAll"}}),e._v(" "),i("div",{staticClass:"scene-list-head-name"},[e._v("显示的场景")]),e._v(" "),i("div",{staticClass:"scene-list-head-detail"},[e._v(e._s(e.leftCheckItems.length+"/"+e.checkedLeftData.length))])],1),e._v(" "),i("div",{staticClass:"scene-list-body"},[i("draggable",{staticStyle:{"min-height":"100px"},attrs:{move:e.leftMove,options:{group:"list",forceFallback:!1,fallbackClass:"draggingStyle"}},on:{end:e.leftMoveEnd},model:{value:e.checkedLeftData,callback:function(t){e.checkedLeftData=t},expression:"checkedLeftData"}},e._l(e.checkedLeftData,(function(t,n){return i("flexbox",{key:n,staticClass:"list-item"},[i("div",{staticClass:"default-mark",class:{"default-mark-active":t.sceneId==e.defaultId}}),e._v(" "),i("el-checkbox",{staticClass:"list-item-check",on:{change:e.leftCheckItemChange},model:{value:t.check,callback:function(i){e.$set(t,"check",i)},expression:"item.check"}}),e._v(" "),i("div",{staticClass:"list-item-name"},[e._v(e._s(t.name))]),e._v(" "),i("div",{staticClass:"list-item-handle"},[1!=t.isSystem?i("i",{staticClass:"el-icon-edit",on:{click:function(i){e.itemHandle("edit",t,n)}}}):e._e(),e._v(" "),1!=t.isSystem?i("i",{staticClass:"el-icon-delete",on:{click:function(i){e.itemHandle("delete",t,n)}}}):e._e(),e._v(" "),t.sceneId!=e.defaultId?i("el-dropdown",{on:{command:function(i){e.defaultHandle(arguments[0],t)}},model:{value:t.visible,callback:function(i){e.$set(t,"visible",i)},expression:"item.visible"}},[i("el-button",{staticClass:"dropdown-btn menu-edit-btn",attrs:{icon:"wk wk-manage",size:"small"}}),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{command:"default"}},[e._v("设置为默认场景")])],1)],1):e._e()],1)],1)})))],1)],1),e._v(" "),i("div",{staticClass:"scene-middle-list"},[i("el-button",{staticClass:"scene-middle-left-button",class:{"scene-middle-button-select":e.rightCheckItems.length>0},attrs:{disabled:0==e.rightCheckItems.length},on:{click:function(t){e.changePositon("left")}}},[i("i",{staticClass:"el-icon-arrow-left scene-middle-icon"})]),e._v(" "),i("el-button",{staticClass:"scene-middle-right-button",class:{"scene-middle-button-select":e.leftCheckItems.length>0},attrs:{disabled:0==e.leftCheckItems.length},on:{click:function(t){e.changePositon("right")}}},[i("i",{staticClass:"el-icon-arrow-right scene-middle-icon"})])],1),e._v(" "),i("div",{staticClass:"scene-list-box"},[i("flexbox",{staticClass:"scene-list-head"},[i("el-checkbox",{attrs:{indeterminate:e.isrightIndeterminate},on:{change:e.handlerightCheckAllChange},model:{value:e.checkrightAll,callback:function(t){e.checkrightAll=t},expression:"checkrightAll"}}),e._v(" "),i("div",{staticClass:"scene-list-head-name"},[e._v("隐藏的场景")]),e._v(" "),i("div",{staticClass:"scene-list-head-detail"},[e._v(e._s(e.rightCheckItems.length+"/"+e.checkedRightData.length))])],1),e._v(" "),i("div",{staticClass:"scene-list-body"},[i("draggable",{staticStyle:{"min-height":"100px"},attrs:{move:e.rightMove,options:{group:"list",forceFallback:!1,fallbackClass:"draggingStyle"}},on:{end:e.rightMoveEnd},model:{value:e.checkedRightData,callback:function(t){e.checkedRightData=t},expression:"checkedRightData"}},e._l(e.checkedRightData,(function(t,n){return i("flexbox",{key:n,staticClass:"list-item"},[i("el-checkbox",{staticClass:"list-item-check",staticStyle:{"margin-left":"9px"},on:{change:e.rightCheckItemChange},model:{value:t.check,callback:function(i){e.$set(t,"check",i)},expression:"item.check"}}),e._v(" "),i("div",{staticClass:"list-item-name"},[e._v(e._s(t.name))])],1)})))],1)],1)]),e._v(" "),i("div",{staticClass:"handle-bar"},[i("el-button",{staticStyle:{"padding-right":"0","padding-left":"0"},attrs:{type:"text"},on:{click:function(t){e.addAndEditScene("add",{})}}},[e._v("+ 新建场景")])],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),i("el-button",{nativeOn:{click:function(t){return e.handleCancel(t)}}},[e._v("取消")])],1),e._v(" "),i("scene-create",{attrs:{"field-list":e.fieldList,props:e.createProps,"condition-type-fun":e.conditionTypeFun,"dialog-visible":e.showCreateScene,obj:e.filterObj,name:e.filterName,"edit-id":e.filterEditId,"is-default":e.filterDefault},on:{"update:dialogVisible":function(t){e.showCreateScene=t},"save-success":e.refreshSceneList}})],1)},H=[],q=(i("a434"),i("b64b"),i("25f0"),i("b76a")),M=i.n(q),z=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.editId?"编辑场景":"新建场景",visible:e.visible,"close-on-click-modal":!1,"append-to-body":!0,width:"800px"},on:{"update:visible":function(t){e.visible=t},close:e.handleCancel}},[i("div",{staticClass:"scene-name-container"},[i("div",{staticClass:"scene-name"},[e._v("场景名称")]),e._v(" "),i("el-input",{staticClass:"scene-input",attrs:{maxlength:10,placeholder:"场景名称，最多10个字符"},model:{value:e.saveName,callback:function(t){e.saveName="string"===typeof t?t.trim():t},expression:"saveName"}})],1),e._v(" "),i("div",{staticClass:"scene-name"},[e._v("筛选条件")]),e._v(" "),i("filter-fields",{attrs:{id:"scene-filter-container","condition-type-fun":e.conditionTypeFun,form:e.form,"field-list":e.fieldList,"show-export":!1},on:{"on-field-change":e.getError,"on-field-delete":e.handleDelete}}),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:e.showErrors,expression:"showErrors"}],staticClass:"el-icon-warning warning-info"},[i("span",{staticClass:"desc"},[e._v(e._s(e.errorTypes[e.errorType]))])]),e._v(" "),i("el-button",{staticStyle:{"padding-right":"0","padding-left":"0"},attrs:{type:"text"},on:{click:e.handleAdd}},[e._v("+ 添加筛选条件")]),e._v(" "),i("div",{staticClass:"save"},[i("div",{staticClass:"save-setting"},[i("el-checkbox",{model:{value:e.saveDefault,callback:function(t){e.saveDefault=t},expression:"saveDefault"}},[e._v("设置为默认")]),e.config.help?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.config.help.type,"data-id":e.config.help.id}}):e._e()],1)]),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.handleCancel}},[e._v("取消")])],1)],1)},U=[],J=(i("fb6a"),i("ed08")),Y=i("8ed6"),Q={updateRequest:null,updateParams:null,saveRequest:null,saveParams:null,help:null},X={name:"SceneCreate",components:{FilterFields:$},mixins:[L["a"]],props:{dialogVisible:{type:Boolean,required:!0,default:!1},fieldList:{type:Array,required:!0,default:function(){return[]}},obj:{type:Object,required:!0,default:function(){return{}}},props:Object,conditionTypeFun:Function,name:{type:String,default:""},isDefault:{type:Boolean,default:!1},editId:{type:String,default:""}},data:function(){return{form:[],visible:!1,errorTypes:["筛选条件中有重复项！","请选择筛选条件！"],errorType:0,showErrors:!1,saveDefault:!1,saveName:null,timeTypes:[{text:"本年度",value:"year"},{text:"上一年度",value:"lastYear"},{text:"下一年度",value:"nextYear"},{text:"上半年",value:"firstHalfYear"},{text:"下半年",value:"nextHalfYear"},{text:"本季度",value:"quarter"},{text:"上一季度",value:"lastQuarter"},{text:"下一季度",value:"nextQuarter"},{text:"本月",value:"month"},{text:"上月",value:"lastMonth"},{text:"下月",value:"nextMonth"},{text:"本周",value:"week"},{text:"上周",value:"lastWeek"},{text:"下周",value:"nextWeek"},{text:"今天",value:"today"},{text:"昨天",value:"yesterday"},{text:"明天",value:"tomorrow"},{text:"过去7天",value:"previous7day"},{text:"过去30天",value:"previous30day"},{text:"未来7天",value:"future7day"},{text:"未来30天",value:"future30day"}]}},computed:{config:function(){return Object(Y["a"])(Object(l["a"])({},Q),this.props||{})}},watch:{dialogVisible:{handler:function(e){var t=this;if(e){var i=[];if(this.editId){var n=function(){var e=t.obj.obj[a],n=t.getFilterItem();if(n.fieldName=e.name,n.formType=e.formType,n.type=e.type,t.getAdvancedFilterOptions(e.formType,e.fieldName).forEach((function(t){e.type===t.type&&(n.condition=t.value)})),"checkbox"!==n.formType&&"select"!==n.formType&&"dealStatus"!==n.formType&&"checkStatus"!==n.formType||(n.setting=t.getEditSetting(n.formType,n.fieldName)),"date"==e.formType||"datetime"==e.formType)if(n.value="",t.$set(n,"timeType",""),t.$set(n,"timeTypeName",""),t.$set(n,"range",[]),14===n.type)if(Object(C["a"])(e.values)&&e.values.length>0)if(1===e.values.length){n.timeType=e.values[0];var s=t.timeTypes.find((function(e){return e.value===n.timeType}));s&&(n.timeTypeName=s.text)}else t.$set(n,"range",e.values.slice(0,2));else n.value="";else n.value=e.values[0];else if("number"==e.formType||"floatnumber"==e.formType||"percent"==e.formType)n.min="",n.max="",n.value="",14===n.type?(n.min=Object(C["a"])(e.values)&&!Object(C["b"])(e.values[0])?e.values[0]:"",n.max=Object(C["a"])(e.values)&&!Object(C["b"])(e.values[1])?e.values[1]:""):n.value=e.values[0];else if("business_type"==e.formType){if(n.typeId=e.values[0],n.statusId=e.values.length>1?e.values[1]:null,n.typeOption=t.getEditSetting(n.formType,n.fieldName),e.typeId){var o=n.typeOption.find((function(t){return t.typeId===e.typeId}));n.statusOption=o?o.statusList:[]}}else if("user"==e.formType||"single_user"==e.formType||"structure"==e.formType||"checkbox"==e.formType||"position"==e.formType)n.value=e.values;else if("select"==e.formType)"string"!=t.getSettingValueType(n.setting)?n.value=e.values[0]:n.value=e.values;else if("category"==e.formType)n.value=[],t.getProductCategoryValue(n,e.values[0]);else if("map_address"==e.formType){var l=e.values[0].split(",");n.address={state:l.length>0?l[0]:"",city:l.length>1?l[1]:"",area:l.length>2?l[2]:""}}else n.setting=e.setting,n.value=e.values.join(";");i.push(n)};for(var a in this.obj.obj)n()}else i=Object(J["D"])(this.obj.form),0==i.length&&i.push(this.getFilterItem());this.form=i,this.name?this.saveName=this.name:this.saveName="",this.isDefault?this.saveDefault=this.isDefault:this.saveDefault=!1}this.visible=this.dialogVisible},deep:!0,immediate:!0},form:function(){this.$nextTick((function(){var e=document.getElementById("scene-filter-container");e.scrollTop=e.scrollHeight}))}},methods:{getEditSetting:function(e,t){var i=this.fieldList.find((function(i){return i.formType==e&&i.fieldName==t}));return i?i.setting:[]},getProductCategoryValue:function(e,t){var i=this;Object(k["T"])().then((function(n){var a=n.data||[],s=[];i.filterProductTree(a,t,s),e.value=s.reverse()})).catch((function(){}))},filterProductTree:function(e,t,i){for(var n=0;n<e.length;n++){var a=e[n];if(a.categoryId==t){i.push(t),this.filterProductTree(e,a.parentId,i);break}}},getFilterItem:function(){return{fieldName:"",name:"",formType:"",isOut:0,condition:"contains",type:3,value:"",setting:[],typeOption:[],settingList:[],flowName:"",settingName:"",address:{state:"",city:"",area:""}}},handleCancel:function(){this.visible=!1,this.$emit("update:dialogVisible",!1)},getError:function(){this.showErrors=!1;for(var e=[],t=0;t<this.form.length;t++){var i=this.form[t];if(""===i.fieldName||void 0===i.fieldName||null===i.fieldName){this.errorType=1,this.showErrors=!0;break}if(e.includes(i.fieldName)){this.errorType=0,this.showErrors=!0;break}e.push(i.fieldName)}return this.showErrors},handleConfirm:function(){var e=this;if(!this.getError())if(this.saveName&&""!==this.saveName){for(var t=0;t<this.form.length;t++){var i=this.form[t];if(!i.fieldName||""===i.fieldName)return void this.$message.error("筛选的字段名称不能为空！");if("business_type"==i.formType){if(!i.typeId&&!i.statusId)return void this.$message.error("筛选内容不能为空！")}else if("map_address"==i.formType){if(!i.address.state&&!i.address.city&&!i.address.area)return void this.$message.error("筛选内容不能为空！")}else if("date"==i.formType||"datetime"==i.formType){if("isNull"!=i.condition&&"isNotNull"!=i.condition)if(14===i.type){if(Object(C["b"])(i.timeType)&&Object(C["b"])(i.range))return void this.$message.error("筛选内容不能为空！")}else if(Object(C["b"])(i.value))return void this.$message.error("筛选内容不能为空！")}else if("number"==i.formType||"floatnumber"==i.formType||"percent"==i.formType){if("isNull"!=i.condition&&"isNotNull"!=i.condition)if(14===i.type){if(Object(C["b"])(i.min)||Object(C["b"])(i.max))return void this.$message.error("筛选内容不能为空！")}else if(Object(C["b"])(i.value))return void this.$message.error("筛选内容不能为空！")}else if("user"==i.formType||"single_user"==i.formType||"structure"==i.formType||"category"==i.formType||"checkbox"==i.formType||"position"==i.formType){if((!i.value||0===i.value.length)&&"isNull"!=i.condition&&"isNotNull"!=i.condition)return void this.$message.error("筛选内容不能为空！")}else if(Object(C["b"])(i.value)&&"isNull"!=i.condition&&"isNotNull"!=i.condition)return void this.$message.error("筛选内容不能为空！")}var n=[];this.form.forEach((function(t){if("datetime"==t.formType||"date"==t.formType){var i=[];i=14===t.type?Object(C["b"])(t.timeType)?t.range:[t.timeType]:[t.value],n.push({formType:t.formType,name:t.fieldName,type:t.type,values:i})}else if("number"==t.formType||"floatnumber"==t.formType||"percent"==t.formType){var a=[];a=14===t.type?[Object(C["b"])(t.min)?"":t.min,Object(C["b"])(t.max)?"":t.max]:[t.value],n.push({formType:t.formType,name:t.fieldName,type:t.type,values:a})}else if("business_type"==t.formType)n.push({formType:t.formType,name:t.statusId?"statusId":"typeId",type:1,values:t.statusId?[t.typeId,t.statusId]:[t.typeId]});else if("user"==t.formType||"single_user"==t.formType)n.push({type:t.type,values:t.value,formType:t.formType,name:t.fieldName});else if("position"==t.formType)n.push({type:t.type,values:t.value.filter((function(e){return!Object(C["b"])(e.name)})).map((function(e){return JSON.stringify(e)})),formType:t.formType,name:t.fieldName});else if("structure"==t.formType)n.push({type:t.type,values:t.value,formType:t.formType,name:t.fieldName});else if("checkbox"==t.formType)n.push({type:t.type,values:t.value,formType:t.formType,name:t.fieldName});else if("category"==t.formType)n.push({type:1,values:[t.value[t.value.length-1]],formType:t.formType,name:t.fieldName});else if("select"==t.formType){var s=t.value;"string"!=e.getSettingValueType(t.setting)&&(s=[t.value]),n.push({type:t.type,values:s,formType:t.formType,name:t.fieldName})}else if("map_address"==t.formType){var o=[];for(var l in t.address){var c=t.address[l];c&&o.push(c)}n.push({values:[o.join(",")],type:1,formType:t.formType,name:t.fieldName})}else{var r=[];if("string"===typeof t.value){var d=t.value.replace(/；/g,";");r=d.split(";").filter((function(e){return""!==e&&null!==e}))}else r=[t.value];n.push({type:t.type,values:r,formType:t.formType,name:t.fieldName})}}));var a={obj:n,form:this.form,saveDefault:this.saveDefault,saveName:this.saveName};this.requestCreateScene(a)}else this.$message.error("场景名称不能为空！")},requestCreateScene:function(e){var t=this;this.editId?this.config.updateRequest(Object(l["a"])({isDefault:e.saveDefault?1:0,name:e.saveName,sceneId:this.editId,data:JSON.stringify(e.obj)},this.config.updateParams)).then((function(e){t.$message({type:"success",message:"编辑成功"}),t.$emit("save-success"),t.handleCancel()})).catch((function(){})):this.config.saveRequest(Object(l["a"])({isDefault:e.saveDefault?1:0,name:e.saveName,data:JSON.stringify(e.obj)},this.config.saveParams)).then((function(e){t.$emit("save-success"),t.$message({type:"success",message:"创建成功"}),t.handleCancel()})).catch((function(){}))},handleAdd:function(){this.form.push(this.getFilterItem())},handleDelete:function(e){var t=this;this.$confirm("您确定要删除这一条数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.form.splice(e,1),t.getError()})).catch((function(){}))},getSettingValueType:function(e){if(e&&e.length>0){var t=e[0];return Object(p["a"])(t)}return[]}}},G=X,K=(i("3932"),Object(w["a"])(G,z,U,!1,null,"653c79b9",null)),Z=K.exports,ee={indexRequest:null,indexParams:null,sortRequest:null,sortParams:null,defaultsRequest:null,defaultsParams:null,deleteRequest:null,deleteParams:null,fieldsIndexRequest:null,fieldsIndexParams:null,help:null},te={name:"SceneSet",components:{draggable:M.a,SceneCreate:Z},props:{dialogVisible:{type:Boolean,required:!0,default:!1},props:Object,createProps:Object,conditionTypeFun:Function},data:function(){return{defaultId:"",visible:!1,isleftIndeterminate:!1,checkleftAll:!1,checkedLeftData:[],leftCheckItems:[],isrightIndeterminate:!1,checkrightAll:!1,checkedRightData:[],rightCheckItems:[],moveItem:{},handlDefaultItem:{},showCreateScene:!1,fieldList:[],filterObj:{form:[]},filterName:"",filterDefault:!1,filterEditId:""}},computed:{config:function(){return Object(Y["a"])(Object(l["a"])({},ee),this.props||{})}},watch:{dialogVisible:{handler:function(e){this.visible=e,e&&this.getSceneList()},deep:!0,immediate:!0}},mounted:function(){},methods:{refreshSceneList:function(){this.getSceneList(),this.$emit("save-success")},getSceneList:function(){var e=this;this.config.indexRequest(Object(l["a"])({},this.config.indexParams)).then((function(t){var i=t.data||{};e.checkedLeftData=i.value.map((function(e){return e.check=!1,e.data=e.data?JSON.parse(e.data):[],e})),e.checkedRightData=i.hideValue.map((function(e){return e.check=!1,e.data=e.data?JSON.parse(e.data):[],e}));var n=e.checkedLeftData.filter((function(e){return 1==e.isDefault}));n&&n.length>0?e.defaultId=n[0].sceneId:e.defaultId=""})).catch((function(){}))},handleConfirm:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(0!==this.checkedLeftData.length){var i=this.checkedLeftData.filter((function(t){return t.sceneId==e.defaultId}));0==i.length&&(this.defaultId=""),this.config.sortRequest(Object(l["a"])({noHideIds:this.checkedLeftData.map((function(e){return e.sceneId})),hideIds:this.checkedRightData.map((function(e){return e.sceneId}))},this.config.sortParams)).then((function(i){!1!==t&&(e.$message({type:"success",message:"操作成功"}),e.handleCancel()),e.$emit("save-success")})).catch((function(){}))}else this.$message.error("场景不能全部隐藏！")},itemHandle:function(e,t,i){var n=this;t.isTemp?this.$message.error("请保存后操作"):"edit"==e?this.addAndEditScene("edit",t):"delete"==e&&this.$confirm("您确定要删除这一条数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.config.deleteRequest({sceneId:t.sceneId}).then((function(e){n.$message({type:"success",message:"删除成功"}),n.checkedLeftData.splice(i,1),n.leftCheckItemChange(),n.$emit("save-success")})).catch((function(){}))})).catch((function(){}))},defaultHandle:function(e,t){var i=this;this.config.defaultsRequest({sceneId:t.sceneId}).then((function(e){i.$message({type:"success",message:"操作成功"}),i.defaultId=t.sceneId,i.handleConfirm(!1)})).catch((function(){}))},addAndEditScene:function(e,t){var i=this;this.config.fieldsIndexRequest(Object(l["a"])({},this.config.fieldsIndexParams)).then((function(n){var a=n.data||[];i.fieldList=a;var s=a.map((function(e){return e.fieldName}));"edit"==e?(i.filterObj={form:[],obj:t.data.filter((function(e){return s.includes(e.name)}))},i.filterName=t.name,i.filterDefault=1==t.isDefault,i.filterEditId=t.sceneId.toString()):(i.filterObj={form:[]},i.filterEditId="",i.filterName="",i.filterDefault=!1),i.showCreateScene=!0})).catch((function(){}))},handleCancel:function(){this.visible=!1,this.$emit("update:dialogVisible",!1)},rightMoveEnd:function(e){this.moveItem.check=!1,this.leftCheckItemChange(),this.rightCheckItemChange()},rightMove:function(e){this.moveItem=e.draggedContext.element},leftMoveEnd:function(e){this.moveItem.check=!1,this.leftCheckItemChange(),this.rightCheckItemChange()},leftMove:function(e){this.moveItem=e.draggedContext.element},handleleftCheckAllChange:function(e){e&&(this.isleftIndeterminate=!1),this.checkedLeftData.forEach((function(t){t.check=e})),this.leftCheckItems=e?this.checkedLeftData.filter((function(e){return e.check})):[]},leftCheckItemChange:function(){this.leftCheckItems=this.checkedLeftData.filter((function(e){return 1==e.check})),this.leftCheckItems.length>0?this.leftCheckItems.length==this.checkedLeftData.length?(this.checkleftAll=!0,this.isleftIndeterminate=!1):(this.checkleftAll=!1,this.isleftIndeterminate=!0):(this.checkleftAll=!1,this.isleftIndeterminate=!1)},handlerightCheckAllChange:function(e){e&&(this.isrightIndeterminate=!1),this.checkedRightData.forEach((function(t){t.check=e})),this.rightCheckItems=e?this.checkedRightData:[]},rightCheckItemChange:function(){this.rightCheckItems=this.checkedRightData.filter((function(e){return 1==e.check})),this.rightCheckItems.length>0?this.rightCheckItems.length==this.checkedRightData.length?(this.checkrightAll=!0,this.isrightIndeterminate=!1):(this.checkrightAll=!1,this.isrightIndeterminate=!0):(this.checkrightAll=!1,this.isrightIndeterminate=!1)},changePositon:function(e){var t=this;"left"==e?(this.checkedRightData=this.checkedRightData.filter((function(e,i,n){var a=!1;return t.rightCheckItems.forEach((function(t,i){e.sceneId==t.sceneId&&(a=!0)})),!a})),this.rightCheckItems.forEach((function(e,i){e.check=!1,e.isTemp=!0,t.checkedLeftData.push(e)})),this.rightCheckItems=[],this.isrightIndeterminate=!1,this.checkrightAll=!1,this.leftCheckItemChange(),this.rightCheckItemChange()):(this.checkedLeftData=this.checkedLeftData.filter((function(e,i,n){var a=!1;return t.leftCheckItems.forEach((function(t,i){e.sceneId==t.sceneId&&(a=!0)})),!a})),this.leftCheckItems.forEach((function(e,i){e.check=!1,t.checkedRightData.push(e)})),this.leftCheckItems=[],this.isleftIndeterminate=!1,this.checkleftAll=!1,this.leftCheckItemChange(),this.rightCheckItemChange())}}},ie=te,ne=(i("55f0"),Object(w["a"])(ie,R,H,!1,null,"748fefe1",null)),ae=ne.exports,se=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{visible:e.visible,"close-on-click-modal":!1,"modal-append-to-body":"","append-to-body":"",title:"高级筛选",width:"900px"},on:{"update:visible":function(t){e.visible=t},close:e.handleCancel}},[i("div",{staticStyle:{"margin-bottom":"8px"}},[e._v("筛选条件"),e.config.exportHelp?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.config.exportHelp.type,"data-id":e.config.exportHelp.id}}):e._e()]),e._v(" "),i("filter-fields",{attrs:{"condition-type-fun":e.conditionTypeFun,form:e.form,"field-list":e.fieldList,"show-export":e.config.showExport},on:{"on-field-change":e.getError,"on-field-delete":e.handleDelete,"on-field-export":e.handleExport}}),e._v(" "),i("p",{directives:[{name:"show",rawName:"v-show",value:e.showErrors,expression:"showErrors"}],staticClass:"el-icon-warning warning-info"},[i("span",{staticClass:"desc"},[e._v(e._s(e.errorTypes[e.errorType]))])]),e._v(" "),i("el-button",{staticStyle:{"padding-right":"0","padding-left":"0"},attrs:{type:"text"},on:{click:e.handleAdd}},[e._v("+ 添加筛选条件")]),e._v(" "),e.config.showSaveScene?i("div",{staticClass:"save"},[i("el-checkbox",{model:{value:e.saveChecked,callback:function(t){e.saveChecked=t},expression:"saveChecked"}},[e._v("保存为场景")]),e._v(" "),i("el-input",{directives:[{name:"show",rawName:"v-show",value:e.saveChecked,expression:"saveChecked"}],staticClass:"name",attrs:{maxlength:10,placeholder:"场景名称，最多10个字符"},model:{value:e.saveName,callback:function(t){e.saveName="string"===typeof t?t.trim():t},expression:"saveName"}}),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.saveChecked,expression:"saveChecked"}],staticClass:"save-setting"},[i("el-checkbox",{model:{value:e.saveDefault,callback:function(t){e.saveDefault=t},expression:"saveDefault"}},[e._v("设置为默认")]),e.config.help?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.config.help.type,"data-id":e.config.help.id}}):e._e()],1)],1):e._e(),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.handleConfirm(!1)}}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.handleCancel}},[e._v("取消")])],1)],1)},oe=[],le={showExport:!0,showSaveScene:!0,help:null,exportHelp:null,saveRequest:null,saveParams:null},ce={name:"FilterForm",components:{FilterFields:$},mixins:[],props:{dialogVisible:{type:Boolean,required:!0,default:!1},fieldList:{type:Array,required:!0,default:function(){return[]}},obj:{type:Object,required:!0,default:function(){return{}}},conditionTypeFun:Function,props:Object},data:function(){return{form:[],visible:!1,errorTypes:["筛选条件中有重复项！","请选择筛选条件！"],errorType:0,showErrors:!1,saveChecked:!1,saveDefault:!1,saveName:null}},computed:{config:function(){return Object(Y["a"])(Object(l["a"])({},le),this.props||{})}},watch:{dialogVisible:{handler:function(e){e&&(this.form=Object(J["D"])(this.obj.form),0==this.form.length&&this.form.push(this.getFilterItem()),this.saveChecked=!1,this.saveDefault=!1,this.saveName=null),this.visible=this.dialogVisible},deep:!0,immediate:!0}},methods:{handleCancel:function(){this.$emit("update:dialogVisible",!1)},getError:function(){this.showErrors=!1;for(var e=[],t=0;t<this.form.length;t++){var i=this.form[t];if(""===i.fieldName||void 0===i.fieldName||null===i.fieldName){this.errorType=1,this.showErrors=!0;break}if(e.includes(i.fieldName)){this.errorType=0,this.showErrors=!0;break}e.push(i.fieldName)}return this.showErrors},handleConfirm:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.getError())if(!this.saveChecked||this.saveName&&""!==this.saveName){var t={obj:F(this.form),form:this.form,saveChecked:this.saveChecked,saveDefault:this.saveDefault,saveName:this.saveName};this.$emit("filter",t,e),this.saveExportFields()}else this.$message.error("场景名称不能为空！")},saveExportFields:function(){if(this.config.saveRequest){var e=[];this.form.forEach((function(t){1===t.isOut&&e.push(t.fieldName)}));var t=this.config.saveParams?Object(l["a"])({defaultValue:JSON.stringify(e)},this.config.saveParams):{defaultValue:JSON.stringify(e)};this.config.saveRequest(t).then((function(e){})).catch((function(){}))}},handleAdd:function(){this.form.push(this.getFilterItem())},getFilterItem:function(){return{fieldName:"",name:"",formType:"",isOut:0,condition:"contains",type:3,value:"",setting:[],typeOption:[],settingList:[],flowName:"",settingName:"",address:{state:"",city:"",area:""}}},handleDelete:function(e){var t=this;this.$confirm("您确定要删除这一条数据吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.form.splice(e,1),t.getError()})).catch((function(){}))},handleExport:function(){this.handleConfirm(!0)}}},re=ce,de=(i("ea7b"),Object(w["a"])(re,se,oe,!1,null,"1d492cf8",null)),ue=de.exports,fe={showFilterView:!0,showExportFields:!1},pe={name:"WkTableHeader",components:{WkFilterHeader:c["a"],FilterExportFields:B,SceneSet:ae,FilterForm:ue},mixins:[L["a"]],inheritAttrs:!1,props:{props:Object,filterHeaderProps:Object,filterFormProps:Object,sceneSetProps:Object,sceneCreateProps:Object,conditionTypeFun:Function,fields:[Array,Function]},data:function(){return{filterObj:{form:[],obj:[]},exportFieldsShow:!1,fieldList:[],filterShow:!1,sceneSetShow:!1}},computed:{config:function(){return Object(Y["a"])(Object(l["a"])({},fe),this.props||{})},filterHeaderConfig:function(){return Object(l["a"])({tabSetShow:!0,tabSetLabel:"场景设置",tabSetCommand:"sceneSet"},this.filterHeaderProps||{})},exportFields:function(){return this.filterObj.form?this.filterObj.form.filter((function(e){return 1===e.isOut})):[]},filterValidCount:function(){var e=Object(C["a"])(this.filterObj.obj)?this.filterObj.obj:[],t=e.filter((function(e){return e.values&&e.values.length>0||[5,6].includes(e.type)}));return t.length}},watch:{"config.showExportFields":{handler:function(e){var t=this;return Object(o["a"])(Object(s["a"])().m((function i(){return Object(s["a"])().w((function(i){while(1)switch(i.n){case 0:!t.config.showFilterView||Object(C["a"])(t.fieldList)&&0!==t.fieldList.length||(t.initFieldList(),t.exportFieldsShow=e);case 1:return i.a(2)}}),i)})))()},immediate:!0}},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{filterHeaderHandle:function(e,t){e===this.filterHeaderConfig.tabSetCommand&&(this.sceneSetShow=!0)},collapseHandle:function(e){var t=void 0!==e?e:!this.exportFieldsShow;t!==this.exportFieldsShow&&(this.exportFieldsShow=t,this.$emit("event-change","export-fields-collapse",this.exportFieldsShow))},exportFilter:function(e){this.filterObj.obj=e,this.$emit("filter-change",e)},clearFilterVal:function(e){this.filterObj.obj=[],this.resetAdvancedFilterFieldsValue(e),this.$emit("filter-change",[])},showFilterClick:function(){var e=this;return Object(o["a"])(Object(s["a"])().m((function t(){return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:if(Object(C["a"])(e.fieldList)&&0!==e.fieldList.length){t.n=2;break}return t.n=1,e.initFieldList();case 1:t.n=4;break;case 2:return t.n=3,e.getFieldList();case 3:e.fieldList=t.v;case 4:e.filterShow=!0;case 5:return t.a(2)}}),t)})))()},initFieldList:function(){var e=this;return Object(o["a"])(Object(s["a"])().m((function t(){var i,n;return Object(s["a"])().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getFieldList();case 1:i=t.v,n=[],i.forEach((function(t){if(1===t.isOut){var i=Object(J["D"])(t);n.push(e.getAdvancedFilterDefaultItemByFormType(i,t,e.conditionTypeFun))}})),e.fieldList=i,e.filterObj.form=n;case 2:return t.a(2)}}),t)})))()},getFieldList:function(){var e=this;return new Promise((function(t){Object(C["a"])(e.fields)?t(e.fields):e.fields instanceof Function&&e.fields().then((function(e){t(e)}))}))},handleFilter:function(e,t){var i=this;this.filterObj=e,t?this.exportFieldsShow||this.collapseHandle(!0):(this.filterShow=!1,e.saveChecked&&this.sceneCreateProps.saveRequest(Object(l["a"])({isDefault:e.saveDefault?1:0,name:e.saveName,data:JSON.stringify(e.obj)},this.sceneCreateProps.saveParams)).then((function(e){i.updateSceneList()})).catch((function(){})),this.$emit("filter-change",e.obj))},updateSceneList:function(){this.$emit("event-change","scene-refresh")}}},he=pe,me=(i("bed6"),Object(w["a"])(he,n,a,!1,null,"2ebb3516",null));t["a"]=me.exports},"70b7":function(e,t,i){"use strict";i("5be9")},"713e":function(e,t,i){"use strict";i("4e1a")},7442:function(e,t,i){"use strict";i("7b0c")},7860:function(e,t,i){},"7b0c":function(e,t,i){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},"83f1":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{class:["xr-radio-menu-wrap",{"is-show":e.popverVisible}]},[i("el-popover",{attrs:{width:e.width,placement:"bottom-start","popper-class":"no-padding-popover",trigger:"click"},model:{value:e.popverVisible,callback:function(t){e.popverVisible=t},expression:"popverVisible"}},[i("div",{staticClass:"xr-radio-menu"},[i("div",{staticClass:"xr-radio-menu__content"},[e._l(e.options,(function(t,n){return i("div",{key:n,staticClass:"xr-radio-menu-item",class:{selected:e.selectValue==t.command},on:{click:function(i){e.selectClick(t)}}},[i("div",{staticClass:"mark"}),e._v(e._s(t.label)+"\n        ")])})),e._v(" "),i("div",{staticClass:"xr-radio-menu-default"},[i("span",[i("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:e.showDefault,expression:"showDefault"}],attrs:{"true-label":1,"false-label":0},on:{change:function(t){e.$emit("update:isDefault",e.isChecked)}},model:{value:e.isChecked,callback:function(t){e.isChecked=t},expression:"isChecked"}},[e._v("保存为默认值")])],1),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确定")])],1)],2)]),e._v(" "),e._t("reference",null,{slot:"reference"})],2),e._v(" "),i("wk-dep-user-dialog",{attrs:{"user-value":e.users,"dep-value":e.strucs,visible:e.membersDepVisible},on:{"update:userValue":function(t){e.users=t},"update:depValue":function(t){e.strucs=t},"update:visible":function(t){e.membersDepVisible=t},close:e.membersDepCancel,change:e.membersDepSelect},nativeOn:{click:function(e){e.stopPropagation()}}})],1)},a=[],s=(i("d81d"),i("b0c0"),i("e9f5"),i("ab43"),i("a9e3"),i("d3b7"),i("3ca3"),i("ddb0"),i("ed08")),o={name:"XrRadioMenu",components:{WkDepUserDialog:function(){return Promise.resolve().then(i.bind(null,"2581"))}},props:{width:{type:[String,Number],default:200},showDefault:{type:Boolean,default:!0},isDefault:{type:[String,Number],default:0},options:Array,value:[String,Number],userCheckedData:{type:Array,default:function(){return[]}},depCheckedData:{type:Array,default:function(){return[]}}},data:function(){return{popverVisible:!1,selectItem:{},selectValue:"",isChecked:0,membersDepVisible:!1,users:[],strucs:[],selectUsers:[],selectStrucs:[]}},computed:{},watch:{popverVisible:function(e){e&&(this.selectValue=this.value,this.selectUsers=Object(s["D"])(this.userCheckedData||[]),this.selectStrucs=Object(s["D"])(this.depCheckedData||[]),this.users=Object(s["D"])((this.userCheckedData||[]).map((function(e){return e.userId}))),this.strucs=Object(s["D"])((this.depCheckedData||[]).map((function(e){return e.deptId}))),this.isChecked=this.isDefault)}},mounted:function(){},methods:{membersDepCancel:function(){"custom"!=this.selectValue&&(this.users=[],this.strucs=[]),this.popverVisible=!0},membersDepSelect:function(e,t,i,n){i.length||n.length?(this.selectValue="custom",this.selectUsers=i.map((function(e){return{userId:e.userId,realname:e.realname}})),this.selectStrucs=n.map((function(e){return{name:e.name,deptId:e.deptId}}))):(this.selectValue=1,this.users=[],this.strucs=[],this.selectUsers=[],this.selectStrucs=[])},selectClick:function(e){this.isChecked=0,this.$emit("update:isDefault",this.isChecked),"custom"==e.command?this.membersDepVisible=!0:this.selectValue=e.command},confirm:function(){this.$emit("input",this.selectValue),"custom"==this.selectValue?this.$emit("select",this.selectValue,{users:Object(s["D"])(this.selectUsers),strucs:Object(s["D"])(this.selectStrucs)}):this.$emit("select",this.selectValue),this.popverVisible=!1}}},l=o,c=(i("e50e"),i("2877")),r=Object(c["a"])(l,n,a,!1,null,"53b3e57f",null);t["a"]=r.exports},"8f37":function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("flexbox",{staticClass:"reminder-wrapper"},[i("flexbox",{staticClass:"reminder-body",attrs:{align:"stretch"}},[i("i",{staticClass:"wk wk-warning reminder-icon"}),e._v(" "),i("div",{staticClass:"reminder-content",style:{"font-size":e.fontSize+"px"},domProps:{innerHTML:e._s(e.content)}}),e._v(" "),e._t("default"),e._v(" "),e.closeShow?i("i",{staticClass:"el-icon-close close",on:{click:e.close}}):e._e()],2)],1)},a=[],s={name:"Reminder",components:{},props:{closeShow:{type:Boolean,default:!1},content:{type:String,default:"内容"},fontSize:{type:String,default:"14"}},data:function(){return{}},computed:{},mounted:function(){},destroyed:function(){},methods:{close:function(){this.$emit("close")}}},o=s,l=(i("713e"),i("2877")),c=Object(l["a"])(o,n,a,!1,null,"82904f82",null);t["a"]=c.exports},9227:function(e,t,i){},9373:function(e,t,i){},"9baf":function(e,t,i){},a0e9:function(e,t,i){},b167:function(e,t,i){"use strict";i("9baf")},b1c0:function(e,t,i){"use strict";i("17b4")},b2dd:function(e,t,i){},b5d5:function(e,t,i){"use strict";i("b933")},b933:function(e,t,i){},bed6:function(e,t,i){"use strict";i("9227")},c377:function(e,t,i){"use strict";i("eb73")},c517:function(e,t,i){"use strict";i("4eee")},c96d:function(e,t,i){"use strict";i("45f9")},e50e:function(e,t,i){"use strict";i("a0e9")},e5a7:function(e,t,i){},ea7b:function(e,t,i){"use strict";i("f1ac")},eb73:function(e,t,i){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},f1ac:function(e,t,i){},f468:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("flexbox",{staticClass:"xr-header"},[e.iconClass?i("div",{staticClass:"xr-header__icon",style:{backgroundColor:e.iconColor}},[i("i",{class:e.iconClass})]):e._e(),e._v(" "),i("div",{staticClass:"xr-header__label"},[e.$slots.label?e._t("label"):[e._v(e._s(e.label)),e._t("otherLabel")]],2),e._v(" "),e.showSearch?i("el-input",e._b({staticClass:"xr-header__search",style:{"margin-top":e.ftTop},attrs:{placeholder:e.placeholder},on:{input:e.inputChange},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchClick(t)}},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},"el-input",e.inputAttr,!1),[i("el-button",{attrs:{slot:"suffix",type:"icon",icon:"wk wk-sousuo"},on:{click:e.searchClick},slot:"suffix"})],1):e._e(),e._v(" "),i("div",{staticClass:"xr-header__ft",style:{top:e.ftTop}},[e._t("ft")],2)],1)},a=[],s=(i("a9e3"),i("ac1f"),i("841c"),{name:"XrHeader",components:{},props:{iconClass:[String,Array],iconColor:String,label:String,showSearch:{type:Boolean,default:!1},placeholder:{type:String,default:"请输入内容"},ftTop:{type:String,default:"0"},content:[String,Number],inputAttr:{type:Object,default:function(){}}},data:function(){return{search:""}},computed:{},watch:{content:{handler:function(){this.search!=this.content&&(this.search=this.content)},immediate:!0}},mounted:function(){},beforeDestroy:function(){},methods:{inputChange:function(){this.$emit("update:content",this.search)},searchClick:function(){this.$emit("search",this.search)}}}),o=s,l=(i("3813"),i("2877")),c=Object(l["a"])(o,n,a,!1,null,"7eb05d11",null);t["a"]=c.exports},fdca:function(e,t,i){"use strict";var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("flexbox",{staticClass:"wk-page-header",attrs:{justify:"space-between"}},[i("div",{staticClass:"wk-page-header__left"},[e.title||e.$slots.title?i("span",{staticClass:"wk-page-header-title"},[e._t("title",[e._v(e._s(e.title))])],2):e._e(),e._v(" "),e.help?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.help.type,"data-id":e.help.id}}):e._e(),e._v(" "),e._t("left")],2),e._v(" "),i("div",{staticClass:"wk-page-header__center"},[e._t("center")],2),e._v(" "),i("div",{staticClass:"wk-page-header__right"},[e._t("right"),e._v(" "),e.dropdowns&&e.dropdowns.length>0?i("el-dropdown",{class:{"margin-left-interval":this.$slots.right},attrs:{trigger:"click"},on:{command:e.dropdownCommand}},[i("el-button",{staticClass:"dropdown-btn",attrs:{icon:"el-icon-more"}}),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.dropdowns,(function(t,n){return i("el-dropdown-item",{key:n,attrs:{icon:t.icon,command:t.command}},[e._v(e._s(t.name))])})))],1):e._e()],2)])},a=[],s={name:"WkPageHeader",components:{},props:{title:String,dropdowns:Array,help:Object},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{dropdownCommand:function(e){this.$emit("command",e)}}},o=s,l=(i("37af"),i("2877")),c=Object(l["a"])(o,n,a,!1,null,"73dad9ce",null);t["a"]=c.exports}}]);