(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ccd9e01e"],{"08c2":function(t,e,a){"use strict";var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),t._v(" "),a("span",{staticClass:"text"},[t._v(t._s(t.title))])]),t._v(" "),t.showFilterView?[t.showYearSelect?t._e():a("time-type-select",{on:{change:t.timeTypeChange}}),t._v(" "),t.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":t.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.yearValue,callback:function(e){t.yearValue=e},expression:"yearValue"}}):t._e(),t._v(" "),t._t("after-time"),t._v(" "),t.showSimpleChoose?[t.showUserSelect&&t.showDeptSelect?a("el-select",{model:{value:t.simpleChooseType,callback:function(e){t.simpleChooseType=e},expression:"simpleChooseType"}},t._l(t.simpleOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))):t._e(),t._v(" "),1===t.simpleChooseType&&t.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:t.structuresSelectValue,callback:function(e){t.structuresSelectValue=e},expression:"structuresSelectValue"}}):t._e(),t._v(" "),2===t.simpleChooseType&&t.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:t.userSelectValue,callback:function(e){t.userSelectValue=e},expression:"userSelectValue"}}):t._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:t.dataTypeOptions,"user-checked-data":t.filterValue.userList,"dep-checked-data":t.filterValue.deptList,width:250},on:{select:t.radioMenuSelect},model:{value:t.filterDataType,callback:function(e){t.filterDataType=e},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.avatarData.realname,callback:function(e){t.$set(t.avatarData,"realname",e)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),t.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:t.businessStatusValue,callback:function(e){t.businessStatusValue=e},expression:"businessStatusValue"}},t._l(t.businessOptions,(function(t){return a("el-option",{key:t.flowId,attrs:{label:t.flowName,value:t.flowId}})}))):t._e(),t._v(" "),t.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:t.productValue,callback:function(e){t.productValue=e},expression:"productValue"}}):t._e(),t._v(" "),t.showCustomSelect?a("el-select",{on:{change:t.customSelectChange},model:{value:t.customValue,callback:function(e){t.customValue=e},expression:"customValue"}},t._l(t.customOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}))):t._e(),t._v(" "),t._t("append"),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(e){return t.emitFilter(e)}}},[t._v("查询")]),t._v(" "),t._t("default")]:t._e()],2)},i=[],o=a("5530"),l=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),n=a("ea20"),r=a("657f"),u=a("bfba"),c=a("8f81"),p=a("83f1"),d=a("2f62"),h={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:u["a"],WkUserDialogSelect:c["a"],XrRadioMenu:p["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(o["a"])(Object(o["a"])({},Object(d["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var t=(this.filterValue.userList||[]).map((function(t){return t.realname})),e=(this.filterValue.deptList||[]).map((function(t){return t.name}));return{realname:t.concat(e).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var t=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){t.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(t){var e=this;Object(l["r"])().then((function(a){e.businessOptions=a.data||[],e.businessOptions.length>0&&(e.businessStatusValue=e.businessOptions[0].flowId),t(!0)})).catch((function(){e.$emit("error")}))},getProductCategoryIndex:function(){var t=this;Object(n["T"])({type:"tree"}).then((function(e){t.productOptions=e.data})).catch((function(){}))},radioMenuSelect:function(t,e){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=e.users,this.filterValue.deptList=e.strucs)},timeTypeChange:function(t){this.timeTypeValue=t},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var t=this,e={};this.showSimpleChoose?1===this.simpleChooseType?e.deptList=(this.structuresSelectValue||"").split(",").filter((function(t){return!!t})):e.userList=(this.userSelectValue||"").split(",").filter((function(t){return!!t})):"custom"!==this.filterValue.dataType?e.dataType=this.filterValue.dataType:(e.dataType=0,e.deptList=(this.filterValue.deptList||[]).map((function(t){return t.deptId})),e.userList=(this.filterValue.userList||[]).map((function(t){return t.userId}))),this.showYearSelect?(e.dateFilter="custom",e.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(e.startDate=this.timeTypeValue.startTime,e.endDate=this.timeTypeValue.endTime,e.dateFilter="custom"):e.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(e.typeId=this.businessStatusValue,e.businessItem=this.businessOptions.map((function(e){if(e.flowId===t.businessStatusValue)return e}))),this.showProductSelect&&(e.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",e)}}},m=h,f=(a("965d"),a("2877")),w=Object(f["a"])(m,s,i,!1,null,"6d7c8f9a",null);e["a"]=w.exports},"2c72":function(t,e,a){"use strict";a("5f3b")},"5f3b":function(t,e,a){},"965d":function(t,e,a){"use strict";a("c558")},"9b3e":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"员工通话记录","module-type":"product"},on:{load:function(e){t.loading=!0},change:t.getProductDatalist}},[a("el-input",{staticClass:"input-with-select",attrs:{slot:"append"},slot:"append",model:{value:t.talkTime,callback:function(e){t.talkTime=e},expression:"talkTime"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"通话时长"},slot:"prepend",model:{value:t.talkTimeCondition,callback:function(e){t.talkTimeCondition=e},expression:"talkTimeCondition"}},[a("el-option",{attrs:{label:"大于",value:">"}}),t._v(" "),a("el-option",{attrs:{label:"小于",value:"<"}}),t._v(" "),a("el-option",{attrs:{label:"等于",value:"="}})],1),t._v(" "),a("el-button",{attrs:{slot:"append",type:"text"},slot:"append"},[t._v("秒")])],1)],1),t._v(" "),a("div",{staticClass:"content"},[a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{id:"crm-table",size:"small",data:t.list,stripe:t.WKConfig.tableStyle.stripe,height:t.tableHeight,"cell-style":t.cellStyle},on:{"row-click":t.handleRowClick}},[t._l(t.headFieldList,(function(e,s){return a("el-table-column",{key:s,attrs:{"min-width":e.width,formatter:t.timeFormatter,prop:e.field,label:e.name,"show-overflow-tooltip":""}})})),t._v(" "),a("el-table-column",{attrs:{prop:"type",label:"通话状态",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(t.typeArr[e.row.type])+"-"+t._s(t.stateArr[e.row.state])+"\n        ")]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"audio",label:"录音播放",width:"300px"},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.row;return[a("audios",{attrs:{duration:s.talkTime,props:t.getAudiosProps(s)}})]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"down",label:"操作",width:"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){t.download(e.row.callRecordId,e.row.fileName)}}},[t._v("下载")])]}}])})],2),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":t.currentPage,"page-sizes":t.pageSizes,"page-size":t.pageSize,total:t.total,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:pageSize":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),t._v(" "),t.showCustomerView&&"contract"===t.customertype?a("contract-detail",{staticClass:"d-view",attrs:{id:t.rowID},on:{"hide-view":function(e){t.showCustomerView=!1}}}):t.showCustomerView&&"customer"===t.customertype?a("customer-detail",{staticClass:"d-view",attrs:{id:t.rowID},on:{"hide-view":function(e){t.showCustomerView=!1}}}):t.showCustomerView&&"leads"===t.customertype?a("leads-detail",{staticClass:"d-view",attrs:{id:t.rowID},on:{"hide-view":function(e){t.showCustomerView=!1}}}):t._e()],1)},i=[],o=a("5530"),l=(a("14d9"),a("d2b8")),n=a("995c"),r=a("d5b3"),u=a("de88"),c=a("df55"),p=a("b988"),d=a("ed08"),h={name:"CallDetailsStatistics",components:{Audios:p["a"],ContractDetail:n["a"],CustomerDetail:r["a"],LeadsDetail:u["a"]},mixins:[c["a"]],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-210,postParams:{},stateArr:["未振铃","未接通","接通","呼入未接通"],typeArr:["呼出","呼入"],headFieldList:[{field:"ownerUserName",name:"姓名",width:"115px"},{field:"createTime",name:"通话时间",width:"115px"},{field:"number",name:"呼/被叫号码",width:"115px"},{field:"modelName",name:"相关客户",width:"115px"},{field:"talkTime",name:"通话时长",width:"115px"},{field:"dialTime",name:"摘机时长",width:"115px"}],list:[],spanList:[],newList:[],showCustomerView:!1,customertype:"",rowID:"",pageData:{},talkTime:"",talkTimeCondition:"<"}},computed:{},mounted:function(){var t=this;window.onresize=function(){t.tableHeight=document.documentElement.clientHeight-210}},methods:{handleRowClick:function(t,e,a){"modelName"===e.property&&(this.rowID=t.modelId,this.customertype=t.model,this.showCustomerView=!0)},cellStyle:function(t){t.row;var e=t.column;t.rowIndex,t.columnIndex;return"modelName"===e.property?{color:"#3E84E9",cursor:"pointer"}:{}},getProductDatalist:function(t){this.pageData=t;var e=Object(o["a"])(Object(o["a"])({},t),{},{talkTime:this.talkTime,talkTimeCondition:this.talkTimeCondition,page:1});this.getList(e)},handleShowInfo:function(){for(var t=[],e=[],a=0,s=0,i=0,o=0,l=0,n=0,r=0;r<this.list.length;r++){var u=this.list[r];if(0==e.length)a=0,s=0,i=parseFloat(u.productNum),o=parseFloat(u.productSubtotal),l=parseFloat(u.productNum),n=parseFloat(u.productSubtotal),e.push({rowspan:1,productRowspan:1}),t.push(u);else if(u.categoryId!=this.list[r-1].categoryId){var c=e[a];c.rowspan+=1,t.push({productNum:i,productSubtotal:o}),e.push({rowspan:0,productRowspan:1,isSum:!0}),t.push({productNum:l,productSubtotal:n}),e.push({rowspan:1,productRowspan:1,isAllSum:!0}),e.push({rowspan:1,productRowspan:1}),i=parseFloat(u.productNum),o=parseFloat(u.productSubtotal),l=parseFloat(u.productNum),n=parseFloat(u.productSubtotal),t.push(u),a=e.length-1,s=e.length-1}else{c=e[a];if(c.rowspan+=1,u.productId==this.list[r-1].productId){var p=e[s];p.productRowspan+=1,e.push({rowspan:0,productRowspan:0}),i+=parseFloat(u.productNum),o+=parseFloat(u.productSubtotal),l+=parseFloat(u.productNum),n+=parseFloat(u.productSubtotal),t.push(u)}else c.rowspan+=1,t.push({productNum:i,productSubtotal:o}),e.push({rowspan:0,productRowspan:1,isSum:!0}),e.push({rowspan:0,productRowspan:1}),s=e.length-1,i=parseFloat(u.productNum),o=parseFloat(u.productSubtotal),l+=parseFloat(u.productNum),n+=parseFloat(u.productSubtotal),t.push(u)}if(this.list.length-1==r){c=e[a];c.rowspan+=1,t.push({productNum:i,productSubtotal:o}),i=0,o=0,e.push({rowspan:0,productRowspan:1,isSum:!0}),t.push({productNum:l,productSubtotal:n}),l=0,n=0,e.push({rowspan:1,productRowspan:1,isAllSum:!0})}}this.spanList=e,this.newList=t},getAudiosProps:function(t){return{fileRequest:l["b"],fileParams:{id:t.callRecordId}}},download:function(t,e){Object(l["b"])({id:t}).then((function(t){var a=new Blob([t.data],{type:"audio/mpeg"});Object(d["i"])(a,e)})).catch((function(){}))},timeFormatter:function(t,e,a,s){switch(e.label){case"外呼接通率":return(parseInt(a)||"0")+"%";case"接通率":return(parseInt(a)||"0")+"%";case"通话时长":return this.MillisecondToDate(a);case"摘机时长":return this.MillisecondToDate(a);case"外呼通话时长":return this.MillisecondToDate(a);default:return a}},MillisecondToDate:function(t){var e;if(e=null!=t&&""!=t?parseFloat(t):0,e<60)return e+"秒";if(e<3600){var a=Math.floor(e/60);return a+"分"+Math.floor(e-60*a)+"秒"}var s=Math.floor(e/3600),i=Math.floor((e-3600*s)/60);return s+"小时"+i+"分"},getList:function(t){var e=this;this.loading=!0,Object(l["d"])(t).then((function(t){e.list=t.data.list,e.total=t.data.totalRow,e.loading=!1})).catch((function(){e.loading=!1}))}}},m=h,f=(a("2c72"),a("2877")),w=Object(f["a"])(m,s,i,!1,null,"5761083e",null);e["default"]=w.exports},c558:function(t,e,a){},df55:function(t,e,a){"use strict";var s=a("5530"),i=(a("d3b7"),a("08c2")),o=a("7a1a"),l=a("ed08"),n=a("a347"),r=a.n(n);e["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},textColor:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{lineStyle:{color:r.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:r.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:i["a"]},props:{},computed:{},watch:{},mounted:function(){var t=this;this.debouncedResize=Object(o["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",t.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(t){this.pageData.limit=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(t){this.pageData.page=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(t,e){return new Promise((function(a,s){t(e).then((function(t){Object(l["g"])(t),a&&a(t)})).catch((function(t){s&&s(t)}))}))},getChartYAxisStyle:function(t){var e=Object(l["D"])(this.chartYAxisStyle);if(!t)return e;for(var a in t){var i=e[a],o=t[a];e[a]=i?Object(s["a"])(Object(s["a"])({},i),o):o}return e}},deactivated:function(){}}}}]);