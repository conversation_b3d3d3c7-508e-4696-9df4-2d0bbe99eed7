(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-026cd134"],{"08c2":function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),e._v(" "),a("span",{staticClass:"text"},[e._v(e._s(e.title))])]),e._v(" "),e.showFilterView?[e.showYearSelect?e._e():a("time-type-select",{on:{change:e.timeTypeChange}}),e._v(" "),e.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":e.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:e.yearValue,callback:function(t){e.yearValue=t},expression:"yearValue"}}):e._e(),e._v(" "),e._t("after-time"),e._v(" "),e.showSimpleChoose?[e.showUserSelect&&e.showDeptSelect?a("el-select",{model:{value:e.simpleChooseType,callback:function(t){e.simpleChooseType=t},expression:"simpleChooseType"}},e._l(e.simpleOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),1===e.simpleChooseType&&e.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:e.structuresSelectValue,callback:function(t){e.structuresSelectValue=t},expression:"structuresSelectValue"}}):e._e(),e._v(" "),2===e.simpleChooseType&&e.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:e.userSelectValue,callback:function(t){e.userSelectValue=t},expression:"userSelectValue"}}):e._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:e.dataTypeOptions,"user-checked-data":e.filterValue.userList,"dep-checked-data":e.filterValue.deptList,width:250},on:{select:e.radioMenuSelect},model:{value:e.filterDataType,callback:function(t){e.filterDataType=t},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:e.avatarData.realname,callback:function(t){e.$set(e.avatarData,"realname",t)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),e._v(" "),e.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:e.businessStatusValue,callback:function(t){e.businessStatusValue=t},expression:"businessStatusValue"}},e._l(e.businessOptions,(function(e){return a("el-option",{key:e.flowId,attrs:{label:e.flowName,value:e.flowId}})}))):e._e(),e._v(" "),e.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:e.productValue,callback:function(t){e.productValue=t},expression:"productValue"}}):e._e(),e._v(" "),e.showCustomSelect?a("el-select",{on:{change:e.customSelectChange},model:{value:e.customValue,callback:function(t){e.customValue=t},expression:"customValue"}},e._l(e.customOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):e._e(),e._v(" "),e._t("append"),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(t){return e.emitFilter(t)}}},[e._v("查询")]),e._v(" "),e._t("default")]:e._e()],2)},i=[],r=a("5530"),o=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),l=a("ea20"),n=a("657f"),c=a("bfba"),u=a("8f81"),p=a("83f1"),h=a("2f62"),d={name:"FiltrateHandleView",components:{TimeTypeSelect:n["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:p["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(r["a"])(Object(r["a"])({},Object(h["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var e=(this.filterValue.userList||[]).map((function(e){return e.realname})),t=(this.filterValue.deptList||[]).map((function(e){return e.name}));return{realname:e.concat(t).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var e=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){e.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(e){var t=this;Object(o["r"])().then((function(a){t.businessOptions=a.data||[],t.businessOptions.length>0&&(t.businessStatusValue=t.businessOptions[0].flowId),e(!0)})).catch((function(){t.$emit("error")}))},getProductCategoryIndex:function(){var e=this;Object(l["T"])({type:"tree"}).then((function(t){e.productOptions=t.data})).catch((function(){}))},radioMenuSelect:function(e,t){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=t.users,this.filterValue.deptList=t.strucs)},timeTypeChange:function(e){this.timeTypeValue=e},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var e=this,t={};this.showSimpleChoose?1===this.simpleChooseType?t.deptList=(this.structuresSelectValue||"").split(",").filter((function(e){return!!e})):t.userList=(this.userSelectValue||"").split(",").filter((function(e){return!!e})):"custom"!==this.filterValue.dataType?t.dataType=this.filterValue.dataType:(t.dataType=0,t.deptList=(this.filterValue.deptList||[]).map((function(e){return e.deptId})),t.userList=(this.filterValue.userList||[]).map((function(e){return e.userId}))),this.showYearSelect?(t.dateFilter="custom",t.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(t.startDate=this.timeTypeValue.startTime,t.endDate=this.timeTypeValue.endTime,t.dateFilter="custom"):t.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(t.typeId=this.businessStatusValue,t.businessItem=this.businessOptions.map((function(t){if(t.flowId===e.businessStatusValue)return t}))),this.showProductSelect&&(t.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",t)}}},m=d,f=(a("965d"),a("2877")),y=Object(f["a"])(m,s,i,!1,null,"6d7c8f9a",null);t["a"]=y.exports},"965d":function(e,t,a){"use strict";a("c558")},b80b:function(e,t,a){"use strict";var s=a("b85c"),i=a("2909"),r=a("3835"),o=a("5530"),l=(a("99af"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("f665"),a("ab43"),a("d3b7"),a("ac1f"),a("2532"),a("841c"),a("ddb0"),a("2a64")),n=a("6683"),c=a("f2ec"),u=a("c1df"),p=a.n(u);t["a"]={data:function(){return{fieldReportList:null,reportListShow:!1,reportData:{title:"",placeholder:"",crmType:"",request:null,params:null,paging:!0,sortable:!1},search:"",isSeas:!1}},components:{ReportList:l["a"]},methods:{handleRowClick:function(e,t,a){if("selection"!==t.type){var s=this.detailFields.map((function(e){return e.name}));if(s.includes(t.property)){var i=this.detailFields.find((function(e){return e.name==t.property}));if(i){var r=t.label,l=Object(o["a"])(Object(o["a"])({},i),{},{title:r}),n=null;"followTimes"===i.fieldType?(this.reportData.crmType="customer",n=Object(o["a"])({crmType:2,label:2,queryType:0,type:2},this.postParams)):"followCustomes"===i.fieldType?(this.reportData.crmType="customer",n={type:2,userList:[e.userId],search:"",dataType:this.postParams.dataType,dateFilter:this.postParams.dateFilter},i.customerNum&&(n.searchList=[{formType:"user",name:"ownerUserId",type:3,values:[e.userId]}],n.dataType=0,n.category=2)):"product"===this.type?n=Object(o["a"])({search:"",id:e.productId},this.postParams):(n=this.getDetailParams(i,e),["putInNum"].includes(i.name)&&(n.id=e.userId||e.ownerUserId),"product"===i.crmType&&(n.id=e.productId)),delete l.name,this.reportData=Object(o["a"])(Object(o["a"])(Object(o["a"])({},this.reportData),l),{},{params:n}),i.flowName&&e.isEnd&&(this.reportData.params.categoryId=this.postParams.typeId,this.reportData.params.dateFilter=this.postParams.dateFilter,this.reportData.request=c["d"]),i.isBusiness&&0==this.postParams.dataType&&(this.reportData.params.deptList=this.postParams.deptList,this.reportData.params.userList=this.postParams.userList),"followTimes"===i.fieldType?(this.recordParams.dateFilter=this.postParams.dateFilter,"custom"===this.recordParams.dateFilter&&(this.recordParams.startDate=this.postParams.startDate,this.recordParams.endDate=this.postParams.endDate),"times"!==i.followType&&(this.recordParams.dataType=this.postParams.dataType),this.recordParams.userList=[e.userId],this.recordShow=!0):this.reportListShow=!0,"custom"===this.reportData.params.dateFilter&&(this.reportData.params.startDate=this.postParams.startDate,this.reportData.params.endDate=this.postParams.endDate),["customer","address","address","product"].includes(this.type)&&0==this.postParams.dataType&&(this.reportData.params.deptList=this.postParams.deptList,this.reportData.params.userList=this.postParams.userList),"invoice"==i.crmType&&0==this.postParams.dataType&&(this.reportData.params.deptList=this.postParams.deptList,this.reportData.params.userList=this.postParams.userList),["popular","productPopular"].includes(i.fieldType)&&(this.reportData.params.dateFilter=this.postParams.dateFilter,"custom"===this.reportData.params.dateFilter&&(this.reportData.params.startDate=this.postParams.startDate,this.reportData.params.endDate=this.postParams.endDate),0==this.postParams.dataType&&(this.reportData.params.deptList=this.postParams.deptList),"popular"==i.fieldType?(this.reportData.params.searchType||(this.reportData.params.dataType=0),this.reportData.params.userList=[e.ownerUserId]):(0==this.postParams.dataType&&(this.reportData.params.deptList=this.postParams.deptList,this.reportData.params.userList=this.postParams.userList),this.reportData.params.id=e.productId),delete this.reportData.params.searchList)}}else this.reportListShow=!1}},getDetailParams:function(e,t){this.reportData.crmType=e.crmType||"customer";var a=this.isSeas?n["a"].pool:n["a"][this.reportData.crmType],s="",i=null;if(e.padDate){var l=t.type.split("-"),c=Object(r["a"])(l,2),u=c[0],h=c[1];s=p()("".concat(u,"-").concat(h,"-01")).endOf("month").format("YYYY-MM-DD"),i=[t.type,s]}else i="custom"==this.postParams.dateFilter?[this.postParams.startDate,this.postParams.endDate||s]:[this.postParams.dateFilter];var d=this.getSearchList(e,i,t);return Object(o["a"])({search:this.search,type:t.isEnd?t.isEnd:a,searchList:d,dataType:this.postParams.dataType},e.params)},getSearchList:function(e,t,a){var r=e.timeName,o=e.list,l=e.last,n=e.flowName,c=e.isBusiness,u=[];if(n||(u=[{formType:["orderDate","returnTime","realInvoiceDate"].includes(r)?"date":"datetime",name:r||"createTime",type:14,values:t}].concat(Object(i["a"])(o||[]))),n&&c&&!a.isEnd){var p=this.businessItem[0];u=[{formType:"datetime",name:"createTime",type:14,values:t},{formType:"business_cause",name:"flowName",group:!0,type:1,values:[p.flowName]},{formType:"business_cause",name:"settingName",stage:!0,type:1,values:[a.settingName]}]}var h,d=Object(s["a"])(u);try{for(d.s();!(h=d.n()).done;){var m=h.value;"ownerUserId"===m.name||"createUserId"===m.name?m.values=[a.userId||a.ownerUserId]:"isEnd"===m.name?m.values=[a.isEnd]:"address"===m.name?m.values=[a.type]:"customerRank"===e.fieldType&&"user"===m.formType&&(m.values=[a.userId])}}catch(f){d.e(f)}finally{d.f()}return l&&u.push({formType:"datetime",name:"lastTime",type:14,values:t}),u},cellClassName:function(e){e.row;var t=e.column,a=(e.rowIndex,e.columnIndex,this.detailFields.map((function(e){return e.name})));return a.includes(t.property)?"can-visit--underline":""}}}},c558:function(e,t,a){},df55:function(e,t,a){"use strict";var s=a("5530"),i=(a("d3b7"),a("08c2")),r=a("7a1a"),o=a("ed08"),l=a("a347"),n=a.n(l);t["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight},textColor:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight},axisLine:{lineStyle:{color:n.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:n.a.colorBlack,fontWeight:n.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:n.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:i["a"]},props:{},computed:{},watch:{},mounted:function(){var e=this;this.debouncedResize=Object(r["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",e.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(e){this.pageData.limit=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(e){this.pageData.page=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(e,t){return new Promise((function(a,s){e(t).then((function(e){Object(o["g"])(e),a&&a(e)})).catch((function(e){s&&s(e)}))}))},getChartYAxisStyle:function(e){var t=Object(o["D"])(this.chartYAxisStyle);if(!e)return t;for(var a in e){var i=t[a],r=e[a];t[a]=i?Object(s["a"])(Object(s["a"])({},i),r):r}return t}},deactivated:function(){}}},f2ec:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return l}));var s=a("b775");function i(e){return Object(s["a"])({url:"biFunnel/addBusinessAnalyze",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(e){return Object(s["a"])({url:"biFunnel/win",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(e){return Object(s["a"])({url:"crmBiSearch/searchBusinessPageList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(s["a"])({url:"crmInstrument/queryContendBusinessList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}}}]);