(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-05b6b9e2"],{a04c:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"handel-header"},[t.viewLoaded?a("crm-relative",{ref:"crmrelative",attrs:{visible:t.showSelectView,radio:!1,"selected-data":t.selectedData,props:{showScene:!1,ignoreFilterFields:["status"],searchList:[{formType:"select",name:"status",type:1,values:[1]}]},"crm-type":"product"},on:{"update:visible":function(e){t.showSelectView=e},changeCheckout:t.selectInfos}}):t._e(),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.addClick}},[t._v("添加产品")])],1),t._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{size:"small",data:t.productList}},[a("el-table-column",{attrs:{prop:"name","show-overflow-tooltip":"",label:"产品名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"categoryName","show-overflow-tooltip":"",label:"产品类别"}}),t._v(" "),a("el-table-column",{attrs:{prop:"unit","show-overflow-tooltip":"",label:"单位"}}),t._v(" "),a("el-table-column",{attrs:{prop:"price","show-overflow-tooltip":"",label:"标准价格"}}),t._v(" "),a("el-table-column",{attrs:{label:"售价"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number"}],attrs:{placeholder:"请输入",type:"number"},on:{input:function(a){t.salesPriceChange(e)}},model:{value:e.row.salesPrice,callback:function(a){t.$set(e.row,"salesPrice",a)},expression:"scope.row.salesPrice"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"数量"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number"}],attrs:{type:"number",placeholder:"请输入"},on:{input:function(a){t.numChange(e)}},model:{value:e.row.num,callback:function(a){t.$set(e.row,"num",a)},expression:"scope.row.num"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"折扣（%）"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number"}],attrs:{placeholder:"请输入",type:"number"},on:{input:function(a){t.discountChange(e)}},model:{value:e.row.discount,callback:function(a){t.$set(e.row,"discount",a)},expression:"scope.row.discount"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"subtotal",label:"合计"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(a){t.removeItem(e.$index)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("flexbox",{staticClass:"handle-footer"},[a("div",{staticClass:"discount-title"},[t._v("整单折扣（%）：")]),t._v(" "),a("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number"}],staticStyle:{width:"80px"},attrs:{placeholder:"请输入",type:"number"},on:{input:t.rateChange},model:{value:t.discountRate,callback:function(e){t.discountRate=e},expression:"discountRate"}}),t._v(" "),a("div",{staticClass:"total-info discount-title"},[t._v("已选中产品：\n      "),a("span",{staticClass:"info-yellow"},[t._v(t._s(t.productList.length))]),t._v(" 种  总金额：\n      "),a("el-input",{directives:[{name:"wk-number",rawName:"v-wk-number"}],staticStyle:{width:"120px"},attrs:{placeholder:"请输入",type:"number"},on:{input:t.totalPriceChange,blur:function(e){t.totalPrice||(t.totalPrice=0)}},model:{value:t.totalPrice,callback:function(e){t.totalPrice=e},expression:"totalPrice"}}),t._v(" 元\n    ")],1)],1)],1)},i=[],c=(a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("b680"),a("d3b7"),a("2532"),a("159b"),a("a9e3"),{data:function(){return{dataValue:{}}},watch:{value:function(t){this.dataValue=t}},props:{value:{type:Object,default:function(){return{}}},index:Number,item:Object,disabled:{type:Boolean,default:!1}},created:function(){this.dataValue=this.value},methods:{valueChange:function(t){this.$emit("value-change",{index:this.index,value:t})}}}),n=a("9a4b"),l={name:"XhProduct",components:{CrmRelative:n["default"]},mixins:[c],props:{},data:function(){return{showSelectView:!1,viewLoaded:!1,productList:[],totalPrice:0,discountRate:"",selectedData:{product:[]}}},computed:{},watch:{dataValue:function(t){this.refreshProductList()},productList:function(t,e){var a=this;if(t.length!==e.length){var o=e.map((function(t){return t.productId})),i=[];t.forEach((function(t){o.includes(t.productId)||i.push(t)}));var c=[],n=t.map((function(t){return t.productId}));e.forEach((function(t){n.includes(t.productId)||c.push(t)})),this.$refs.crmrelative&&(i.forEach((function(t){a.$refs.crmrelative.toggleRowSelection("productId",t.productId,!0)})),c.forEach((function(t){a.$refs.crmrelative.toggleRowSelection("productId",t.productId,!1)})))}this.selectedData={product:this.productList||[]}}},mounted:function(){this.refreshProductList()},methods:{refreshProductList:function(){this.productList=this.dataValue.product||[],this.totalPrice=this.dataValue.totalPrice||0,this.discountRate=this.dataValue.discountRate||""},selectInfos:function(t){var e=this;if(t.data){var a=[];t.data.forEach((function(t){var o=e.productList.find((function(e){return e.productId==t.productId}));o?a.push(o):a.push(e.getShowItem(t))})),this.productList=a,this.calculateToal()}},getShowItem:function(t){var e={};return e.name=t.name,e.categoryName=t.categoryName,e.unit=t.unit,e.price=t.price||0,e.salesPrice=t.price||0,e.num=1,e.discount=0,e.subtotal=t.price||0,e.productId=t.productId,e},salesPriceChange:function(t){var e=t.row;if(0!==e.price){var a=(e.price-e.salesPrice||0)/e.price*100;a=a.toFixed(2),e.discount!==a&&(e.discount=a)}this.calculateSubTotal(e),this.calculateToal()},numChange:function(t){var e=t.row;e.num<0&&(e.num=0),this.calculateSubTotal(e),this.calculateToal()},discountChange:function(t){var e=t.row,a=e.price*(100-parseFloat(e.discount||0))/100;a=a.toFixed(2),e.salesPrice!==a&&(e.salesPrice=a),this.calculateSubTotal(e),this.calculateToal()},calculateSubTotal:function(t){t.subtotal=(t.salesPrice*parseFloat(t.num||0)).toFixed(2)},calculateToal:function(){var t=this.getProductTotal();t=t*(100-parseFloat(this.discountRate||0))/100,this.totalPrice=t.toFixed(2),this.updateValue()},getProductTotal:function(){for(var t=0,e=0;e<this.productList.length;e++){var a=this.productList[e];t+=parseFloat(a.subtotal)}return t},rateChange:function(){this.calculateToal()},totalPriceChange:function(){var t=this.getProductTotal();t&&(this.discountRate=(100-parseFloat(this.totalPrice)/t*100).toFixed(2)),this.updateValue()},removeItem:function(t){var e=this.productList.splice(t,1);this.$refs.crmrelative&&e.length>0&&this.$refs.crmrelative.toggleRowSelection("productId",e[0].productId,!1),this.calculateToal()},updateValue:function(){this.valueChange({product:this.productList,totalPrice:this.totalPrice,discountRate:this.discountRate})},addClick:function(){this.viewLoaded=!0,this.showSelectView=!0}}},r=l,u=(a("d821"),a("2877")),s=Object(u["a"])(r,o,i,!1,null,"5ce86b5e",null);e["default"]=s.exports},afa3:function(t,e,a){},d821:function(t,e,a){"use strict";a("afa3")}}]);