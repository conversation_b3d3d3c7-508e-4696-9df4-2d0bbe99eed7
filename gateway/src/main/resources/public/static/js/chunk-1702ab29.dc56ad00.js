(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1702ab29"],{"08c2":function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),e._v(" "),a("span",{staticClass:"text"},[e._v(e._s(e.title))])]),e._v(" "),e.showFilterView?[e.showYearSelect?e._e():a("time-type-select",{on:{change:e.timeTypeChange}}),e._v(" "),e.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":e.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:e.yearValue,callback:function(t){e.yearValue=t},expression:"yearValue"}}):e._e(),e._v(" "),e._t("after-time"),e._v(" "),e.showSimpleChoose?[e.showUserSelect&&e.showDeptSelect?a("el-select",{model:{value:e.simpleChooseType,callback:function(t){e.simpleChooseType=t},expression:"simpleChooseType"}},e._l(e.simpleOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),1===e.simpleChooseType&&e.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:e.structuresSelectValue,callback:function(t){e.structuresSelectValue=t},expression:"structuresSelectValue"}}):e._e(),e._v(" "),2===e.simpleChooseType&&e.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:e.userSelectValue,callback:function(t){e.userSelectValue=t},expression:"userSelectValue"}}):e._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:e.dataTypeOptions,"user-checked-data":e.filterValue.userList,"dep-checked-data":e.filterValue.deptList,width:250},on:{select:e.radioMenuSelect},model:{value:e.filterDataType,callback:function(t){e.filterDataType=t},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:e.avatarData.realname,callback:function(t){e.$set(e.avatarData,"realname",t)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),e._v(" "),e.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:e.businessStatusValue,callback:function(t){e.businessStatusValue=t},expression:"businessStatusValue"}},e._l(e.businessOptions,(function(e){return a("el-option",{key:e.flowId,attrs:{label:e.flowName,value:e.flowId}})}))):e._e(),e._v(" "),e.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:e.productValue,callback:function(t){e.productValue=t},expression:"productValue"}}):e._e(),e._v(" "),e.showCustomSelect?a("el-select",{on:{change:e.customSelectChange},model:{value:e.customValue,callback:function(t){e.customValue=t},expression:"customValue"}},e._l(e.customOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):e._e(),e._v(" "),e._t("append"),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(t){return e.emitFilter(t)}}},[e._v("查询")]),e._v(" "),e._t("default")]:e._e()],2)},i=[],l=a("5530"),o=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),n=a("ea20"),r=a("657f"),c=a("bfba"),u=a("8f81"),h=a("83f1"),p=a("2f62"),d={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:h["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(l["a"])(Object(l["a"])({},Object(p["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var e=(this.filterValue.userList||[]).map((function(e){return e.realname})),t=(this.filterValue.deptList||[]).map((function(e){return e.name}));return{realname:e.concat(t).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var e=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){e.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(e){var t=this;Object(o["r"])().then((function(a){t.businessOptions=a.data||[],t.businessOptions.length>0&&(t.businessStatusValue=t.businessOptions[0].flowId),e(!0)})).catch((function(){t.$emit("error")}))},getProductCategoryIndex:function(){var e=this;Object(n["T"])({type:"tree"}).then((function(t){e.productOptions=t.data})).catch((function(){}))},radioMenuSelect:function(e,t){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=t.users,this.filterValue.deptList=t.strucs)},timeTypeChange:function(e){this.timeTypeValue=e},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var e=this,t={};this.showSimpleChoose?1===this.simpleChooseType?t.deptList=(this.structuresSelectValue||"").split(",").filter((function(e){return!!e})):t.userList=(this.userSelectValue||"").split(",").filter((function(e){return!!e})):"custom"!==this.filterValue.dataType?t.dataType=this.filterValue.dataType:(t.dataType=0,t.deptList=(this.filterValue.deptList||[]).map((function(e){return e.deptId})),t.userList=(this.filterValue.userList||[]).map((function(e){return e.userId}))),this.showYearSelect?(t.dateFilter="custom",t.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(t.startDate=this.timeTypeValue.startTime,t.endDate=this.timeTypeValue.endTime,t.dateFilter="custom"):t.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(t.typeId=this.businessStatusValue,t.businessItem=this.businessOptions.map((function(t){if(t.flowId===e.businessStatusValue)return t}))),this.showProductSelect&&(t.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",t)}}},f=d,m=(a("965d"),a("2877")),b=Object(m["a"])(f,s,i,!1,null,"6d7c8f9a",null);t["a"]=b.exports},"3eef":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"新增项目","module-type":"business"},on:{load:function(t){e.loading=!0},change:e.searchClick}}),e._v(" "),a("div",{staticClass:"content"},[e._m(0),e._v(" "),a("div",{staticClass:"table-content"},[a("el-table",{class:e.WKConfig.tableStyle.class,attrs:{size:"small",data:e.list,stripe:e.WKConfig.tableStyle.stripe,"cell-class-name":e.cellClassName,height:"150","highlight-current-row":""},on:{"row-click":e.handleRowClick}},e._l(e.fieldList,(function(e,t){return a("el-table-column",{key:t,attrs:{prop:e.field,label:e.name,"show-overflow-tooltip":""}})})))],1)]),e._v(" "),a("report-list",{attrs:{show:e.reportListShow,title:e.reportData.title,"crm-type":"business",placeholder:e.reportData.placeholder,request:e.reportData.request,params:e.reportData.params,"field-list":e.fieldReportList},on:{"update:show":function(t){e.reportListShow=t}}})],1)},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"axis-content"},[a("div",{attrs:{id:"axismain"}})])}],l=a("5530"),o=(a("14d9"),a("f2ec")),n=a("2a64"),r=a("df55"),c=a("313e"),u=a("8add"),h={name:"BusinessTrendStatistics",components:{ReportList:n["a"]},mixins:[r["a"]],data:function(){return{loading:!1,axisOption:null,list:[],postParams:{},axisList:[],fieldList:[],reportListShow:!1,fieldReportList:null,reportData:{title:"",placeholder:"",request:null,params:null}}},computed:{},mounted:function(){this.initAxis()},methods:{searchClick:function(e){this.postParams=e,this.getDataList()},getDataList:function(){var e=this;this.loading=!0,Object(o["b"])(this.postParams).then((function(t){e.loading=!1,e.axisList=t.data||[];for(var a=[{name:"日期",field:"name"}],s=[],i=[],l=[],o={name:"数量"},n=0;n<e.axisList.length;n++){var r=e.axisList[n];s.push(r.businessMoney),i.push(r.businessNum),l.push(r.type),a.push({name:"".concat(r.type),field:"type".concat(n)}),o["type".concat(n)]=r.businessNum}e.fieldList=a,e.list=[o],e.axisOption.xAxis[0].data=l,e.axisOption.series[0].data=s,e.axisOption.series[1].data=i,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},cellClassName:function(e){e.row,e.column,e.rowIndex;var t=e.columnIndex;return t?"can-visit--underline":""},handleRowClick:function(e,t,a){if("name"!==t.property){this.reportData.title="".concat(t.label,"详情"),this.reportData.request=o["a"];var s=Object(u["a"])(t.label,this.postParams.dateFilter);this.postParams.searchList=[{formType:"datetime",name:"createTime",type:14,values:s}];var i=Object(l["a"])(Object(l["a"])({},this.postParams),{},{dataType:this.postParams.dataType});i.type=5,this.reportData.params=i,this.reportListShow=!0}},initAxis:function(){var e=c["b"](document.getElementById("axismain")),t={color:this.echartLineBarColors,toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(l["a"])({data:["新增项目金额","新增项目数量"]},this.chartDefaultOptions.legend),grid:this.chartDefaultOptions.grid,xAxis:[Object(l["a"])({type:"category",data:[]},this.chartXAxisStyle)],yAxis:[Object(l["a"])({type:"value",name:"新增项目金额"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}元"}})),Object(l["a"])({type:"value",name:"新增项目数量"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}个"},splitLine:{show:!0}}))],series:[Object(l["a"])(Object(l["a"])({},this.chartDefaultOptions.seriesLine),{},{name:"新增项目金额",type:"line",yAxisIndex:0,data:[]}),Object(l["a"])(Object(l["a"])({},this.chartDefaultOptions.seriesLine),{},{name:"新增项目数量",type:"line",yAxisIndex:1,data:[]})]};e.setOption(t,!0),e.on("click",(function(e){})),this.axisOption=t,this.chartObj=e}}},p=h,d=(a("e54a"),a("2877")),f=Object(d["a"])(p,s,i,!1,null,"62944bf3",null);t["default"]=f.exports},"8add":function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));var s=a("3835"),i=(a("99af"),a("caad"),a("c1df")),l=a.n(i);function o(e,t){var a=["year","lastYear","quarter","lastQuarter"].includes(t),i=e.split("-"),o=Object(s["a"])(i,2),n=o[0],r=o[1],c="";if(a){var u=l()("".concat(n,"-").concat(r,"-01")).endOf("month").format("YYYY-MM-DD");return[e,u]}return c="".concat(e," 23:59:59"),["".concat(e," 00:00:00"),c]}},"965d":function(e,t,a){"use strict";a("c558")},a3ca:function(e,t,a){},c558:function(e,t,a){},df55:function(e,t,a){"use strict";var s=a("5530"),i=(a("d3b7"),a("08c2")),l=a("7a1a"),o=a("ed08"),n=a("a347"),r=a.n(n);t["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},textColor:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{lineStyle:{color:r.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:r.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:i["a"]},props:{},computed:{},watch:{},mounted:function(){var e=this;this.debouncedResize=Object(l["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",e.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(e){this.pageData.limit=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(e){this.pageData.page=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(e,t){return new Promise((function(a,s){e(t).then((function(e){Object(o["g"])(e),a&&a(e)})).catch((function(e){s&&s(e)}))}))},getChartYAxisStyle:function(e){var t=Object(o["D"])(this.chartYAxisStyle);if(!e)return t;for(var a in e){var i=t[a],l=e[a];t[a]=i?Object(s["a"])(Object(s["a"])({},i),l):l}return t}},deactivated:function(){}}},e54a:function(e,t,a){"use strict";a("a3ca")},f2ec:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return o})),a.d(t,"d",(function(){return n}));var s=a("b775");function i(e){return Object(s["a"])({url:"biFunnel/addBusinessAnalyze",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(s["a"])({url:"biFunnel/win",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(e){return Object(s["a"])({url:"crmBiSearch/searchBusinessPageList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(e){return Object(s["a"])({url:"crmInstrument/queryContendBusinessList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}}}]);