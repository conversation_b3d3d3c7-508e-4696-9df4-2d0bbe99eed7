(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-755b342c"],{"40e7":function(e,t,a){},a4da:function(e,t,a){"use strict";a("40e7")},ea51:function(e,t,a){},f4ea:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("flexbox",{staticClass:"main",attrs:{direction:"column",align:"stretch"}},[a("xr-header",{attrs:{label:"企业首页"}}),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"body"},[a("flexbox",{attrs:{align:"stretch"}},[a("flexbox-item",[a("div",{staticClass:"section section-top-border"},[a("div",{staticClass:"section-title"},[e._v("快捷入口"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"18","data-id":"163"}})]),e._v(" "),a("div",{staticClass:"section-content"},e._l(e.quickList,(function(t,i){return a("flexbox",{key:i,staticClass:"quick-item",nativeOn:{click:function(a){e.jumpTo(t)}}},[a("img",{attrs:{src:t.img}}),e._v(" "),a("div",{staticClass:"quick-item-icon"},[a("i",{class:t.iconClass})]),e._v(" "),a("div",{staticClass:"quick-item-title"},[a("div",[e._v(e._s(t.title))]),e._v(" "),a("div",[e._v(e._s(t.desc))])])])})))])]),e._v(" "),a("flexbox-item",[a("div",{staticClass:"section section-top-border"},[a("div",{staticClass:"section-title"},[e._v("企业基本信息设置")]),e._v(" "),a("div",{staticClass:"section-content"},[a("el-form",{ref:"ruleForm",attrs:{model:e.companyForm,rules:e.companyRules,"label-width":"100px","label-position":"top"}},[a("el-form-item",{attrs:{prop:"companyDomain"}},[a("template",{slot:"label"},[e._v("公司名称"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"18","data-id":"159"}})]),e._v(" "),a("el-input",{attrs:{disabled:!e.systemSaveAuth,maxlength:50},model:{value:e.companyForm.companyName,callback:function(t){e.$set(e.companyForm,"companyName","string"===typeof t?t.trim():t)},expression:"companyForm.companyName"}})],2),e._v(" "),a("el-form-item",{attrs:{prop:""}},[a("template",{slot:"label"},[e._v("导航头企业LOGO"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"18","data-id":"160"}})]),e._v(" "),e.companyLogo?a("div",{staticClass:"upload-show"},[a("img",{directives:[{name:"src",rawName:"v-src",value:e.companyLogo,expression:"companyLogo"}]}),e._v(" "),e.systemSaveAuth?a("i",{staticClass:"el-icon-remove icon-delete",on:{click:e.deleteCompanyLogo}}):e._e()]):a("el-upload",{staticClass:"upload",attrs:{"show-file-list":!1,"http-request":e.fileUpload,drag:"",disabled:!e.systemSaveAuth,action:"http",accept:"image/*"}},[a("i",{staticClass:"el-icon-plus uploader-icon"})])],2),e._v(" "),a("el-form-item",{attrs:{prop:""}},[a("template",{slot:"label"},[e._v("登录页企业LOGO（长logo）"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"18","data-id":"161"}})]),e._v(" "),e.companyLoginLogo?a("div",{staticClass:"upload-show is-login"},[a("img",{directives:[{name:"src",rawName:"v-src",value:e.companyLoginLogo,expression:"companyLoginLogo"}]}),e._v(" "),e.systemSaveAuth?a("i",{staticClass:"el-icon-remove icon-delete",on:{click:e.deleteCompanyLoginLogo}}):e._e()]):a("el-upload",{staticClass:"upload is-login",attrs:{"show-file-list":!1,"http-request":e.loginFileUpload,disabled:!e.systemSaveAuth,drag:"",action:"http",accept:"image/*"}},[a("i",{staticClass:"el-icon-plus uploader-icon"})])],2)],1),e._v(" "),e.systemSaveAuth?a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.saveClick}},[e._v("保存")])],1):e._e()],1)]),e._v(" "),a("div",{staticClass:"section section-top-border"},[a("div",{staticClass:"section-title"},[e._v("最新发布"),a("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"18","data-id":"164"}})]),e._v(" "),a("div",{staticClass:"section-content is-message"},[a("div",{staticClass:"message-content"},[e._v(e._s(e.message))])])])])],1)],1),e._v(" "),a("edit-image",{key:e.uploadType,attrs:{"fixed-number":"login"===e.uploadType?[15,4]:[4,4],show:e.showEditImage,image:e.editImage,file:e.editFile,"preview-width":"login"===e.uploadType?"150px":"40px",title:"编辑企业logo","preview-height":"40px","preview-radius":"0",width:"550px","save-button-title":"确定"},on:{save:e.submiteImage,close:function(t){e.showEditImage=!1}}})],1)},o=[],s=a("53ca"),n=a("5530"),l=(a("14d9"),a("d3b7"),a("3ca3"),a("498a"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("dea6")),c=a("2934"),r=a("b775");function p(e){return Object(r["a"])({url:"gateway/queryUpgradeFile",method:"post",data:e})}var m=a("f87f"),d=a("f468"),u=a("2f62"),g=a("ed08"),h={name:"SystemConfig",components:{EditImage:m["a"],XrHeader:d["a"]},data:function(){return{loading:!1,showEditImage:!1,editImage:null,editFile:null,uploadType:"",companyLogo:"",companyLoginLogo:"",domainForm:{companyDomain:""},companyForm:{companyName:""},companyRules:{companyName:[{required:!0,message:"公司名称不能为空",trigger:"change"}]},domainRules:{companyDomain:[{required:!0,message:"站点名称不能为空",trigger:"change"},{pattern:g["f"],message:"域名格式不正确（必须由英文、数字、中横线组成，以英文字母或数字开头并且为3至30字符）"}]},quickList:[{title:"客户管理-自定义字段设置",desc:"维护字段和布局",iconClass:"wk wk-customer-line",path:"/manage/customer/custom-field"},{title:"审批流程管理",desc:" 配置审批节点及流程",iconClass:"wk wk-approve-line",path:"/manage/customer/examine"},{title:"部门和员工管理",desc:"配置公司员工与部门的组织架构",iconClass:"wk wk-icon-s-seas-line",path:"/manage/employee-dep"},{title:"角色权限管理-客户管理角色",desc:"配置不同角色不同员工的客户管理应用中权限",iconClass:"wk wk-icon-employees-line",path:"/manage/role-auth/role-auth/2/客户管理角色"}],message:""}},computed:Object(n["a"])(Object(n["a"])({},Object(u["b"])(["manage"])),{},{systemSaveAuth:function(){return this.manage&&this.manage.system&&this.manage.system.update}}),created:function(){this.getDetail(),this.getUpdateContent()},methods:{jumpTo:function(e){this.$router.push(e.path)},getUpdateContent:function(){var e=this;this.loading=!0,p().then((function(t){var a=t.data||{};e.message=a.content,e.loading=!1})).catch((function(){e.loading=!1}))},fileUpload:function(e){var t=new FileReader,a=this;t.onload=function(t){var i;i="object"===Object(s["a"])(t.target.result)?window.URL.createObjectURL(new Blob([t.target.result])):t.target.result,a.editImage=i,a.editFile=e.file,a.uploadType="workbench",a.showEditImage=!0},t.readAsDataURL(e.file)},loginFileUpload:function(e){var t=new FileReader,a=this;t.onload=function(t){var i;i="object"===Object(s["a"])(t.target.result)?window.URL.createObjectURL(new Blob([t.target.result])):t.target.result,a.editImage=i,a.editFile=e.file,a.uploadType="login",a.showEditImage=!0},t.readAsDataURL(e.file)},deleteCompanyLogo:function(){this.companyLogo=""},deleteCompanyLoginLogo:function(){this.companyLoginLogo=""},getDetail:function(){var e=this;this.loading=!0,this.$store.dispatch("SystemLogoAndName").then((function(t){e.loading=!1;var a=t.data||{};e.companyForm.companyName=a.companyName?a.companyName:"",e.companyLogo=a.companyLogo,e.companyLoginLogo=a.companyLoginLogo})).catch((function(){e.loading=!1}))},submiteImage:function(e){var t=this;this.loading=!0;var a={file:e.blob};"login"===this.uploadType&&(a.isPublic=1),Object(c["h"])(a).then((function(e){var a=e.data||{};"workbench"==t.uploadType?t.companyLogo=a.url:t.companyLoginLogo=a.url,t.loading=!1})).catch((function(){t.loading=!1}))},saveClick:function(){var e=this;this.companyForm.companyName&&this.companyForm.companyName.trim()?(this.loading=!0,Object(l["f"])({companyName:this.companyForm.companyName,companyLogo:this.companyLogo,companyLoginLogo:this.companyLoginLogo}).then((function(t){e.loading=!1,e.$message.success("操作成功"),e.getDetail()})).catch((function(){e.loading=!1}))):this.$message.error("企业名称不能为空")}}},v=h,f=(a("a4da"),a("2877")),y=Object(f["a"])(v,i,o,!1,null,"4ddab8fe",null);t["default"]=y.exports},f87f:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,width:e.width,"append-to-body":!0,"close-on-click-modal":!1,visible:e.showDialog},on:{"update:visible":function(t){e.showDialog=t},close:e.hiddenView}},[a("flexbox",{staticClass:"content"},[a("div",{staticClass:"cropper-box"},[a("vueCropper",{ref:"cropper",attrs:{"can-move":!0,"auto-crop":!0,fixed:!0,"fixed-number":e.fixedNumber,img:e.cropperImg,"output-type":"png"},on:{realTime:e.realTime}})],1),e._v(" "),a("div",{staticClass:"preview"},[a("div",{staticClass:"preview-name"},[e._v("预览")]),e._v(" "),a("img",{staticClass:"preview-img",style:{width:e.previewWidth,height:e.previewHeight,"border-radius":e.previewRadius},attrs:{src:e.previewImg}})])]),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.submiteImage()}}},[e._v(e._s(e.saveButtonTitle))])],1)],1)},o=[],s=a("7e79"),n={name:"EditImage",components:{VueCropper:s["VueCropper"]},props:{width:{type:String,default:"450px"},title:{type:String,default:"编辑头像"},saveButtonTitle:{type:String,default:"开始上传"},show:{type:Boolean,default:!1},fixedNumber:{type:Array,default:function(){return[1,1]}},previewWidth:{type:String,default:"70px"},previewHeight:{type:String,default:"70px"},previewRadius:{type:String,default:"35px"},file:[File],image:String},data:function(){return{loading:!1,showDialog:!1,cropperImg:"",previewImg:""}},computed:{},watch:{show:{handler:function(e){this.showDialog=e},deep:!0,immediate:!0},image:function(e){this.cropperImg=e}},mounted:function(){this.cropperImg=this.image},methods:{realTime:function(e){var t=this;this.$refs.cropper.getCropData((function(e){t.previewImg=e}))},submiteImage:function(){var e=this;this.$refs.cropper.getCropBlob((function(t){e.$emit("save",{blob:t,file:e.file,image:e.previewImg}),e.hiddenView()}))},hiddenView:function(){this.$emit("close")}}},l=n,c=(a("fcc2"),a("2877")),r=Object(c["a"])(l,i,o,!1,null,"3f7d9903",null);t["a"]=r.exports},fcc2:function(e,t,a){"use strict";a("ea51")}}]);