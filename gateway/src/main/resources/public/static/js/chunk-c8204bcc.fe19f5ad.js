(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c8204bcc"],{"0010":function(e,t,i){"use strict";i("47cd")},"0481":function(e,t,i){"use strict";var a=i("23e7"),s=i("a2bf"),n=i("7b0b"),l=i("07fa"),o=i("5926"),r=i("65f0");a({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,t=n(this),i=l(t),a=r(t,0);return a.length=s(a,t,t,i,0,void 0===e?1:o(e)),a}})},"0ac7":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjU0OTlCOUZDRERCMjExRTg4MjVEQ0RGOThFMUY2OTEyIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjU0OTlCOUZERERCMjExRTg4MjVEQ0RGOThFMUY2OTEyIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NTQ5OUI5RkFEREIyMTFFODgyNURDREY5OEUxRjY5MTIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NTQ5OUI5RkJEREIyMTFFODgyNURDREY5OEUxRjY5MTIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5iPiS9AAAAY0lEQVR42mI8c+aMLQMDw0IgVmQgHdwH4jgmIDEHiNOBmJEMDNI3lxHokv9QAXLBfyYGKoBRQzABC7bQJkIfIyFDGEdjZxAZcguIXcjUD9J3CxTFKUC8CIgVyDDkARDHAgQYANTlEN4TaBTMAAAAAElFTkSuQmCC"},"153e":function(e,t,i){},"1de7":function(e,t,i){},3628:function(e,t,i){"use strict";i("c23e")},"368a":function(e,t,i){"use strict";i("909f")},4058:function(e,t,i){"use strict";i("d9259")},4069:function(e,t,i){"use strict";var a=i("44d2");a("flat")},"412d":function(e,t,i){"use strict";i("b565")},"41d6":function(e,t,i){"use strict";i("ba9d")},"47cd":function(e,t,i){},55244:function(e,t,i){"use strict";i("153e")},"59c9":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"system-customer main"},[i("xr-header",{attrs:{label:"业务参数设置"}}),e._v(" "),i("div",{staticClass:"main-content-wrap"},[i("div",{staticClass:"main-nav"},[i("div",{staticClass:"main-nav__content"},[i("div",{staticClass:"nav-sections-wrap"},e._l(e.menuList,(function(t,a){return i("div",{key:a,staticClass:"menu-item",class:{"is-select":t.key==e.menuIndex},on:{click:function(i){e.menuSelect(t.key)}}},[e._v("\n            "+e._s(t.label)+"\n          ")])})))])]),e._v(" "),i("keep-alive",[i(e.componentName,{tag:"component",staticClass:"main-content",attrs:{types:e.types}})],1)],1)],1)},s=[],n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[i("div",{staticClass:"content-header"},[e._m(0),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1),e._v(" "),i("div",{staticClass:"content-body"},[e._l(e.list,(function(t,a){return i("div",{key:a,staticClass:"input-item"},[i("el-input",{attrs:{maxlength:100},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}}),e._v(" "),i("i",{staticClass:"el-icon-remove",on:{click:function(i){e.deleteItem(t,a)}}})],1)})),e._v(" "),i("el-button",{staticClass:"add-btn",attrs:{type:"primary-text"},on:{click:e.addItem}},[e._v("+添加类型")])],2)])},l=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("span",[e._v("跟进记录类型设置"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"216"}})])}],o=(i("d81d"),i("14d9"),i("a434"),i("e9f5"),i("ab43"),i("d3b7"),i("ea20")),r={name:"FollowLogTypeSet",components:{},data:function(){return{loading:!1,list:[]}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,Object(o["A"])().then((function(t){e.loading=!1,e.list=t.data.map((function(e){return{value:e}}))})).catch((function(){e.loading=!1}))},addItem:function(){this.list.push({value:""})},deleteItem:function(e,t){this.list.splice(t,1)},save:function(){for(var e=this,t=[],i=0;i<this.list.length;i++){var a=this.list[i];a.value&&t.push(a.value)}this.loading=!0,Object(o["z"])(t).then((function(t){e.loading=!1,e.getDetail(),e.$message.success("操作成功")})).catch((function(){e.loading=!1}))}}},c=r,d=(i("55244"),i("2877")),u=Object(d["a"])(c,n,l,!1,null,"426cab5f",null),f=u.exports,p=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[i("div",{staticClass:"content-header"},[e._m(0),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.addStage}},[e._v("添加阶段流程")])],1),e._v(" "),i("div",{staticClass:"stage-content"},[i("div",{staticClass:"stage-filter"},[i("el-select",{staticClass:"el-select",attrs:{clearable:"",placeholder:"选择状态"},on:{change:e.getStageList},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},e._l([{label:"启用",value:1},{label:"停用",value:2}],(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))),e._v(" "),i("el-select",{staticClass:"el-select",attrs:{clearable:"",placeholder:"选择关联对象"},on:{change:e.getStageList},model:{value:e.label,callback:function(t){e.label=t},expression:"label"}},e._l(e.crmModelList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1),e._v(" "),i("div",{staticClass:"customer-table"},[i("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.stageData,height:e.tableHeight,stripe:e.WKConfig.tableStyle.stripe}},[e._l(e.stageList,(function(t,a){return i("el-table-column",{key:a,attrs:{prop:t.field,label:t.label,formatter:e.fieldFormatter,"show-overflow-tooltip":""}})})),e._v(" "),i("el-table-column",{attrs:{fixed:"right",label:"操作",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.stageEdit(t.row,!1)}}},[e._v("编辑")]),e._v(" "),1!=t.row.status?i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.stageDelect(t)}}},[e._v("删除")]):e._e(),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.stageStatus(t)}}},[e._v(e._s(2===t.row["status"]?"启用":"停用"))]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.stageEdit(t.row,!0)}}},[e._v("复制并新建")])]}}])})],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),e._v(" "),i("stage-dialog",{attrs:{"info-data":e.stageObj,"stage-dialog-visible":e.stageDialogVisible,"stage-title":e.stageTitle,"crm-model-list":e.crmModelList},on:{"update-status":e.updateStageStatus,stageClose:e.stageClose,stageSubmit:e.stageSubmit}})],1)},m=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("span",[e._v("阶段流程设置"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"217"}})])}],h=(i("4de4"),i("a15b"),i("7d54"),i("a9e3"),i("b64b"),i("159b"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{ref:"wkDialog",attrs:{title:e.stageTitle,visible:e.stageDialogVisible,"before-close":e.stageClose,"close-on-click-modal":!1,width:"900px"},on:{"update:visible":function(t){e.stageDialogVisible=t}}},[i("create-sections",{attrs:{title:"基础信息"}},[i("el-form",{ref:"crmForm",staticClass:"wk-form",attrs:{model:e.fieldsForm,rules:e.fieldsRules,"validate-on-rule-change":!1,"label-position":"top"}},e._l(e.fields,(function(t,a){return i("wk-form-items",{key:a,attrs:{"field-from":e.fieldsForm,"field-list":t},on:{change:e.formChange},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.data;return[a&&"userDep"==a.formType?i("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":e.fieldsForm.userIdList,"dep-value":e.fieldsForm.deptIdList},on:{"update:userValue":function(t){e.$set(e.fieldsForm,"userIdList",t)},"update:depValue":function(t){e.$set(e.fieldsForm,"deptIdList",t)}}}):e._e()]}}])})})))],1),e._v(" "),i("create-sections",{attrs:{title:"阶段设置"}},[i("div",{staticClass:"stage-setting-content"},[i("div",{staticClass:"stage-setting-table-header"},[i("span",{staticClass:"drag-hook"}),e._v(" "),i("span",[e._v("阶段")]),e._v(" "),i("span",[e._v("阶段名称")]),e._v(" "),5==e.fieldsForm.label?i("span",{staticClass:"rate"},[e._v("\n          赢单率\n        ")]):e._e(),e._v(" "),i("span",[e._v("\n          表单"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"223"}})]),e._v(" "),i("span",[e._v("\n          阶段工作"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"224"}})]),e._v(" "),i("span",[e._v("\n          审批设置"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"225"}})]),e._v(" "),i("span",{staticClass:"icon-box"})]),e._v(" "),i("div",{staticClass:"stage-setting-table-body"},[i("draggable",{attrs:{options:{handle:".drag-hook"},list:e.settingList}},e._l(e.settingList,(function(t,a){return i("div",{key:a,staticClass:"stage-setting-table"},[i("span",{staticClass:"drag-hook"},[e._v("⋮⋮")]),e._v(" "),i("span",[e._v(e._s("阶段"+(a+1)))]),e._v(" "),i("span",[i("el-input",{attrs:{maxlength:15},model:{value:t.name,callback:function(i){e.$set(t,"name",i)},expression:"item.name"}})],1),e._v(" "),5==e.fieldsForm.label?i("span",{staticClass:"rate icon-span"},[i("el-input-number",{attrs:{controls:!1,min:0,max:100,"step-strictly":""},model:{value:t.rate,callback:function(i){e.$set(t,"rate",i)},expression:"item.rate"}}),e._v(" %\n            ")],1):e._e(),e._v(" "),i("span",[i("el-checkbox",{model:{value:t.openForm,callback:function(i){e.$set(t,"openForm",i)},expression:"item.openForm"}}),e._v(" "),i("el-button",{attrs:{type:"text"},on:{click:function(i){e.fieldsEdit(t,a)}}},[e._v("编辑表单")])],1),e._v(" "),i("span",[i("el-checkbox",{model:{value:t.openTask,callback:function(i){e.$set(t,"openTask",i)},expression:"item.openTask"}}),e._v(" "),i("el-button",{attrs:{type:"text"},on:{click:function(i){e.phaseEdit(t,a)}}},[e._v("编辑阶段工作")])],1),e._v(" "),i("span",[i("el-checkbox",{model:{value:t.openExamine,callback:function(i){e.$set(t,"openExamine",i)},expression:"item.openExamine"}}),e._v(" "),i("el-button",{attrs:{type:"text"},on:{click:function(i){e.examineEdit(t)}}},[e._v("配置审批")])],1),e._v(" "),i("span",{staticClass:"icon-box"},[i("span",{staticClass:"el-icon-circle-plus",on:{click:e.addIcon}}),e._v(" "),0!=a?i("span",{staticClass:"el-icon-remove",on:{click:function(t){e.removeIcon(a)}}}):e._e()])])}))),e._v(" "),5==e.fieldsForm.label?[i("div",{staticClass:"stage-setting-table"},[i("span",{staticClass:"drag-hook"}),e._v(" "),i("span",[e._v("结束")]),e._v(" "),i("span",[e._v("赢单")]),e._v(" "),i("span",{staticClass:"rate"},[e._v(e._s(e.winSingle)+" %")]),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span",{staticClass:"icon-box"})]),e._v(" "),i("div",{staticClass:"stage-setting-table"},[i("span",{staticClass:"drag-hook"}),e._v(" "),i("span",[e._v("结束")]),e._v(" "),i("span",[e._v("输单")]),e._v(" "),i("span",{staticClass:"rate"},[e._v(e._s(e.loseSingle)+" %")]),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span",{staticClass:"icon-box"})]),e._v(" "),i("div",{staticClass:"stage-setting-table"},[i("span",{staticClass:"drag-hook"}),e._v(" "),i("span",[e._v("结束")]),e._v(" "),i("span",[e._v("无效")]),e._v(" "),i("span",{staticClass:"rate"},[e._v(e._s(e.invalidSingle)+" %")]),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span",{staticClass:"icon-box"})])]:[i("div",{staticClass:"stage-setting-table"},[i("span",{staticClass:"drag-hook"}),e._v(" "),i("span",[e._v("结束-成功")]),e._v(" "),i("span",[i("el-input",{attrs:{maxlength:15},model:{value:e.successName,callback:function(t){e.successName=t},expression:"successName"}})],1),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span",{staticClass:"icon-box"})]),e._v(" "),i("div",{staticClass:"stage-setting-table"},[i("span",{staticClass:"drag-hook"}),e._v(" "),i("span",[e._v("结束-失败")]),e._v(" "),i("span",[i("el-input",{attrs:{maxlength:15},model:{value:e.failedName,callback:function(t){e.failedName=t},expression:"failedName"}})],1),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span"),e._v(" "),i("span",{staticClass:"icon-box"})])]],2)])]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.stageSubmit,expression:"stageSubmit"}],attrs:{type:"primary"}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.stageClose}},[e._v("取消")])],1),e._v(" "),e.phaseEditVisible?i("phase-dialog",{attrs:{visible:e.phaseEditVisible,"row-data":e.rowData,"row-index":e.rowIndex},on:{"update:visible":function(t){e.phaseEditVisible=t},change:e.phaseChange}}):e._e(),e._v(" "),e.fieldsEditVisible?i("fields-dialog",{attrs:{visible:e.fieldsEditVisible,"row-data":e.rowData,"row-index":e.rowIndex},on:{"update:visible":function(t){e.fieldsEditVisible=t},change:e.fieldsChange}}):e._e(),e._v(" "),e.examineEditVisible?i("examine-dialog",{attrs:{detail:e.rowData},on:{change:e.examineChange,close:function(t){e.examineEditVisible=!1}}}):e._e()],1)}),g=[],v=i("5530"),b=(i("99af"),i("7db0"),i("caad"),i("b0c0"),i("f665"),i("3ca3"),i("ddb0"),i("b592")),y=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{visible:e.visible,"before-close":e.handleClose,title:e.title,"append-to-body":"",width:"600px"},on:{"update:visible":function(t){e.visible=t}}},[i("div",{staticClass:"content"},[i("div",{staticClass:"list"},e._l(e.taskList,(function(t,a){return i("flexbox",{key:a,staticClass:"list-item",attrs:{justify:"space-between"}},[i("el-input",{attrs:{maxlength:50},model:{value:t.taskName,callback:function(i){e.$set(t,"taskName",i)},expression:"item.taskName"}},[i("template",{slot:"prepend"},[e._v(e._s("任务"+(a+1)))])],2),e._v(" "),i("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.isNull,callback:function(i){e.$set(t,"isNull",i)},expression:"item.isNull"}},[e._v("是否必做")]),e._v(" "),i("el-button",{attrs:{type:"text"},on:{click:function(t){e.delTask(a)}}},[e._v("删除")])],1)}))),e._v(" "),i("el-button",{attrs:{type:"text"},on:{click:e.addTask}},[i("i",{staticClass:"wk wk-l-plus"}),e._v(" "),i("span",[e._v("添加")])])],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.handleClose}},[e._v("取消")])],1)])},_=[],x=i("ed08"),w={name:"PhaseDialog",props:{visible:{type:Boolean,default:!1},rowData:{type:Object,default:function(){return{}}},rowIndex:Number},data:function(){return{taskList:[]}},computed:{title:function(){return"配置 阶段".concat(this.rowIndex+1," ").concat(this.rowData.name," 阶段工作")}},watch:{rowData:{handler:function(e){this.taskList=Object(x["D"])(e.taskList)},immediate:!0,deep:!0}},methods:{save:function(){this.$emit("change",this.taskList)},handleClose:function(){this.$emit("update:visible",!1)},addTask:function(){this.taskList.push({taskName:"",isNull:0})},delTask:function(e){this.taskList.splice(e,1)}}},k=w,C=(i("66b0"),Object(d["a"])(k,y,_,!1,null,"46f06915",null)),I=C.exports,L=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,"before-close":e.handleClose,"close-on-click-modal":!1,"close-on-press-escape":!1,title:e.title,"custom-class":"no-padding-dialog fields-dialog","append-to-body":"",fullscreen:"","lock-scroll":""},on:{"update:visible":function(t){e.visible=t}}},[a("flexbox",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fields-index body",attrs:{align:"flex-start",justify:"flex-start"}},[a("flexbox-item",{staticClass:"body-left"},[a("div",{staticClass:"body-left_title"},[e._v("字段库")]),e._v(" "),a("ul",[a("draggable",{staticClass:"lib-wrapper",attrs:{list:e.fieldLibList,options:e.dragLeftConfig,clone:e.dragLeftMove},on:{end:e.dragLeftEnd}},e._l(e.fieldLibList,(function(t){return a("div",{key:t.id,staticClass:"lib-item",on:{click:function(i){e.handleLibFieldClick(t)}}},[a("i",{staticClass:"lib-item-icon",class:t.icon}),e._v(" "),a("span",[e._v(e._s(t.name))])])})))],1)]),e._v(" "),a("div",{staticClass:"body-content"},[a("flexbox",{staticClass:"body-content-warp",attrs:{align:"flex-start",justify:"flex-start",direction:"column"}},[a("el-header",[a("div",{staticClass:"title"},[e._v("编辑字段")]),e._v(" "),a("div",[a("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleSave,expression:"handleSave"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),a("el-button",{on:{click:e.handleCancel}},[e._v("返回")])],1)]),e._v(" "),a("flexbox-item",{staticClass:"body-content-main",staticStyle:{"margin-left":"0"}},[a("el-main",[a("draggable",{staticClass:"field-list",attrs:{list:e.fieldArr,options:e.dragListConfig},on:{end:e.dragListEnd}},e._l(e.fieldArr,(function(t,i){return a("flexbox",{key:i,staticClass:"field-row",attrs:{align:"flex-start",justify:"flex-start"}},e._l(t,(function(t,s){return a(e._f("typeToComponentName")(t),{key:s,ref:"fieldItem",refInFor:!0,tag:"component",attrs:{field:t,"field-arr":e.fieldArr,point:[i,s],"active-point":e.selectedPoint},on:{action:e.handleAction,"child-drag-add":e.handleChildDragAdd,click:function(t){e.handleSelect([i,s])}}})})))}))),e._v(" "),e.fieldArr&&0!==e.fieldArr.length?e._e():a("el-empty",{attrs:{image:i("0437"),description:"拖拽或点击左侧字段创建表单"}})],1)],1)],1)],1),e._v(" "),a("flexbox-item",{staticClass:"body-right",staticStyle:{"margin-left":"0"}},[e.selectedField?a("setting-field",{attrs:{field:e.selectedField,point:e.selectedPoint,"field-arr":e.fieldArr,"can-transform":e.canTransform,"transform-data":e.transformData,"show-logic":!1},on:{"child-edit":e.handleChildEdit,"update-width":e.handleUpdateFieldWidth}}):e._e()],1)],1)],1)},S=[],E=(i("a630"),i("910d"),i("25f0"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("2532"),i("4d90"),i("498a"),i("e170")),D=i("6bfe"),A=i("60c3"),F=i("2c81"),T=i("b76a"),N=i.n(T),O=i("4f45"),j=i("5240"),$=i("6683"),P=i("6d94"),B={name:"FieldsDialog",components:{FieldInput:A["i"],FieldTextarea:A["p"],FieldSelect:A["m"],FieldCheckbox:A["c"],FieldFile:A["g"],FieldBoolean:A["b"],FieldPercent:A["k"],FieldPosition:A["l"],FieldLocation:A["j"],FieldDetailTable:A["f"],FieldWritingSign:A["q"],FieldDateInterval:A["d"],FieldDescText:A["e"],FieldSerialNumber:A["n"],FieldTag:A["o"],FieldAttention:A["a"],FieldGroup:A["h"],SettingField:F["a"],draggable:N.a},filters:{typeToComponentName:function(e){return Object(P["b"])(e)}},props:{visible:{type:Boolean,default:!1},rowData:{type:Object,default:function(){return{}}},rowIndex:Number},data:function(){return{loading:!1,fieldLibList:[],dragLeftConfig:{group:{pull:"clone",put:!1,name:"libList"},forceFallback:!0,sort:!1},movedField:null,dragListConfig:{delay:100,group:{name:"list",put:["libList"],pull:!0},forceFallback:!0,fallbackClass:"draggingStyle"},fieldArr:[],rejectHandle:!0,isChildDragAdd:!1,selectedPoint:[null,null],selectedField:null,transformData:null}},computed:{title:function(){return"配置 阶段".concat(this.rowIndex+1," ").concat(this.rowData.name," 表单")},canTransform:function(){var e=this.selectedPoint[0],t=this.selectedPoint[1];if(!Object(D["b"])(e)&&!Object(D["b"])(t)){var i=this.fieldArr[e][t];if("detail_table"===i.formType)return!1}return!1}},created:function(){this.initCom(),this.canTransform&&this.getTransformField()},methods:{initCom:function(){this.fieldLibList=j["a"].filter((function(e){return!["pic","detail_table","field_group","field_attention","serial_number","field_tag","handwriting_sign"].includes(e.formType)})),this.getFieldList()},getFieldList:function(){this.loading=!0,this.fieldArr=this.rowData.formList||[];var e=[];this.fieldArr.forEach((function(t){t.forEach((function(t){if(t){var i=t.operating.toString(2).padStart(8,"0");if("1"==i.charAt(5)){var a=i.split("");a[5]="0",t.operating=a.join("")}}e.push(t)}))})),this.fieldArr.length>0&&this.handleSelect([0,0]),this.rejectHandle=!1,this.loading=!1},handleLibFieldClick:function(e){this.movedField=e,this.dragLeftEnd()},dragLeftMove:function(e){this.movedField=e},dragLeftEnd:function(e){var t=this;if(!this.rejectHandle){if("field_tag"==this.movedField.formType){var i=!0;if(this.fieldArr.forEach((function(e){e.forEach((function(e){e.formType==t.movedField.formType&&(i=!1)}))})),!i)return void this.$message.error("只允许添加一个自定义标签字段")}var a=new O["a"]({name:this.movedField.name,formType:this.movedField.formType});switch(a.stylePercent=50,a.operating=235,this.movedField.formType){case"desc_text":a.name="";break;case"select":case"checkbox":a.options="选1,选2,选3",a.setting=["选1","选2","选3"];break;case"detail_table":a.operating=232,a.fieldExtendList=[],a.defaultValue=null,a.remark="添加".concat(a.name);break;case"serial_number":a.operating=245,a.isUnique=1,a.setting=[{type:1,resetType:4,startNumber:void 0,stepNumber:void 0}];break;case"field_tag":a.operating=249;break;case"field_attention":a.operating=250,a.defaultValue=null;break;case"field_group":a.operating=224;break}if(delete a.fieldId,this.isChildDragAdd)return["detail_table","desc_text","handwriting_sign","pic","serial_number","field_tag","field_attention","field_group"].includes(a.formType)?void this.$message.error("此字段内部不能添加该类型的字段"):void this.childDragAddEnd(a,e);var s=null;s=e&&"clone"===e.pullMode&&!Object(D["b"])(e.newIndex)?e.newIndex:0===this.fieldArr.length?0:this.selectedPoint[0]+1,this.fieldArr.splice(s,0,[a]),this.handleSelect([s,0])}},handleChildDragAdd:function(e,t){this.selectedPoint=e,this.isChildDragAdd=!0},childDragAddEnd:function(e,t){e.stylePercent=50,e.operating=171;var i=j["a"].find((function(t){return e.formType===t.formType}));i&&(e.type=i.type);var a=this.selectedPoint[0],s=this.selectedPoint[1],n=this.fieldArr[a][s];Object(D["b"])(n.fieldExtendList)&&(n.fieldExtendList=[]),e.fieldName=this.generateFieldName(n.fieldExtendList),n.fieldExtendList.push(e),this.$set(this.fieldArr,a,this.fieldArr[a]),this.handleSelect(this.selectedPoint,e),this.isChildDragAdd=!1},dragListEnd:function(e){this.selectedPoint.splice(0,1,e.newIndex)},handleAction:function(e,t){switch(e){case"top":this.handleActionMoveTop(t);break;case"bottom":this.handleActionMoveBottom(t);break;case"left":this.handleActionExchange(t,-1);break;case"right":this.handleActionExchange(t,1);break;case"copy":this.handleActionCopy(t);break;case"delete":this.handleDelete(t)}},handleActionMoveTop:function(e){var t=this.fieldArr[e[0]-1];if(t&&!(t.length>=2)){var i=this.fieldArr[e[0]][e[1]],a=t[0];if("detail_table"===i.formType||"detail_table"===a.formType||"field_group"===i.formType||"field_group"===a.formType){var s=[this.fieldArr[e[0]],this.fieldArr[e[0]-1]];this.fieldArr[e[0]-1]=s[0],this.fieldArr[e[0]]=s[1],this.handleSelect([e[0]-1,0])}else{t.push(Object(x["D"])(i));var n=this.getWidth(t.length);t.forEach((function(e){e.stylePercent=n.stylePercent})),this.$set(this.fieldArr,e[0]-1,t);var l=this.fieldArr[e[0]];l.splice(e[1],1),0===l.length?this.fieldArr.splice(e[0],1):(n=this.getWidth(l.length),l.forEach((function(e){e.stylePercent=n.stylePercent})),this.$set(this.fieldArr,e[0],l)),this.handleSelect([e[0]-1,t.length-1])}}},handleActionMoveBottom:function(e){var t=this.fieldArr[e[0]][e[1]],i=this.fieldArr[e[0]+1][0],a=this.fieldArr[e[0]];if("detail_table"===t.formType||"detail_table"===i.formType||"field_group"===t.formType||"field_group"===i.formType||1===a.length){var s=[this.fieldArr[e[0]],this.fieldArr[e[0]+1]];this.fieldArr[e[0]+1]=s[0],this.fieldArr[e[0]]=s[1],this.handleSelect([e[0]+1,0])}else{t.stylePercent=50,this.fieldArr.splice(e[0]+1,0,[t]),this.fieldArr[e[0]].splice(e[1],1);var n=this.getWidth(a.length);a.forEach((function(e){e.stylePercent=n.stylePercent})),this.$set(this.fieldArr,e[0],a),this.handleSelect([e[0]+1,0])}},handleActionExchange:function(e,t){var i=this.fieldArr[e[0]],a=this.fieldArr[e[0]][e[1]];i.splice(e[1],1),i.splice(e[1]+t,0,a),this.handleSelect([e[0],e[1]+t])},handleActionCopy:function(e){var t=this.fieldArr[e[0]][e[1]],i=Object(x["D"])(t);if(delete i.fieldId,delete i.fieldName,delete i.relevant,i.fieldType=0,i.operating=239,"pic"===i.formType&&(i.operating=235),"desc_text"===i.formType&&(i.name=""),"serial_number"===i.formType&&(i.operating=245),"field_tag"==i.formType){var a=!0;if(this.fieldArr.forEach((function(e){e.forEach((function(e){e.formType==i.formType&&(a=!1)}))})),!a)return void this.$message.error("只允许添加一个自定义标签字段")}"field_tag"===i.formType&&(i.operating=249),"field_attention"===i.formType&&(i.operating=250,i.defaultValue=null),"field_group"===i.formType&&(i.operating=224),this.fieldArr.splice(e[0]+1,0,[i]),this.handleSelect([e[0]+1,e[1]])},handleUpdateFieldWidth:function(){for(var e=this.fieldArr[this.selectedPoint[0]],t=[],i=[],a=0,s=0;s<e.length;s++){var n=e[s];a+=n.stylePercent,a<100?i.push(n):a>100?(t.push(Object(x["D"])(i)),i=[],i.push(n),a=n.stylePercent):(i.push(n),t.push(Object(x["D"])(i)),i=[],a=0)}if(i.length>0&&t.push(i),t.length>1){var l,o=this.selectedPoint[0],r=this.selectedPoint[1];(l=this.fieldArr).splice.apply(l,[o,1].concat(t));for(var c=0,d=0;d<t.length;d++)if(c+=t[d].length,c>=r+1){o+=d,r=r-c+t[d].length;break}this.handleSelect([o,r])}},getWidth:function(e){return 1===e||2===e?{stylePercent:50}:e>2?{stylePercent:25}:void 0},handleDelete:function(e){var t=this;this.$confirm("确定删除该自定义字段吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.selectedPoint=[null,null],t.selectedField=null,t.fieldArr[e[0]].splice([e[1]],1),0===t.fieldArr[e[0]].length&&t.fieldArr.splice(e[0],1)})).catch((function(){}))},handleSelect:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.selectedPoint=e,this.selectedField=t||this.fieldArr[e[0]][e[1]]},handleChildEdit:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e?this.selectedField=e:this.handleSelect(this.selectedPoint)},generateFieldName:function(e){var t=e.map((function(e){return e.fieldName})),i=function(e){for(var a="abcdefghijklmnopqrstuvwxyz",s="",n=0;n<e;n++){var l=Math.ceil(25*Math.random());s+=a[l]}var o="field_"+s;return t.includes(o)?i(e):o};return i(6)},handleSave:function(){var e=this;if(!this.rejectHandle){var t=[];this.loading=!0,Object(x["D"])(this.fieldArr).forEach((function(e,i){e.forEach((function(e,a){t.push(Object(v["a"])(Object(v["a"])({},e),{},{formPosition:"".concat(i,",").concat(a)}))}))}));for(var i,a=t.map((function(e){return e.formAssistId})),s="select|update|union|and|or|delete|insert|trancate|char|substr|ascii|declare|exec|count|master|into|drop|execute".split("|"),n=[],l=function(){var i=t[o],l=i.formPosition.split(","),r="第".concat(Number(l[0])+1,"行第").concat(Number(l[1])+1,"列");if(i.name=(i.name||"").trim(),"desc_text"!==i.formType){if(!i.name)return e.$message.error(r+"自定义字段，标识名不能为空"),e.loading=!1,{v:void 0};if(s.includes(i.name))return e.$message.error(r+"“".concat(i.name,"”字段，标识名与系统字段重复，请使用其他字段！")),e.loading=!1,{v:void 0};if(n.includes(i.name))return e.$message.error(r+"“".concat(i.name,"”字段，标识名重复")),e.loading=!1,{v:void 0};if(1===i.isNull&&1===i.isHidden)return e.$message.error(r+"“".concat(i.name,"”字段，不能同时设置必填和隐藏")),e.loading=!1,{v:void 0};if("detail_table"===i.formType){if(Object(D["b"])(i.fieldExtendList))return e.$message.error(r+"“".concat(i.name,"”字段，不能为空")),e.loading=!1,{v:void 0};for(var c=0;c<i.fieldExtendList.length;c++){var d=i.fieldExtendList[c];if(delete d.companyId,delete d.id,Object(D["b"])(d.defaultValue)&&(d.defaultValue=null),d.name=(d.name||"").trim(),!d.name)return e.$message.error(r+"“".concat(d.name,"”字段，标识名不能为空")),e.loading=!1,{v:void 0};if(s.includes(d.name))return e.$message.error(r+"“".concat(d.name,"”字段，标识名与系统字段重复，请使用其他字段！")),e.loading=!1,{v:void 0}}var u=i.fieldExtendList.map((function(e){return e.name}));if(u.length!==Array.from(new Set(u)).length)return e.$message.error(r+"“".concat(i.name,"”字段，标识名重复")),e.loading=!1,{v:void 0}}n.push(i.name)}else if(!Object(D["b"])(i.defaultValue)&&i.defaultValue.length>2e3)return e.$message.error(r+"“".concat(i.name,"”字段，描述文字类型字段最多设置2000字")),e.loading=!1,{v:void 0};if(!i.type){var f=j["a"].find((function(e){return e.formType===i.formType}));f&&(i.type=f.type)}"serial_number"==i.formType&&i.setting.forEach((function(e){3==e.type&&(-100===e.value?e.value="createTime":a.includes(e.value)?t.forEach((function(t){t.formAssistId==e.value&&(e.value=t.name)})):e.value="")})),i.hasOwnProperty("optionsData")&&delete i.optionsData},o=0;o<t.length;o++)if(i=l(),i)return i.v;var r=this.fieldArr;this.$emit("change",r),this.loading=!1}},getTransformField:function(){var e=this;Object(E["F"])({label:$["a"]["customer"],type:1}).then((function(t){for(var i={text:[],textarea:[],select:[],checkbox:[],number:[],floatnumber:[],mobile:[],email:[],date:[],datetime:[],user:[],structure:[],boolean_value:[],percent:[],position:[],location:[],handwriting_sign:[],date_interval:[]},a=0;a<t.data.length;a++){var s=t.data[a],n=i[s.formType];n&&n.push({label:s.name,value:s.fieldId})}e.transformData=i})).catch((function(){}))},handleCancel:function(){this.$emit("update:visible",!1)},handleClose:function(){this.$emit("update:visible",!1)}}},R=B,V=(i("412d"),Object(d["a"])(R,L,S,!1,null,"53a8b840",null)),M=V.exports,z=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("transition",{attrs:{name:"opacity-fade"}},[i("div",{staticClass:"business-approve-flow-create"},[i("wk-backgroud-tabs",{attrs:{options:e.tabs},model:{value:e.tabIndex,callback:function(t){e.tabIndex=t},expression:"tabIndex"}},[i("template",{slot:"right"},[i("el-button",{attrs:{type:"primary"},on:{click:e.sendClick}},[e._v("保存")]),e._v(" "),i("i",{staticClass:"el-icon-close create-close",on:{click:e.closeClick}})],1)],2),e._v(" "),i("base-info-set",{directives:[{name:"show",rawName:"v-show",value:"base"===e.tabIndex,expression:"tabIndex === 'base'"}],ref:"baseInfoSet",attrs:{fields:e.fields,"fields-form":e.fieldsForm,"fields-rules":e.fieldsRules},on:{change:e.formChange}}),e._v(" "),i("wk-approve-flow",{directives:[{name:"show",rawName:"v-show",value:"flow"===e.tabIndex,expression:"tabIndex === 'flow'"}],ref:"wkApproveFlow",attrs:{props:e.approveFlowConfig,list:e.flowList,"send-node":e.sendNode}})],1)])},U=[],H=(i("0481"),i("4069"),i("4378")),G=i("0904"),Y=i("43b6"),Z=i("07307"),W=i("3817"),Q={name:"ExamineDialog",components:{WkBackgroudTabs:H["a"],BaseInfoSet:G["a"],WkApproveFlow:Y["a"]},filters:{},mixins:[Z["a"],W["a"]],props:{detail:Object,moduleType:String},data:function(){return{loading:!1,height:document.documentElement.clientHeight-100,tabs:[{label:"1.配置基础信息",value:"base"},{label:"2.配置流程",value:"flow"}],tabIndex:"base",sendNode:{name:"发起人",content:"具有新建权限的员工"},fields:[],fieldsForm:{},fieldsRules:{},flowList:[],conditionFields:null}},computed:{approveFlowConfig:function(){var e=(this.detail.formList||[]).flat().filter((function(e){return e.isNull&&["select","checkbox","number","floatnumber"].includes(e.formType)})).map((function(e){var t=j["a"].find((function(t){return t.formType===e.formType}))||{};return Object(v["a"])({type:t.type},e)}));return{conditionSelectList:e}}},created:function(){this.getBaseField(),this.detail.examineSaveBO&&Object.keys(this.detail.examineSaveBO).length>0&&this.detail.examineSaveBO.dataList&&this.detail.examineSaveBO.dataList.length>0?this.getFlowList():this.flowList=[Object(x["D"])(Y["b"])]},mounted:function(){var e=this;this.$el.style.zIndex=Object(x["w"])(),document.body.appendChild(this.$el),window.onresize=function(){e.height=document.documentElement.clientHeight-100}},destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},methods:{getFlowList:function(){var e=this.detail.examineSaveBO.dataList||[],t=[];this.getListInfo(e,t),this.flowList=t},validateSetting:function(e){this.requestConditionFields().then((function(t){var i=t.find((function(t){return t.fieldName===e.fieldName}));e.setting=i?i.setting:[]}))},requestConditionFields:function(){var e=this,t=this.approveFlowConfig,i=t.conditionSelectRequest,a=t.conditionSelectParams,s=t.conditionSelectList;return s?Promise.resolve(s):this.conditionFields?Promise.resolve(this.conditionFields):new Promise((function(t,s){i(a).then((function(i){var a=i.data||[];e.conditionFields=a,t(a)})).catch((function(){t([])}))}))},getBaseField:function(){var e=[];e.push({field:"recheckType",formType:"select",isNull:0,name:"审批被拒后重新提交",setting:[{name:"返回审批流初始层级",value:1},{name:"跳过审批流已通过的层级，返回拒绝的层级",value:2}],inputTips:"",value:this.detail.examineSaveBO?this.detail.examineSaveBO.recheckType:1}),e.push({field:"managerList",formType:"user",isNull:1,name:"审批流管理员",setting:[],radio:!1,tipType:"tooltip",inputTips:'<div>1、可以在"配置流程"设置当审批人为空，审批<br>自动转交给审批流管理员；当管理员也请假/离<br>职，审批将转交给超级管理员。</div><div>2、可指定多个管理员，审批方式为或签。</div>',value:this.detail.examineSaveBO?this.detail.examineSaveBO.managerList:1}),e.push({field:"remarks",formType:"textarea",isNull:0,name:"流程说明",maxlength:200,setting:[],inputTips:"请填写相关注意事项，方便员工在申请时查阅，限制输入200字",value:this.detail.examineSaveBO?this.detail.examineSaveBO.remarks:""}),this.handleFields(e)},handleFields:function(e){var t=this,i={},a={};e.forEach((function(e){"userDep"===e.formType?(a.userList=[],a.deptList=[]):(i[e.field]=t.getRules(e),a[e.field]=e.value)})),this.fields=Object(x["D"])(e),this.fieldsForm=a,this.fieldsRules=i},formChange:function(e,t,i){"label"===e.field&&(this.flowList=[Object(x["D"])(Y["b"])])},sendClick:function(){var e=this;this.$refs.baseInfoSet.validate().then((function(t){if(t){var i=e.$refs.wkApproveFlow.getParams();if(i.isError)e.$message.error("请完善信息");else{var a=Object(v["a"])(Object(v["a"])({},e.fieldsForm),{},{dataList:i.list});e.$emit("change",a)}}}))},closeClick:function(){this.$emit("close")}}},J=Q,X=(i("368a"),Object(d["a"])(J,z,U,!1,null,"48e0e604",null)),K=X.exports,q=i("10ff"),ee=i("215e"),te=i("9dba"),ie={name:"StageDialog",components:{WkUserDepDialogSelect:b["a"],PhaseDialog:I,FieldsDialog:M,ExamineDialog:K,CreateSections:q["a"],WkFormItems:ee["default"],Draggable:N.a},mixins:[te["a"]],props:{stageDialogVisible:Boolean,stageTitle:String,infoData:{type:Object,default:function(){return{}}},crmModelList:{type:Array,default:function(){return[]}}},data:function(){return{loading:!1,fields:[[{field:"flowName",formType:"text",isNull:1,name:"阶段流程名称",stylePercent:50,setting:[]},{field:"stageUserDep",formType:"userDep",isNull:0,name:"适用范围",setting:[],inputTips:"默认全公司",stylePercent:50}],[{field:"label",formType:"select",isNull:1,name:"关联对象",stylePercent:50,setting:this.crmModelList,helpType:"24",helpId:"205"}]],fieldsForm:{flowName:"",userIdList:[],deptIdList:[],label:""},fieldsRules:{flowName:[{required:!0,message:"请输入阶段流程名称",trigger:["blur","change"]}],label:[{required:!0,message:"请选择关联对象",trigger:["blur","change"]}]},settingList:[],successName:"",failedName:"",winSingle:100,loseSingle:0,invalidSingle:0,phaseEditVisible:!1,fieldsEditVisible:!1,examineEditVisible:!1,rowData:{},rowIndex:""}},computed:{},watch:{infoData:function(e){var t=this;"edit"==e.type&&(this.fieldsForm.flowName=e.flowName||"",this.fieldsForm.label=e.label||"",this.fieldsForm.userIdList=e.userIdList||[],this.fieldsForm.deptIdList=e.deptIdList||[]),this.successName=e.successName||"",this.failedName=e.failedName||"",this.settingList=e.settingList||[],this.settingList.forEach((function(e){t.$set(e,"openForm",e.formList&&e.formList.length>0),t.$set(e,"openExamine",e.examineSaveBO&&Object.keys(e.examineSaveBO).length>0),t.$set(e,"openTask",e.taskList&&e.taskList.length>0)})),0==this.settingList.length&&(this.settingList=[this.getSetItem()])}},mounted:function(){},methods:{fieldsChange:function(e){this.$set(this.rowData,"formList",e),this.$set(this.rowData,"openForm",e.length>0),this.fieldsEditVisible=!1},examineChange:function(e){this.$set(this.rowData,"examineSaveBO",e),this.$set(this.rowData,"openExamine",!0),this.examineEditVisible=!1},phaseChange:function(e){this.$set(this.rowData,"taskList",e),this.$set(this.rowData,"openTask",e.length>0),this.phaseEditVisible=!1},phaseEdit:function(e,t){this.rowData=e,this.rowIndex=t,this.phaseEditVisible=!0},fieldsEdit:function(e,t){this.rowData=e,this.rowIndex=t,this.fieldsEditVisible=!0},examineEdit:function(e){this.rowData=e,this.examineEditVisible=!0},stageClose:function(){this.resetFields(),this.$emit("stageClose")},stageSubmit:function(){var e=this;this.$refs.crmForm.validate((function(t){if(t){for(var i=0;i<e.settingList.length;i++){var a=e.settingList[i];if(Object(D["b"])(a.name))return void e.$message.error("请输入阶段".concat(i+1,"阶段名称"));if(5==e.fieldsForm.label&&Object(D["b"])(a.rate))return void e.$message.error("请输入阶段".concat(i+1,"成功率"));if(a.openForm&&0==a.formList.length)return void e.$message.error("请设置阶段".concat(i+1,"表单字段"));if(a.openTask&&0==a.taskList.length)return void e.$message.error("请设置阶段".concat(i+1,"阶段工作"));if(a.openExamine&&(!a.examineSaveBO||0==Object.keys(a.examineSaveBO).length))return void e.$message.error("请设置阶段".concat(i+1,"审批"))}if(5!=e.fieldsForm.label&&(!e.successName||!e.failedName))return void e.$message.error("请设置结束阶段名称");var s=Object(x["D"])(e.settingList);s.forEach((function(e,t){if(e.openForm||(e.formList=[]),e.openTask||(e.taskList=[]),e.openExamine){var i=function(e){Object(D["c"])(e)?(e.userList&&(e.userList=e.userList.map((function(e){return Object(D["c"])(e)?e.userId:e}))),e.deptList&&(e.deptList=e.deptList.map((function(e){return Object(D["c"])(e)?e.deptId:e}))),Object.keys(e).forEach((function(t){["userList","deptList"].includes(t)||i(e[t])}))):Object(D["a"])(e)&&e.forEach((function(e){i(e)}))};i(e.examineSaveBO)}else e.examineSaveBO=null;delete e.openForm,delete e.openExamine,delete e.openTask,e.orderNum=t;var a=[];e.formList&&e.formList.forEach((function(e,t){e.forEach((function(e,i){a.push(Object(v["a"])(Object(v["a"])({},e),{},{formPosition:"".concat(t,",").concat(i)}))}))})),a.forEach((function(e){if(e.hasOwnProperty("optionsData")&&delete e.optionsData,!e.type){var t=j["a"].find((function(t){return t.formType===e.formType}));t&&(e.type=t.type)}})),e.formList=a}));var n={flowName:e.fieldsForm.flowName,label:e.fieldsForm.label,userIdList:e.fieldsForm.userIdList,deptIdList:e.fieldsForm.deptIdList,successName:e.successName,failedName:e.failedName,settingList:s},l={label:e.fieldsForm.label};"编辑阶段流程"==e.stageTitle?(n.flowId=e.infoData.flowId,l.flowId=e.infoData.flowId,e.$confirm("阶段编辑之后，只对新数据生效，不更新已有数据的阶段信息。且已有数据的阶段信息也不会参与项目阶段统计，是否继续？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveRequest(n,l)})).catch((function(){}))):e.saveRequest(n,l)}}))},saveRequest:function(e,t){var i=this;this.loading=!0,Promise.all([Object(o["k"])(e),Object(o["i"])(t)]).then((function(e){var t=!!e[1].data;if(i.infoData.flowId&&2===i.infoData.status&&!t)return i.$confirm("保存成功, 是否启用此阶段流程？","提示",{confirmButtonText:"启用",cancelButtonText:"取消",type:"success"}).then((function(){i.resetFields(),i.$emit("update-status",i.infoData)})).catch((function(){i.resetFields(),i.$emit("stageSubmit")})),void(i.loading=!1);t&&5!==i.fieldsForm.label?(i.$confirm("保存成功, 因当前有已启用的阶段流程【".concat(e[1].data,"】此阶段流程默认停用。"),"提示",{confirmButtonText:"确定",showCancelButton:!1,type:"success"}).then((function(){i.resetFields(),i.$emit("stageSubmit")})).catch((function(){i.resetFields(),i.$emit("stageSubmit")})),i.loading=!1):(i.$message.success("操作成功"),i.resetFields(),i.$emit("stageSubmit"),i.loading=!1)})).catch((function(){i.loading=!1}))},addIcon:function(){this.settingList.push(this.getSetItem())},getSetItem:function(){return{name:"",rate:"",openForm:!1,openExamine:!1,openTask:!1,taskList:[],formList:[],examineSaveBO:null}},removeIcon:function(e){this.settingList.splice(e,1)},formChange:function(e,t,i,a){},resetFields:function(){this.fieldsForm={flowName:"",userIdList:[],deptIdList:[],label:""},this.$refs["crmForm"].resetFields()}}},ae=ie,se=(i("0010"),Object(d["a"])(ae,h,g,!1,null,"2ceba815",null)),ne=se.exports,le={name:"StageFlowSet",components:{StageDialog:ne},data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-344,stageObj:{type:"save",flowName:"",label:"",userIdList:[],deptIdList:[],settingList:""},stageData:[],stageList:[{label:"阶段流程名称",field:"flowName"},{label:"关联对象",field:"label"},{label:"适用范围",field:"stageUserDep"},{label:"最后修改时间",field:"updateTime"},{label:"最后修改人",field:"updateUserName"},{label:"创建时间",field:"createTime"},{label:"创建人",field:"createUserName"},{label:"状态",field:"status"}],stageDialogVisible:!1,stageTitle:"添加阶段流程",currentPage:1,pageSize:10,pageSizes:[10,20,30,40],total:0,status:"",label:""}},computed:{crmModelList:function(){var e=$["a"].typeToNameData,t=[];return Object.keys(e).forEach((function(i){t.push({label:e[i],value:Number(i)})})),t.filter((function(e){return"公海"!==e.label}))}},created:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-344},this.getStageList()},methods:{handleSizeChange:function(e){this.pageSize=e,this.getStageList()},handleCurrentChange:function(e){this.currentPage=e,this.getStageList()},getStageList:function(){var e=this;this.loading=!0,Object(o["j"])({page:this.currentPage,pageType:1,limit:this.pageSize,label:this.label,status:this.status}).then((function(t){e.loading=!1,e.stageData=t.data.list,e.total=t.data.totalRow})).catch((function(){e.loading=!1}))},fieldFormatter:function(e,t,i){if("label"==t.property)return $["a"].typeToNameData[i]||"";if("stageUserDep"==t.property){var a=e.deptNameList||[],s=e.userNameList||[],n=a.map((function(e){return e})).join("、")+s.map((function(e){return e})).join("、");return n||"全公司"}return"status"===t.property?1==e[t.property]?"启用":"停用":e[t.property]},stageEdit:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Object(o["h"])({flowId:e.flowId}).then((function(a){a.data.settingList.forEach((function(e){e.examinePreviewList?(e.examinePreviewList.dataList=e.examinePreviewList.examineFlowList||[],delete e.examinePreviewList.examineFlowList,e.examineSaveBO=e.examinePreviewList,delete e.examinePreviewList):(e.examineSaveBO=null,delete e.examinePreviewList)})),t.stageObj={type:"edit",flowName:a.data.flowName,flowId:a.data.flowId,userIdList:a.data.userList||[],deptIdList:a.data.deptList||[],label:a.data.label,successName:a.data.successName,failedName:a.data.failedName,settingList:a.data.settingList||[],status:e.status},t.stageTitle=i?"新建阶段流程":"编辑阶段流程",t.stageDialogVisible=!0})).catch((function(){}))},stageDelect:function(e){var t=this;this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o["l"])({flowId:e.row.flowId,status:3}).then((function(i){t.stageData.splice(e.$index,1),t.$message.success("删除成功")})).catch((function(){}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},stageStatus:function(e){var t=this;this.$confirm("您确定要"+(2===e.row.status?"启用":"停用")+"该阶段流程?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.updateStageStatus(e.row)})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},updateStageStatus:function(e){var t=this;this.loading=!0,Object(o["l"])({flowId:e.flowId,status:1===e.status?2:1}).then((function(e){t.getStageList(),t.stageDialogVisible=!1})).catch((function(){t.loading=!1}))},addStage:function(){this.stageObj={type:"save",flowName:"",label:"",stageUserDep:"",userIdList:[],deptIdList:[],settingList:[]},this.stageDialogVisible=!0,this.stageTitle="添加阶段流程"},stageClose:function(){this.stageDialogVisible=!1},stageSubmit:function(){this.getStageList(),this.stageDialogVisible=!1}}},oe=le,re=(i("41d6"),Object(d["a"])(oe,p,m,!1,null,"328eb662",null)),ce=re.exports,de=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e._m(0),e._v(" "),a("div",{staticClass:"content-body"},[a("div",{staticClass:"product-setting-con"},[a("div",{staticClass:"tips"},[e._v("注：产品类别最多设置20级")]),e._v(" "),a("div",[a("el-button",{staticStyle:{"padding-left":"0"},attrs:{type:"primary-text"},nativeOn:{click:function(t){e.handleTreeSetDrop({type:"create-one"})}}},[e._v("+新增一级分类")])],1),e._v(" "),a("div",{staticClass:"tree-box"},[a("el-tree",{attrs:{data:e.treeData,props:e.defaultProps,"default-expand-all":""},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.node;t.data;return a("flexbox",{staticClass:"node-data"},[s.expanded?a("img",{staticClass:"node-img",attrs:{src:i("0ac7")}}):e._e(),e._v(" "),s.expanded?e._e():a("img",{staticClass:"node-img",attrs:{src:i("cd76")}}),e._v(" "),a("div",{staticClass:"node-label"},[e._v(e._s(s.label))]),e._v(" "),s.level<e.maxCreateLevel?a("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleTreeSetDrop}},[a("div",{staticClass:"node-label-set",on:{click:function(t){t.stopPropagation(),e.getChild(s)}}},[e._v("设置")]),e._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.treeSetTypes,(function(t,i){return a("el-dropdown-item",{key:i,attrs:{command:t}},[e._v(e._s(t.name))])})))],1):e._e()],1)}}])})],1)])]),e._v(" "),a("el-dialog",{attrs:{visible:e.productHandleDialog,"close-on-click-modal":!1,title:"提示",width:"400px"},on:{"update:visible":function(t){e.productHandleDialog=t}}},[a("el-form",{attrs:{model:e.productForm}},[a("el-form-item",{attrs:{label:"类别名称","label-width":"80"}},[a("el-input",{attrs:{maxlength:20,autocomplete:"off"},model:{value:e.productForm.name,callback:function(t){e.$set(e.productForm,"name",t)},expression:"productForm.name"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleProduct}},[e._v("确定")]),e._v(" "),a("el-button",{on:{click:function(t){e.productHandleDialog=!1}}},[e._v("取消")])],1)],1)],1)},ue=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"content-header"},[i("span",[e._v("产品类别设置"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"218"}})])])}],fe={name:"ProductCategorySet",components:{},data:function(){return{loading:!1,treeData:[],treeSetTypes:[],maxCreateLevel:20,productHandleDialog:!1,productForm:{name:"",type:"",parentId:"",categoryId:""},defaultProps:{children:"children",label:"label"}}},created:function(){this.getProductCategoryIndex()},methods:{getChild:function(e){var t=[{type:"create-child",name:"新建子分类"},{type:"create-brother",name:"新建平级分类"},{type:"edit",name:"编辑分类"},{type:"delete",name:"删除分类"}];this.treeSetTypes=t.map((function(t,i,a){return t["node"]=e,t}))},handleTreeSetDrop:function(e){var t=this;"create-one"==e.type&&(this.productForm.type=e.type,this.productForm.name="",this.productHandleDialog=!0),"create-child"==e.type?(this.productForm.type=e.type,this.productForm.parentId=e.node.data.categoryId,this.productForm.name="",this.productHandleDialog=!0):"create-brother"==e.type?(this.productForm.type=e.type,this.productForm.parentId=e.node.data.parentId,this.productForm.name="",this.productHandleDialog=!0):"edit"==e.type?(this.productForm.type=e.type,this.productForm.name=e.node.data.name,this.productForm.categoryId=e.node.data.categoryId,this.productForm.parentId=e.node.data.parentId,this.productHandleDialog=!0):"delete"==e.type&&this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(o["S"])({id:e.node.data.categoryId}).then((function(e){t.$message({type:"success",message:"删除成功!"}),t.getProductCategoryIndex(),t.loading=!1})).catch((function(){t.loading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleProduct:function(){var e=this;0!=this.productForm.name.length?(this.productHandleDialog=!1,"create-one"==this.productForm.type?(this.loading=!0,Object(o["U"])({name:this.productForm.name}).then((function(t){e.getProductCategoryIndex(),e.$message.success("新增成功"),e.loading=!1})).catch((function(){e.loading=!1}))):"create-child"==this.productForm.type||"create-brother"==this.productForm.type?(this.loading=!0,Object(o["U"])({parentId:this.productForm.parentId,name:this.productForm.name}).then((function(t){e.getProductCategoryIndex(),e.$message.success("新建成功"),e.loading=!1})).catch((function(){e.loading=!1}))):"edit"==this.productForm.type&&(this.loading=!0,Object(o["U"])({categoryId:this.productForm.categoryId,parentId:this.productForm.parentId,name:this.productForm.name}).then((function(t){e.getProductCategoryIndex(),e.$message.success("编辑成功"),e.loading=!1})).catch((function(){e.loading=!1})))):this.$message({message:"请填写名称",type:"warning"})},getProductCategoryIndex:function(){var e=this;this.loading=!0,Object(o["T"])({type:"tree"}).then((function(t){e.loading=!1,e.treeData=t.data})).catch((function(){e.loading=!1}))}}},pe=fe,me=(i("4058"),Object(d["a"])(pe,de,ue,!1,null,"277bab79",null)),he=me.exports,ge=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[i("div",{staticClass:"content-header"},[e._m(0),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1),e._v(" "),i("div",{staticClass:"content-body"},[i("div",{staticClass:"tips"},[e._v("设置提前提醒天数之后，根据合同的”合同到期时间”计算提醒时间")]),e._v(" "),i("div",{staticClass:"set-content"},[i("el-radio",{attrs:{label:0},model:{value:e.contractConfig,callback:function(t){e.contractConfig=t},expression:"contractConfig"}},[e._v("不提醒")]),e._v(" "),i("el-radio",{attrs:{label:1},model:{value:e.contractConfig,callback:function(t){e.contractConfig=t},expression:"contractConfig"}},[e._v("提前提醒天数")]),e._v(" "),1==e.contractConfig?i("div",{staticClass:"time-set"},[i("el-input-number",{attrs:{controls:!1,min:0,"step-strictly":""},model:{value:e.contractDay,callback:function(t){e.contractDay=t},expression:"contractDay"}}),i("span",[e._v("天")])],1):e._e()],1)])])},ve=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("span",[e._v("合同到期提醒设置"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"219"}})])}],be={name:"ContractExpireSet",components:{},data:function(){return{loading:!1,contractDay:0,contractConfig:0}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,this.$store.dispatch("CRMSettingConfig").then((function(t){e.loading=!1,e.contractDay=t.data.contractDay,e.contractConfig=parseInt(t.data.contractConfig)})).catch((function(){e.loading=!1}))},save:function(){var e=this;this.loading=!0;var t={};1==this.contractConfig?(t.contractDay=this.contractDay,t.status=1):t.status=0,Object(o["v"])(t).then((function(t){e.loading=!1,e.$message.success("操作成功")})).catch((function(){e.loading=!1}))}}},ye=be,_e=(i("8328"),Object(d["a"])(ye,ge,ve,!1,null,"5341e20e",null)),xe=_e.exports,we=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"content-header"},[i("span",[e._v(e._s(e.title)),e.helpTypeId?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":e.helpTypeId},on:{click:function(e){e.stopPropagation()}}}):e._e()]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.addRule}},[e._v("添加规则")])],1),e._v(" "),i("div",{staticClass:"customer-table"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.list,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight}},[e._l(e.fieldList,(function(t,a){return i("el-table-column",{key:a,attrs:{prop:t.field,label:t.label,formatter:e.fieldFormatter,"show-overflow-tooltip":""}})})),e._v(" "),i("el-table-column",{attrs:{fixed:"right",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleEdit(t.row)}}},[e._v("编辑")]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleDelete(t)}}},[e._v("删除")])]}}])})],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),i("edit-customer-limit",{attrs:{visible:e.showAddEdit,types:e.types,action:e.action},on:{"update:visible":function(t){e.showAddEdit=t},success:e.getList}})],1)},ke=[],Ce=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{ref:"wkDialog",attrs:{visible:e.visible,title:e.title,"append-to-body":!0,"close-on-click-modal":!1,"before-close":e.close,width:"550px"},on:{"update:visible":function(t){e.visible=t}}},[i("div",{staticClass:"position-relative"},[i("flexbox",{staticClass:"handle-item",attrs:{align:"stretch"}},[i("div",{staticClass:"handle-item-name",staticStyle:{"margin-top":"8px"}},[e._v("适用范围：")]),e._v(" "),i("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":e.users,"dep-value":e.strucs},on:{"update:userValue":function(t){e.users=t},"update:depValue":function(t){e.strucs=t}}})],1),e._v(" "),i("flexbox",{staticClass:"handle-item",attrs:{align:"stretch"}},[i("div",{staticClass:"handle-item-name",staticStyle:{"margin-top":"8px"}},[e._v(e._s(e.valueLabel))]),e._v(" "),i("el-input-number",{attrs:{controls:!1,min:0,"step-strictly":""},model:{value:e.customerNum,callback:function(t){e.customerNum=t},expression:"customerNum"}})],1),e._v(" "),e.showDeal?i("flexbox",{staticClass:"handle-item"},[i("div",{staticClass:"handle-item-name"},[e._v(e._s(e.dealLabel))]),e._v(" "),i("el-radio-group",{model:{value:e.customerDeal,callback:function(t){e.customerDeal=t},expression:"customerDeal"}},[i("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1):e._e()],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.sure,expression:"sure"}],attrs:{type:"primary"}},[e._v("确定")]),e._v(" "),i("el-button",{nativeOn:{click:function(t){return e.close(t)}}},[e._v("取消")])],1)])},Ie=[],Le={name:"EditCustomerLimit",components:{WkUserDepDialogSelect:b["a"]},mixins:[te["a"]],props:{types:[String,Number],visible:{type:Boolean,default:!1},action:{type:Object,default:function(){return{type:"save"}}}},data:function(){return{loading:!1,customerDeal:1,customerNum:"",users:[],strucs:[]}},computed:{valueLabel:function(){return{1:"拥有客户数上限（个）",2:"锁定客户数上限（个）"}[this.types]},dealLabel:function(){return{1:"成交客户是否占有拥有客户数：",2:"成交客户是否占有锁定客户数："}[this.types]},title:function(){return"update"==this.action.type?"编辑规则":"添加规则"},showDeal:function(){return 1==this.types}},watch:{visible:function(e){var t=this;if(e)if("save"==this.action.type)this.clearInfo();else if("update"==this.action.type){var i=this.action.data;this.customerDeal=i.customerDeal,this.customerNum=i.customerNum,this.$nextTick((function(){t.users=i.userIds?i.userIds.map((function(e){return e.userId})):[],t.strucs=i.deptIds?i.deptIds.map((function(e){return e.id})):[]}))}}},mounted:function(){},methods:{close:function(){this.$emit("update:visible",!1)},sure:function(){var e=this;if(this.customerNum<=0)this.$message.error("请输入正确的客户数");else if(this.users.length||this.strucs.length){this.loading=!0;var t={userIds:this.users.map((function(e){return{userId:e}})),deptIds:this.strucs.map((function(e){return{id:e}})),customerNum:this.customerNum,type:this.types};this.showDeal&&(t.customerDeal=this.customerDeal),"update"==this.action.type&&(t.settingId=this.action.data.settingId),Object(o["y"])(t).then((function(t){e.$emit("success"),e.$message.success("".concat(e.title,"成功")),e.close(),e.loading=!1})).catch((function(){e.loading=!1}))}else this.$message.error("请完善信息")},clearInfo:function(){this.users=[],this.strucs=[],this.customerDeal=1,this.customerNum=""}}},Se=Le,Ee=(i("a8ba"),Object(d["a"])(Se,Ce,Ie,!1,null,"123ef3d7",null)),De=Ee.exports,Ae={name:"CustomerLimitSet",components:{EditCustomerLimit:De},props:{types:[String,Number]},data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-290,list:[],showAddEdit:!1,action:{},currentPage:1,pageSize:10,pageSizes:[10,20,30,40],total:0}},computed:{title:function(){return{1:"拥有客户数限制",2:"锁定客户数限制"}[this.types]},helpTypeId:function(){return{1:"220",2:"221"}[this.types]},fieldList:function(){var e=[{label:"适用范围",field:"userIds"},{label:{1:"拥有客户数上限",2:"锁定客户数上限"}[this.types],field:"customerNum"}];return 1==this.types&&e.push({label:{1:"成交客户是否占有拥有客户数",2:"成交客户是否占有锁定客户数"}[this.types],field:"customerDeal"}),e}},watch:{types:function(){this.list=[],this.getList()}},created:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-290},this.getList()},methods:{handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},getList:function(){var e=this;this.loading=!0,Object(o["x"])({page:this.currentPage,limit:this.pageSize,type:this.types}).then((function(t){e.loading=!1,e.list=t.data.list,e.total=t.data.totalRow})).catch((function(){e.loading=!1}))},fieldFormatter:function(e,t){if("customerDeal"==t.property)return 1==e.customerDeal?"是":"否";if("userIds"===t.property){var i=e["deptIds"]||[],a=i.map((function(e){return e.name})).join("、"),s=e["userIds"]||[],n=s.map((function(e){return e.realname})).join("、");a&&n&&(a+="、");var l=a+n;return l||"全公司"}return"status"===t.property?0===e[t.property]?"停用":"启用":e[t.property]},handleEdit:function(e){this.action={type:"update",data:e},this.showAddEdit=!0},addRule:function(){this.action={type:"save"},this.showAddEdit=!0},handleDelete:function(e){var t=this;this.$confirm("确定删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(o["w"])({settingId:e.row.settingId}).then((function(i){t.list.splice(e.$index,1),t.$message.success("删除成功"),t.loading=!1})).catch((function(){t.loading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}},Fe=Ae,Te=(i("3628"),Object(d["a"])(Fe,we,ke,!1,null,"67cab813",null)),Ne=Te.exports,Oe=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"visit-remind-set"},[i("div",{staticClass:"content-header"},[e._m(0),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1),e._v(" "),i("div",{staticClass:"content-body"},[i("div",{staticClass:"tips"},[e._v("设置回访提醒后，到期会自动提醒，合同生效是指到达合同开始时间")]),e._v(" "),i("div",{staticClass:"set-content"},[i("el-radio",{attrs:{label:0},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},[e._v("不提醒")]),e._v(" "),i("el-radio",{attrs:{label:1},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},[e._v("提醒")]),e._v(" "),1==e.status?i("div",{staticClass:"time-set"},[i("span",[e._v("合同生效后")]),e._v(" "),i("el-input",{attrs:{type:"number"},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}}),i("span",[e._v("天提醒")])],1):e._e()],1)])])},je=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("span",[e._v("客户回访提醒设置"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"24","data-id":"222"}})])}],$e={name:"VisitRemindSet",components:{},data:function(){return{loading:!1,value:0,status:0}},created:function(){this.getDetail()},methods:{getDetail:function(){var e=this;this.loading=!0,Object(o["V"])().then((function(t){e.loading=!1,e.value=t.data.value,e.status=parseInt(t.data.status)})).catch((function(){e.loading=!1}))},save:function(){var e=this;this.loading=!0;var t={};1==this.status?(t.value=this.value,t.status=1):t.status=0,Object(o["W"])(t).then((function(t){e.loading=!1,e.$message.success("操作成功")})).catch((function(){e.loading=!1}))}}},Pe=$e,Be=(i("d89d"),Object(d["a"])(Pe,Oe,je,!1,null,"d8a97258",null)),Re=Be.exports,Ve=i("f468"),Me={name:"BizParam",components:{StageFlowSet:ce,ProductCategorySet:he,FollowLogTypeSet:f,ContractExpireSet:xe,CustomerLimitSet:Ne,VisitRemindSet:Re,XrHeader:Ve["a"]},data:function(){return{menuList:[{label:"跟进记录类型设置",key:"follow-log-type-set"},{label:"阶段流程设置",key:"stage-flow-set"},{label:"产品类别设置",key:"product-category-set"},{label:"合同到期提醒设置",key:"contract-expire-set"},{label:"拥有客户数限制",key:"own"},{label:"锁定客户数限制",key:"lock"},{label:"客户回访提醒设置",key:"VisitRemindSet"}],menuIndex:"follow-log-type-set",types:""}},computed:{componentName:function(){return"own"==this.menuIndex||"lock"==this.menuIndex?"customer-limit-set":this.menuIndex}},methods:{menuSelect:function(e){"own"!=e&&"lock"!=e||(this.types={own:1,lock:2}[e]),this.menuIndex=e}}},ze=Me,Ue=(i("cbdb"),Object(d["a"])(ze,a,s,!1,null,"1667a201",null));t["default"]=Ue.exports},"66b0":function(e,t,i){"use strict";i("1de7")},"6b58":function(e,t,i){},8328:function(e,t,i){"use strict";i("ac55")},"88ee":function(e,t,i){},"909f":function(e,t,i){},a1da:function(e,t,i){},a2bf:function(e,t,i){"use strict";var a=i("e8b5"),s=i("07fa"),n=i("3511"),l=i("0366"),o=function(e,t,i,r,c,d,u,f){var p,m,h=c,g=0,v=!!u&&l(u,f);while(g<r)g in i&&(p=v?v(i[g],g,t):i[g],d>0&&a(p)?(m=s(p),h=o(e,t,p,m,h,d-1)-1):(n(h+1),e[h]=p),h++),g++;return h};e.exports=o},a8ba:function(e,t,i){"use strict";i("a1da")},ac55:function(e,t,i){},b565:function(e,t,i){},ba9d:function(e,t,i){},c23e:function(e,t,i){},cbdb:function(e,t,i){"use strict";i("88ee")},cd76:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjVENzhCOEIzRERCMjExRTg4QjUxREJEMjVGMDBFOUEyIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjVENzhCOEI0RERCMjExRTg4QjUxREJEMjVGMDBFOUEyIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NUQ3OEI4QjFEREIyMTFFODhCNTFEQkQyNUYwMEU5QTIiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NUQ3OEI4QjJEREIyMTFFODhCNTFEQkQyNUYwMEU5QTIiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5zKmg/AAAAcElEQVR42mI8c+aMLQMDw0IgVmQgHdwH4jgmIDEHiNOBmJEMDNI3lxHokv9QAXLBfyYGKgBiDPlPDUMYho4hLESGAboYIyFDGLEYwDiCApYYQxjp5pJbQOxCpn6QvlugKE4B4kVArECGIQ+AOBYgwAB2pxPdB9NdPQAAAABJRU5ErkJggg=="},d89d:function(e,t,i){"use strict";i("6b58")},d9259:function(e,t,i){}}]);