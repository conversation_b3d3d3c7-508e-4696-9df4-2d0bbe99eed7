(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e82d3f90"],{1303:function(t,e,a){},"236a":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"address-book-index main"},[a("flexbox",{staticClass:"main-header",attrs:{justify:"space-between"}},[a("div",{staticClass:"main-header__left"},[a("span",{staticClass:"title"},[t._v("通讯录")])])]),t._v(" "),a("div",{staticClass:"main-content-wrap"},[a("div",{staticClass:"main-nav"},[a("div",{staticClass:"main-nav__title"},[t._v("企业组织架构")]),t._v(" "),a("div",{staticClass:"main-nav__content"},[a("div",{staticClass:"nav-sections-wrap"},[a("div",{staticClass:"nav-section"},[a("div",{staticClass:"nav-section__content is-padding"},[a("el-tree",{ref:"tree",attrs:{data:t.depTree,"expand-on-click-node":!1,props:{label:"name"},"highlight-current":"","default-expand-all":"","node-key":"deptId"},on:{"node-click":t.deptClick},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,n=e.data;return a("span",{},[1==i.level?a("i",{staticClass:"wk wk-customer",staticStyle:{"margin-right":"4px"}}):t._e(),t._v("\n                  "+t._s(n.name)+"\n                ")])}}])})],1)])])])]),t._v(" "),a("div",{staticClass:"main-content"},[a("wk-filter-header",{attrs:{tabs:t.tabs,props:{searchPlaceholder:"请输入员工姓名/手机号"},"active-tab":t.bookType},on:{"update:activeTab":function(e){t.bookType=e},"tabs-change":t.refreshList,"event-change":t.filterHeaderHandle}}),t._v(" "),a("div",{staticClass:"letters"},[t._v("\n        筛选:\n        "),t._l(t.letters,(function(e){return a("span",{key:e.label,staticClass:"letter",class:{"is-current":t.letter===e.value},on:{click:function(a){t.letterClick(e)}}},[t._v("\n          "+t._s(e.label)+"\n        ")])}))],2),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{size:"small",data:t.list,height:t.tableHeight},on:{"sort-change":t.sortTableList}},[a("el-table-column",{attrs:{label:"字母",align:"center",prop:"initial",sortable:"",width:"90"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"关注",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"wk wk-focus-on focus-icon",class:{active:1===e.row.status},on:{click:function(a){t.toggleStar(e.$index,e.row.status)}}})]}}])}),t._v(" "),a("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"realname",label:"姓名"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("flexbox",{staticClass:"user-box"},[a("xr-avatar",{staticClass:"user-img",attrs:{id:e.row.userId,name:e.row.realname,size:30,src:e.row.img}}),t._v(" "),a("span",[t._v(t._s(e.row.realname))])],1)]}}])}),t._v(" "),t._l(t.tableMap,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.key,label:t.label,"show-overflow-tooltip":""}})}))],2),t._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":t.currentPage,"page-sizes":t.pageSizes,"page-size":t.pageSize,"pager-count":5,total:t.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:currentPage":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"size-change":t.refreshList,"current-change":t.getList}})],1)],1)])],1)},n=[],s=(a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("ac1f"),a("841c"),a("159b"),a("b775"));function l(t){return Object(s["a"])({url:"adminUser/queryListName",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(s["a"])({url:"adminUser/attention",method:"post",data:t})}var o=a("2934"),c=a("1bfc"),d=a("b0a8"),u="A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,#".split(","),h={name:"AddressBookIndex",components:{WkFilterHeader:c["a"]},mixins:[d["a"]],data:function(){return{bookType:"all",deptIdValue:"",depTree:[],search:"",initialOrder:1,letter:"",letters:[],tabs:[{label:"全部",value:"all"},{label:"我关注的",value:"attention"}],list:[],tableMap:[{label:"手机",key:"mobile"},{label:"部门",key:"deptName"},{label:"岗位",key:"post"}],loading:!1}},created:function(){this.otherTableHeight=300,this.rowHeight=47;var t=[{label:"全部",value:""}];u.forEach((function(e){t.push({label:e,value:e})})),this.letters=t,this.getDepTreeList(),this.getList()},methods:{getDepTreeList:function(){var t=this;this.loading=!0,Object(o["m"])({type:"tree"}).then((function(e){t.depTree=e.data||[],t.loading=!1})).catch((function(){t.loading=!1}))},deptClick:function(t){this.deptIdValue!==t.deptId?(this.deptIdValue=t.deptId,this.refreshList()):(this.$refs.tree.setCurrentKey(),this.deptIdValue="",this.refreshList())},letterClick:function(t){this.letter=t.value,this.refreshList()},filterHeaderHandle:function(t,e){"searchUpdate"===t&&(this.search=e,this.refreshList())},getList:function(){var t=this,e={page:this.currentPage,limit:15,search:this.search||"",initial:this.initialOrder||1,deptId:this.deptIdValue};e.acronym=this.letter,"attention"==this.bookType&&(e.status=1),this.loading=!0,l(e).then((function(e){t.loading=!1;var a=e.data||{};t.total=a.totalRow,t.list=a.list||[],t.updateTableHeight()})).catch((function(){t.loading=!1}))},sortTableList:function(t){"ascending"===t.order?this.initialOrder=1:"descending"===t.order?this.initialOrder=2:this.initialOrder=null,this.refreshList()},toggleStar:function(t,e){var a=this;this.loading=!0,r({userId:this.list[t].userId}).then((function(){a.loading=!1,a.list[t].status=0===e?1:0,a.$set(a.list,t,a.list[t])})).catch((function(){a.loading=!1}))},refreshList:function(){this.currentPage=1,this.getList()}}},p=h,g=(a("9c99"),a("2877")),f=Object(g["a"])(p,i,n,!1,null,"190d2a73",null);e["default"]=f.exports},"9c99":function(t,e,a){"use strict";a("1303")},b0a8:function(t,e,a){"use strict";var i=a("5579");e["a"]={components:{WkEmpty:i["a"]},props:{},data:function(){return{currentPage:1,pageSize:15,pageSizes:[15,30,60,100],total:0,rowHeight:44,otherTableHeight:265,tableHeight:200}},computed:{},watch:{},created:function(){},mounted:function(){var t=this;window.onresize=function(){t.updateTableHeight()}},beforeDestroy:function(){},methods:{handleSizeChange:function(t){this.pageSize=t,this.getList()},handleCurrentChange:function(t){this.currentPage=t,this.getList()},updateTableHeight:function(){var t=document.documentElement.clientHeight-this.otherTableHeight,e=this.rowHeight*this.list.length+41;this.tableHeight=e>t?t:0===this.list.length?200:e}}}}}]);