(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b72a87f0"],{"2fa7":function(e,t,i){"use strict";i("eea3")},c249:function(e,t,i){"use strict";i("dbeb")},d97a:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"main"},[i("xr-header",{attrs:{label:"业务审批流"}},[i("el-button",{attrs:{slot:"ft",type:"primary"},on:{click:e.addExamine},slot:"ft"},[e._v("新建审批流程")])],1),e._v(" "),i("div",{staticClass:"main-body"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-table",class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",id:"examine-table",data:e.list,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight,"highlight-current-row":""}},[i("el-table-column",{attrs:{width:"100",label:"审批流图标"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"table-icon"},[i("i",{class:e.getLableIcon(t.row.label)})])]}}])}),e._v(" "),e._l(e.fieldList,(function(t,a){return i("el-table-column",{key:a,attrs:{formatter:e.fieldFormatter,prop:t.prop,"min-width":t.width,label:t.label,"show-overflow-tooltip":""}})})),e._v(" "),i("el-table-column",{attrs:{fixed:"right",label:"操作",width:"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("edit",t)}}},[e._v("编辑")]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("delete",t)}}},[e._v("删除")]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("change",t)}}},[e._v(e._s(2===t.row["status"]?"启用":"停用"))]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("copy",t)}}},[e._v("复制并新建")])]}}])})],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.createShow?i("business-approve-flow-create",{attrs:{detail:e.rowDetail,"is-copy":e.isCopyCreate,"label-list":e.labelList},on:{success:e.getList,close:function(t){e.createShow=!1}}}):e._e()],1)},n=[],s=(i("7db0"),i("a15b"),i("d81d"),i("a434"),i("b0c0"),i("e9f5"),i("f665"),i("ab43"),i("d3b7"),i("ffce")),l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("transition",{attrs:{name:"opacity-fade"}},[i("div",{staticClass:"business-approve-flow-create"},[i("wk-backgroud-tabs",{attrs:{options:e.tabs},model:{value:e.tabIndex,callback:function(t){e.tabIndex=t},expression:"tabIndex"}},[i("template",{slot:"right"},[i("el-button",{attrs:{type:"primary"},on:{click:e.sendClick}},[e._v("发布")]),e._v(" "),i("i",{staticClass:"el-icon-close create-close",on:{click:e.closeClick}})],1)],2),e._v(" "),i("base-info-set",{directives:[{name:"show",rawName:"v-show",value:"base"===e.tabIndex,expression:"tabIndex === 'base'"}],ref:"baseInfoSet",attrs:{fields:e.fields,"fields-form":e.fieldsForm,"fields-rules":e.fieldsRules},on:{change:e.formChange}}),e._v(" "),i("wk-approve-flow",{directives:[{name:"show",rawName:"v-show",value:"flow"===e.tabIndex,expression:"tabIndex === 'flow'"}],ref:"wkApproveFlow",attrs:{props:e.approveFlowConfig,list:e.flowList,"send-node":e.sendNode}})],1)])},o=[],r=i("5530"),c=(i("14d9"),i("7d54"),i("159b"),i("4378")),u=i("0904"),d=i("43b6"),h=i("ed08"),p=i("07307"),f=i("3817"),m={name:"BusinessApproveFlowCreate",components:{WkBackgroudTabs:c["a"],BaseInfoSet:u["a"],WkApproveFlow:d["a"]},filters:{},mixins:[p["a"],f["a"]],props:{isCopy:{type:Boolean,default:!1},labelList:Array,detail:Object},data:function(){return{loading:!1,height:document.documentElement.clientHeight-100,tabs:[{label:"1.配置基础信息",value:"base"},{label:"2.配置流程",value:"flow"}],tabIndex:"base",sendNode:{name:"发起人",content:"具有新建权限的员工"},fields:[],fieldsForm:{},fieldsRules:{},flowList:[],conditionFields:null}},computed:{approveFlowConfig:function(){return{conditionSelectRequest:s["k"],conditionSelectParams:{label:this.fieldsForm.label},conditionSelectList:3===this.fieldsForm.label?[{fieldId:-1,fieldName:"invoiceMoney",fieldType:1,name:"开票金额",setting:null,type:6},{fieldId:-1,fieldName:"invoiceType",fieldType:1,name:"开票类型",setting:[{label:"增值税专用发票",value:"1"},{label:"增值税普通发票",value:"2"},{label:"国税通用机打发票",value:"3"},{label:"地税通用机打发票",value:"4"},{label:"收据",value:"5"}],type:3}]:null}},examineId:function(){return this.detail?this.detail.examineId:null}},created:function(){this.getBaseField(),this.examineId?this.getFlowList(this.examineId):this.flowList=[Object(h["D"])(d["b"])]},mounted:function(){var e=this;this.$el.style.zIndex=Object(h["w"])(),document.body.appendChild(this.$el),window.onresize=function(){e.height=document.documentElement.clientHeight-100}},destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},methods:{getFlowList:function(e){var t=this;Object(s["j"])({examineId:e}).then((function(e){var i=e.data||[],a=[];t.getListInfo(i,a),t.flowList=a})).catch((function(){}))},validateSetting:function(e){this.requestConditionFields().then((function(t){var i=t.find((function(t){return t.fieldName===e.fieldName}));e.setting=i?i.setting:[]}))},requestConditionFields:function(){var e=this,t=this.approveFlowConfig,i=t.conditionSelectRequest,a=t.conditionSelectParams,n=t.conditionSelectList;return n?Promise.resolve(n):this.conditionFields?Promise.resolve(this.conditionFields):new Promise((function(t,n){i(a).then((function(i){var a=i.data||[];e.conditionFields=a,t(a)})).catch((function(){t([])}))}))},getBaseField:function(){var e=[];e.push({field:"examineName",formType:"text",isNull:1,name:"审批流名称",setting:[],inputTips:"",value:this.detail?this.detail.examineName:""}),e.push({field:"label",formType:"select",isNull:0,name:"关联对象",setting:this.labelList,value:this.detail?this.detail.label:this.labelList&&this.labelList.length>0?this.labelList[0].value:""}),e.push({field:"recheckType",formType:"select",isNull:0,name:"审批被拒后重新提交",setting:[{name:"返回审批流初始层级",value:1},{name:"跳过审批流已通过的层级，返回拒绝的层级",value:2}],inputTips:"",value:this.detail?this.detail.recheckType:1}),e.push({field:"managerList",formType:"user",isNull:1,name:"审批流管理员",setting:[],radio:!1,tipType:"tooltip",inputTips:'<div>1、可以在"配置流程"设置当审批人为空，审批<br>自动转交给审批流管理员；当管理员也请假/离<br>职，审批将转交给超级管理员。</div><div>2、可指定多个管理员，审批方式为或签。</div>',value:this.detail?this.detail.managerList:1}),e.push({field:"remarks",formType:"textarea",isNull:0,name:"流程说明",maxlength:200,setting:[],inputTips:"请填写相关注意事项，方便员工在申请时查阅，限制输入200字",value:this.detail?this.detail.remarks:""}),this.handleFields(e)},handleFields:function(e){var t=this,i={},a={};e.forEach((function(e){"userDep"===e.formType?(a.userList=[],a.deptList=[]):(i[e.field]=t.getRules(e),a[e.field]=e.value)})),this.fields=Object(h["D"])(e),this.fieldsForm=a,this.fieldsRules=i},formChange:function(e,t,i){"label"===e.field&&(this.flowList=[Object(h["D"])(d["b"])])},sendClick:function(){var e=this;this.$refs.baseInfoSet.validate().then((function(t){if(t){var i=e.$refs.wkApproveFlow.getParams();if(i.isError)e.$message.error("请完善信息");else{var a=Object(r["a"])(Object(r["a"])({},e.fieldsForm),{},{dataList:i.list});e.examineId&&!e.isCopy&&(a.examineId=e.examineId),e.submiteRequest(a)}}}))},submiteRequest:function(e){var t=this;Object(s["g"])(e).then((function(e){t.$message.success("创建成功"),t.$emit("success"),t.closeClick()})).catch((function(){}))},closeClick:function(){this.$emit("close")}}},b=m,g=(i("2fa7"),i("2877")),w=Object(g["a"])(b,l,o,!1,null,"08440b34",null),v=w.exports,y=i("f468"),k={name:"SystemExamine",components:{XrHeader:y["a"],BusinessApproveFlowCreate:v},mixins:[],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-220,list:[],fieldList:[{prop:"examineName",label:"审批流名称",width:150},{prop:"label",label:"关联对象",width:150},{prop:"recheckType",label:"审批被拒后重新提交",width:150},{prop:"updateUserName",label:"最后修改人",width:150},{prop:"updateTime",label:"最后修改时间",width:150},{prop:"status",label:"状态",width:100}],currentPage:1,pageSize:20,pageSizes:[10,20,30,40],currentRoute:null,total:0,rowDetail:null,isCopyCreate:!1,createShow:!1}},computed:{labelList:function(){return this.currentRoute&&{customerExamine:[{name:"合同",value:1},{name:"回款",value:2},{name:"发票",value:3}]}[this.currentRoute.name]||[]}},watch:{"$route.name":{handler:function(){this.currentRoute=this.$route,this.getList()},immediate:!0}},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-220}},methods:{getList:function(){var e=this;this.loading=!0,Object(s["l"])({label:{customerExamine:1}[this.currentRoute.name],page:this.currentPage,limit:this.pageSize}).then((function(t){e.list=t.data.list,e.total=t.data.totalRow,e.loading=!1})).catch((function(){e.loading=!1}))},fieldFormatter:function(e,t){if("label"===t.property){var i=this.labelList.find((function(t){return t.value===e.label}));return i?i.name:"--"}if("userList"===t.property){var a=e["deptList"]||[],n=a.map((function(e){return e.name})).join("、"),s=e["userList"]||[],l=s.map((function(e){return e.realname})).join("、");n&&l&&(n+="、");var o=n+l;return o||"全公司"}return"status"===t.property?2===e[t.property]?"停用":"启用":"recheckType"===t.property?{1:"返回审批流初始层级",2:"跳过审批流已通过的层级，返回拒绝的层级"}[e[t.property]]:e[t.property]},addExamine:function(){this.isCopyCreate=!1,this.rowDetail=null,this.createShow=!0},handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},handleClick:function(e,t){var i=this;"edit"===e?(this.isCopyCreate=!1,this.rowDetail=t.row,this.createShow=!0):"delete"===e?this.$confirm("您确定要删除该审批流?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["n"])({examineId:t.row["examineId"],status:3}).then((function(e){i.list.splice(t.$index,1),0===i.list.length&&(i.currentPage=i.currentPage-1>0?i.currentPage-1:1),i.getList(),i.$message({type:"success",message:"操作成功"})})).catch((function(){}))})).catch((function(){i.$message({type:"info",message:"已取消删除"})})):"change"===e?this.$confirm("您确定要"+(2===t.row["status"]?"启用":"停用")+"该审批流?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["n"])({examineId:t.row["examineId"],status:2===t.row["status"]?1:2}).then((function(e){i.$message({type:"success",message:"操作成功"}),i.getList()})).catch((function(){}))})).catch((function(){})):"copy"===e&&(this.isCopyCreate=!0,this.rowDetail=t.row,this.createShow=!0)},getLableIcon:function(e){return{1:"wk wk-contract",2:"wk wk-receivables",3:"wk wk-invoice",4:"wk wk-icon-pay-solid",5:"wk wk-icon-shop-cart",6:"wk wk-drafts",7:"wk wk-record",8:"wk wk-approval-13",9:"wk wk-icon-category-note",10:"wk wk-l-record",11:"wk wk-icon-search-note",12:"wk wk-approval-11"}[e]}}},x=k,C=(i("c249"),Object(g["a"])(x,a,n,!1,null,"6132ebe1",null));t["default"]=C.exports},dbeb:function(e,t,i){},eea3:function(e,t,i){}}]);