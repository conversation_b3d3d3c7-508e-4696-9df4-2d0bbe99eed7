(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-345f54f0"],{"26b0":function(t,n,e){"use strict";var a=e("5530"),o=e("df55"),i=e("a347"),r=e.n(i);n["a"]={data:function(){return{loading:!1,axisOption:{color:["#1890ff"],toolbox:{showTitle:!1,feature:{saveAsImage:{pixelRatio:2}}},tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},trigger:"axis",formatter:"{b} : {c}元",axisPointer:{type:"shadow"}}},postParams:{},list:[],fieldList:[]}},mixins:[o["a"]],components:{},props:{},computed:{},watch:{},mounted:function(){this.axisOption=Object(a["a"])(Object(a["a"])({},this.axisOption),{},{grid:o["a"].data().chartDefaultOptions.grid,xAxis:[Object(a["a"])(Object(a["a"])({},o["a"].data().chartXAxisStyle),{},{name:"（元）"})],yAxis:[Object(a["a"])(Object(a["a"])({},o["a"].data().chartYAxisStyle),{},{axisLine:{show:!0},type:"category"})],series:[{type:"bar",label:o["a"].data().chartDefaultBase.label,barMaxWidth:20,data:[]}]})},methods:{},deactivated:function(){}}},"6c7a":function(t,n,e){"use strict";e.r(n);var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[e("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-user-select":!1,title:"新增联系人数排行","module-type":"ranking"},on:{load:function(n){t.loading=!0},change:t.getDataList}}),t._v(" "),e("div",{staticClass:"content"},[e("div",{staticClass:"content-title"},[t._v("新增联系人数排行（按负责人、创建时间统计）")]),t._v(" "),e("div",{directives:[{name:"empty",rawName:"v-empty",value:0===t.list.length,expression:"list.length === 0"}],staticClass:"axis-content",attrs:{"xs-empty-text":"暂无排行"}},[e("div",{attrs:{id:"axismain"}})]),t._v(" "),e("div",{staticClass:"table-content"},[e("div",{staticClass:"handle-bar"},[e("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),e("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,"cell-class-name":t.cellClassName,stripe:t.WKConfig.tableStyle.stripe,height:"400","highlight-current-row":""},on:{"row-click":t.handleRowClick}},[e("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"公司总排名"},scopedSlots:t._u([{key:"default",fn:function(n){return[t._v("\n            "+t._s(n.$index+1)+"\n          ")]}}])}),t._v(" "),t._l(t.fieldList,(function(t,n){return e("el-table-column",{key:n,attrs:{prop:t.field,label:t.name,"show-overflow-tooltip":""}})}))],2)],1)]),t._v(" "),e("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(n){t.reportListShow=n}}})],1)},o=[],i=(e("a434"),e("b0c0"),e("26b0")),r=e("b80b"),s=e("313e"),c=e("a475"),l={name:"RankingAddContactsStatistics",mixins:[i["a"],r["a"]],data:function(){return{postParams:{},detailFields:[{name:"count",crmType:"contacts",list:[{formType:"user",name:"ownerUserId",type:3,values:[]}],request:c["a"],params:null}]}},computed:{},mounted:function(){this.fieldList=[{field:"realname",name:"创建人"},{field:"deptName",name:"部门"},{field:"count",name:"新增联系人数（个）"}],this.initAxis()},methods:{getDataList:function(t){var n=this;this.postParams=t,this.loading=!0,Object(c["b"])(t).then((function(t){n.loading=!1,n.list=t.data||[];for(var e=[],a=[],o=t.data.length>10?10:t.data.length,i=0;i<o;i++){var r=t.data[i];e.splice(0,0,parseFloat(r.count)),a.splice(0,0,r.realname)}n.axisOption.yAxis[0].data=a,n.axisOption.series[0].data=e,n.chartObj.setOption(n.axisOption,!0)})).catch((function(){n.loading=!1}))},initAxis:function(){this.chartObj=s["b"](document.getElementById("axismain")),this.axisOption.tooltip.formatter="{b} : {c}个",this.axisOption.xAxis[0].name="（个）",this.chartObj.setOption(this.axisOption,!0)},exportClick:function(){this.requestExportInfo(c["c"],this.postParams)}}},p=l,u=(e("f5f7"),e("2877")),d=Object(u["a"])(p,a,o,!1,null,"333499e2",null);n["default"]=d.exports},a475:function(t,n,e){"use strict";e.d(n,"f",(function(){return o})),e.d(n,"g",(function(){return i})),e.d(n,"l",(function(){return r})),e.d(n,"m",(function(){return s})),e.d(n,"r",(function(){return c})),e.d(n,"s",(function(){return l})),e.d(n,"j",(function(){return p})),e.d(n,"k",(function(){return u})),e.d(n,"d",(function(){return d})),e.d(n,"e",(function(){return h})),e.d(n,"b",(function(){return b})),e.d(n,"c",(function(){return f})),e.d(n,"p",(function(){return m})),e.d(n,"q",(function(){return g})),e.d(n,"n",(function(){return y})),e.d(n,"o",(function(){return x})),e.d(n,"h",(function(){return T})),e.d(n,"i",(function(){return j})),e.d(n,"a",(function(){return C}));var a=e("b775");function o(t){return Object(a["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biRanking/contractRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(a["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biRanking/receivablesRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"biRanking/contractCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(a["a"])({url:"biRanking/productCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"biRanking/productCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(a["a"])({url:"biRanking/customerCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(a["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(a["a"])({url:"biRanking/contactsCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(a["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(a["a"])({url:"biRanking/recordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(a["a"])({url:"biRanking/customerRecordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(a["a"])({url:"biRanking/customerRecordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(a["a"])({url:"biRanking/travelCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(a["a"])({url:"biRanking/travelCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(a["a"])({url:"crmBiSearch/searchContactsPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},e1f9:function(t,n,e){},f5f7:function(t,n,e){"use strict";e("e1f9")}}]);