(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0f925c0d"],{"0499":function(t,e,n){},"20a5":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[n("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-business-select":!0,title:"销售漏斗","module-type":"business"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),n("div",{staticClass:"content"},[t._m(0),t._v(" "),n("div",{staticClass:"table-content"},[t.showTable?n("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,"cell-class-name":t.cellClassName,stripe:t.WKConfig.tableStyle.stripe,"summary-method":t.getSummaries,height:"400","show-summary":"","highlight-current-row":""},on:{"sort-change":function(e){var n=e.prop,a=e.order;return t.mixinSortFn(t.list,n,a)},"row-click":t.handleRowClick}},t._l(t.fieldList,(function(t,e){return n("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,sortable:t.sortable,"show-overflow-tooltip":""}})}))):t._e()],1)]),t._v(" "),n("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},r=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"axis-content"},[n("div",{attrs:{id:"axismain"}})])}],i=n("15fd"),o=(n("4de4"),n("d81d"),n("14d9"),n("13d5"),n("b0c0"),n("e9f5"),n("d866"),n("910d"),n("7d54"),n("ab43"),n("9485"),n("a9e3"),n("b680"),n("d3b7"),n("159b"),n("df55")),s=n("f643"),c=n("b80b"),u=n("313e"),l=n("ef89"),d=n("f2ec"),f=["businessItem"],p={name:"FunnelStatistics",components:{},mixins:[o["a"],s["a"],c["a"]],data:function(){return{loading:!1,list:[],fieldList:[{field:"settingName",name:"阶段"},{field:"businessMoney",name:"金额",sortable:"custom"},{field:"businessNum",name:"项目数",sortable:"custom"}],postParams:{},businessItem:[],detailFields:[{name:"businessNum",flowName:!0,isBusiness:!0,crmType:"business",list:[],request:d["a"],params:null}],funnelOption:null}},computed:{},mounted:function(){this.initAxis()},methods:{getDataList:function(t){var e=this,n=t.businessItem,a=Object(i["a"])(t,f);this.businessItem=n.filter((function(t){return t})).map((function(e){if(e.flowId==t.typeId)return e})),this.postParams=a,this.loading=!0,Object(l["c"])(a).then((function(t){e.loading=!1,e.list=t.data;for(var n=[],a=0,r=0;r<t.data.length;r++){var i=t.data[r];i.settingName={1:"赢单",2:"输单"}[i.isEnd]||i.settingName,n.push({name:(i.settingName||"")+"("+i.businessMoney+"元)",value:parseFloat(i.businessNum)}),a+=parseFloat(i.businessNum)}e.funnelOption.series[0].data=n,e.funnelOption.legend.data=n.map((function(t){return t.name})),e.funnelOption.series[0].max=a<1?1:a,e.chartObj.setOption(e.funnelOption,!0)})).catch((function(){e.loading=!1}))},getSummaries:function(t){var e=t.columns,n=t.data,a=[];return e.forEach((function(t,e){if(0!==e){var r=n.map((function(e){return Number(e[t.property])}));r.every((function(t){return isNaN(t)}))?a[e]="":(a[e]=r.reduce((function(t,e){var n=Number(e);return isNaN(n)?t:t+e}),0),1===e&&(a[e]=a[e].toFixed(2)))}else a[e]="合计"})),a},initAxis:function(){var t=u["b"](document.getElementById("axismain")),e={toolbox:this.toolbox,tooltip:{trigger:"item",formatter:"{b} <br/> 项目个数: {c}个"},calculable:!0,legend:{data:[],formatter:function(t){return t.split("(")[0]},textStyle:{color:this.chartDefaultBase.textColor,fontWeight:this.chartDefaultBase.fontWeight}},grid:{left:0,right:0,bottom:0,top:0},color:this.chartColors,series:[{name:"漏斗图",type:"funnel",left:"20%",width:"56%",sort:"none",gap:2,label:{color:this.chartDefaultBase.textColor,fontWeight:this.chartDefaultBase.fontWeight},labelLine:{length:20,lineStyle:{width:2,type:"solid"}},data:[]}]};t.setOption(e,!0),this.funnelOption=e,this.chartObj=t}}},h=p,m=(n("5c1d"),n("2877")),b=Object(m["a"])(h,a,r,!1,null,"b8775c7a",null);e["default"]=b.exports},"4e82":function(t,e,n){"use strict";var a=n("23e7"),r=n("e330"),i=n("59ed"),o=n("7b0b"),s=n("07fa"),c=n("083a"),u=n("577e"),l=n("d039"),d=n("addb"),f=n("a640"),p=n("3f7e"),h=n("99f4"),m=n("1212"),b=n("ea83"),g=[],v=r(g.sort),y=r(g.push),w=l((function(){g.sort(void 0)})),C=l((function(){g.sort(null)})),T=f("sort"),j=!l((function(){if(m)return m<70;if(!(p&&p>3)){if(h)return!0;if(b)return b<603;var t,e,n,a,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)g.push({k:e+a,v:n})}for(g.sort((function(t,e){return e.v-t.v})),a=0;a<g.length;a++)e=g[a].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),N=w||!C||!T||!j,S=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:u(e)>u(n)?1:-1}};a({target:"Array",proto:!0,forced:N},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(j)return void 0===t?v(e):v(e,t);var n,a,r=[],u=s(e);for(a=0;a<u;a++)a in e&&y(r,e[a]);d(r,S(t)),n=s(r),a=0;while(a<n)e[a]=r[a++];while(a<u)c(e,a++);return e}})},"5c1d":function(t,e,n){"use strict";n("0499")},ef89:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"g",(function(){return o})),n.d(e,"h",(function(){return s})),n.d(e,"c",(function(){return c})),n.d(e,"e",(function(){return u})),n.d(e,"f",(function(){return l})),n.d(e,"d",(function(){return d})),n.d(e,"i",(function(){return f}));var a=n("b775");function r(t){return Object(a["a"])({url:"biAchievement/taskCompleteStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biAchievement/taskCompleteStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(a["a"])({url:"biProduct/productStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biProduct/productStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"crmBiSearch/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"crmBiSearch/searchContractPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(a["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f643:function(t,e,n){"use strict";n("4e82"),n("a9e3"),n("d3b7"),n("25f0");e["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(t))return[];if(!e)return t;function a(t,a){if(t[e]===a[e])return 0;var r=!isNaN(Number(t[e]))&&!isNaN(Number(a[e])),i=r?Number(t[e])<Number(a[e]):t[e]<a[e];return"descending"===n?i?1:-1:i?-1:1}t.sort(a)}}}}}]);