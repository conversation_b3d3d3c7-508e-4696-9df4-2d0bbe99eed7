(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-da9c6778"],{"08c2":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),e._v(" "),a("span",{staticClass:"text"},[e._v(e._s(e.title))])]),e._v(" "),e.showFilterView?[e.showYearSelect?e._e():a("time-type-select",{on:{change:e.timeTypeChange}}),e._v(" "),e.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":e.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:e.yearValue,callback:function(t){e.yearValue=t},expression:"yearValue"}}):e._e(),e._v(" "),e._t("after-time"),e._v(" "),e.showSimpleChoose?[e.showUserSelect&&e.showDeptSelect?a("el-select",{model:{value:e.simpleChooseType,callback:function(t){e.simpleChooseType=t},expression:"simpleChooseType"}},e._l(e.simpleOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),1===e.simpleChooseType&&e.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:e.structuresSelectValue,callback:function(t){e.structuresSelectValue=t},expression:"structuresSelectValue"}}):e._e(),e._v(" "),2===e.simpleChooseType&&e.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:e.userSelectValue,callback:function(t){e.userSelectValue=t},expression:"userSelectValue"}}):e._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:e.dataTypeOptions,"user-checked-data":e.filterValue.userList,"dep-checked-data":e.filterValue.deptList,width:250},on:{select:e.radioMenuSelect},model:{value:e.filterDataType,callback:function(t){e.filterDataType=t},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:e.avatarData.realname,callback:function(t){e.$set(e.avatarData,"realname",t)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),e._v(" "),e.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:e.businessStatusValue,callback:function(t){e.businessStatusValue=t},expression:"businessStatusValue"}},e._l(e.businessOptions,(function(e){return a("el-option",{key:e.flowId,attrs:{label:e.flowName,value:e.flowId}})}))):e._e(),e._v(" "),e.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:e.productValue,callback:function(t){e.productValue=t},expression:"productValue"}}):e._e(),e._v(" "),e.showCustomSelect?a("el-select",{on:{change:e.customSelectChange},model:{value:e.customValue,callback:function(t){e.customValue=t},expression:"customValue"}},e._l(e.customOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):e._e(),e._v(" "),e._t("append"),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(t){return e.emitFilter(t)}}},[e._v("查询")]),e._v(" "),e._t("default")]:e._e()],2)},s=[],n=a("5530"),i=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),r=a("ea20"),l=a("657f"),c=a("bfba"),u=a("8f81"),p=a("83f1"),h=a("2f62"),d={name:"FiltrateHandleView",components:{TimeTypeSelect:l["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:p["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(n["a"])(Object(n["a"])({},Object(h["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var e=(this.filterValue.userList||[]).map((function(e){return e.realname})),t=(this.filterValue.deptList||[]).map((function(e){return e.name}));return{realname:e.concat(t).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var e=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){e.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(e){var t=this;Object(i["r"])().then((function(a){t.businessOptions=a.data||[],t.businessOptions.length>0&&(t.businessStatusValue=t.businessOptions[0].flowId),e(!0)})).catch((function(){t.$emit("error")}))},getProductCategoryIndex:function(){var e=this;Object(r["T"])({type:"tree"}).then((function(t){e.productOptions=t.data})).catch((function(){}))},radioMenuSelect:function(e,t){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=t.users,this.filterValue.deptList=t.strucs)},timeTypeChange:function(e){this.timeTypeValue=e},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var e=this,t={};this.showSimpleChoose?1===this.simpleChooseType?t.deptList=(this.structuresSelectValue||"").split(",").filter((function(e){return!!e})):t.userList=(this.userSelectValue||"").split(",").filter((function(e){return!!e})):"custom"!==this.filterValue.dataType?t.dataType=this.filterValue.dataType:(t.dataType=0,t.deptList=(this.filterValue.deptList||[]).map((function(e){return e.deptId})),t.userList=(this.filterValue.userList||[]).map((function(e){return e.userId}))),this.showYearSelect?(t.dateFilter="custom",t.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(t.startDate=this.timeTypeValue.startTime,t.endDate=this.timeTypeValue.endTime,t.dateFilter="custom"):t.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(t.typeId=this.businessStatusValue,t.businessItem=this.businessOptions.map((function(t){if(t.flowId===e.businessStatusValue)return t}))),this.showProductSelect&&(t.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",t)}}},f=d,m=(a("965d"),a("2877")),b=Object(m["a"])(f,o,s,!1,null,"6d7c8f9a",null);t["a"]=b.exports},2602:function(e,t,a){"use strict";a.d(t,"r",(function(){return s})),a.d(t,"s",(function(){return n})),a.d(t,"t",(function(){return i})),a.d(t,"o",(function(){return r})),a.d(t,"m",(function(){return l})),a.d(t,"n",(function(){return c})),a.d(t,"c",(function(){return u})),a.d(t,"f",(function(){return p})),a.d(t,"g",(function(){return h})),a.d(t,"h",(function(){return d})),a.d(t,"u",(function(){return f})),a.d(t,"v",(function(){return m})),a.d(t,"x",(function(){return b})),a.d(t,"a",(function(){return y})),a.d(t,"b",(function(){return T})),a.d(t,"i",(function(){return C})),a.d(t,"j",(function(){return S})),a.d(t,"p",(function(){return j})),a.d(t,"q",(function(){return g})),a.d(t,"l",(function(){return v})),a.d(t,"k",(function(){return w})),a.d(t,"d",(function(){return O})),a.d(t,"w",(function(){return x})),a.d(t,"e",(function(){return V}));var o=a("b775");function s(e){return Object(o["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(e){return Object(o["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(e){return Object(o["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(e){return Object(o["a"])({url:"biCustomer/customerRecordStats",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(o["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(e){return Object(o["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(e){return Object(o["a"])({url:"biCustomer/customerConversionStats",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(e){return Object(o["a"])({url:"biCustomer/poolStats",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(e){return Object(o["a"])({url:"biCustomer/poolTable",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(e){return Object(o["a"])({url:"biCustomer/poolTableExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(e){return Object(o["a"])({url:"biCustomer/employeeCycle",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(e){return Object(o["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(e){return Object(o["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(e){return Object(o["a"])({url:"biCustomer/districtCycle",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(e){return Object(o["a"])({url:"biCustomer/districtCycleExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(e){return Object(o["a"])({url:"biCustomer/productCycle",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(e){return Object(o["a"])({url:"biCustomer/productCycleExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(e){return Object(o["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(e){return Object(o["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(e){return Object(o["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(e){return Object(o["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:e,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(e){return Object(o["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(e){return Object(o["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function V(e){return Object(o["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"27c4":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return n}));var o=a("b775");function s(e){return Object(o["a"])({url:"biRanking/addressAnalyse",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(e){var t=e.type_analyse;return"industry"===t?Object(o["a"])({url:"biRanking/portrait",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}}):"level"===t?Object(o["a"])({url:"biRanking/portraitLevel",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}}):"source"===t?Object(o["a"])({url:"biRanking/portraitSource",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}}):void 0}},"965d":function(e,t,a){"use strict";a("c558")},c558:function(e,t,a){},df55:function(e,t,a){"use strict";var o=a("5530"),s=(a("d3b7"),a("08c2")),n=a("7a1a"),i=a("ed08"),r=a("a347"),l=a.n(r);t["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},textColor:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},axisLine:{lineStyle:{color:l.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:l.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:s["a"]},props:{},computed:{},watch:{},mounted:function(){var e=this;this.debouncedResize=Object(n["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",e.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(e){this.pageData.limit=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(e){this.pageData.page=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(e,t){return new Promise((function(a,o){e(t).then((function(e){Object(i["g"])(e),a&&a(e)})).catch((function(e){o&&o(e)}))}))},getChartYAxisStyle:function(e){var t=Object(i["D"])(this.chartYAxisStyle);if(!e)return t;for(var a in e){var s=t[a],n=e[a];t[a]=s?Object(o["a"])(Object(o["a"])({},s),n):n}return t}},deactivated:function(){}}}}]);