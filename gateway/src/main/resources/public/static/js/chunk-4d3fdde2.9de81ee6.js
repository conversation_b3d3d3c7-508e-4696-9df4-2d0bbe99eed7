(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4d3fdde2"],{2602:function(t,e,a){"use strict";a.d(e,"r",(function(){return r})),a.d(e,"s",(function(){return o})),a.d(e,"t",(function(){return i})),a.d(e,"o",(function(){return s})),a.d(e,"m",(function(){return c})),a.d(e,"n",(function(){return u})),a.d(e,"c",(function(){return l})),a.d(e,"f",(function(){return d})),a.d(e,"g",(function(){return p})),a.d(e,"h",(function(){return h})),a.d(e,"u",(function(){return f})),a.d(e,"v",(function(){return m})),a.d(e,"x",(function(){return b})),a.d(e,"a",(function(){return C})),a.d(e,"b",(function(){return y})),a.d(e,"i",(function(){return T})),a.d(e,"j",(function(){return j})),a.d(e,"p",(function(){return v})),a.d(e,"q",(function(){return g})),a.d(e,"l",(function(){return x})),a.d(e,"k",(function(){return O})),a.d(e,"d",(function(){return F})),a.d(e,"w",(function(){return w})),a.d(e,"e",(function(){return S}));var n=a("b775");function r(t){return Object(n["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(n["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(n["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(n["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(n["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(n["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(n["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(n["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(n["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(n["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(n["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(n["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(n["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(n["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(n["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function F(t){return Object(n["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(t){return Object(n["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(t){return Object(n["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),s=a("07fa"),c=a("083a"),u=a("577e"),l=a("d039"),d=a("addb"),p=a("a640"),h=a("3f7e"),f=a("99f4"),m=a("1212"),b=a("ea83"),C=[],y=r(C.sort),T=r(C.push),j=l((function(){C.sort(void 0)})),v=l((function(){C.sort(null)})),g=p("sort"),x=!l((function(){if(m)return m<70;if(!(h&&h>3)){if(f)return!0;if(b)return b<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)C.push({k:e+n,v:a})}for(C.sort((function(t,e){return e.v-t.v})),n=0;n<C.length;n++)e=C[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),O=j||!v||!g||!x,F=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:u(e)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:O},{sort:function(t){void 0!==t&&o(t);var e=i(this);if(x)return void 0===t?y(e):y(e,t);var a,n,r=[],u=s(e);for(n=0;n<u;n++)n in e&&T(r,e[n]);d(r,F(t)),a=s(r),n=0;while(n<a)e[n]=r[n++];while(n<u)c(e,n++);return e}})},"535b":function(t,e,a){},8673:function(t,e,a){"use strict";a("535b")},f4f4:function(t,e,a){"use strict";a("d81d"),a("13d5"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("d3b7"),a("159b");e["a"]={data:function(){return{summaryData:null}},methods:{getSummariesData:function(t){this.summaryData=t||{}},getSummaries:function(t){var e=t.columns,a=t.data,n=[];return e.forEach((function(t,e){if(0!==e){var r=a.map((function(e){return Number(e[t.property])}));r.every((function(t){return isNaN(t)}))?n[e]="":n[e]=r.reduce((function(t,e){var a=Number(e);return isNaN(a)?t:t+e}),0)}else n[e]="合计"})),n}}}},f643:function(t,e,a){"use strict";a("4e82"),a("a9e3"),a("d3b7"),a("25f0");e["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(t))return[];if(!e)return t;function n(t,n){if(t[e]===n[e])return 0;var r=!isNaN(Number(t[e]))&&!isNaN(Number(n[e])),o=r?Number(t[e])<Number(n[e]):t[e]<n[e];return"descending"===a?o?1:-1:o?-1:1}t.sort(n)}}}},f83a:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:"公海客户分析","module-type":"customer"},on:{load:function(e){t.loading=!0},change:t.searchClick}}),t._v(" "),a("div",{staticClass:"content"},[t._m(0),t._v(" "),a("div",{staticClass:"table-content"},[a("div",{staticClass:"handle-bar"},[a("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),t.showTable?a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small","cell-class-name":t.cellClassName,data:t.list,stripe:t.WKConfig.tableStyle.stripe,"summary-method":t.getSummaries,height:"400","show-summary":"","highlight-current-row":""},on:{"row-click":t.handleRowClick,"sort-change":function(e){var a=e.prop,n=e.order;return t.mixinSortFn(t.list,a,n)}}},t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,sortable:"custom","show-overflow-tooltip":""}})}))):t._e()],1)]),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},r=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"axis-content"},[a("div",{attrs:{id:"axismain"}})])}],o=a("5530"),i=(a("14d9"),a("df55")),s=a("f643"),c=a("f4f4"),u=a("b80b"),l=a("313e"),d=a("2602"),p={name:"CustomerPoolStatistics",mixins:[i["a"],s["a"],c["a"],u["a"]],data:function(){return{loading:!1,axisOption:null,list:[],postParams:{},dataIndex:null,axisList:[],fieldList:[{field:"realname",name:"姓名"},{field:"deptName",name:"部门"},{field:"receiveNum",name:"公海池领取客户数"},{field:"putInNum",name:"进入公海客户数"}],isSeas:!0,detailFields:[{name:"receiveNum",list:[{formType:"text",name:"isReceive",type:1,values:[2]},{formType:"user",name:"ownerUserId",type:3,values:[]}],request:d["d"],params:null}]}},mounted:function(){this.initAxis()},methods:{searchClick:function(t){this.postParams=t,this.getDataList(),this.getRecordList()},getDataList:function(){var t=this;this.loading=!0,Object(d["f"])(this.postParams).then((function(e){t.loading=!1,t.axisList=e.data||[];for(var a=[],n=[],r=[],o=0;o<e.data.length;o++){var i=e.data[o];a.push(i.putInNum),n.push(i.receiveNum),r.push(i.type)}t.axisOption.xAxis[0].data=r,t.axisOption.series[0].data=a,t.axisOption.series[1].data=n,t.chartObj.setOption(t.axisOption,!0)})).catch((function(){t.loading=!1}))},getRecordList:function(t){var e=this;this.dataIndex=t,this.list=[],this.loading=!0,Object(d["g"])(this.postParams).then((function(t){e.loading=!1,e.list=t.data||[]})).catch((function(){e.loading=!1}))},initAxis:function(){var t=this,e=l["b"](document.getElementById("axismain")),a={color:this.echartLineBarColors,toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(o["a"])({data:["进入公海客户数","公海池领取客户数"]},this.chartDefaultOptions.legend),grid:Object(o["a"])(Object(o["a"])({},this.chartDefaultOptions.grid),{},{right:"40px"}),xAxis:[Object(o["a"])({type:"category",data:[]},this.chartXAxisStyle)],yAxis:[Object(o["a"])({type:"value",name:"进入公海客户数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}个"}})),Object(o["a"])({type:"value",name:"公海池领取客户数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}次"},splitLine:{show:!0}}))],series:[{name:"进入公海客户数",type:"bar",yAxisIndex:0,barMaxWidth:15,data:[]},{name:"公海池领取客户数",type:"bar",yAxisIndex:1,barMaxWidth:15,data:[]}]};e.setOption(a,!0),e.on("click",(function(e){t.getRecordList(e.dataIndex)})),this.axisOption=a,this.chartObj=e},exportClick:function(){this.requestExportInfo(d["h"],this.postParams)}}},h=p,f=(a("8673"),a("2877")),m=Object(f["a"])(h,n,r,!1,null,"444f61dc",null);e["default"]=m.exports}}]);