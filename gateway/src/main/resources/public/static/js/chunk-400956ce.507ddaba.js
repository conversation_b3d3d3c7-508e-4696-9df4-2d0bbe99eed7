(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-400956ce"],{"108c":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("flexbox",{staticClass:"main",attrs:{direction:"column",align:"stretch"}},[i("xr-header",{attrs:{label:"办公审批流"}},[i("el-button",{attrs:{slot:"ft",type:"primary"},on:{click:e.addExamine},slot:"ft"},[e._v("新建审批流程")])],1),e._v(" "),i("div",{staticClass:"main-body"},[i(e.leftType,{tag:"component"})],1)],1)},a=[],s=(i("b0c0"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-table",class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",id:"examine-table",data:e.list,height:e.tableHeight,stripe:e.WKConfig.tableStyle.stripe,"highlight-current-row":""}},[i("el-table-column",{attrs:{width:"100",label:"审批流图标"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{staticClass:"table-icon"},[i("i",{class:e.row.iconClass})])]}}])}),e._v(" "),e._l(e.fieldList,(function(t,n){return i("el-table-column",{key:n,attrs:{formatter:e.fieldFormatter,prop:t.prop,"min-width":t.width,label:t.label,"show-overflow-tooltip":""}})})),e._v(" "),i("el-table-column",{attrs:{fixed:"right",label:"操作",width:"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("edit-table",t)}}},[e._v("编辑表单")]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("edit",t)}}},[e._v("编辑")]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{disabled:1===t.row.isSys,type:"primary-text"},on:{click:function(i){e.handleClick("delete",t)}}},[e._v("删除")]),e._v(" "),i("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:function(i){e.handleClick("change",t)}}},[e._v(e._s(2===t.row["status"]?"启用":"停用"))])]}}])})],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.createShow?i("oa-approve-flow-create",{attrs:{detail:e.rowDetail},on:{success:e.saveSuccess,close:function(t){e.createShow=!1}}}):e._e()],1)}),o=[],l=(i("a15b"),i("d81d"),i("14d9"),i("a434"),i("e9f5"),i("7d54"),i("ab43"),i("d3b7"),i("159b"),i("ffce")),c=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("transition",{attrs:{name:"opacity-fade"}},[i("div",{staticClass:"business-approve-flow-create"},[i("wk-backgroud-tabs",{attrs:{options:e.tabs},model:{value:e.tabIndex,callback:function(t){e.tabIndex=t},expression:"tabIndex"}},[i("template",{slot:"right"},[i("el-button",{attrs:{type:"primary"},on:{click:e.sendClick}},[e._v("发布")]),e._v(" "),i("i",{staticClass:"el-icon-close create-close",on:{click:e.closeClick}})],1)],2),e._v(" "),i("base-info-set",{directives:[{name:"show",rawName:"v-show",value:"base"===e.tabIndex,expression:"tabIndex === 'base'"}],ref:"baseInfoSet",attrs:{fields:e.fields,"fields-form":e.fieldsForm,"fields-rules":e.fieldsRules,"validate-on-rule-change":!1},on:{change:e.formChange},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.data;return[n&&"icon"==n.formType?i("xh-icon-select",{attrs:{value:e.fieldsForm[n.field]},on:{"value-change":function(t){e.oldChange(t,n)}}}):e._e()]}}])}),e._v(" "),i("wk-approve-flow",{directives:[{name:"show",rawName:"v-show",value:"flow"===e.tabIndex,expression:"tabIndex === 'flow'"}],ref:"wkApproveFlow",attrs:{props:e.approveFlowConfig,list:e.flowList,"send-node":e.sendNode}})],1)])},r=[],u=i("5530"),d=(i("99af"),i("4378")),f=i("0904"),h=i("43b6"),p=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"header-icon",style:{backgroundColor:e.iconColor}},[i("i",{class:e.iconClass})]),e._v(" "),i("xh-icon-popover",{ref:"iconPopover",attrs:{visible:e.iconVisible,"select-icon":e.selectIcon},on:{"update:visible":function(t){e.iconVisible=t},select:e.iconSelect}},[i("el-button",{attrs:{slot:"reference",type:e.iconVisible?"selected":""},slot:"reference"},[e._v("选择图标")])],1)],1)},m=[],v=(i("a9e3"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-popover",{attrs:{width:"335","popper-class":"no-padding-popover",trigger:"click"},model:{value:e.showPopover,callback:function(t){e.showPopover=t},expression:"showPopover"}},[i("div",{staticClass:"main"},[i("div",{staticClass:"main__hd"},[e._v("审批流图标")]),e._v(" "),i("flexbox",{staticClass:"main__bd",attrs:{wrap:"wrap"}},e._l(e.iconList,(function(t,n){return i("div",{key:n,staticClass:"icon-list",class:{"is-select":e.currentIcon&&e.currentIcon.icon==t.icon},on:{click:function(i){e.iconSelect(t)}}},[i("div",{staticClass:"icon-list__item",style:{backgroundColor:t.color}},[i("i",{class:t.icon})])])}))),e._v(" "),i("div",{staticClass:"main__ft"},[i("el-button",{attrs:{type:"primary"},on:{click:e.sureClick}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.closeClick}},[e._v("取消")])],1)],1),e._v(" "),e._t("reference",null,{slot:"reference"})],2)}),g=[],b={name:"XhIconPopover",components:{},props:{selectIcon:Object,visible:Boolean},data:function(){return{showPopover:!1,iconList:[],currentIcon:null}},computed:{},watch:{showPopover:{handler:function(e){e&&(this.currentIcon=this.selectIcon),this.$emit("update:visible",this.showPopover)},immediate:!0}},mounted:function(){this.getIconList()},beforeDestroy:function(){},methods:{getIconList:function(){for(var e=["l-record","leave","trip","overtime","reimbursement","go-out"],t=1;t<=20;t++)e.push("approval-".concat(t));for(var i=["#1890ff","#1890ff","#1890ff","#1890ff","#1890ff","#1890ff"],n=[],a=0;a<e.length;a++){var s=e[a],o={icon:"wk wk-".concat(s)};o.color=i[a%6],n.push(o)}this.iconList=n},closeClick:function(){this.showPopover=!1},sureClick:function(){this.currentIcon&&(this.showPopover=!1,this.$emit("select",this.currentIcon.icon,this.currentIcon.color))},iconSelect:function(e){this.currentIcon=e}}},w=b,x=(i("ee16"),i("2877")),y=Object(x["a"])(w,v,g,!1,null,"b94dd418",null),k=y.exports,C={name:"XhIconSelect",components:{XhIconPopover:k},props:{value:{type:String,default:""},index:Number,item:Object},data:function(){return{iconVisible:!1,dataValue:""}},computed:{iconColor:function(){var e=this.dataValue.split(",");return e.length>1?e[1]:"#1890ff"},iconClass:function(){var e=this.dataValue.split(",");return e.length>1?e[0]:"wk wk-approve"},selectIcon:function(){var e=this.dataValue.split(",");return e.length>1?{icon:e[0],color:e[1]}:null}},watch:{value:function(e){this.dataValue=e}},mounted:function(){this.dataValue=this.value},beforeDestroy:function(){},methods:{iconSelect:function(e,t){this.dataValue="".concat(e,",").concat(t),this.valueChange()},valueChange:function(){this.$emit("value-change",{index:this.index,value:this.dataValue})}}},_=C,I=(i("52bc"),Object(x["a"])(_,p,m,!1,null,"e2ee096e",null)),L=I.exports,S=i("ed08"),$=i("07307"),T=i("3817"),j={name:"OaApproveFlowCreate",components:{WkBackgroudTabs:d["a"],BaseInfoSet:f["a"],WkApproveFlow:h["a"],XhIconSelect:L},filters:{},mixins:[$["a"],T["a"]],props:{detail:Object},data:function(){return{loading:!1,height:document.documentElement.clientHeight-100,tabs:[{label:"1.配置基础信息",value:"base"},{label:"2.配置流程",value:"flow"}],tabIndex:"base",fields:[],fieldsForm:{},rangeObj:{},fieldsRules:{},flowList:[]}},computed:{approveFlowConfig:function(){return{conditionSelectRequest:l["k"],conditionSelectParams:{label:0,categoryId:this.examineId}}},sendNode:function(){var e=this.rangeObj,t=e.userList,i=e.deptList,n=i||[],a=n.map((function(e){return e.name})),s=t||[],o=s.map((function(e){return e.realname})),l=a.concat(o).join("、");return{name:"发起人",content:l||"全公司"}},examineId:function(){return this.detail?this.detail.examineId:null}},created:function(){this.getBaseField(),this.detail&&(this.rangeObj={userList:this.detail.userList,deptList:this.detail.deptList}),this.examineId?this.getFlowList(this.examineId):this.flowList=[Object(S["D"])(h["b"])]},mounted:function(){var e=this;this.$el.style.zIndex=Object(S["w"])(),document.body.appendChild(this.$el),window.onresize=function(){e.height=document.documentElement.clientHeight-100}},destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},methods:{getFlowList:function(e){var t=this;Object(l["j"])({examineId:e}).then((function(e){var i=e.data||[],n=[];t.getListInfo(i,n),t.flowList=n})).catch((function(){}))},getBaseField:function(){var e=[];e.push({field:"examineName",formType:"text",isNull:1,name:"审批类型名称",maxlength:20,setting:[],inputTips:"",value:this.detail?this.detail.examineName:""}),e.push({field:"examineIcon",formType:"icon",isNull:1,name:"审批类型图标",setting:[],inputTips:"",value:this.detail?this.detail.examineIcon||"":"wk wk-l-record,#1890ff"}),e.push({field:"dept",formType:"userDep",isNull:0,name:"发起人范围",setting:[],inputTips:"默认全公司",value:{userList:this.detail&&this.detail.userList?this.detail.userList.map((function(e){return e.userId})):[],deptList:this.detail&&this.detail.deptList?this.detail.deptList.map((function(e){return e.id})):[]}}),e.push({field:"recheckType",formType:"select",isNull:0,name:"审批被拒后重新提交",setting:[{name:"返回审批流初始层级",value:1},{name:"跳过审批流已通过的层级，返回拒绝的层级",value:2}],inputTips:"",value:this.detail?this.detail.recheckType:1}),e.push({field:"managerList",formType:"user",isNull:1,name:"审批流管理员",setting:[],radio:!1,tipType:"system-help",inputTips:'<div>1、可以在"配置流程"设置当审批人为空，审批<br>自动转交给审批流管理员；当管理员也请假/离<br>职，审批将转交给超级管理员。</div><div>2、可指定多个管理员，审批方式为或签。</div>',value:this.detail?this.detail.managerList:[],helpType:"26",helpId:"234"}),e.push({field:"remarks",formType:"textarea",isNull:0,name:"审批类型说明",maxlength:200,setting:[],inputTips:"请填写相关注意事项，方便员工在申请时查阅，限制输入200字",value:this.detail?this.detail.remarks:""}),this.handleFields(e)},handleFields:function(e){var t=this,i={},n={};e.forEach((function(e){"userDep"===e.formType?(n.userList=e.value.userList,n.deptList=e.value.deptList):(i[e.field]=t.getRules(e),n[e.field]=e.value)})),this.fields=Object(S["D"])(e),this.fieldsForm=n,this.fieldsRules=i},formChange:function(e,t,i){"userDep"===e.formType&&(this.rangeObj=i)},oldChange:function(e,t){this.$set(this.fieldsForm,t.field,e.value),this.$refs.baseInfoSet.form.validateField(t.field)},sendClick:function(){var e=this;this.$refs.baseInfoSet.validate().then((function(t){if(t){var i=e.$refs.wkApproveFlow.getParams();if(i.isError)e.$message.error("请完善信息");else{var n=Object(u["a"])(Object(u["a"])({},e.fieldsForm),{},{label:0,dataList:i.list});e.examineId&&(n.examineId=e.examineId,n.oaType=e.detail.oaType),e.submiteRequest(n)}}}))},submiteRequest:function(e){var t=this;Object(l["g"])(e).then((function(e){t.$emit("success"),t.examineId?(t.$message.success("创建成功"),t.closeClick()):t.$confirm("您将继续完成审批表单的创建","创建成功",{showCancelButton:!1,closeOnClickModal:!1,closeOnPressEscape:!1,showClose:!1,confirmButtonText:"确定",type:"warning",callback:function(i){t.closeClick(),"confirm"===i&&t.$router.push({name:"workbenchHandlefield",params:{type:"oa_examine",label:"10",id:e.data.examineId}})}})})).catch((function(){}))},closeClick:function(){this.$emit("close")}}},O=j,E=(i("983d"),Object(x["a"])(O,c,r,!1,null,"2d46948a",null)),F=E.exports,P={name:"ExamineManager",components:{OaApproveFlowCreate:F},data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-220,list:[],fieldList:[{prop:"examineName",label:"审批类型名称",width:150},{prop:"userList",label:"发起人范围",width:150},{prop:"recheckType",label:"审批被拒后重新提交",width:150},{prop:"remarks",label:"审批类型说明",width:150},{prop:"updateTime",label:"最后修改时间",width:150},{prop:"status",label:"状态",width:150}],currentPage:1,pageSize:10,pageSizes:[10,20,30,40],total:0,rowDetail:null,createShow:!1}},watch:{},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-220},this.getList()},methods:{saveSuccess:function(){this.currentPage=1,this.getList()},getList:function(){var e=this;this.loading=!0,Object(l["l"])({page:this.currentPage,limit:this.pageSize,label:0}).then((function(t){var i=t.data||{},n=i.list||[];n.forEach((function(e){var t=e.examineIcon?e.examineIcon.split(","):[];t.length>1?(e.iconClass=t[0],e.iconColor=t[1]):(e.iconClass="wk wk-approve",e.iconColor="#1890ff")})),e.list=n,e.total=i.totalRow,e.loading=!1})).catch((function(){e.loading=!1}))},fieldFormatter:function(e,t){if("userList"===t.property){var i=e["deptList"]||[],n=i.map((function(e){return e.name})).join("、"),a=e["userList"]||[],s=a.map((function(e){return e.realname})).join("、");n&&s&&(n+="、");var o=n+s;return o||"全公司"}return"status"===t.property?2===e[t.property]?"停用":"启用":"recheckType"===t.property?{1:"返回审批流初始层级",2:"跳过审批流已通过的层级，返回拒绝的层级"}[e[t.property]]:e[t.property]},addExamine:function(){this.rowDetail=null,this.createShow=!0},handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},handleClick:function(e,t){var i=this;"edit-table"===e?this.$router.push({name:"workbenchHandlefield",params:{type:"oa_examine",label:"10",id:t.row.examineId}}):"edit"===e?(this.rowDetail=t.row,this.createShow=!0):"delete"===e?this.$confirm("您确定要删除该审批流?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i.loading=!0,Object(l["n"])({examineId:t.row["examineId"],status:3}).then((function(e){i.list.splice(t.$index,1),0==i.list.length&&(i.currentPage=i.currentPage-1>0?i.currentPage-1:1),i.getList(),i.$message({type:"success",message:"操作成功"}),i.loading=!1})).catch((function(){i.loading=!1}))})).catch((function(){i.$message({type:"info",message:"已取消删除"})})):"change"===e&&this.$confirm("您确定要"+(2===t.row["status"]?"启用":"停用")+"该审批流?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(l["n"])({examineId:t.row["examineId"],status:2===t.row["status"]?1:2}).then((function(e){i.$message({type:"success",message:"操作成功"}),i.getList()})).catch((function(){}))})).catch((function(){i.$message({type:"info",message:"已取消删除"})}))}}},N=P,z=(i("c067"),Object(x["a"])(N,s,o,!1,null,"3b84e900",null)),B=z.exports,V=i("f468"),D={components:{ExamineManager:B,XrHeader:V["a"]},data:function(){return{leftType:"ExamineManager",leftSides:[{name:"审批类型管理",type:"ExamineManager"}]}},mounted:function(){},methods:{addExamine:function(){var e=this.getChildByName("ExamineManager");e&&e.addExamine()},getChildByName:function(e){var t=function(e,i){for(var n=0;n<i.length;n++){var a=i[n],s=a.$options.name||a.$options._componentTag;if(s===e)return a;if(a.$children&&a.$children.length){var o=t(e,a.$children);if(o)return o}}return null};return t(e,this.$children)}}},H=D,R=(i("6219"),Object(x["a"])(H,n,a,!1,null,"447f13fc",null));t["default"]=R.exports},"23d8":function(e,t,i){},"52bc":function(e,t,i){"use strict";i("b905")},6219:function(e,t,i){"use strict";i("f808")},"6f00":function(e,t,i){},"983d":function(e,t,i){"use strict";i("23d8")},b905:function(e,t,i){},c067:function(e,t,i){"use strict";i("c59c")},c59c:function(e,t,i){},ee16:function(e,t,i){"use strict";i("6f00")},f808:function(e,t,i){}}]);