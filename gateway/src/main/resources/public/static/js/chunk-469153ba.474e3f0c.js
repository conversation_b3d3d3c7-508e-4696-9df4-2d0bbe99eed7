(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-469153ba"],{"06f1":function(e,t,o){"use strict";o("3e71")},"3e71":function(e,t,o){},"4e70":function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("wk-page-header",{attrs:{title:e.config.showModuleName?"公海管理":"",help:e.getHelpObj(e.crmType,"index"),dropdowns:e.headerMoreHandles},on:{command:e.pageHeaderCommand}}),e._v(" "),o("div",{directives:[{name:"empty",rawName:"v-empty",value:!e.indexAuth&&e.poolAuth.index,expression:"!indexAuth && poolAuth.index"}],staticClass:"crm-container",attrs:{"xs-empty-icon":"nopermission","xs-empty-text":"暂无权限"}},[o("wk-table-header",{ref:"tableHeader",attrs:{search:e.search,tabs:e.sceneList,"active-tab":e.sceneId,"selection-list":e.tableSelectionList,operations:e.handleOperations,"condition-type-fun":void 0,fields:e.getFilterFields,props:e.tableHeaderProps.props,"filter-header-props":e.tableHeaderProps.filterHeaderProps,"filter-form-props":e.tableHeaderProps.filterFormProps,"scene-set-props":e.tableHeaderProps.sceneSetProps,"scene-create-props":e.tableHeaderProps.sceneCreateProps},on:{"update:search":function(t){e.search=t},"update:activeTab":function(t){e.sceneId=t},"tabs-change":e.sceneSelect,"operations-click":e.tableOperationsClick,"event-change":e.tableHeaderHandle,"filter-change":e.handleFilter}},[o("template",{slot:"custom"},[o("el-select",{staticStyle:{"margin-left":"10px"},attrs:{mode:"no-border"},on:{change:e.poolChange},model:{value:e.poolId,callback:function(t){e.poolId=t},expression:"poolId"}},e._l(e.poolList,(function(e){return o("el-option",{key:e.poolId,attrs:{label:e.poolName,value:e.poolId}})})))],1)],2),e._v(" "),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.crmTableClass,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,"row-key":e.crmType+"Id",stripe:e.tableStyleObj.stripe,"use-virtual":"","highlight-current-row":""},on:{"row-click":e.handleRowClick,"sort-change":e.sortChange,"header-dragend":e.handleHeaderDragend,"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{"show-overflow-tooltip":"","reserve-selection":"",type:"selection",fixed:"",align:"center",width:"55"}}),e._v(" "),e._l(e.fieldList,(function(t,n){return o("el-table-column",{key:n,attrs:{fixed:1===t.isLock,prop:t.prop,label:t.label,width:t.width,"class-name":t.width>60?"column":"",sortable:"custom","show-overflow-tooltip":""},scopedSlots:e._u([{key:"otherHeader",fn:function(n){return t.width>60?[o("el-button",{staticClass:"el-lock-btn",attrs:{icon:1===t.isLock?"wk wk-unlock":"wk wk-lock",type:"text"},on:{click:function(o){o.stopPropagation(),e.fieldFixed(t)}}}),e._v(" "),e.showFilter(t)?o("el-button",{staticClass:"el-filter-btn",attrs:{type:"text",icon:"wk wk-screening"},on:{click:function(o){o.stopPropagation(),e.showFilterClick(t)}}}):e._e()]:void 0}},{key:"default",fn:function(n){var a=n.row,l=n.column;n.$index;return["dealStatus"==t.prop?[o("span",{class:e._f("dealIcon")(a[t.prop])}),e._v(" "),o("span",[e._v(e._s(e._f("dealName")(a[t.prop])))])]:"status"==t.prop?[2==a.status?o("i",{staticClass:"wk wk-circle-password customer-lock"}):e._e()]:o("wk-field-view",{attrs:{props:t,"form-type":t.formType,value:a[l.property]},scopedSlots:e._u([{key:"default",fn:function(o){o.data;return[e._v("\n              "+e._s(e.fieldFormatter(a,l,a[l.property],t))+"\n            ")]}}])})]}}])})})),e._v(" "),o("el-table-column"),e._v(" "),o("field-set",{attrs:{slot:"other","is-seas":e.isSeas,"crm-type":e.crmType,"pool-id":e.poolId},on:{change:e.setSave},slot:"other"})],2),e._v(" "),o("div",{staticClass:"pagination-container"},[o("el-dropdown",{attrs:{trigger:"click",placement:"top"}},[o("el-button",{staticClass:"dropdown-btn"},[o("i",{staticClass:"el-icon-s-fold"})]),e._v(" "),o("el-dropdown-menu",{staticClass:"wk-table-style-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[o("el-dropdown-item",[o("span",{on:{click:function(e){e.stopPropagation()}}},[o("el-switch",{model:{value:e.tableStyleObj.rightBorderShow,callback:function(t){e.$set(e.tableStyleObj,"rightBorderShow",t)},expression:"tableStyleObj.rightBorderShow"}}),e._v("显示竖向分割线")],1)]),e._v(" "),o("el-dropdown-item",[o("span",{on:{click:function(e){e.stopPropagation()}}},[o("el-switch",{model:{value:e.tableStyleObj.bottomBorderShow,callback:function(t){e.$set(e.tableStyleObj,"bottomBorderShow",t)},expression:"tableStyleObj.bottomBorderShow"}}),e._v("显示横向分割线")],1)]),e._v(" "),o("el-dropdown-item",[o("span",{on:{click:function(e){e.stopPropagation()}}},[o("el-switch",{model:{value:e.tableStyleObj.stripe,callback:function(t){e.$set(e.tableStyleObj,"stripe",t)},expression:"tableStyleObj.stripe"}}),e._v("显示斑马纹")],1)])],1)],1),e._v(" "),o("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.showDview?o("customer-detail",{staticClass:"d-view",attrs:{id:e.rowID,"page-list":e.crmType==e.rowType?e.list:[],"page-index":e.rowIndex,"pool-id":e.poolId,"is-seas":e.isSeas},on:{"update:id":function(t){e.rowID=t},"update:pageIndex":function(t){e.rowIndex=t},handle:e.handleHandle,"hide-view":function(t){e.showDview=!1}}}):e._e(),e._v(" "),o("alloc-handle",{attrs:{"crm-type":e.crmType,"pool-id":e.poolId,"selection-list":e.selectionList,"dialog-visible":e.allocDialogShow},on:{"update:dialogVisible":function(t){e.allocDialogShow=t},handle:e.handleHandle}})],1)},a=[],l=(o("d81d"),o("14d9"),o("e9f5"),o("ab43"),o("d3b7"),o("ec3a")),i=o("d5b3"),s=o("5868"),r=o("e505"),c={name:"SeacIndex",components:{CustomerDetail:i["a"],AllocHandle:s["a"]},filters:{dealIcon:function(e){return 1==e?"deal-suc":"deal-un"},dealName:function(e){return 1==e?"已成交":"未成交"}},mixins:[r["a"]],data:function(){return{crmType:"customer",isSeas:!0,poolId:"",poolAuth:{},poolList:[],allocDialogShow:!1}},computed:{headerMoreHandles:function(){var e=[];return this.poolId&&this.poolAuth&&this.poolAuth.excelexport&&(e.push({command:"enter",name:"导入",icon:"wk wk-import"}),e.push({command:"out",name:"导出",icon:"wk wk-export"})),this.$auth("manage.crm.pool")&&e.push({command:"seasSet",name:"公海规则",icon:"wk wk-manage"}),e},handleOperations:function(){return this.getOperations(["alloc","get","export","delete"])}},watch:{poolId:{handler:function(e){e&&this.getCustomerPoolAuth(e)},deep:!0,immediate:!0}},created:function(){this.getPoolList()},activated:function(){this.isRequested&&this.getList()},deactivated:function(){},methods:{tableOperationsClick:function(e){var t=this;"alloc"==e?this.allocDialogShow=!0:"get"===e?this.$confirm("确定要领取该客户吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(l["Z"])({ids:t.selectionList.map((function(e){return e.customerId})),poolId:t.poolId}).then((function(o){t.loading=!1,t.$message({type:"success",message:"操作成功"}),t.$store.dispatch("GetMessageNum"),t.handleHandle({type:e})})).catch((function(){t.loading=!1}))})).catch((function(){})):"export"===e?this.$wkExport.export(this.crmType,{params:{poolId:this.poolId,ids:this.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]}))},request:l["x"],isSeas:this.isSeas,poolId:this.poolId}):"delete"===e&&this.$confirm("若客户下有联系人，联系人将一并删除。确定删除？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(l["u"])({ids:t.selectionList.map((function(e){return e["".concat(t.crmType,"Id")]})),poolId:t.poolId}).then((function(o){t.loading=!1,t.$message({type:"success",message:"删除成功"}),t.handleHandle({type:e})})).catch((function(){t.handleHandle({type:e}),t.loading=!1}))})).catch((function(){}))},getCustomerPoolAuth:function(e){var t=this;Object(l["B"])({poolId:e}).then((function(e){t.poolAuth=e.data||{}})).catch((function(){}))},menuSelect:function(e,t){this.$emit("menu-select",e,t)},getPoolList:function(){var e=this;Object(l["A"])().then((function(t){e.poolList=t.data||[],e.poolId=e.poolList.length>0?e.poolList[0].poolId:"",e.getFieldList(),e.tableHeaderProps=e.getBaseTableHeaderProps()})).catch((function(){}))},poolChange:function(){this.$refs.tableHeader.initFieldList(),this.currentPage=1,this.getFieldList(!0)},cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"customerName"===t.property?"can-visit--underline":"businessCheck"===t.property?"can-visit":""}}},d=c,p=(o("06f1"),o("2877")),u=Object(p["a"])(d,n,a,!1,null,"e625a9dc",null);t["default"]=u.exports}}]);