(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70a94779"],{"1fbf":function(e,t,s){"use strict";s("85d6")},2415:function(e,t,s){},"4dd8":function(e,t,s){"use strict";var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-dialog",{ref:"wkDialog",attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,width:"500px"},on:{close:e.close}},[s("div",{staticClass:"el-dialog__title",attrs:{slot:"title"},slot:"title"},[e._v("\n    "+e._s(e.title)),e.userShow?s("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[s("div",{attrs:{slot:"content"},slot:"content"},[e._v("1、可以将员工角色复制给其他员工。"),s("br"),e._v("\n        2、若选择的员工已有角色，原角色会被覆盖。"),s("br"),e._v("\n        3、若选择部门，该部门所有员工的角色将相同，"),s("br"),e._v("\n             可保存后再对员工独立调整。\n      ")]),e._v(" "),s("i",{staticClass:"wk wk-help wk-help-tips",staticStyle:{"margin-left":"3px"}})]):e._e()],1),e._v(" "),s("el-form",{ref:"editRoleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px","label-position":"top"}},[e.userShow?s("el-form-item",{attrs:{label:"选择员工和部门",prop:"userIds"}},[s("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":e.ruleForm.userIds,"dep-value":e.ruleForm.deptIds},on:{"update:userValue":function(t){e.$set(e.ruleForm,"userIds",t)},"update:depValue":function(t){e.$set(e.ruleForm,"deptIds",t)}}})],1):e._e(),e._v(" "),s("el-form-item",{attrs:{label:"设置角色",prop:"roleList"}},[s("role-employee-select",{staticStyle:{width:"100%"},attrs:{props:e.roleSelectProps,multiple:""},model:{value:e.ruleForm.roleList,callback:function(t){e.$set(e.ruleForm,"roleList",t)},expression:"ruleForm.roleList"}})],1)],1),e._v(" "),s("div",{}),e._v(" "),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.sureClick}},[e._v("确定")]),e._v(" "),s("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)},a=[],n=(s("d9e2"),s("99af"),s("a630"),s("caad"),s("d81d"),s("14d9"),s("e9f5"),s("7d54"),s("ab43"),s("d3b7"),s("6062"),s("1e70"),s("79a4"),s("c1a1"),s("8b00"),s("a4e7"),s("1e5a"),s("72c3"),s("2532"),s("3ca3"),s("159b"),s("ddb0"),s("8d55")),o=s("612a"),l=s("94d4"),r=s("b592"),c=s("9dba"),d={name:"EditRoleDialog",components:{RoleEmployeeSelect:l["a"],WkUserDepDialogSelect:r["a"]},mixins:[c["a"]],props:{selectionList:Array,userShow:{type:Boolean,default:!0},visible:{type:Boolean,required:!0,default:!1}},data:function(){return{loading:!1,roleValue:[],ruleForm:{roleList:[],userIds:[],deptIds:[]}}},computed:{title:function(){return this.userShow?"复制角色":"编辑角色"},rules:function(){var e=this,t=function(t,s,i){e.ruleForm.userIds&&e.ruleForm.userIds.length>0||e.ruleForm.deptIds&&e.ruleForm.deptIds.length>0?i():i(new Error("请选择"))},s={roleList:[{required:!0,message:"请选择",trigger:"change"}]};return this.userShow&&(s.userIds=[{validator:t,trigger:""}]),s},roleSelectProps:function(){return{roleRequest:o["a"]}}},watch:{},created:function(){if(this.userShow&&this.selectionList.length>0||!this.userShow&&1===this.selectionList.length){var e=this.selectionList[0];this.ruleForm.roleList=e.roleId?this.selectionList[0].roleId.split(","):[]}},methods:{close:function(){this.$emit("update:visible",!1)},sureClick:function(){var e=this;this.$refs.editRoleForm.validate((function(t){if(!t)return!1;var s=[],i=[];e.ruleForm.roleList.forEach((function(e){if(e.includes("@")){var t=e.split("@");if(t.length>1){var a=t[1].split(",");i=i.concat(a)}}else s.push(e)}));var a=Array.from(new Set(s.concat(i))),o={roleIds:a};e.userShow?(o.userIds=e.ruleForm.userIds,o.deptIds=e.ruleForm.deptIds):o.userIds=e.selectionList.map((function(e){return e.userId})),Object(n["b"])(o).then((function(t){e.$message.success("操作成功"),e.$emit("change"),e.close()})).catch((function(){}))}))}}},u=d,p=(s("b5c2"),s("2877")),h=Object(p["a"])(u,i,a,!1,null,"2b9dc912",null);t["a"]=h.exports},"575d":function(e,t,s){},"5a80":function(e,t,s){"use strict";s("a946")},"5dde":function(e,t,s){"use strict";s("2415")},"612a":function(e,t,s){"use strict";s.d(t,"k",(function(){return a})),s.d(t,"l",(function(){return n})),s.d(t,"m",(function(){return o})),s.d(t,"p",(function(){return l})),s.d(t,"o",(function(){return r})),s.d(t,"n",(function(){return c})),s.d(t,"c",(function(){return d})),s.d(t,"a",(function(){return u})),s.d(t,"b",(function(){return p})),s.d(t,"d",(function(){return h})),s.d(t,"h",(function(){return m})),s.d(t,"i",(function(){return f})),s.d(t,"g",(function(){return v})),s.d(t,"t",(function(){return g})),s.d(t,"s",(function(){return b})),s.d(t,"r",(function(){return w})),s.d(t,"q",(function(){return y})),s.d(t,"j",(function(){return C})),s.d(t,"f",(function(){return k})),s.d(t,"e",(function(){return _}));s("e9f5"),s("7d54"),s("b64b"),s("d3b7"),s("159b");var i=s("b775");function a(e){return Object(i["a"])({url:"adminDept/deleteDept/"+e.id,method:"post"})}function n(e){return Object(i["a"])({url:"adminDept/setDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(e){return Object(i["a"])({url:"adminDept/addDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(i["a"])({url:"adminUser/setUser",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(e){return Object(i["a"])({url:"adminUser/addUser",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(e){return Object(i["a"])({url:"adminRole/getAllRoleList",method:"post",data:e})}function d(){return Object(i["a"])({url:"adminRole/queryDefaultRole",method:"post"})}function u(e){return Object(i["a"])({url:"adminRole/getRoleList",method:"post",data:e})}function p(e){return Object(i["a"])({url:"adminRole/queryAuthRole/".concat(e),method:"post"})}function h(e,t){return Object(i["a"])({url:"adminRole/updateAuthRole/".concat(e),method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(e){return Object(i["a"])({url:"adminUser/resetPassword",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(e){return Object(i["a"])({url:"adminUser/usernameEdit",method:"post",data:e})}function v(e){return Object(i["a"])({url:"adminUser/usernameEditByManager",method:"post",data:e})}function g(e){return Object(i["a"])({url:"adminUser/setUserStatus",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(e){return Object(i["a"])({url:"adminUser/downloadExcel",method:"post",data:e,responseType:"blob"})}function w(e){var t=new FormData;return Object.keys(e).forEach((function(s){t.append(s,e[s])})),Object(i["a"])({url:"adminUser/excelImport",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"},timeout:6e4})}function y(e){return Object(i["a"])({url:"adminUser/downExcel",method:"post",data:e,responseType:"blob"})}function C(e){return Object(i["a"])({url:"crmCall/authorize",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function k(e){return Object(i["a"])({url:"adminUser/setUserDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function _(){return Object(i["a"])({url:"adminUser/countNumOfUser",method:"post"})}},"649eb":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"employee-dep-management main"},[i("xr-header",[i("template",{slot:"label"},[e._v("员工与部门管理"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"21","data-id":"170"},on:{click:function(e){e.stopPropagation()}}})]),e._v(" "),i("el-input",{staticClass:"search-input",attrs:{slot:"ft",placeholder:"请输入员工名称/手机号"},on:{blur:e.headerSearch},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.headerSearch(t)}},slot:"ft",model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}},[i("el-button",{attrs:{slot:"suffix",type:"icon",icon:"wk wk-sousuo"},nativeOn:{click:function(t){return e.headerSearch(t)}},slot:"suffix"})],1),e._v(" "),e.userSaveAuth?i("el-button",{staticClass:"add-user-btn",attrs:{slot:"ft",type:"primary"},on:{click:e.addEmployee},slot:"ft"},[e._v("添加员工")]):e._e()],2),e._v(" "),i("div",{staticClass:"main-content-wrap"},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.depLoading,expression:"depLoading"}],staticClass:"main-nav"},[i("div",{staticClass:"main-nav__title"},[e._v("企业组织架构"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"21","data-id":"169"},on:{click:function(e){e.stopPropagation()}}}),e._v(" "),e.strucSaveAuth?i("el-button",{staticClass:"add-btn",attrs:{type:"text",icon:"el-icon-plus"},on:{click:e.addStruc}},[e._v("创建部门")]):e._e()],1),e._v(" "),i("div",{staticClass:"main-nav__content"},[i("div",{staticClass:"nav-sections-wrap"},[i("div",{staticClass:"nav-section is-padding"},[i("div",{staticClass:"nav-section__content is-top-padding"},[i("el-tree",{ref:"tree",attrs:{data:e.showDepData,"expand-on-click-node":!1,props:{label:"name"},"node-key":"deptId","highlight-current":"","default-expand-all":""},on:{"node-click":e.changeDepClick},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.node,a=t.data;return i("span",{},[1==s.level?i("i",{staticClass:"wk wk-customer",staticStyle:{"margin-right":"4px"}}):e._e(),e._v(e._s(a.name)+"\n                ")])}}])})],1)])])])]),e._v(" "),i("div",{staticClass:"main-content flex-index"},[0===e.selectionList.length?i("flexbox",{staticClass:"table-top",attrs:{justify:"space-between"}},[i("div",{staticClass:"table-top__title"},[i("span",[e._v(e._s(""+(e.currentMenuData?e.currentMenuData.name:"")))]),e._v(" "),i("span",{staticClass:"des"},[e._v("所有员工"),i("span",{staticClass:"value"},[e._v(e._s(e.userCountObj.allUserCount||0))]),e._v("人，已激活"),i("span",{staticClass:"value"},[e._v(e._s(e.userCountObj.activateCount||0))]),e._v("人，未激活"),i("span",{staticClass:"value"},[e._v(e._s(e.userCountObj.inactiveCount||0))]),e._v("人，停用"),i("span",{staticClass:"value"},[e._v(e._s(e.userCountObj.disableCount||0))]),e._v("人")])]),e._v(" "),i("div",{staticClass:"table-top__ft"},[e.isDepUserShow?i("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:e.refreshUserList},model:{value:e.isNeedChild,callback:function(t){e.isNeedChild=t},expression:"isNeedChild"}},[e._v("包含子部门")]):e._e(),e._v(" "),i("el-select",{staticStyle:{width:"120px","margin-left":"8px"},attrs:{mode:"no-border"},on:{change:e.refreshUserList},model:{value:e.employeeType,callback:function(t){e.employeeType=t},expression:"employeeType"}},e._l(e.employeeMenu,(function(e){return i("el-option",{key:e.type,attrs:{label:e.label,value:e.type}})}))),e._v(" "),e.isApplyUser?i("el-select",{staticStyle:{width:"120px","margin-left":"8px"},attrs:{mode:"no-border"},on:{change:e.refreshUserList},model:{value:e.applyType,callback:function(t){e.applyType=t},expression:"applyType"}},e._l([{label:"待审核邀请",value:1},{label:"已审核邀请",value:2}],(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),e.strucSaveAuth&&e.currentMenuData&&e.currentMenuData.deptId?i("el-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"text"},on:{click:function(t){e.appendStruc(e.currentMenuData)}}},[e._v("创建子部门")]):e._e(),e._v(" "),e.currentMenuData&&e.currentMenuData.deptId&&e.strucMoreOptions.length>0?i("el-dropdown",{attrs:{trigger:"click"},on:{command:e.strucMoreHandleClick}},[i("el-button",{staticClass:"dropdown-btn",attrs:{icon:"el-icon-more"}}),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.strucMoreOptions,(function(t,s){return i("el-dropdown-item",{key:s,attrs:{command:t.type}},[e._v(e._s(t.name))])})))],1):e._e()],1)]):i("flexbox",{staticClass:"selection-bar"},[i("div",{staticClass:"selected—title"},[e._v("已选中 "),i("span",{staticClass:"selected—count"},[e._v(e._s(e.selectionList.length))]),e._v(" 项")]),e._v(" "),i("flexbox",{staticClass:"selection-items-box"},e._l(e.selectionInfo,(function(t,s){return i("el-button",{key:s,attrs:{icon:e._f("wkIconPre")(t.icon),size:"medium"},nativeOn:{click:function(s){e.selectionBarClick(t.type)}}},[e._v(e._s(t.name))])})))],1),e._v(" "),i("div",{staticClass:"flex-box"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],class:e.WKConfig.tableStyle.class,attrs:{size:"small",id:"depTable",data:e.tableData,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight},on:{"selection-change":e.handleSelectionChange,"row-click":e.rowClick}},[e.tableUpdateAuth?i("el-table-column",{attrs:{type:"selection",width:"55",fixed:""}}):e._e(),e._v(" "),e.call?i("el-table-column",{attrs:{prop:"call",align:"right","min-width":"36"},scopedSlots:e._u([{key:"header",fn:function(e){return[i("i",{staticClass:"el-icon-phone"})]}},{key:"default",fn:function(t){return[1===t.row.hisTable?i("i",{staticClass:"el-icon-phone",staticStyle:{color:"rgb(70, 205, 207)"}}):e._e()]}}])}):e._e(),e._v(" "),e.isApplyUser?e._e():i("el-table-column",{attrs:{prop:"status","min-width":"80","show-overflow-tooltip":"",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;t.column;return[i("el-tag",{attrs:{color:e.getStatusColor(s.status),"disable-transitions":""}},[i("span",[e._v(e._s(e.getStatusName(s.status)))])])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"realname","min-width":"150","show-overflow-tooltip":"",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;t.column;return[i("span",{staticClass:"status-name"},[i("span",[e._v(e._s(s.realname))]),e._v(" "),0===s.userIdentity?i("el-tag",{attrs:{"disable-transitions":"",type:"warning"}},[e._v("主账号")]):e._e(),e._v(" "),1===s.userIdentity?i("el-tag",{attrs:{"disable-transitions":"",type:"warning"}},[e._v("负责人")]):e._e()],1)]}}])}),e._v(" "),e._l(e.currentFieldList,(function(t,s){return i("el-table-column",{key:s,attrs:{"min-width":t.width,prop:t.field,label:t.value,formatter:e.tableFormatter,"show-overflow-tooltip":""}})}))],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)]),e._v(" "),i("el-dialog",{attrs:{visible:e.depCreateDialog,"close-on-click-modal":!1,title:e.depCreateTitle,"before-close":e.depCreateClose,width:"500px"},on:{"update:visible":function(t){e.depCreateDialog=t}}},[i("flexbox",{staticClass:"nav-dialog-div"},[i("label",[e._v(e._s(e.depCreateLabel)+"：")]),e._v(" "),i("el-input",{attrs:{maxlength:20,placeholder:"请输入内容"},model:{value:e.depCreateLabelValue,callback:function(t){e.depCreateLabelValue=t},expression:"depCreateLabelValue"}})],1),e._v(" "),0!=e.depSelect?i("flexbox",{staticClass:"nav-dialog-div"},[i("label",[e._v("上级部门：")]),e._v(" "),i("el-select",{attrs:{clearable:!1},model:{value:e.depSelect,callback:function(t){e.depSelect=t},expression:"depSelect"}},e._l(e.superDepList,(function(e){return i("el-option",{key:e.deptId,attrs:{label:e.name,value:e.deptId}})})))],1):e._e(),e._v(" "),i("flexbox",{staticClass:"nav-dialog-div"},[i("label",[e._v("部门负责人：")]),e._v(" "),i("wk-user-dialog-select",{attrs:{radio:""},model:{value:e.depOwnerUserId,callback:function(t){e.depOwnerUserId=t},expression:"depOwnerUserId"}})],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitDialog}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:function(t){e.depCreateDialog=!1}}},[e._v("取消")])],1)],1),e._v(" "),e.employeeDetailDialog?i("employee-detail",{attrs:{data:e.selectUserObj,"page-list":e.tableData},on:{"update:data":function(t){e.selectUserObj=t},edit:e.editBtn,command:e.handleCommand,"hide-view":function(t){e.employeeDetailDialog=!1}}}):e._e(),e._v(" "),i("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{visible:e.resetPasswordVisible,"close-on-click-modal":!1,"modal-append-to-body":!1,"before-close":e.resetPasswordClose,title:"重置密码",width:"500px"},on:{"update:visible":function(t){e.resetPasswordVisible=t}}},[i("el-form",{ref:"passForm",attrs:{model:e.passForm,rules:e.rules}},[i("el-form-item",{staticClass:"wk-form-item",attrs:{label:"密码",prop:"password"}},[i("el-input",{attrs:{type:"password"},model:{value:e.passForm.password,callback:function(t){e.$set(e.passForm,"password",t)},expression:"passForm.password"}})],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.passSubmit(e.passForm)}}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.resetPasswordClose}},[e._v("取消")])],1)],1),e._v(" "),i("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{visible:e.resetUserNameVisible,"close-on-click-modal":!1,"modal-append-to-body":!1,"before-close":function(){e.resetUserNameVisible=!1},title:"重置登录账号",width:"500px"},on:{"update:visible":function(t){e.resetUserNameVisible=t}}},[i("div",{staticClass:"el-password"},[i("el-form",{ref:"resetUserNameForm",attrs:{model:e.resetUserNameForm,rules:e.dialogRules}},[i("el-form-item",{staticClass:"wk-form-item",attrs:{label:"新账号（手机号）",prop:"username"}},[i("el-input",{model:{value:e.resetUserNameForm.username,callback:function(t){e.$set(e.resetUserNameForm,"username",t)},expression:"resetUserNameForm.username"}})],1),e._v(" "),i("el-form-item",{staticClass:"wk-form-item",attrs:{label:"新密码",prop:"password"}},[i("el-input",{attrs:{type:"password"},model:{value:e.resetUserNameForm.password,callback:function(t){e.$set(e.resetUserNameForm,"password",t)},expression:"resetUserNameForm.password"}})],1),e._v(" "),e.isManageReset?[i("el-popover",{attrs:{disabled:e.slideVerifyPass||!e.canSlideVerify,placement:"top-start",width:"332","popper-class":"no-padding-popover",trigger:"click"},model:{value:e.slideVerifyShow,callback:function(t){e.slideVerifyShow=t},expression:"slideVerifyShow"}},[i("slide-verify",{attrs:{phone:e.resetUserNameForm.username,props:e.slideVerifyProps,"slider-text":"向右滑动"},on:{success:e.sliderSuccess,fail:e.sliderFail,refresh:e.sliderRefresh,close:function(t){e.slideVerifyShow=!1}}}),e._v(" "),i("div",{staticClass:"verify-picture",class:{success:e.slideVerifyPass},attrs:{slot:"reference"},slot:"reference"},[e.slideVerifyPass?[i("img",{staticClass:"icon",attrs:{src:s("d679"),alt:""}}),e._v(" "),i("span",{staticClass:"text"},[e._v("验证成功")])]:[i("img",{staticClass:"icon",attrs:{src:s("b64d"),alt:""}}),e._v(" "),i("span",{staticClass:"text"},[e._v("点击完成验证")])]],2)],1),e._v(" "),i("el-form-item",{staticClass:"wk-form-item"},[i("div",{staticClass:"sms-box"},[i("el-input",{ref:"smscode",attrs:{placeholder:"请输入短信验证码"},model:{value:e.resetUserNameForm.smscode,callback:function(t){e.$set(e.resetUserNameForm,"smscode","string"===typeof t?t.trim():t)},expression:"resetUserNameForm.smscode"}}),e._v(" "),i("el-button",{attrs:{disabled:e.codeTime!==e.codeSecond},on:{click:e.getSmsCode}},[i("div",{staticClass:"btn-content"},[e.codeTime===e.codeSecond?[i("span",[e._v("获取验证码")])]:[i("span",[e._v("重新发送("+e._s(e.codeSecond)+"s)")])]],2)])],1)])]:e._e()],2),e._v(" "),i("div",{staticClass:"tips",staticStyle:{"margin-top":"20px"}},[e._v("重置登录帐号后，员工需用新账号登录。请及时告知员工，确保正常使用")])],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.passUserNameSubmit(e.resetUserNameForm)}}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:function(){e.resetUserNameVisible=!1}}},[e._v("取消")])],1)]),e._v(" "),e.employeeCreateDialog?i("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.userEditTitle,visible:e.employeeCreateDialog,"close-on-click-modal":!1,"modal-append-to-body":!0,"append-to-body":!0,"before-close":e.newHandleClose,width:"700px"},on:{"update:visible":function(t){e.employeeCreateDialog=t}}},[i("el-form",{ref:"dialogRef",staticClass:"wk-form",attrs:{model:e.userEditForm,rules:e.dialogRules,"label-position":"top"}},e._l(e.tableList,(function(t,s){return i("el-form-item",{key:s,staticClass:"wk-form-item",attrs:{label:t.value,prop:t.field}},[i("span",{attrs:{slot:"label"},slot:"label"},[e._v(e._s(t.value))]),e._v(" "),t.helpType?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{slot:"label","data-type":t.helpType,"data-id":t.helpId},on:{click:function(e){e.stopPropagation()}},slot:"label"}):e._e(),e._v(" "),"select"==t.type?[i("el-select",{attrs:{filterable:""},model:{value:e.userEditForm[t.field],callback:function(s){e.$set(e.userEditForm,t.field,s)},expression:"userEditForm[item.field]"}},e._l(e.optionsList[t.field],(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})))]:"user"==t.type?[i("wk-user-dialog-select",{attrs:{radio:""},model:{value:e.userEditForm[t.field],callback:function(s){e.$set(e.userEditForm,t.field,s)},expression:"userEditForm[item.field]"}})]:"structure"==t.type?[i("wk-dept-dialog-select",{attrs:{radio:""},on:{change:e.depChange},model:{value:e.userEditForm[t.field],callback:function(s){e.$set(e.userEditForm,t.field,s)},expression:"userEditForm[item.field]"}})]:"selectCheckout"==t.type?[i("el-select",{attrs:{"popper-append-to-body":!1,disabled:e.selectUserObj&&0===e.selectUserObj.userIdentity||t.disable,"popper-class":"select-popper-class",filterable:"",multiple:""},model:{value:e.userEditForm[t.field],callback:function(s){e.$set(e.userEditForm,t.field,s)},expression:"userEditForm[item.field]"}},e._l(e.groupsList,(function(t){return i("el-option-group",{key:t.parentId,attrs:{label:t.name}},e._l(t.list,(function(e){return i("el-option",{key:e.roleId,attrs:{label:e.roleName,value:e.roleId}})})))})))]:i("el-input",{attrs:{maxlength:100,disabled:"编辑员工"==e.userEditTitle&&"username"==t.field},model:{value:e.userEditForm[t.field],callback:function(s){e.$set(e.userEditForm,t.field,s)},expression:"userEditForm[item.field]"}})],2)}))),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.newDialogSubmit}},[e._v("保存")]),e._v(" "),i("el-button",{on:{click:function(t){e.employeeCreateDialog=!1}}},[e._v("取消")])],1)],1):e._e(),e._v(" "),i("el-dialog",{attrs:{visible:e.callVisible,title:"提示",width:"500px"},on:{"update:visible":function(t){e.callVisible=t}}},[i("span",[e._v(" 这些员工账号将被开启呼叫中心，是否继续?")]),e._v(" "),i("div",{staticStyle:{"margin-top":"20px"}},[i("el-radio",{attrs:{label:1},model:{value:e.callType,callback:function(t){e.callType=t},expression:"callType"}},[e._v("软呼")]),e._v(" "),i("el-radio",{attrs:{label:0},model:{value:e.callType,callback:function(t){e.callType=t},expression:"callType"}},[e._v("硬呼（单卡）")]),e._v(" "),i("el-radio",{attrs:{label:2},model:{value:e.callType,callback:function(t){e.callType=t},expression:"callType"}},[e._v("硬呼（多卡）")])],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.setCall}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:function(t){e.callVisible=!1}}},[e._v("取消")])],1)]),e._v(" "),i("el-dialog",{attrs:{visible:e.callInfoVisible,title:"修改成功",width:"500px"},on:{"update:visible":function(t){e.callInfoVisible=t}}},[e._l(e.callInfo,(function(t,s){return[i("div",{key:s},[e._v("\n        "+e._s("姓名："+t.realName+"，座机号："+t.agent)+"\n      ")])]})),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.callInfoVisible=!1}}},[e._v("确定")])],1)],2),e._v(" "),i("bulk-import-user",{attrs:{show:e.bulkImportShow},on:{close:function(t){e.bulkImportShow=!1},success:e.addSuccess}}),e._v(" "),e.editRoleDialogShow?i("edit-role-dialog",{attrs:{"user-show":"copyRole"===e.editRoleType,"selection-list":e.selectionList,visible:e.editRoleDialogShow},on:{"update:visible":function(t){e.editRoleDialogShow=t},change:e.getUserList}}):e._e(),e._v(" "),e.editDepDialogShow?i("edit-dep-dialog",{attrs:{"selection-list":e.selectionList,visible:e.editDepDialogShow},on:{"update:visible":function(t){e.editDepDialogShow=t},change:e.getUserList}}):e._e(),e._v(" "),e.resetDialogVisible?i("reset-dialog",{attrs:{visible:e.resetDialogVisible,phone:e.selectionList[0].mobile},on:{"update:visible":function(t){e.resetDialogVisible=t}}}):e._e()],1)},a=[],n=(s("a4d3"),s("e01a"),s("d28b"),s("d9e2"),s("d3b7"),s("3ca3"),s("ddb0"),s("53ca"));function o(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],s=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&s>=e.length&&(e=void 0),{value:e&&e[s++],done:!e}}}}throw new TypeError(Object(n["a"])(e)+" is not iterable")}var l=s("c14f"),r=s("1da1"),c=s("5530"),d=(s("99af"),s("4de4"),s("a630"),s("caad"),s("a15b"),s("d81d"),s("14d9"),s("b0c0"),s("e9f5"),s("910d"),s("7d54"),s("ab43"),s("ac1f"),s("00b4"),s("6062"),s("1e70"),s("79a4"),s("c1a1"),s("8b00"),s("a4e7"),s("1e5a"),s("72c3"),s("2532"),s("159b"),s("612a")),u=s("dea6"),p=s("2934"),h=s("7ded"),m=s("2f62"),f=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-dialog",{attrs:{visible:e.showDialog,"append-to-body":!0,"show-close":e.showCancel,"close-on-click-modal":!1,title:"批量导入",width:"750px"},on:{"update:visible":function(t){e.showDialog=t},close:e.closeView}},[s("div",{staticClass:"dialog-body"},[s("el-steps",{attrs:{active:e.stepsActive,simple:""}},e._l(e.stepList,(function(e,t){return s("el-step",{key:t,attrs:{title:e.title,icon:e.icon,status:e.status}})}))),e._v(" "),1==e.stepsActive?s("div",{staticClass:"sections"},[s("div",[e._v("请选择需要导入的文件")]),e._v(" "),s("div",{staticClass:"content"},[s("flexbox",{staticClass:"file-select"},[s("el-input",{attrs:{disabled:!0},model:{value:e.file.name,callback:function(t){e.$set(e.file,"name",t)},expression:"file.name"}}),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:e.selectFile}},[e._v("选择文件")])],1)],1),e._v(" "),s("div",{staticClass:"download",on:{click:e.download}},[e._v("\n        点击下载《员工导入模板》")]),e._v(" "),s("div",{staticClass:"content content-tips"},[s("div",[e._v("操作步骤：")]),e._v(" "),s("div",[e._v("1、下载《员工导入模板》")]),e._v(" "),s("div",[e._v("2、打开《员工导入模板》将对应字段信息输入或粘贴进本表。为保障粘贴信息被有效导入，请使用纯文本或数字")]),e._v(" "),s("div",[e._v("3、信息输入完毕，点击“选择文件”按钮，选择excel文件上传")]),e._v(" "),s("div",[e._v("4、点击“确定”开始进行员工导入")])])]):2==e.stepsActive?s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"sections",attrs:{"element-loading-text":"数据导入中","element-loading-spinner":"el-icon-loading"}}):3==e.stepsActive?s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"sections"},[s("div",{staticClass:"result-info"},[s("i",{staticClass:"wk wk-success result-info__icon"}),e._v(" "),s("p",{staticClass:"result-info__des"},[e._v("数据导入完成")]),e._v(" "),s("p",{staticClass:"result-info__detail"},[e._v("导入总数据"),s("span",{staticClass:"result-info__detail--all"},[e._v(e._s(e.resultData.totalSize))]),e._v("条，导入成功"),s("span",{staticClass:"result-info__detail--suc"},[e.resultData?[e._v(e._s(e.resultData.totalSize-(e.resultData.errSize||0)))]:e._e()],2),e._v("条，导入失败"),s("span",{staticClass:"result-info__detail--err"},[e._v(e._s(e.resultData.errSize||0))]),e._v("条")]),e._v(" "),e.resultData&&e.resultData.errSize>0?s("el-button",{staticClass:"result-info__btn--err",staticStyle:{padding:"0"},attrs:{type:"primary-text"},on:{click:e.downloadErrData}},[e._v("下载错误数据")]):e._e()],1)]):e._e(),e._v(" "),s("input",{ref:"userFileInput",attrs:{id:"import-input-file",type:"file"},on:{change:e.uploadFile}})],1),e._v(" "),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.sureTitle?s("el-button",{attrs:{type:"primary"},on:{click:e.sureClick}},[e._v(e._s(e.sureTitle))]):e._e(),e._v(" "),s("el-button",{class:{"is-hidden":!e.showCancel},on:{click:e.closeView}},[e._v("取消")])],1)])},v=[],g=s("ed08"),b={name:"BulkImportUser",components:{},props:{show:{type:Boolean,default:!1},crmType:{type:String,default:""}},data:function(){return{loading:!1,showDialog:!1,file:{name:""},stepsActive:1,stepList:[{icon:"wk wk-upload",title:"上传文件",status:"wait"},{icon:"wk wk-data-import",title:"导入数据",status:"wait"},{icon:"wk wk-success",title:"导入完成",status:"wait"}],resultData:null}},computed:{sureTitle:function(){return{1:"立即导入",2:"",3:"确定"}[this.stepsActive]},showCancel:function(){return 2!=this.stepsActive}},watch:{show:function(e){this.showDialog=e,this.resetData()}},mounted:function(){},methods:{sureClick:function(){var e=this;1==this.stepsActive?"finish"==this.stepList[0].status?(this.stepList[1].status="process",this.stepsActive=2,this.updateFile((function(t){e.stepList[1].status="finish",e.stepsActive=3,t.data&&(e.resultData=t.data,t.data.errSize>0?e.stepList[2].status="error":e.stepList[2].status="finish")}))):this.$message.error("请选择导入文件"):3==this.stepsActive&&this.closeView()},updateFile:function(e){var t=this;this.file.name?(this.loading=!0,Object(d["r"])({file:this.file}).then((function(s){t.loading=!1,e&&e(s),t.$emit("success")})).catch((function(){e&&e(!1),t.loading=!1}))):this.$message.error("请选择导入文件")},downloadErrData:function(){this.getImportError(this.resultData.token)},getImportError:function(e){var t=this;this.loading=!0,Object(d["q"])({token:e}).then((function(e){Object(g["g"])(e),t.loading=!1})).catch((function(){t.loading=!1}))},download:function(){Object(d["s"])().then((function(e){Object(g["g"])(e)})).catch((function(){}))},selectFile:function(){this.$refs.userFileInput.click()},uploadFile:function(e){var t=e.target.files,s=t[0];Object(g["K"])(s.name)&&(this.file=s,this.stepList[0].status="finish"),e.target.value=""},closeView:function(){this.$emit("close")},resetData:function(){this.file={name:""},this.stepList=[{icon:"wk wk-upload",title:"上传文件",status:"wait"},{icon:"wk wk-data-import",title:"导入数据",status:"wait"},{icon:"wk wk-success",title:"导入完成",status:"wait"}],this.stepsActive=1,this.resultData=null}}},w=b,y=(s("f52db"),s("2877")),C=Object(y["a"])(w,f,v,!1,null,"18442058",null),k=C.exports,_=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("slide-view",{staticClass:"d-view",attrs:{"listener-ids":["manager-main-container"],"no-listener-class":["el-table__body"],"body-style":{padding:"0",height:"100%"}},on:{close:e.hideView}},[s("flexbox",{staticClass:"main",attrs:{orient:"vertical"}},[s("div",{staticClass:"detail-body"},[s("div",{staticClass:"content-header"},[s("flexbox",{staticClass:"dialog-top"},[s("xr-avatar",{key:e.data.realname,staticClass:"user-img",attrs:{name:e.data.realname,size:48,src:e.data.img}}),e._v(" "),s("div",{staticClass:"user-name"},[e._v(e._s(e.data.realname)),e.pageList&&e.pageList.length>1?s("el-button-group",{staticClass:"wk-header-page-btn"},[s("el-button",{attrs:{type:"subtle",icon:"el-icon-arrow-left"},on:{click:function(t){e.pageChange("left")}}}),e._v(" "),s("el-button",{attrs:{type:"subtle",icon:"el-icon-arrow-right"},on:{click:function(t){e.pageChange("right")}}})],1):e._e()],1),e._v(" "),s("div",{staticClass:"dialog-btn-group"},[e.userUpdateAuth?s("el-button",{attrs:{type:"primary"},on:{click:e.editBtn}},[e._v(" 编辑 ")]):e._e(),e._v(" "),e.userUpdateAuth||e.userEnablesAuth?s("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleCommand}},[s("el-button",{staticClass:"dropdown-btn",attrs:{icon:"el-icon-more"}}),e._v(" "),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e.userUpdateAuth?s("el-dropdown-item",{attrs:{command:"reset"}},[e._v("重置密码")]):e._e(),e._v(" "),e.userEnablesAuth?s("el-dropdown-item",{attrs:{command:"status"}},[e._v(e._s(0===e.data.status?"激 活":"禁 用"))]):e._e()],1)],1):e._e()],1)],1),e._v(" "),s("div",{staticClass:"dialog-remark"},[s("p",[s("span",[e._v("账号状态：")]),e._v(e._s(e.userStatusName))]),e._v(" "),s("p",[s("span",[e._v("创建时间：")]),e._v(e._s(e.data.createTime))])])],1),e._v(" "),s("div",{staticClass:"dialog-content"},e._l(e.detailList,(function(t,i){return s("flexbox",{key:i,staticClass:"content-items",attrs:{align:"stretch"}},[s("div",{staticClass:"content-items-name"},[e._v(e._s(t.value))]),e._v(" "),s("div",{staticClass:"content-items-value"},[e._v(e._s(e._f("formatedInfo")(e.data,t.field)))])])})))])])],1)},D=[],I=s("130f"),S={name:"EmployeeDetail",components:{SlideView:I["a"]},filters:{formatedInfo:function(e,t){return"sex"==t?{1:"男",2:"女"}[e.sex]:e[t]}},props:{pageList:Array,data:Object},data:function(){return{pageIndex:0,detailList:[{field:"username",value:"手机号（登录名）"},{field:"realname",value:"姓名"},{field:"sex",value:"性别",type:"select"},{field:"email",value:"邮箱"},{field:"deptName",value:"部门",type:"select"},{field:"post",value:"岗位"},{field:"parentName",value:"直属上级",type:"select"},{field:"roleName",value:"角色",type:"selectCheckout"}]}},computed:Object(c["a"])(Object(c["a"])({},Object(m["b"])(["manage"])),{},{userUpdateAuth:function(){return this.manage&&this.manage.users&&this.manage.users.userUpdate},userEnablesAuth:function(){return this.manage&&this.manage.users&&this.manage.users.userEnables},userStatusName:function(){return this.data?{0:"禁用",1:"激活",2:"未激活"}[this.data.status]:""}}),watch:{data:{handler:function(){var e=this;this.data&&this.$nextTick((function(){for(var t=0;t<e.pageList.length;t++){var s=e.pageList[t];if(s.userId===e.data.userId){e.pageIndex=t;break}}}))},immediate:!0}},mounted:function(){},methods:{editBtn:function(){this.$emit("edit")},handleCommand:function(e){this.$emit("command",e)},hideView:function(){this.$emit("hide-view")},pageChange:function(e){"left"===e?this.pageIndex>0?(--this.pageIndex,this.$emit("update:data",this.pageList[this.pageIndex])):this.$message.error("没有更多了"):this.pageIndex<this.pageList.length-1?(++this.pageIndex,this.$emit("update:data",this.pageList[this.pageIndex])):this.$message.error("没有更多了")}}},U=S,x=(s("f5bd"),Object(y["a"])(U,_,D,!1,null,"758158ad",null)),A=x.exports,j=s("f468"),L=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.showLoading,expression:"showLoading"}],staticClass:"slide-verify",attrs:{id:"slideVerify",onselectstart:"return false;"},on:{click:function(e){e.stopPropagation()}}},[s("div",{staticClass:"close-box"},[s("span",{staticClass:"el-icon-close",on:{click:function(t){e.$emit("close")}}})]),e._v(" "),e._m(0),e._v(" "),s("div",{staticClass:"verify-info"},[s("div",{staticClass:"verify-img",style:{backgroundImage:"url("+e.verifyImg+")",transform:"rotate("+e.rotate+"deg)"}})]),e._v(" "),s("div",{staticClass:"slide-verify-slider",class:{"container-active":e.containerActive,"container-success":e.containerSuccess,"container-fail":e.containerFail}},[s("div",{staticClass:"slide-verify-slider-mask",style:{width:e.sliderMaskWidth}},[s("div",{staticClass:"slide-verify-slider-mask-item",style:{left:e.sliderLeft},on:{mousedown:e.sliderDown,touchstart:e.touchStartEvent,touchmove:e.touchMoveEvent,touchend:e.touchEndEvent}},[s("div",{staticClass:"slide-verify-slider-mask-item-icon"})])]),e._v(" "),s("span",{staticClass:"slide-verify-slider-text"},[e._v(e._s(e.sliderText))])])])},F=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"verify-title"},[s("div",{staticClass:"title"},[e._v("\n      身份验证\n    ")]),e._v(" "),s("div",{staticClass:"desc"},[e._v("\n      拖动滑块，使图片角度为正\n    ")])])}],O=s("8ed6"),R={verifyRequest:null},E={name:"SlideVerify",props:{phone:String,props:Object,sliderText:{type:String,default:"Slide filled right"}},data:function(){return{w:300,containerActive:!1,containerSuccess:!1,containerFail:!1,verifyImg:this.getRandomImg(),originX:void 0,isMouseDown:!1,sliderLeft:0,rotate:0,sliderMaskWidth:0,showLoading:!1}},computed:{config:function(){return Object(O["a"])(Object(c["a"])({},R),this.props||{})}},mounted:function(){this.init()},methods:{init:function(){this.bindEvents()},getRandomImg:function(){return"/api/captcha/".concat(this.getRandomNumberByRange(1,20))},getRandomNumberByRange:function(e,t){return Math.round(Math.random()*(t-e)+e)},refresh:function(){this.reset(),this.$emit("refresh")},sliderDown:function(e){this.originX=e.clientX,this.isMouseDown=!0},touchStartEvent:function(e){this.originX=e.changedTouches[0].pageX,this.isMouseDown=!0},bindEvents:function(){var e=this;document.addEventListener("mousemove",(function(t){if(!e.isMouseDown)return!1;var s=t.clientX-e.originX;if(s<0||s+38>=e.w)return!1;if(e.sliderLeft=s+"px",s>0){var i=parseInt(s/260*360);e.rotate=i>360?360:i}else e.rotate=0;e.containerActive=!0,e.sliderMaskWidth=s+"px"})),document.addEventListener("mouseup",(function(t){return!!e.isMouseDown&&(e.isMouseDown=!1,t.clientX!==e.originX&&(e.containerActive=!1,void e.verify()))}))},touchMoveEvent:function(e){if(!this.isMouseDown)return!1;var t=e.pageX-this.originX;if(t<0||t+38>=this.w)return!1;if(this.sliderLeft=t+"px",t>0){var s=parseInt(t/260*360);this.rotate=s>360?360:s}else this.rotate=0;this.containerActive=!0,this.sliderMaskWidth=t+"px"},touchEndEvent:function(e){return!!this.isMouseDown&&(this.isMouseDown=!1,e.pageX!==this.originX&&(this.containerActive=!1,void this.verify()))},verify:function(){var e=this;this.showLoading=!0,this.config.verifyRequest({tel:this.phone,angle:this.rotate}).then((function(t){e.showLoading=!1,1==t.data?(e.containerSuccess=!0,e.$emit("success")):(e.containerFail=!0,e.$emit("fail"),setTimeout((function(){e.reset()}),1e3))})).catch((function(){e.showLoading=!1,e.reset()}))},reset:function(){this.containerActive=!1,this.containerSuccess=!1,this.containerFail=!1,this.sliderLeft=0,this.sliderMaskWidth=0,this.rotate=0,this.verifyImg=this.getRandomImg()}}},T=E,N=(s("8410"),Object(y["a"])(T,L,F,!1,null,"18ba5b13",null)),M=N.exports,V=s("4dd8"),P=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-dialog",{ref:"wkDialog",attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,title:"重置部门",width:"500px"},on:{close:e.handleCancel}},[s("div",[s("el-form",{ref:"form",attrs:{model:e.fieldFrom,rules:e.rules,"label-position":"top"}},[s("el-form-item",{attrs:{label:"部门",prop:"deptId"}},[s("wk-dept-dialog-select",{staticStyle:{width:"100%"},attrs:{radio:""},model:{value:e.fieldFrom.deptId,callback:function(t){e.$set(e.fieldFrom,"deptId",t)},expression:"fieldFrom.deptId"}})],1)],1)],1),e._v(" "),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),s("el-button",{nativeOn:{click:function(t){return e.handleCancel(t)}}},[e._v("取消")])],1)])},z=[],B=s("bfba"),G=s("9dba"),Z={name:"EditDepDialog",components:{WkDeptDialogSelect:B["a"]},mixins:[G["a"]],props:{selectionList:Array,visible:{type:Boolean,required:!0,default:!1}},data:function(){return{fieldFrom:{deptId:""},rules:{deptId:{required:!0,message:"请选择",trigger:"change"}}}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{handleCancel:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this;this.$refs.form.validate((function(t){if(t){var s=e.selectionList.map((function(e){return e.userId}));e.loading=!0,Object(d["f"])(Object(c["a"])(Object(c["a"])({},e.fieldFrom),{},{userIdList:s})).then((function(t){e.loading=!1,e.$message.success("操作成功"),e.$emit("change"),e.handleCancel()})).catch((function(){e.loading=!1}))}}))}}},J=Z,Y=(s("1fbf"),Object(y["a"])(J,P,z,!1,null,"24817196",null)),W=Y.exports,$=s("8f81"),X=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-dialog",{attrs:{title:"重置主账号",visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,"before-close":e.handleClose,width:"750px"},on:{"update:visible":function(t){e.visible=t}}},[s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"dialog-body"},[s("el-steps",{attrs:{active:e.active,simple:""}},e._l(e.stepList,(function(e,t){return s("el-step",{key:t,attrs:{title:e.title,icon:e.icon,status:e.status}})}))),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:1===e.active,expression:"active === 1"}],staticClass:"reset-body"},[s("div",{staticClass:"reset-body-title"},[e._v("为了保护你的账号安全，请先验证当前账号，验证成功后进行下一步操作")]),e._v(" "),s("el-form",{ref:"oldForm",attrs:{model:e.oldForm,rules:e.oldRules,"label-position":"top"}},[s("el-form-item",{attrs:{label:"当前手机号",prop:"oldPhone"}},[s("el-input",{attrs:{disabled:!0},model:{value:e.oldForm.oldPhone,callback:function(t){e.$set(e.oldForm,"oldPhone",t)},expression:"oldForm.oldPhone"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"验证码",prop:"oldSmsCode"}},[s("el-input",{model:{value:e.oldForm.oldSmsCode,callback:function(t){e.$set(e.oldForm,"oldSmsCode",t)},expression:"oldForm.oldSmsCode"}},[s("el-button",{staticClass:"sms-btn",attrs:{slot:"suffix",type:"primary",disabled:e.time!==e.second},on:{click:e.getOldSmsCode},slot:"suffix"},[e.time===e.second?[s("span",[e._v("获取验证码")])]:[s("span",[e._v("重新发送("+e._s(e.second)+"s)")])]],2)],1)],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"50px"}},[s("el-button",{on:{click:e.handleClose}},[e._v("取消")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:e.resetFirst}},[e._v("下一步")])],1)],1),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:2===e.active,expression:"active === 2"}],staticClass:"reset-body"},[s("el-form",{ref:"newForm",attrs:{model:e.newForm,rules:e.newRules,"label-position":"top"}},[s("el-form-item",{attrs:{label:"新手机号",prop:"newPhone"}},[s("el-input",{model:{value:e.newForm.newPhone,callback:function(t){e.$set(e.newForm,"newPhone",t)},expression:"newForm.newPhone"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"验证码",prop:"newSmsCode"}},[s("el-input",{model:{value:e.newForm.newSmsCode,callback:function(t){e.$set(e.newForm,"newSmsCode",t)},expression:"newForm.newSmsCode"}},[s("el-button",{staticClass:"sms-btn",attrs:{slot:"suffix",type:"primary",disabled:!e.ruleResult.newPhone||e.time!==e.second},on:{click:e.getNewSmsCode},slot:"suffix"},[e.time===e.second?[s("span",[e._v("获取验证码")])]:[s("span",[e._v("重新发送("+e._s(e.second)+"s)")])]],2)],1)],1),e._v(" "),s("el-form-item",{attrs:{label:"设置新密码",prop:"newPassword"}},[s("el-input",{attrs:{type:e.passwordType},model:{value:e.newForm.newPassword,callback:function(t){e.$set(e.newForm,"newPassword",t)},expression:"newForm.newPassword"}},[s("i",{staticClass:"wk wk-icon-eye-solid",class:[""===e.passwordType?"canSee":"cannotSeee"],attrs:{slot:"suffix"},on:{click:function(t){e.passwordType=""===e.passwordType?"password":""}},slot:"suffix"})])],1),e._v(" "),s("el-form-item",{attrs:{label:"重复新密码",prop:"newPasswordCheck"}},[s("el-input",{attrs:{type:e.passwordCheckType},model:{value:e.newForm.newPasswordCheck,callback:function(t){e.$set(e.newForm,"newPasswordCheck",t)},expression:"newForm.newPasswordCheck"}},[s("i",{staticClass:"wk wk-icon-eye-solid",class:[""===e.passwordCheckType?"canSee":"cannotSeee"],attrs:{slot:"suffix"},on:{click:function(t){e.passwordCheckType=""===e.passwordCheckType?"password":""}},slot:"suffix"})])],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer"},[s("el-button",{on:{click:function(t){e.active=1}}},[e._v("上一步")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:e.resetSecend}},[e._v("确定")])],1)],1),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:3===e.active,expression:"active === 3"}]},[s("div",{staticClass:"reset-body"},[s("i",{staticClass:"wk wk-success reset-body-icon"}),e._v(" "),s("div",{staticClass:"reset-body-success"},[e._v("主账号重置完成，点击确定按钮后将自动退出到系统登录页面，")]),e._v(" "),s("div",{staticClass:"reset-body-success"},[e._v("重新登录以验证新账号及密码。")]),e._v(" "),s("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"100px"}},[s("el-button",{attrs:{type:"primary"},on:{click:e.reload}},[e._v("确定并退出到登录页")])],1)])])],1)])},H=[],q={props:{visible:{type:Boolean,required:!0,default:!1},phone:{type:String,required:!0}},data:function(){var e=this,t=function(t,s,i){s!==e.newForm.newPassword?i(new Error("两次输入密码不一致!")):i()};return{dialogVisible:!0,loading:!1,active:1,time:60,second:60,timer:null,passwordType:"password",passwordCheckType:"password",stepList:[{icon:"wk wk-icon-user",title:"验证当前账号",status:"wait"},{icon:"wk wk-icon-user-add",title:"设置新账号",status:"wait"},{icon:"wk wk-icon-success2",title:"重置完成",status:"wait"}],oldForm:{oldPhone:"",oldSmsCode:""},newForm:{newPassword:"",newPasswordCheck:"",newPhone:"",newSmsCode:""},oldRules:{oldPhone:[{required:!0,trigger:"blur"}],oldSmsCode:[{required:!0,message:"短信验证码不能为空",trigger:"change"},{required:!0,message:"短信验证码不能为空",trigger:"blur"}]},newRules:{newPhone:[{required:!0,message:"手机号码不能为空",trigger:"change"},{pattern:g["d"],message:"目前只支持中国大陆的手机号码",trigger:"change"}],newSmsCode:[{required:!0,message:"短信验证码不能为空",trigger:"change"}],newPassword:[{required:!0,message:"密码不能为空",trigger:"change"},{pattern:g["c"],message:"密码必须由6-20位字母、数字组成",trigger:"change"}],newPasswordCheck:[{required:!0,message:"密码不能为空",trigger:"change"},{validator:t,trigger:"blur"}]},ruleResult:{newPhone:null,newPassword:null,newSmsCode:null,newPasswordChec:null}}},watch:{newForm:{handler:function(){var e=this;this.$nextTick((function(){e.changeFormState()}))},deep:!0}},mounted:function(){this.phone&&(this.oldForm.oldPhone=this.phone)},beforeDestroy:function(){this.timer&&(clearTimeout(this.timer),this.second=this.time)},methods:{handleClose:function(){var e=this;this.$confirm("确认关闭？").then((function(){e.$emit("update:visible",!1)}))},getOldSmsCode:function(){var e=this;Object(h["d"])({telephone:this.oldForm.oldPhone,type:2}).then((function(){e.startTimer(),e.stepList[0].status="process "}))},getNewSmsCode:function(){var e=this;Object(h["d"])({telephone:this.newForm.newPhone,type:1}).then((function(){e.startTimer(),e.stepList[1].status="process "}))},changeFormState:function(){var e=this,t=this.$refs.newForm;t.$children.forEach((function(t){t.prop&&(e.ruleResult[t.prop]="success"===t.validateState)}))},resetFirst:function(){var e=this;this.loading=!0,this.$refs["oldForm"].validate((function(t){t?Object(h["f"])({phone:e.oldForm.oldPhone,smsCode:e.oldForm.oldSmsCode}).then((function(t){1===t.data?(e.active=2,e.stepList[0].status="finish",e.timer&&(clearTimeout(e.timer),e.second=e.time)):e.$message.error("验证码错误"),e.loading=!1,e.active=2})):e.loading=!1}))},resetSecend:function(){var e=this;this.loading=!0,this.$refs["newForm"].validate((function(t){t?Object(h["c"])({newPassword:e.newForm.newPassword,newPhone:e.newForm.newPhone,newVerifyCode:e.newForm.newSmsCode,oldPhone:e.oldForm.oldPhone,oldVerifyCode:e.oldForm.oldSmsCode}).then((function(t){e.active=3,e.stepList[1].status="finish",e.stepList[2].status="finish",e.timer&&(clearTimeout(e.timer),e.second=e.time),e.loading=!1})):e.loading=!1}))},startTimer:function(){var e=this;this.second===this.time&&this.second--,this.timer=setTimeout((function(){e.second--,e.second>=0?e.startTimer():(clearTimeout(e.timer),e.timer=null,e.second=e.time)}),1e3)},reload:function(){location.reload()}}},Q=q,K=(s("7e9e"),Object(y["a"])(Q,X,H,!1,null,"1a198235",null)),ee=K.exports,te=s("6bfe"),se={name:"EmployeeDep",components:{EmployeeDetail:A,BulkImportUser:k,XrHeader:j["a"],SlideVerify:M,EditRoleDialog:V["a"],EditDepDialog:W,ResetDialog:ee,WkUserDialogSelect:$["a"],WkDeptDialogSelect:B["a"]},data:function(){return{userCountObj:{},employeeType:"all",employeeMenu:[{icon:"wk wk-employees",label:"所有员工",type:"all",field:"allUserCount",count:0,tips:""},{icon:"wk wk-new-employee",label:"新加入的员工",type:"new",field:"addNewlyCount",count:0,tips:"入职7天内的员工"},{icon:"wk wk-active-employee",label:"激活员工",type:"active",field:"activateCount",count:0,tips:"已经登录系统的员工"},{icon:"wk wk-inactive-employee",label:"未激活员工",type:"inactive",field:"inactiveCount",count:0,tips:"未登录过系统的员工"},{icon:"wk wk-disable-employees",label:"停用员工",type:"disable",field:"disableCount",count:0,tips:"已禁用的员工，无法登录系统"},{icon:"",label:"邀请成员审核",type:"applyUser",field:"applyUserCount",count:0,tips:""}],applyType:1,isStartCall:!0,depCreateTitle:"新建",depCreateDialog:!1,depSelect:"",superDepList:[],depCreateLabel:"",depOwnerUserId:"",allDepData:[],showDepData:[],depLoading:!1,loading:!1,searchInput:"",fieldList:[{field:"username",value:"手机号（登录名）",width:"150"},{field:"sex",value:"性别",type:"select",width:"50"},{field:"email",value:"邮箱",width:"150"},{field:"deptName",value:"部门",type:"select",width:"100"},{field:"post",value:"岗位",width:"150"},{field:"parentName",value:"直属上级",type:"select",width:"150"},{field:"roleName",value:"角色",type:"selectCheckout",width:"150"}],selectionList:[],tableData:[],tableHeight:document.documentElement.clientHeight-240,currentMenuData:null,currentPage:1,isNeedChild:1,pageSize:15,pageSizes:[15,30,45,60],total:0,employeeDetailDialog:!1,selectUserObj:{},employeeCreateDialog:!1,userEditTitle:"新建员工",userEditForm:{},depCreateLabelValue:"",treeEditId:"",optionsList:{sex:[{id:0,name:"请选择"},{id:1,name:"男"},{id:2,name:"女"}]},groupsList:[],groupsIdList:[],resetPasswordVisible:!1,rules:{password:[{required:!0,message:"请输入密码",trigger:"blur"}],username:[{required:!0,message:"手机号不能为空",trigger:"blur"}]},passForm:{},dialogRules:{realname:[{required:!0,message:"姓名不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],username:[{required:!0,message:"手机号码不能为空",trigger:"blur"}],email:[{pattern:g["j"],message:"请输入正确的邮箱格式",trigger:"blur"}],deptId:[{required:!0,message:"部门不能为空",trigger:"change"}]},resetUserNameVisible:!1,resetDialogVisible:!1,resetUserNameForm:{username:"",password:""},isManageReset:!1,slideVerifyShow:!1,slideVerifyPass:!1,codeTime:60,codeSecond:60,codeTimer:null,bulkImportShow:!1,editRoleType:"",editRoleDialogShow:!1,editDepDialogShow:!1,callVisible:!1,callType:1,callInfoVisible:!1,callInfo:[],defaultRole:null}},computed:Object(c["a"])(Object(c["a"])({},Object(m["b"])(["manage","call"])),{},{userSaveAuth:function(){return this.manage&&this.manage.users&&this.manage.users.userSave},userUpdateAuth:function(){return this.manage&&this.manage.users&&this.manage.users.userUpdate},userEnablesAuth:function(){return this.manage&&this.manage.users&&this.manage.users.userEnables},tableUpdateAuth:function(){return this.userEnablesAuth||this.userUpdateAuth||this.userUpdateRoleAuth},isApplyUser:function(){return"applyUser"===this.employeeType},strucSaveAuth:function(){return this.manage&&this.manage.users&&this.manage.users.deptSave},strucUpdateAuth:function(){return this.manage&&this.manage.users&&this.manage.users.deptUpdate},strucDeleteAuth:function(){return this.currentMenuData&&"0"!=this.currentMenuData.parentId&&this.manage&&this.manage.users&&this.manage.users.deptDelete},userUpdateRoleAuth:function(){return this.manage&&this.manage.permission},strucMoreOptions:function(){var e=[];return this.strucUpdateAuth&&e.push({type:"edit",name:"编辑部门",icon:"edit"}),this.strucDeleteAuth&&e.push({type:"delete",name:"删除部门",icon:"delete"}),e},currentFieldList:function(){var e=[];return this.isApplyUser?(e.push({field:"username",value:"手机号（登录名）",width:"150"}),e.push({field:"inviteUserName",value:"邀请人",width:"150"})):e=this.fieldList,e},selectionInfo:function(){var e=[],t=[];if(this.isApplyUser)e=[{name:"通过",type:"pass",icon:"wk wk-success"},{name:"拒绝",type:"refuse",icon:"wk wk-close"}];else{if(this.userEnablesAuth&&(e=[{name:"禁用",type:"lock",icon:"wk wk-remove"},{name:"激活",type:"unlock",icon:"wk wk-activation"}]),this.isStartCall&&(t=[{name:"启用呼叫中心",type:"setCall",icon:"wk wk-activation"},{name:"停用呼叫中心",type:"stopCall",icon:"wk wk-remove"}],e=e.concat(t)),this.userUpdateAuth&&(e=1===this.selectionList.length?e.concat([{name:"编辑",type:"edit",icon:"wk wk-edit"},{name:"重置密码",type:"reset",icon:"wk wk-circle-password"},{name:"重置登录账号",type:"resetName",icon:"wk wk-reset"}]):e.concat([{name:"重置密码",type:"reset",icon:"wk wk-circle-password"}])),this.userUpdateRoleAuth){1===this.selectionList.length&&e.push({name:"复制角色",type:"copyRole",icon:"wk wk-icon-double-note"});var s=this.selectionList.filter((function(e){return 0===e.userIdentity}));0===s.length&&e.push({name:"编辑角色",type:"editRole",icon:"wk wk-edit"})}this.userUpdateAuth&&e.push({name:"重置部门",type:"editDep",icon:"wk wk-employees"})}return e},tableList:function(){var e=[{field:"realname",value:"姓名"},{field:"sex",value:"性别",type:"select"},{field:"email",value:"邮箱"},{field:"deptId",value:"部门",type:"structure"},{field:"post",value:"岗位"},{field:"parentId",value:"直属上级",type:"user"},{field:"roleId",value:"角色",type:"selectCheckout",helpType:"21",helpId:"171",disable:!this.userUpdateRoleAuth}];return"新建员工"===this.userEditTitle?[{field:"username",value:"手机号（登录名）",helpType:"21",helpId:"263"},{field:"password",value:"登录密码"}].concat(e):[{field:"username",value:"手机号（登录名）",helpType:"21",helpId:"171"}].concat(e)},canSlideVerify:function(){return g["d"].test(this.resetUserNameForm.username)},isDepUserShow:function(){return this.currentMenuData&&this.currentMenuData.deptId},slideVerifyProps:function(){return{verifyRequest:h["e"]}}}),mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-240},this.enterDefaultMenu(),this.getUserList(),this.getCallAuth(),this.getUserCount()},methods:{enterDefaultMenu:function(){var e=this;this.getDepTreeList().then((function(){e.showDepData.length>0&&(e.changeDepClick(e.showDepData[0]),e.$nextTick((function(){e.$refs.tree.setCurrentNode(e.showDepData[0])})))}))},getUserCount:function(){var e=this;this.depLoading=!0,Object(d["e"])().then((function(t){e.depLoading=!1;var s=t.data||{};e.employeeMenu.forEach((function(e){e.count=s[e.field]})),e.userCountObj=s})).catch((function(){e.depLoading=!1}))},changeDepClick:function(e){this.currentMenuData=e,this.refreshUserList()},refreshUserList:function(){this.currentPage=1,this.getUserList()},getUserList:function(){var e=this;this.loading=!0;var t={page:this.currentPage,limit:this.pageSize,realname:this.searchInput},s=p["w"];this.isDepUserShow&&(t.isNeedChild=this.isNeedChild),"all"!==this.employeeType&&("active"==this.employeeType?t.status=1:this.isApplyUser?(t.type=this.applyType,s=p["o"]):t.label={all:0,new:1,inactive:2,disable:3}[this.employeeType]),this.currentMenuData&&this.currentMenuData.deptId&&(t.deptId=this.currentMenuData.deptId),s(t).then((function(t){e.tableData=t.data.list,e.total=t.data.totalRow,e.loading=!1})).catch((function(){e.loading=!1}))},headerSearch:function(){this.refreshUserList()},bulkImportClick:function(){this.bulkImportShow=!0},handleClose:function(){this.employeeDetailDialog=!1},rowClick:function(e,t,s){this.selectUserObj=e,"realname"==t.property&&(this.employeeDetailDialog=!0)},newHandleClose:function(){this.employeeCreateDialog=!1},addEmployee:function(){this.getHandleEmployeeRelateData(),this.userEditTitle="新建员工",this.userEditForm={roleId:[],deptId:this.currentMenuData&&this.currentMenuData.deptId?this.currentMenuData.deptId:"",parentId:this.currentMenuData&&this.currentMenuData.ownerUserId&&"0"!=this.currentMenuData.ownerUserId?this.currentMenuData.ownerUserId:""},this.selectUserObj=null,this.employeeCreateDialog=!0},depChange:function(e,t){var s=t&&t.length>0?t[0]:null;this.$set(this.userEditForm,"parentId",s?s.ownerUserId:"")},addSuccess:function(){this.$store.dispatch("GetUserDepMap"),this.refreshUserList()},getHandleEmployeeRelateData:function(){this.getRoleList()},editBtn:function(){var e=this;return Object(r["a"])(Object(l["a"])().m((function t(){var s,i,a;return Object(l["a"])().w((function(t){while(1)switch(t.n){case 0:return e.userEditTitle="编辑员工",t.n=1,e.getRoleList();case 1:s={},i=Object(l["a"])().m((function t(){var i,n,o,r;return Object(l["a"])().w((function(t){while(1)switch(t.n){case 0:i=e.tableList[a],"password"!==i.field&&("roleId"===i.field?(n=e.selectUserObj.roleId?e.selectUserObj.roleId.split(","):[],o=[],r=[],n.forEach((function(t){e.groupsIdList.includes(t)?r.push(t):o.push(t)})),s.otherRoleIds=o,s[i.field]=r):"parentId"===i.field?s.parentId=e.selectUserObj.parentId&&"0"!=e.selectUserObj.ownerUserId?e.selectUserObj.ownerUserId:"":"deptId"===i.field?s.deptId=e.selectUserObj.deptId:s[i.field]=e.selectUserObj[i.field]);case 1:return t.a(2)}}),t)})),a=0;case 2:if(!(a<e.tableList.length)){t.n=4;break}return t.d(o(i()),3);case 3:a++,t.n=2;break;case 4:s["userId"]=e.selectUserObj.userId,e.userEditForm=s,e.employeeCreateDialog=!0;case 5:return t.a(2)}}),t)})))()},getRoleList:function(){var e=this;return Promise.all([Object(d["a"])(),Object(d["c"])()]).then((function(t){e.groupsList=t[0].data||[];var s=[];e.groupsList.forEach((function(e){e.list&&e.list.forEach((function(e){s.push(e.roleId)}))})),e.groupsIdList=s,e.defaultRole=t[1].data,e.defaultRole&&e.groupsList.push({name:"默认",parentId:-1,list:[e.defaultRole]})})).catch((function(){}))},addStruc:function(){var e=this.allDepData&&this.allDepData.length?this.allDepData[0].deptId:"";e&&(this.depCreateLabelValue="",this.depCreateLabel="新增部门",this.depCreateTitle="新增部门",this.depSelect=e,this.getStructuresListBySuperior({id:e,type:"save"}),this.depCreateDialog=!0)},strucMoreHandleClick:function(e){"edit"==e?this.editStruc(this.currentMenuData):"delete"==e&&this.deleteStruc(this.currentMenuData)},appendStruc:function(e){this.depCreateLabelValue="",this.depCreateLabel="新增部门",this.depCreateTitle="新增部门",this.depSelect=e.deptId,this.depOwnerUserId="",this.getStructuresListBySuperior({id:e.deptId,type:"save"}),this.depCreateDialog=!0},getStructuresListBySuperior:function(e){var t=this;this.superDepList=[],Object(p["m"])(e).then((function(e){t.superDepList=e.data})).catch((function(){}))},editStruc:function(e){this.depCreateLabelValue=e.name,this.treeEditId=e.deptId,this.depSelect=e.parentId,this.depOwnerUserId=e.ownerUserId,this.depCreateTitle="编辑部门",this.depCreateLabel="编辑部门",this.getStructuresListBySuperior({id:e.deptId,type:"update"}),this.depCreateDialog=!0},deleteStruc:function(e){var t=this;this.$confirm("此操作将删除".concat(e.name,"部门，是否继续？"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(d["k"])({id:e.deptId}).then((function(e){t.enterDefaultMenu(),t.$message.success("删除成功"),t.loading=!1})).catch((function(){t.loading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},depCreateClose:function(){this.depCreateDialog=!1},submitDialog:function(){var e=this;""!==this.depCreateLabelValue?"新增部门"==this.depCreateLabel?Object(d["m"])({name:this.depCreateLabelValue,parentId:this.depSelect,ownerUserId:this.depOwnerUserId}).then((function(t){e.$store.dispatch("GetUserDepMap"),e.getDepTreeList(),e.depCreateClose()})):Object(d["l"])({name:this.depCreateLabelValue,deptId:this.treeEditId,parentId:this.depSelect,ownerUserId:this.depOwnerUserId}).then((function(t){e.$store.dispatch("GetUserDepMap"),e.$message.success("操作成功"),e.getDepTreeList(),e.depCreateClose()})):this.$message.error("部门名称不能为空!")},getDepTreeList:function(){var e=this;return new Promise((function(t){e.depLoading=!0,Object(p["m"])({type:"tree"}).then((function(s){e.allDepData=s.data,e.showDepData=s.data||[],e.depLoading=!1,t()})).catch((function(){e.depLoading=!1}))}))},newDialogSubmit:function(){var e=this;this.$refs.dialogRef.validate((function(t){if(!t){if(e.$refs.dialogRef.fields)for(var s=0;s<e.$refs.dialogRef.fields.length;s++){var i=e.$refs.dialogRef.fields[s];if("error"==i.validateState){e.$message.error(i.validateMessage);break}}return!1}if("新建员工"==e.userEditTitle){e.loading=!0;var a=Object(g["D"])(e.userEditForm);a.roleId=a.roleId.join(","),Object(d["o"])(a).then((function(t){e.$message.success("新增成功"),e.employeeCreateDialog=!1,e.refreshUserList(),e.getUserCount(),e.loading=!1,e.$store.dispatch("GetUserDepMap")})).catch((function(){e.loading=!1}))}else{var n=Object(g["D"])(e.userEditForm);if(Object(te["a"])(n.otherRoleIds)){var o=Array.from(new Set(n.roleId.concat(n.otherRoleIds)));n.roleId=o.join(","),delete n.otherRoleIds}else n.roleId=n.roleId.join(",");n.roleId?e.updateUserReq(n):e.$confirm("您没有为该员工添加角色，系统将自动给该员工添加一个默认角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.roleId=e.defaultRole.roleId,e.updateUserReq(n)})).catch((function(){}))}}))},updateUserReq:function(e){var t=this;this.loading=!0,Object(d["p"])(e).then((function(e){t.employeeDetailDialog&&(t.employeeDetailDialog=!1),t.employeeCreateDialog=!1,t.$message.success("编辑成功"),t.getUserList(),t.loading=!1})).catch((function(){t.loading=!1}))},setCall:function(){var e=this;this.loading=!0;var t=this.selectionList.map((function(e){return e.userId}));Object(d["j"])({userIds:t,state:1,hisUse:this.callType}).then((function(t){e.loading=!1,e.callVisible=!1,t.data.length>0?(e.callInfo=t.data,e.callInfoVisible=!0):e.$message.success("修改成功"),e.usersListFun()})).catch((function(){e.loading=!1}))},handleCommand:function(e){var t=this;switch(e){case"reset":this.passForm={password:""},this.resetPasswordVisible=!0;break;case"status":Object(d["t"])({ids:[this.selectUserObj.userId],status:0===this.selectUserObj.status?1:0}).then((function(e){t.employeeDetailDialog=!1,t.$message.success("修改成功"),t.getUserList()}));break}},selectionBarClick:function(e){var t=this;return Object(r["a"])(Object(l["a"])().m((function s(){var i,a,n,r,c,u;return Object(l["a"])().w((function(s){while(1)switch(s.n){case 0:if(i=t.selectionList.map((function(t,s,i){return"pass"===e||"refuse"===e?t.id:t.userId})).join(","),"lock"!==e&&"unlock"!==e){s.n=1;break}a="lock"===e?"禁用":"激活",t.$confirm("这些员工账号将被"+a+", 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(d["t"])({ids:i.split(","),status:"unlock"===e?1:0}).then((function(e){t.loading=!1,t.$message.success("修改成功"),t.getUserCount(),t.getUserList()})).catch((function(){t.loading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})})),s.n=9;break;case 1:if("reset"!==e){s.n=2;break}t.resetPasswordVisible=!0,s.n=9;break;case 2:if("resetName"!==e){s.n=3;break}0===t.selectionList[0].userIdentity?t.resetDialogVisible=!0:(t.isManageReset=!1,t.slideVerifyPass=!1,t.slideVerifyShow=!1,t.resetUserNameForm={username:"",password:""},t.resetUserNameVisible=!0),s.n=9;break;case 3:if("edit"!==e){s.n=8;break}return t.selectUserObj=t.selectionList[0],t.userEditTitle="编辑员工",s.n=4,t.getRoleList();case 4:n={},r=Object(l["a"])().m((function e(){var s,i,a,o;return Object(l["a"])().w((function(e){while(1)switch(e.n){case 0:s=t.tableList[c],"password"!==s.field&&("roleId"===s.field?(i=t.selectUserObj.roleId?t.selectUserObj.roleId.split(","):[],a=[],o=[],i.forEach((function(e){t.groupsIdList.includes(e)?o.push(e):a.push(e)})),n.otherRoleIds=a,n[s.field]=o):"parentId"===s.field?n.parentId=t.selectUserObj.parentId&&"0"!=t.selectUserObj.parentId?t.selectUserObj.parentId:"":"deptId"===s.field?n.deptId=t.selectUserObj.deptId:n[s.field]=t.selectUserObj[s.field]);case 1:return e.a(2)}}),e)})),c=0;case 5:if(!(c<t.tableList.length)){s.n=7;break}return s.d(o(r()),6);case 6:c++,s.n=5;break;case 7:n["userId"]=t.selectUserObj.userId,t.userEditForm=n,t.employeeCreateDialog=!0,s.n=9;break;case 8:"setCall"===e?t.callVisible=!0:"stopCall"===e?(u="setCall"===e?"启用呼叫中心":"禁用呼叫中心",t.$confirm("这些员工账号将被"+u+", 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0;var s=[];s=i?i.split(","):[],Object(d["j"])({userIds:s,state:"setCall"===e?1:0}).then((function(e){t.loading=!1,t.$message.success("修改成功"),t.usersListFun()})).catch((function(){t.loading=!1}))})).catch((function(){}))):"editRole"===e||"copyRole"===e?(t.editRoleType=e,t.editRoleDialogShow=!0):"editDep"===e?t.editDepDialogShow=!0:"pass"!==e&&"refuse"!==e||(a="pass"===e?"通过":"拒绝",t.$confirm("您确定"+a+t.selectionList.length+"个成员吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var s="pass"===e?p["d"]:p["l"],a={ids:i.split(",")};a.status="pass"===e?1:4,t.loading=!0,s(a).then((function(e){t.loading=!1,t.$message.success("修改成功"),t.getUserCount(),t.refreshUserList()})).catch((function(){t.loading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消".concat(a)})})));case 9:return s.a(2)}}),s)})))()},resetPasswordClose:function(){this.resetPasswordVisible=!1},passSubmit:function(e){var t=this;this.$refs.passForm.validate((function(s){if(!s)return!1;var i=[];t.selectionList.length>0?i=t.selectionList.map((function(e,t,s){return e.userId})):i.push(t.selectUserObj.userId),e.ids=i,t.loading=!0,Object(d["h"])(e).then((function(e){t.$message.success("重置成功"),t.resetPasswordClose(),t.loading=!1})).catch((function(){t.loading=!1}))}))},passUserNameSubmit:function(e){var t=this;this.$refs.resetUserNameForm.validate((function(s){if(!s)return!1;if(t.selectionList.length>0)if(e.id=t.selectionList[0].userId,t.isManageReset){if(!t.resetUserNameForm.smscode)return void t.$message.error("请输入验证码");t.loading=!0,Object(d["g"])(e).then((function(e){t.$message.success("重置成功"),t.resetUserNameVisible=!1,t.loading=!1,t.refreshUserList()})).catch((function(){t.loading=!1}))}else t.loading=!0,Object(d["i"])(e).then((function(e){3===e.status?(t.$message.error("当前为系统注册账号（手机号），重置需要获取新手机号验证码"),t.isManageReset=!0):(t.$message.success("重置成功"),t.resetUserNameVisible=!1,t.refreshUserList()),t.loading=!1})).catch((function(){t.loading=!1}))}))},sliderSuccess:function(){var e=this;setTimeout((function(){e.slideVerifyPass=!0,e.slideVerifyShow=!1}),500)},sliderFail:function(){},sliderRefresh:function(){},getSmsCode:function(){var e=this;this.canSlideVerify?this.slideVerifyPass?Object(h["d"])({telephone:this.resetUserNameForm.username,type:1}).then((function(){e.startTimer()})).catch():this.$message.error("请先进行滑动验证"):this.$message.error("请输入正确的手机号")},startTimer:function(){var e=this;this.codeSecond===this.codeTime&&this.codeSecond--,this.codeTimer=setTimeout((function(){e.codeSecond--,e.codeSecond>=0?e.startTimer():(clearTimeout(e.codeTimer),e.codeTimer=null,e.codeSecond=e.codeTime)}),1e3)},handleSizeChange:function(e){this.pageSize=e,this.refreshUserList()},handleCurrentChange:function(e){this.currentPage=e,this.getUserList()},handleSelectionChange:function(e){this.selectionList=e},getStatusColor:function(e){return 0==e?"#FF6767":1==e?"#46CDCF":2==e?"#CCCCCC":void 0},getStatusName:function(e){return 0==e?"禁用":1==e?"已激活":2==e?"未激活":void 0},tableFormatter:function(e,t){return"sex"==t.property?{1:"男",2:"女"}[e.sex]:e[t.property]},getCallAuth:function(){var e=this;Object(u["d"])().then((function(t){var s=t.data||{};e.isStartCall=1==s.status})).catch((function(){}))}}},ie=se,ae=(s("5dde"),Object(y["a"])(ie,i,a,!1,null,"9c4168f2",null));t["default"]=ae.exports},7037:function(e,t,s){},"7c41":function(e,t,s){},"7c80":function(e,t,s){},"7e9e":function(e,t,s){"use strict";s("7c41")},8410:function(e,t,s){"use strict";s("7c80")},"85d6":function(e,t,s){},"94d4":function(e,t,s){"use strict";var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-select",e._g(e._b({ref:"select",staticClass:"role-employee-select",on:{"visible-change":e.selectVisibleChange,change:e.selectChange},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}},"el-select",e.$attrs,!1),e.$listeners),[s("div",{staticClass:"role-employee-select__body"},[s("el-tabs",{ref:"roleTabs",class:{"el-tabs__header--hidden":e.config.onlyShowRole},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[s("el-tab-pane",{ref:"roleTabPane",attrs:{label:"自选角色",name:"role"}},e._l(e.roleOption,(function(t){return s("div",{key:t.parentId,attrs:{label:t.name}},[s("div",{staticClass:"role-employee-select__title"},[e._v(e._s(t.name))]),e._v(" "),e._l(t.list,(function(e){return s("el-option",{key:e.roleId,staticStyle:{padding:"0 10px"},attrs:{label:e.roleName,value:e.roleId}})}))],2)}))),e._v(" "),s("el-tab-pane",{attrs:{label:"按员工复制角色",name:"employee"}},[s("el-input",{staticClass:"search-input",attrs:{placeholder:"搜索成员",size:"small","prefix-icon":"el-icon-search"},on:{input:e.userSearch},model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}}),e._v(" "),e._l(e.userOption,(function(t){return s("el-option",{directives:[{name:"show",rawName:"v-show",value:!t.isHide,expression:"!item.isHide"}],key:t.userId,staticStyle:{padding:"0 10px"},attrs:{label:t.realname,value:t.userId+"@"+t.roleId}},[s("flexbox",{staticClass:"cell"},[s("xr-avatar",{staticClass:"cell__img",attrs:{name:t.realname,size:24,src:t.img}}),e._v(" "),s("div",{staticClass:"cell__body"},[e._v(e._s(t.realname))]),e._v(" "),s("el-tooltip",{attrs:{content:t.roleName,effect:"dark",placement:"top"}},[s("div",{staticClass:"cell__footer text-one-line"},[e._v(e._s(t.roleName))])])],1)],1)}))],2)],1)],1)])},a=[],n=s("5530"),o=(s("e9f5"),s("7d54"),s("a9e3"),s("d3b7"),s("ac1f"),s("466d"),s("159b"),s("612a")),l=s("2934"),r=s("8122"),c=s("a318"),d=s.n(c),u=s("8ed6"),p={onlyShowRole:!1,roleRequest:null},h={name:"RoleEmployeeSelect",components:{},props:{props:{type:Object,default:function(){return{}}},value:[Array,Number,String]},data:function(){return{selectValue:[],activeName:"",roleOption:[],userOption:[],searchInput:""}},computed:{config:function(){return Object(u["a"])(Object(n["a"])({},p),this.props||{})},select:function(){return this.$refs.select}},watch:{value:{handler:function(){Object(r["valueEquals"])(this.value,this.selectValue)||(this.selectValue=this.value)},immediate:!0}},created:function(){this.getRoleList(),this.getUserList()},mounted:function(){},beforeDestroy:function(){},methods:{selectVisibleChange:function(e){""!==this.activeName&&"0"!==this.activeName||(this.activeName="role")},getRoleList:function(){var e=this,t=this.config.roleRequest||o["n"];t().then((function(t){e.roleOption=t.data||[]})).catch((function(){}))},getUserList:function(){var e=this;Object(l["w"])({pageType:0}).then((function(t){e.userOption=t.data.list||[]})).catch((function(){}))},selectChange:function(){this.$emit("input",this.selectValue)},userSearch:function(){var e=this;this.userOption.forEach((function(t){t.isHide=!d.a.match(t.realname,e.searchInput)}))}}},m=h,f=(s("5a80"),s("c128"),s("2877")),v=Object(f["a"])(m,i,a,!1,null,"6ca934ac",null);t["a"]=v.exports},a946:function(e,t,s){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},a97e:function(e,t,s){},b3fc:function(e,t,s){},b5c2:function(e,t,s){"use strict";s("7037")},b64d:function(e,t){e.exports="data:image/png;base64,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"},c128:function(e,t,s){"use strict";s("575d")},d679:function(e,t){e.exports="data:image/png;base64,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"},f52db:function(e,t,s){"use strict";s("b3fc")},f5bd:function(e,t,s){"use strict";s("a97e")}}]);