(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-314a74e4"],{1023:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-tabs",{staticClass:"main-container",model:{value:t.tabActiveName,callback:function(e){t.tabActiveName=e},expression:"tabActiveName"}},t._l(t.tabList,(function(e,n){return a("el-tab-pane",{key:n,attrs:{label:e.label,name:e.name}},[a("cycle-view",{attrs:{type:e.name,show:e.name==t.tabActiveName}})],1)})))},r=[],o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"cycle-content"},[t.initView?a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{title:t.filterTitle,"module-type":"customer"},on:{load:function(e){t.loading=!0},change:t.getDataList}}):t._e(),t._v(" "),a("div",{staticClass:"content"},[a("div",{staticClass:"axis-content"},[a("div",{staticClass:"content-title"},[t._v(t._s(t.title))]),t._v(" "),a("div",{staticClass:"axismain",attrs:{id:"axismain"+t.type}})]),t._v(" "),a("div",{staticClass:"table-content"},[a("div",{staticClass:"handle-bar"},[a("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),t.showTable?a("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",stripe:t.WKConfig.tableStyle.stripe,"cell-class-name":t.cellClassName,data:t.list,"summary-method":t.getSummaries,height:"400","show-summary":"","highlight-current-row":""},on:{"row-click":t.handleRowClick,"sort-change":function(e){var a=e.prop,n=e.order;return t.mixinSortFn(t.list,a,n)}}},t._l(t.fieldList,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,sortable:"custom","show-overflow-tooltip":""}})}))):t._e()],1)]),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},i=[],s=a("5530"),c=(a("14d9"),a("df55")),u=a("f643"),l=a("f4f4"),d=a("b80b"),p=a("313e"),h=a("2602"),m=a("e508f"),f={name:"CycleView",components:{},mixins:[c["a"],u["a"],l["a"],d["a"]],props:{type:{required:!0,type:String},show:{required:!0,type:Boolean}},data:function(){return{loading:!1,axisOption:null,postParams:null,list:[],fieldList:null,initView:!1,detailFields:[{name:"customerNum",crmType:"customer",list:[{formType:"dealStatus",name:"dealStatus",type:1,values:[1]}],request:h["d"],params:{}}]}},computed:{title:function(){return{customer:"员工客户成交周期（根据合同下单时间和客户创建时间计算）",address:"地区成交周期（根据合同下单时间和客户创建时间计算）",product:"产品成交周期（根据合同下单时间和客户创建时间计算）"}[this.type]},filterTitle:function(){return{customer:"员工客户成交周期分析",address:"地区成交周期分析",product:"产品成交周期分析"}[this.type]}},watch:{show:function(t){var e=this;t&&!this.initView&&(this.initView=!0,this.postParams&&this.getDataList(this.postParams),this.$nextTick((function(){e.initAxis()}))),t&&this.initView&&this.$nextTick((function(){e.resizeFn()}))}},created:function(){var t=this.detailFields[0].list;"customer"===this.type?t.push({formType:"user",name:"ownerUserId",type:3,values:[]}):"address"===this.type?t.push({formType:"map_address",name:"address",type:3,values:[]}):"product"===this.type&&(this.detailFields[0].request=m["c"])},mounted:function(){this.fieldList=this.getFieldList(),this.show&&(this.initView=!0,this.initAxis())},methods:{getDataList:function(t){var e=this;if(this.postParams=t,this.show){this.loading=!0;var a={customer:h["u"],product:h["i"],address:h["a"]}[this.type];a(t).then((function(t){e.loading=!1;var a=t.data||[];"customer"!==e.type&&(e.list=a);for(var n=[],r=[],o=[],i=0;i<a.length;i++){var s=a[i];n.push(s.cycle),r.push(s.customerNum),"customer"===e.type||"address"===e.type?o.push(s.type):"product"===e.type&&o.push(s.productName)}e.axisOption.xAxis[0].data=o,e.axisOption.series[0].data=n,e.axisOption.series[1].data=r,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1})),"customer"===this.type&&Object(h["x"])(t).then((function(t){e.loading=!1,e.list=t.data||[]})).catch((function(){e.loading=!1}))}},initAxis:function(){var t=p["b"](document.getElementById("axismain"+this.type)),e={color:this.echartLineBarColors,toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(s["a"])({data:["成交周期","成交客户数"]},this.chartDefaultOptions.legend),grid:this.chartDefaultOptions.grid,xAxis:[Object(s["a"])({type:"category",data:[]},this.chartXAxisStyle)],yAxis:[Object(s["a"])({type:"value",name:"成交周期"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}天"}})),Object(s["a"])({type:"value",name:"成交客户数"},this.getChartYAxisStyle({axisLabel:{formatter:"{value}个"},splitLine:{show:!0}}))],series:[{name:"成交周期",type:"bar",yAxisIndex:0,barMaxWidth:15,data:[]},{name:"成交客户数",type:"bar",yAxisIndex:1,barMaxWidth:15,data:[]}]};t.setOption(e,!0),this.axisOption=e,this.chartObj=t},getFieldList:function(){return{customer:[{field:"realname",name:"姓名"},{field:"cycle",name:"平均成交周期（天）"},{field:"customerNum",name:"成交客户数"}],product:[{field:"productName",name:"产品名称"},{field:"cycle",name:"平均成交周期（天）"},{field:"customerNum",name:"成交客户数"}],address:[{field:"type",name:"地区"},{field:"cycle",name:"平均成交周期（天）"},{field:"customerNum",name:"成交客户数"}]}[this.type]},exportClick:function(){this.requestExportInfo({customer:h["v"],product:h["j"],address:h["b"]}[this.type],this.postParams)}}},b=f,y=(a("a900"),a("2877")),C=Object(y["a"])(b,o,i,!1,null,"49b36991",null),T=C.exports,v={name:"CustomerCycleStatistics",components:{CycleView:T},data:function(){return{tabActiveName:"customer",tabList:[{label:"员工客户成交周期",name:"customer"},{label:"地区成交周期",name:"address"},{label:"产品成交周期",name:"product"}]}},computed:{},mounted:function(){},methods:{}},j=v,g=(a("37fd"),Object(y["a"])(j,n,r,!1,null,"0c3d99fa",null));e["default"]=g.exports},2602:function(t,e,a){"use strict";a.d(e,"r",(function(){return r})),a.d(e,"s",(function(){return o})),a.d(e,"t",(function(){return i})),a.d(e,"o",(function(){return s})),a.d(e,"m",(function(){return c})),a.d(e,"n",(function(){return u})),a.d(e,"c",(function(){return l})),a.d(e,"f",(function(){return d})),a.d(e,"g",(function(){return p})),a.d(e,"h",(function(){return h})),a.d(e,"u",(function(){return m})),a.d(e,"v",(function(){return f})),a.d(e,"x",(function(){return b})),a.d(e,"a",(function(){return y})),a.d(e,"b",(function(){return C})),a.d(e,"i",(function(){return T})),a.d(e,"j",(function(){return v})),a.d(e,"p",(function(){return j})),a.d(e,"q",(function(){return g})),a.d(e,"l",(function(){return x})),a.d(e,"k",(function(){return O})),a.d(e,"d",(function(){return w})),a.d(e,"w",(function(){return F})),a.d(e,"e",(function(){return S}));var n=a("b775");function r(t){return Object(n["a"])({url:"biCustomer/totalCustomerStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biCustomer/totalCustomerTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(n["a"])({url:"biCustomer/totalCustomerTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(n["a"])({url:"biCustomer/customerRecordStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(n["a"])({url:"biCustomer/customerRecordInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(n["a"])({url:"biCustomer/customerRecordInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(n["a"])({url:"biCustomer/customerConversionStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(n["a"])({url:"biCustomer/poolStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(n["a"])({url:"biCustomer/poolTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(n["a"])({url:"biCustomer/poolTableExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(n["a"])({url:"biCustomer/employeeCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(n["a"])({url:"biCustomer/employeeCycleInfoExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(n["a"])({url:"biCustomer/employeeCycleInfo",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(n["a"])({url:"biCustomer/districtCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(n["a"])({url:"biCustomer/districtCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(n["a"])({url:"biCustomer/productCycle",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function v(t){return Object(n["a"])({url:"biCustomer/productCycleExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(n["a"])({url:"biCustomer/customerSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(n["a"])({url:"biCustomer/customerSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(n["a"])({url:"biCustomer/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(t){return Object(n["a"])({url:"biCustomer/productSatisfactionExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(t){return Object(n["a"])({url:"crmBiSearch/searchCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function F(t){return Object(n["a"])({url:"crmBiSearch/searchPoolCustomerPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function S(t){return Object(n["a"])({url:"crmBiSearch/queryCustomerRecordList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},"37fd":function(t,e,a){"use strict";a("5f11")},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),s=a("07fa"),c=a("083a"),u=a("577e"),l=a("d039"),d=a("addb"),p=a("a640"),h=a("3f7e"),m=a("99f4"),f=a("1212"),b=a("ea83"),y=[],C=r(y.sort),T=r(y.push),v=l((function(){y.sort(void 0)})),j=l((function(){y.sort(null)})),g=p("sort"),x=!l((function(){if(f)return f<70;if(!(h&&h>3)){if(m)return!0;if(b)return b<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:e+n,v:a})}for(y.sort((function(t,e){return e.v-t.v})),n=0;n<y.length;n++)e=y[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),O=v||!j||!g||!x,w=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:u(e)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:O},{sort:function(t){void 0!==t&&o(t);var e=i(this);if(x)return void 0===t?C(e):C(e,t);var a,n,r=[],u=s(e);for(n=0;n<u;n++)n in e&&T(r,e[n]);d(r,w(t)),a=s(r),n=0;while(n<a)e[n]=r[n++];while(n<u)c(e,n++);return e}})},"5f11":function(t,e,a){},a900:function(t,e,a){"use strict";a("f583")},e508f:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i}));var n=a("b775");function r(t){return Object(n["a"])({url:"biProduct/contractProductRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(n["a"])({url:"biCustomer/queryProductTypeList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(n["a"])({url:"crmBiSearch/queryProductSucceedCustomerList ",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},f4f4:function(t,e,a){"use strict";a("d81d"),a("13d5"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("d3b7"),a("159b");e["a"]={data:function(){return{summaryData:null}},methods:{getSummariesData:function(t){this.summaryData=t||{}},getSummaries:function(t){var e=t.columns,a=t.data,n=[];return e.forEach((function(t,e){if(0!==e){var r=a.map((function(e){return Number(e[t.property])}));r.every((function(t){return isNaN(t)}))?n[e]="":n[e]=r.reduce((function(t,e){var a=Number(e);return isNaN(a)?t:t+e}),0)}else n[e]="合计"})),n}}}},f583:function(t,e,a){},f643:function(t,e,a){"use strict";a("4e82"),a("a9e3"),a("d3b7"),a("25f0");e["a"]={data:function(){return{showTable:!0}},methods:{mixinSortFn:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if("[object Array]"!==Object.prototype.toString.call(t))return[];if(!e)return t;function n(t,n){if(t[e]===n[e])return 0;var r=!isNaN(Number(t[e]))&&!isNaN(Number(n[e])),o=r?Number(t[e])<Number(n[e]):t[e]<n[e];return"descending"===a?o?1:-1:o?-1:1}t.sort(n)}}}}}]);