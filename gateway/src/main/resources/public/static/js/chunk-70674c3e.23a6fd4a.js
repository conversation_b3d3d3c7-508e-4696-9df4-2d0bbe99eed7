(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-70674c3e"],{"0dd2":function(e,t,n){"use strict";n("15ce")},"15ce":function(e,t,n){},"39c7":function(e,t,n){"use strict";n("77cd")},"77cd":function(e,t,n){},"92a8":function(e,t,n){"use strict";n("a94e")},"9ed6":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"login"},[n("div",{staticClass:"login__content"},[n("div",{staticClass:"login-title"},[e._v("\n      "+e._s(null!==e.companyInfo.name&&""!==e.companyInfo.name?e.companyInfo.name:"CRM")+"\n    ")]),e._v(" "),"loginPwd"===e.loginType?n("login-by-pwd",{attrs:{username:e.username},on:{"update:username":function(t){e.username=t},toggle:e.toggleClick}}):e._e()],1),e._v(" "),e._m(0)])},i=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"footer"},[n("div",{staticClass:"footer-des"})])}],r=(n("b0c0"),n("dea6")),s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px","label-position":"top"},nativeOn:{submit:function(e){e.preventDefault()}}},[n("div",{staticClass:"form-title"},[e._v("登录您的帐户")]),e._v(" "),n("el-form-item",{attrs:{label:"",prop:"username"}},[e.ruleResult.username.edit?n("el-input",{ref:"userInput",attrs:{disabled:e.loading,placeholder:"请输入手机号"},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.continueClick(t)}},model:{value:e.ruleForm.username,callback:function(t){e.$set(e.ruleForm,"username","string"===typeof t?t.trim():t)},expression:"ruleForm.username"}}):n("show-item",{attrs:{content:e.ruleForm.username},nativeOn:{click:function(t){e.editClick("username")}}})],1),e._v(" "),e.passwordShow?n("el-form-item",{attrs:{label:"",prop:"password"}},[n("el-input",{ref:"passwordInput",attrs:{type:e.passwordType,placeholder:"请输入密码"},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.continueClick(t)}},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password","string"===typeof t?t.trim():t)},expression:"ruleForm.password"}},[n("i",{staticClass:"wk wk-icon-eye-solid",style:{color:""===e.passwordType?"#243858":"#C1C7D0"},attrs:{slot:"suffix"},on:{click:function(t){e.passwordType=""===e.passwordType?"password":""}},slot:"suffix"})])],1):e._e(),e._v(" "),n("div",{staticClass:"handle-bar"},[n("el-button",{attrs:{loading:e.loading,disabled:e.loading,type:"primary"},on:{click:e.continueClick}},[e._v(e._s(e.loginBtnName))])],1)],1)},a=[],u=n("c14f"),l=n("1da1"),c=(n("d9e2"),n("14d9"),n("2934")),d=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"item-show"},[n("div",{staticClass:"item-show__inner"},[e._v(e._s(e.content))]),e._v(" "),e._m(0)])},m=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("span",{staticClass:"item-show__suffix"},[n("span",{staticClass:"item-show__suffix-inner"},[n("i",{staticClass:"wk wk-icon-follow-up"})])])}],f=(n("a9e3"),{name:"ShowItem",components:{},props:{content:[String,Number]},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{}}),p=f,h=(n("39c7"),n("2877")),g=Object(h["a"])(p,d,m,!1,null,"fc9fb0a8",null),w=g.exports,v=n("7a1a"),y=n("6bfe"),b=(n("e9f5"),n("7d54"),n("d3b7"),n("159b"),{data:function(){return{redirect:void 0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},methods:{changeFormState:function(){var e=this;return new Promise((function(t,n){e.$nextTick((function(){var n=e.$refs.ruleForm;n.$children.forEach((function(t){t.prop&&(e.ruleResult[t.prop].validate="success"===t.validateState)})),t(e.ruleResult)}))}))}}}),_=n("5f87"),k={name:"LoginByPwd",components:{ShowItem:w},mixins:[b],props:{username:String},data:function(){var e=this,t=function(t,n,o){e.loading?""===n?o(new Error("手机号不能为空")):(e.loading=!0,Object(c["c"])({username:n}).then((function(t){e.loading=!1,0===t.data?o(new Error("手机号未注册")):o()})).catch((function(){e.loading=!1}))):o()};return{loading:!1,ruleForm:{username:"",password:""},ruleResult:{username:{validate:!1,edit:!0},password:{validate:!1,edit:!1}},rules:{username:[{required:!0,message:"手机号不能为空",trigger:"change"},{validator:t,trigger:""}],password:[{required:!0,message:"密码不能为空",trigger:"change"}]},passwordType:"password"}},computed:{passwordShow:function(){return!this.ruleResult.username.edit&&this.ruleResult.username.validate},loginBtnName:function(){return this.passwordShow?"登录":"继续"}},watch:{ruleForm:{handler:function(){var e=this;this.$nextTick((function(){e.changeFormState()}))},deep:!0},passwordShow:function(){var e=this;this.passwordShow&&this.$nextTick((function(){e.$refs.passwordInput.focus()}))},username:{handler:function(){this.ruleForm.username=this.username},immediate:!0},"ruleForm.username":{handler:function(e){this.$emit("update:username",e)}}},created:function(){window.accessLogin||Object(_["c"])(),this.debouncedHandleLogin=Object(v["debounce"])(300,this.handleLogin)},mounted:function(){},beforeDestroy:function(){},methods:{continueClick:function(){var e=this;this.loading=!0,this.$refs.ruleForm.validate(function(){var t=Object(l["a"])(Object(u["a"])().m((function t(n){var o;return Object(u["a"])().w((function(t){while(1)switch(t.n){case 0:return e.loading=!1,t.n=1,e.changeFormState();case 1:return e.ruleResult.username.validate&&(e.ruleResult.password.validate?(e.loading=!0,o="www"===window.location.hostname.split(".")[0]?"GetAllLoginUser":"Login",e.$store.dispatch(o,e.ruleForm).then((function(t){e.loading=!1;var n=t.data;Object(y["a"])(n)?e.$emit("toggle","multiple",n,e.ruleForm):n.hasOwnProperty("companyList")?e.$emit("toggle","multiple",n.companyList,e.ruleForm):e.$router.push({path:e.redirect||"/"})})).catch((function(){e.loading=!1}))):e.ruleResult.username.edit=!1),t.a(2,!1)}}),t)})));return function(e){return t.apply(this,arguments)}}())},loginClick:function(e){"forgetPwd"===e?this.$emit("toggle","forgetPwd"):"loginCode"===e&&this.$emit("toggle","loginCode")},editClick:function(e){var t=this;this.ruleResult[e].edit=!this.ruleResult[e].edit,this.ruleResult.password.validate=!1,"username"===e&&this.ruleResult[e].edit&&this.$nextTick((function(){t.$refs.userInput.focus()}))}}},C=k,$=(n("0dd2"),Object(h["a"])(C,s,a,!1,null,"03d23809",null)),F=$.exports,L={name:"Login",components:{LoginByPwd:F},props:{},data:function(){return{loginType:"loginPwd",username:"",companyList:[],companyInfo:{name:"",logo:""},loginParams:null}},computed:{},watch:{},created:function(){this.getLogoAndName()},mounted:function(){},beforeDestroy:function(){},methods:{getLogoAndName:function(){var e=this;Object(r["e"])().then((function(t){var n=t.data;n&&(e.companyInfo.logo=n.companyLoginLogo,e.companyInfo.name=n.companyName)})).catch((function(){}))},toggleClick:function(e,t,n){this.loginType=e,"multiple"===e?(this.companyList=t,this.loginParams=n):this.loginParams=null}}},O=L,x=(n("92a8"),Object(h["a"])(O,o,i,!1,null,"0888ece4",null));t["default"]=x.exports},a94e:function(e,t,n){}}]);