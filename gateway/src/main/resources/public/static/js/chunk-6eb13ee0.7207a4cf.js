(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6eb13ee0"],{"08c2":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),t._v(" "),a("span",{staticClass:"text"},[t._v(t._s(t.title))])]),t._v(" "),t.showFilterView?[t.showYearSelect?t._e():a("time-type-select",{on:{change:t.timeTypeChange}}),t._v(" "),t.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":t.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.yearValue,callback:function(e){t.yearValue=e},expression:"yearValue"}}):t._e(),t._v(" "),t._t("after-time"),t._v(" "),t.showSimpleChoose?[t.showUserSelect&&t.showDeptSelect?a("el-select",{model:{value:t.simpleChooseType,callback:function(e){t.simpleChooseType=e},expression:"simpleChooseType"}},t._l(t.simpleOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))):t._e(),t._v(" "),1===t.simpleChooseType&&t.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:t.structuresSelectValue,callback:function(e){t.structuresSelectValue=e},expression:"structuresSelectValue"}}):t._e(),t._v(" "),2===t.simpleChooseType&&t.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:t.userSelectValue,callback:function(e){t.userSelectValue=e},expression:"userSelectValue"}}):t._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:t.dataTypeOptions,"user-checked-data":t.filterValue.userList,"dep-checked-data":t.filterValue.deptList,width:250},on:{select:t.radioMenuSelect},model:{value:t.filterDataType,callback:function(e){t.filterDataType=e},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.avatarData.realname,callback:function(e){t.$set(t.avatarData,"realname",e)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),t.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:t.businessStatusValue,callback:function(e){t.businessStatusValue=e},expression:"businessStatusValue"}},t._l(t.businessOptions,(function(t){return a("el-option",{key:t.flowId,attrs:{label:t.flowName,value:t.flowId}})}))):t._e(),t._v(" "),t.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:t.productValue,callback:function(e){t.productValue=e},expression:"productValue"}}):t._e(),t._v(" "),t.showCustomSelect?a("el-select",{on:{change:t.customSelectChange},model:{value:t.customValue,callback:function(e){t.customValue=e},expression:"customValue"}},t._l(t.customOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}))):t._e(),t._v(" "),t._t("append"),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(e){return t.emitFilter(e)}}},[t._v("查询")]),t._v(" "),t._t("default")]:t._e()],2)},s=[],o=a("5530"),l=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),n=a("ea20"),r=a("657f"),c=a("bfba"),u=a("8f81"),h=a("83f1"),p=a("2f62"),d={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:h["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(o["a"])(Object(o["a"])({},Object(p["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var t=(this.filterValue.userList||[]).map((function(t){return t.realname})),e=(this.filterValue.deptList||[]).map((function(t){return t.name}));return{realname:t.concat(e).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var t=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){t.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(t){var e=this;Object(l["r"])().then((function(a){e.businessOptions=a.data||[],e.businessOptions.length>0&&(e.businessStatusValue=e.businessOptions[0].flowId),t(!0)})).catch((function(){e.$emit("error")}))},getProductCategoryIndex:function(){var t=this;Object(n["T"])({type:"tree"}).then((function(e){t.productOptions=e.data})).catch((function(){}))},radioMenuSelect:function(t,e){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=e.users,this.filterValue.deptList=e.strucs)},timeTypeChange:function(t){this.timeTypeValue=t},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var t=this,e={};this.showSimpleChoose?1===this.simpleChooseType?e.deptList=(this.structuresSelectValue||"").split(",").filter((function(t){return!!t})):e.userList=(this.userSelectValue||"").split(",").filter((function(t){return!!t})):"custom"!==this.filterValue.dataType?e.dataType=this.filterValue.dataType:(e.dataType=0,e.deptList=(this.filterValue.deptList||[]).map((function(t){return t.deptId})),e.userList=(this.filterValue.userList||[]).map((function(t){return t.userId}))),this.showYearSelect?(e.dateFilter="custom",e.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(e.startDate=this.timeTypeValue.startTime,e.endDate=this.timeTypeValue.endTime,e.dateFilter="custom"):e.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(e.typeId=this.businessStatusValue,e.businessItem=this.businessOptions.map((function(e){if(e.flowId===t.businessStatusValue)return e}))),this.showProductSelect&&(e.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",e)}}},f=d,m=(a("965d"),a("2877")),b=Object(m["a"])(f,i,s,!1,null,"6d7c8f9a",null);e["a"]=b.exports},"6c4e":function(t,e,a){"use strict";a("c06a")},7749:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-product-selct":!0,title:"产品分类销量分析","module-type":"product"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),t._m(0),t._v(" "),a("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content"},[a("div",{staticClass:"axis-content"},[a("div",{attrs:{id:"axismain"}})])])}],o=a("5530"),l=(a("14d9"),a("b0c0"),a("df55")),n=a("313e"),r=a("e508f"),c=a("2a64"),u={name:"ProductCategoryStatistics",components:{ReportList:c["a"]},mixins:[l["a"]],data:function(){return{loading:!1,axisOption:null,postParams:{},fieldReportList:null,reportListShow:!1,reportData:{title:"",placeholder:"",crmType:"product",request:r["b"],params:null,paging:!0,sortable:!1},search:""}},computed:{},mounted:function(){var t=this;this.initAxis(),this.chartObj.on("click",(function(e){var a=e.data;t.enterDetail(a)}))},methods:{enterDetail:function(t){this.reportData.title=t.name,this.reportData=Object(o["a"])(Object(o["a"])({},this.reportData),{},{params:Object(o["a"])(Object(o["a"])({},this.postParams),{},{type:4,categoryId:t.categoryId})}),this.reportListShow=!0},getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,Object(r["a"])(t).then((function(t){e.loading=!1;for(var a=[],i=[],s=0;s<t.data.length;s++){var o=t.data[s];a.push({name:o.categoryName,value:o.num,categoryId:o.categoryId}),i.push(o.categoryName)}e.axisOption.legend.data=i,e.axisOption.series[0].data=a,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},initAxis:function(){var t=n["b"](document.getElementById("axismain")),e={title:{text:"产品分类销售",x:"center",bottom:"10"},color:this.chartColors,toolbox:this.toolbox,tooltip:{trigger:"item",formatter:"{b} : {c}"},legend:{textStyle:this.chartDefaultOptions.label,orient:"vertical",type:"scroll",x:"left",data:[]},series:[{label:this.chartDefaultOptions.label,name:"",type:"pie",radius:["50%","70%"],data:[]}]};t.setOption(e,!0),this.axisOption=e,this.chartObj=t}}},h=u,p=(a("6c4e"),a("2877")),d=Object(p["a"])(h,i,s,!1,null,"0db58b31",null);e["default"]=d.exports},"965d":function(t,e,a){"use strict";a("c558")},c06a:function(t,e,a){},c558:function(t,e,a){},df55:function(t,e,a){"use strict";var i=a("5530"),s=(a("d3b7"),a("08c2")),o=a("7a1a"),l=a("ed08"),n=a("a347"),r=a.n(n);e["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},textColor:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{lineStyle:{color:r.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:r.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:s["a"]},props:{},computed:{},watch:{},mounted:function(){var t=this;this.debouncedResize=Object(o["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",t.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(t){this.pageData.limit=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(t){this.pageData.page=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(t,e){return new Promise((function(a,i){t(e).then((function(t){Object(l["g"])(t),a&&a(t)})).catch((function(t){i&&i(t)}))}))},getChartYAxisStyle:function(t){var e=Object(l["D"])(this.chartYAxisStyle);if(!t)return e;for(var a in t){var s=e[a],o=t[a];e[a]=s?Object(i["a"])(Object(i["a"])({},s),o):o}return e}},deactivated:function(){}}},e508f:function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return l}));var i=a("b775");function s(t){return Object(i["a"])({url:"biProduct/contractProductRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"biCustomer/queryProductTypeList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"crmBiSearch/queryProductSucceedCustomerList ",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}}}]);