(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-41cafc9c"],{"26b0":function(t,e,n){"use strict";var a=n("5530"),o=n("df55"),r=n("a347"),i=n.n(r);e["a"]={data:function(){return{loading:!1,axisOption:{color:["#1890ff"],toolbox:{showTitle:!1,feature:{saveAsImage:{pixelRatio:2}}},tooltip:{textStyle:{color:i.a.colorBlack,fontWeight:i.a.axisLabelFontWeight},trigger:"axis",formatter:"{b} : {c}元",axisPointer:{type:"shadow"}}},postParams:{},list:[],fieldList:[]}},mixins:[o["a"]],components:{},props:{},computed:{},watch:{},mounted:function(){this.axisOption=Object(a["a"])(Object(a["a"])({},this.axisOption),{},{grid:o["a"].data().chartDefaultOptions.grid,xAxis:[Object(a["a"])(Object(a["a"])({},o["a"].data().chartXAxisStyle),{},{name:"（元）"})],yAxis:[Object(a["a"])(Object(a["a"])({},o["a"].data().chartYAxisStyle),{},{axisLine:{show:!0},type:"category"})],series:[{type:"bar",label:o["a"].data().chartDefaultBase.label,barMaxWidth:20,data:[]}]})},methods:{},deactivated:function(){}}},"71da":function(t,e,n){"use strict";n("ec3a4")},"9dde":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"main-container"},[n("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"show-user-select":!1,title:"合同金额排行","module-type":"ranking"},on:{load:function(e){t.loading=!0},change:t.getDataList}}),t._v(" "),n("div",{staticClass:"content"},[n("div",{staticClass:"content-title"},[t._v("合同金额排行（按实际下单时间）")]),t._v(" "),n("div",{directives:[{name:"empty",rawName:"v-empty",value:0===t.list.length,expression:"list.length === 0"}],staticClass:"axis-content",attrs:{"xs-empty-text":"暂无排行"}},[n("div",{attrs:{id:"axismain"}})]),t._v(" "),n("div",{staticClass:"table-content"},[n("div",{staticClass:"handle-bar"},[n("el-button",{staticClass:"export-btn",attrs:{size:"small",type:"primary"},on:{click:t.exportClick}},[t._v("导出")])],1),t._v(" "),n("el-table",{class:t.WKConfig.tableStyle.class,attrs:{size:"small",data:t.list,"cell-class-name":t.cellClassName,stripe:t.WKConfig.tableStyle.stripe,height:"400","highlight-current-row":""},on:{"row-click":t.handleRowClick}},[n("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"公司总排名"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n            "+t._s(e.$index+1)+"\n          ")]}}])}),t._v(" "),t._l(t.fieldList,(function(t,e){return n("el-table-column",{key:e,attrs:{prop:t.field,label:t.name,"show-overflow-tooltip":""}})}))],2)],1)]),t._v(" "),n("report-list",{attrs:{show:t.reportListShow,title:t.reportData.title,placeholder:t.reportData.placeholder,"crm-type":t.reportData.crmType,request:t.reportData.request,params:t.reportData.params,"record-request":t.reportData.recordRequest,paging:t.reportData.paging,sortable:t.reportData.sortable},on:{"update:show":function(e){t.reportListShow=e}}})],1)},o=[],r=(n("a434"),n("26b0")),i=n("b80b"),s=n("313e"),c=n("a475"),p=n("ef89"),u={name:"RankingContractStatistics",mixins:[r["a"],i["a"]],data:function(){return{postParams:{},detailFields:[{name:"money",crmType:"contract",timeName:"orderDate",list:[{formType:"checkStatus",name:"checkStatus",type:1,values:[1,10]},{formType:"user",name:"ownerUserId",type:3,values:[]}],request:p["d"]}]}},computed:{},mounted:function(){this.fieldList=[{field:"realname",name:"签订人"},{field:"deptName",name:"部门"},{field:"money",name:"合同金额（元）"}],this.initAxis()},methods:{getDataList:function(t){var e=this;this.postParams=t,this.loading=!0,Object(c["f"])(t).then((function(t){e.loading=!1,e.list=t.data||[];for(var n=[],a=[],o=t.data.length>10?10:t.data.length,r=0;r<o;r++){var i=t.data[r];n.splice(0,0,parseFloat(i.money)),a.splice(0,0,i.realname)}e.axisOption.yAxis[0].data=a,e.axisOption.series[0].data=n,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},initAxis:function(){this.chartObj=s["b"](document.getElementById("axismain")),this.chartObj.setOption(this.axisOption,!0)},exportClick:function(){this.requestExportInfo(c["g"],this.postParams)}}},l=u,d=(n("71da"),n("2877")),h=Object(d["a"])(l,a,o,!1,null,"e81a3a84",null);e["default"]=h.exports},a475:function(t,e,n){"use strict";n.d(e,"f",(function(){return o})),n.d(e,"g",(function(){return r})),n.d(e,"l",(function(){return i})),n.d(e,"m",(function(){return s})),n.d(e,"r",(function(){return c})),n.d(e,"s",(function(){return p})),n.d(e,"j",(function(){return u})),n.d(e,"k",(function(){return l})),n.d(e,"d",(function(){return d})),n.d(e,"e",(function(){return h})),n.d(e,"b",(function(){return b})),n.d(e,"c",(function(){return f})),n.d(e,"p",(function(){return m})),n.d(e,"q",(function(){return g})),n.d(e,"n",(function(){return T})),n.d(e,"o",(function(){return y})),n.d(e,"h",(function(){return j})),n.d(e,"i",(function(){return C})),n.d(e,"a",(function(){return x}));var a=n("b775");function o(t){return Object(a["a"])({url:"biRanking/contractRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(a["a"])({url:"biRanking/contractRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biRanking/receivablesRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biRanking/receivablesRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biRanking/contractCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(a["a"])({url:"biRanking/contractCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"biRanking/productCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"biRanking/productCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"biRanking/customerCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(a["a"])({url:"biRanking/customerCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function b(t){return Object(a["a"])({url:"biRanking/contactsCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(t){return Object(a["a"])({url:"biRanking/contactsCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(t){return Object(a["a"])({url:"biRanking/recordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(t){return Object(a["a"])({url:"biRanking/recordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function T(t){return Object(a["a"])({url:"biRanking/customerRecordCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function y(t){return Object(a["a"])({url:"biRanking/customerRecordCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(t){return Object(a["a"])({url:"biRanking/travelCountRanKing",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(t){return Object(a["a"])({url:"biRanking/travelCountRanKingExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function x(t){return Object(a["a"])({url:"crmBiSearch/searchContactsPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},ec3a4:function(t,e,n){},ef89:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return r})),n.d(e,"g",(function(){return i})),n.d(e,"h",(function(){return s})),n.d(e,"c",(function(){return c})),n.d(e,"e",(function(){return p})),n.d(e,"f",(function(){return u})),n.d(e,"d",(function(){return l})),n.d(e,"i",(function(){return d}));var a=n("b775");function o(t){return Object(a["a"])({url:"biAchievement/taskCompleteStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(a["a"])({url:"biAchievement/taskCompleteStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function i(t){return Object(a["a"])({url:"biProduct/productStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(t){return Object(a["a"])({url:"biProduct/productStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(a["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(a["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(a["a"])({url:"crmBiSearch/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(a["a"])({url:"crmBiSearch/searchContractPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function d(t){return Object(a["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}}}]);