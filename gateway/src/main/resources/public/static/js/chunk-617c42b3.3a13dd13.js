(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-617c42b3"],{"342c":function(t,e,c){"use strict";c("5f20")},"5f20":function(t,e,c){},dec4:function(t,e,c){t.exports=c.p+"static/img/1.3550e114.png"},e870:function(t,e,c){"use strict";c.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"wrapper"},[s("div",{staticClass:"http404-container"},[s("img",{staticClass:"pic",attrs:{src:c("dec4"),alt:""}}),t._v(" "),s("div",{staticClass:"title"},[t._v("抱歉，您没有访问权限")]),t._v(" "),s("div",{staticClass:"desc"},[t._v("请联系贵公司管理员对您的账号进行授权。如果已被授权但无法进入主页，请稍后尝试重新登录")]),t._v(" "),s("div",{staticClass:"btn"},[s("el-button",{attrs:{type:"primary"},on:{click:t.handleBack}},[t._v("返回登录页")]),t._v(" "),t.addRouters&&t.addRouters.length>0?s("el-button",{attrs:{type:"primary"},on:{click:t.handleHome}},[t._v("返回首页")]):t._e()],1)])])},a=[],n=c("5530"),i=(c("14d9"),c("2f62")),o=c("5f87"),r={name:"PageNoAuth",computed:Object(n["a"])({},Object(i["b"])(["addRouters"])),methods:{handleBack:function(){var t=this;Object(o["c"])().then((function(){t.$router.push("/login")}))},handleHome:function(){this.$router.push("/")}}},u=r,l=(c("342c"),c("2877")),d=Object(l["a"])(u,s,a,!1,null,"89b09a42",null);e["default"]=d.exports}}]);