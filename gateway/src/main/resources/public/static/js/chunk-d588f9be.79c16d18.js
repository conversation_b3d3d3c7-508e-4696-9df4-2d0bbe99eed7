(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d588f9be"],{"6ce9":function(n,t,e){"use strict";e.r(t);var a=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:n.loading,expression:"loading"}],staticClass:"init-set main"},[e("xr-header",[e("template",{slot:"label"},[n._v("初始化数据"),e("span",{staticClass:"label-des"},[n._v("（试用CRM后需要删除测试数据，正式管理企业信息）")])])],2),n._v(" "),e("div",{staticClass:"main-bod"},[e("reminder",{staticClass:"xr-reminder",attrs:{content:"提示：1、可以对单个应用或多个应用初始化，应用下的数据将全部删除；<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、注意初始化单个应用后，将影响其他应用下与此应用关联的数据；<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style='color: red;'>初始化的数据不可恢复，请谨慎操作！</span>"}}),n._v(" "),e("div",{staticClass:"init__label"},[n._v("初始化应用")]),n._v(" "),e("div",{staticClass:"init__body"},[e("el-select",{attrs:{multiple:""},model:{value:n.value,callback:function(t){n.value=t},expression:"value"}},n._l(n.options,(function(n){return e("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}))),n._v(" "),e("el-button",{staticClass:"save-btn",attrs:{disabled:0==n.value.length,type:"primary"},on:{click:n.saveClick}},[n._v("确定初始化")])],1)],1)],1)},l=[],s=(e("4de4"),e("caad"),e("a15b"),e("d81d"),e("e9f5"),e("910d"),e("7d54"),e("ab43"),e("d3b7"),e("2532"),e("159b"),e("b775"));function i(n){return Object(s["a"])({url:"adminConfig/verifyPassword",method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"},data:n})}function o(n){return Object(s["a"])({url:"adminConfig/moduleInitData",method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"},data:n})}var c=e("8f37"),u=e("f468"),r={name:"InitSet",components:{Reminder:c["a"],XrHeader:u["a"]},data:function(){return{loading:!1,value:[],optionsObj:{},options:[{value:"all",label:"全部应用"},{value:"crm",label:"客户管理"},{value:"taskExamine",label:"任务/审批"},{value:"log",label:"日志"},{value:"calendar",label:"日历"}]}},computed:{},watch:{value:function(n,t){n.includes("all")&&!t.includes("all")?this.value=this.options.map((function(n){return n.value})):!n.includes("all")&&t.includes("all")&&t.length===this.options.length?this.value=[]:n.includes("all")||n.length!==this.options.length-1?n.includes("all")&&n.length===this.options.length-1&&(this.value=n.filter((function(n){return"all"!==n}))):this.value=this.options.map((function(n){return n.value}))}},created:function(){var n=this;this.options.forEach((function(t){n.optionsObj[t.value]=t.label}))},methods:{saveClick:function(){var n=this;this.$prompt("初始化数据需验证登录密码","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"password",inputPlaceholder:"请输入密码",closeOnClickModal:!1}).then((function(t){var e=t.value;n.loading=!0,i({password:e}).then((function(t){n.loading=!1;var a=n.value.filter((function(n){return"all"!=n})),l=a.map((function(t){return"【".concat(n.optionsObj[t],"】")})).join("、");n.$confirm("确定初始化应用".concat(l,"吗？<br/>数据将永久删除，不可恢复！"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"warning"}).then((function(){n.loading=!0,o({password:e,temporaryCode:t.data,modules:a}).then((function(){n.loading=!1,n.value=[],n.$message({type:"success",message:"操作成功"}),setTimeout((function(){window.location.reload()}),1500)})).catch((function(){n.loading=!1}))})).catch((function(){}))})).catch((function(){n.loading=!1}))})).catch((function(){}))}}},p=r,d=(e("ceaf"),e("2877")),b=Object(d["a"])(p,a,l,!1,null,"090b24e3",null);t["default"]=b.exports},b36e:function(n,t,e){},ceaf:function(n,t,e){"use strict";e("b36e")}}]);