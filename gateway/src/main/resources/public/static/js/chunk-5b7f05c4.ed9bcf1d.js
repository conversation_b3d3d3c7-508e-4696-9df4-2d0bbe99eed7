(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b7f05c4"],{"08c2":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),e._v(" "),a("span",{staticClass:"text"},[e._v(e._s(e.title))])]),e._v(" "),e.showFilterView?[e.showYearSelect?e._e():a("time-type-select",{on:{change:e.timeTypeChange}}),e._v(" "),e.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":e.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:e.yearValue,callback:function(t){e.yearValue=t},expression:"yearValue"}}):e._e(),e._v(" "),e._t("after-time"),e._v(" "),e.showSimpleChoose?[e.showUserSelect&&e.showDeptSelect?a("el-select",{model:{value:e.simpleChooseType,callback:function(t){e.simpleChooseType=t},expression:"simpleChooseType"}},e._l(e.simpleOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))):e._e(),e._v(" "),1===e.simpleChooseType&&e.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:e.structuresSelectValue,callback:function(t){e.structuresSelectValue=t},expression:"structuresSelectValue"}}):e._e(),e._v(" "),2===e.simpleChooseType&&e.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:e.userSelectValue,callback:function(t){e.userSelectValue=t},expression:"userSelectValue"}}):e._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:e.dataTypeOptions,"user-checked-data":e.filterValue.userList,"dep-checked-data":e.filterValue.deptList,width:250},on:{select:e.radioMenuSelect},model:{value:e.filterDataType,callback:function(t){e.filterDataType=t},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:e.avatarData.realname,callback:function(t){e.$set(e.avatarData,"realname",t)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),e._v(" "),e.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:e.businessStatusValue,callback:function(t){e.businessStatusValue=t},expression:"businessStatusValue"}},e._l(e.businessOptions,(function(e){return a("el-option",{key:e.flowId,attrs:{label:e.flowName,value:e.flowId}})}))):e._e(),e._v(" "),e.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:e.productValue,callback:function(t){e.productValue=t},expression:"productValue"}}):e._e(),e._v(" "),e.showCustomSelect?a("el-select",{on:{change:e.customSelectChange},model:{value:e.customValue,callback:function(t){e.customValue=t},expression:"customValue"}},e._l(e.customOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}))):e._e(),e._v(" "),e._t("append"),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(t){return e.emitFilter(t)}}},[e._v("查询")]),e._v(" "),e._t("default")]:e._e()],2)},s=[],o=a("5530"),l=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),n=a("ea20"),r=a("657f"),c=a("bfba"),u=a("8f81"),h=a("83f1"),d=a("2f62"),p={name:"FiltrateHandleView",components:{TimeTypeSelect:r["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:h["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(o["a"])(Object(o["a"])({},Object(d["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var e=(this.filterValue.userList||[]).map((function(e){return e.realname})),t=(this.filterValue.deptList||[]).map((function(e){return e.name}));return{realname:e.concat(t).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var e=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){e.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(e){var t=this;Object(l["r"])().then((function(a){t.businessOptions=a.data||[],t.businessOptions.length>0&&(t.businessStatusValue=t.businessOptions[0].flowId),e(!0)})).catch((function(){t.$emit("error")}))},getProductCategoryIndex:function(){var e=this;Object(n["T"])({type:"tree"}).then((function(t){e.productOptions=t.data})).catch((function(){}))},radioMenuSelect:function(e,t){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=t.users,this.filterValue.deptList=t.strucs)},timeTypeChange:function(e){this.timeTypeValue=e},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var e=this,t={};this.showSimpleChoose?1===this.simpleChooseType?t.deptList=(this.structuresSelectValue||"").split(",").filter((function(e){return!!e})):t.userList=(this.userSelectValue||"").split(",").filter((function(e){return!!e})):"custom"!==this.filterValue.dataType?t.dataType=this.filterValue.dataType:(t.dataType=0,t.deptList=(this.filterValue.deptList||[]).map((function(e){return e.deptId})),t.userList=(this.filterValue.userList||[]).map((function(e){return e.userId}))),this.showYearSelect?(t.dateFilter="custom",t.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(t.startDate=this.timeTypeValue.startTime,t.endDate=this.timeTypeValue.endTime,t.dateFilter="custom"):t.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(t.typeId=this.businessStatusValue,t.businessItem=this.businessOptions.map((function(t){if(t.flowId===e.businessStatusValue)return t}))),this.showProductSelect&&(t.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",t)}}},f=p,m=(a("965d"),a("2877")),w=Object(m["a"])(f,i,s,!1,null,"6d7c8f9a",null);t["a"]=w.exports},2227:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"main-container"},[a("filtrate-handle-view",{staticClass:"filtrate-bar",attrs:{"module-type":"product",title:"员工通话记录分析"},on:{load:function(t){e.loading=!0},change:e.getList}}),e._v(" "),a("div",{staticClass:"content"},[a("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small",data:e.list,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight,"cell-style":e.cellStyle},on:{"row-click":e.handleRowClick}},e._l(e.headFieldList,(function(t,i){return a("el-table-column",{key:i,attrs:{formatter:e.timeFormatter,prop:t.field,label:t.name,width:t.width,"show-overflow-tooltip":""}})}))),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.showContractview?a("contract-detail",{staticClass:"d-view",attrs:{id:e.rowID},on:{"hide-view":function(t){e.showContractview=!1}}}):e._e(),e._v(" "),e.showCustomerView?a("customer-detail",{staticClass:"d-view",attrs:{id:e.rowID},on:{"hide-view":function(t){e.showCustomerView=!1}}}):e._e(),e._v(" "),e.showProductview?a("product-detail",{staticClass:"d-view",attrs:{id:e.rowID},on:{"hide-view":function(t){e.showProductview=!1}}}):e._e()],1)},s=[],o=a("d2b8"),l=a("995c"),n=a("d5b3"),r=a("b4c9"),c=a("df55"),u={name:"CallTalkCount",components:{ContractDetail:l["a"],CustomerDetail:n["a"],ProductDetail:r["a"]},mixins:[c["a"]],data:function(){return{loading:!1,tableHeight:document.documentElement.clientHeight-210,postParams:{},headFieldList:[{field:"userInfo.realname",name:"姓名",width:"115px"},{field:"totalCountCalls",name:"总通话数",width:"115px"},{field:"totalCountAnswer",name:"接通数",width:"115px"},{field:"rateAnswer",name:"接通率",width:"115px"},{field:"totalTimeCalls",name:"总通话时长",width:"115px"},{field:"totalCountCallsOut",name:"外呼总数",width:"115px"},{field:"totalCountAnswerOut",name:"外呼接通数",width:"115px"},{field:"rateAnswerOut",name:"外呼接通率",width:"115px"},{field:"totalTimeCallsOut",name:"外呼通话时长",width:"115px"},{field:"averageTimeCallOut",name:"外呼通话平均时长",width:"140px"},{field:"totalCountCallsIn",name:"呼入通话总数",width:"115px"},{field:"totalCountAnswerIn",name:"呼入通话接通数",width:"115px"},{field:"totalTimeCallsIn",name:"呼入通话总时长",width:"115px"},{field:"averageTimeCallIn",name:"呼入通话平均时长",width:"140px"},{field:"rateAnswerIn",name:"呼入接通率",width:"115px"}],list:[],spanList:[],newList:[],showContractview:!1,showCustomerView:!1,showProductview:!1,rowID:"",pageData:{}}},computed:{},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-210}},methods:{objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex,i=this.spanList[t];return 0==a?0==i.rowspan?{rowspan:0,colspan:0}:{rowspan:i.rowspan,colspan:1}:1==a?0==i.productRowspan?{rowspan:0,colspan:0}:{rowspan:i.productRowspan,colspan:1}:void 0},handleRowClick:function(e,t,a){"customerId"===t.property?(this.showProductview&&(this.showProductview=!1),this.showContractview&&(this.showContractview=!1),this.rowID=e.customerId,this.showCustomerView=!0):"productName"===t.property?(this.showCustomerView&&(this.showCustomerView=!1),this.showContractview&&(this.showContractview=!1),this.rowID=e.productId,this.showProductview=!0):"contractId"===t.property&&(this.showProductview&&(this.showProductview=!1),this.showCustomerView&&(this.showCustomerView=!1),this.rowID=e.contractId,this.showContractview=!0)},cellStyle:function(e){e.row,e.column,e.rowIndex,e.columnIndex},getList:function(e){var t=this;this.loading=!0,Object(o["a"])(e).then((function(e){t.list=e.data.list,t.total=e.data.totalRow,t.loading=!1})).catch((function(){t.loading=!1}))},timeFormatter:function(e,t,a,i){switch(t.label){case"外呼接通率":return 100*(a||"0")+"%";case"接通率":return 100*(a||"0")+"%";case"总通话时长":return this.MillisecondToDate(a);case"外呼通话平均时长":return this.MillisecondToDate(a);case"外呼通话时长":return this.MillisecondToDate(a);case"呼入通话总时长":return this.MillisecondToDate(a);case"呼入通话平均时长":return this.MillisecondToDate(a);case"呼入接通率":return 100*(a||"0")+"%";default:return a}},MillisecondToDate:function(e){var t;if(t=null!=e&&""!=e?parseFloat(e):0,t<60)return t+"秒";if(t<3600){var a=Math.floor(t/60);return a+"分"+Math.floor(t-60*a)+"秒"}var i=Math.floor(t/3600),s=Math.floor((t-3600*i)/60);return i+"小时"+s+"分"}}},h=u,d=(a("4d58"),a("2877")),p=Object(d["a"])(h,i,s,!1,null,"38ccc231",null);t["default"]=p.exports},"4d58":function(e,t,a){"use strict";a("a2cb")},"965d":function(e,t,a){"use strict";a("c558")},a2cb:function(e,t,a){},c558:function(e,t,a){},df55:function(e,t,a){"use strict";var i=a("5530"),s=(a("d3b7"),a("08c2")),o=a("7a1a"),l=a("ed08"),n=a("a347"),r=a.n(n);t["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},textColor:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{lineStyle:{color:r.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:r.a.colorBlack,fontWeight:r.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:r.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:s["a"]},props:{},computed:{},watch:{},mounted:function(){var e=this;this.debouncedResize=Object(o["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",e.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(e){this.pageData.limit=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(e){this.pageData.page=e,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(e,t){return new Promise((function(a,i){e(t).then((function(e){Object(l["g"])(e),a&&a(e)})).catch((function(e){i&&i(e)}))}))},getChartYAxisStyle:function(e){var t=Object(l["D"])(this.chartYAxisStyle);if(!e)return t;for(var a in e){var s=t[a],o=e[a];t[a]=s?Object(i["a"])(Object(i["a"])({},s),o):o}return t}},deactivated:function(){}}}}]);