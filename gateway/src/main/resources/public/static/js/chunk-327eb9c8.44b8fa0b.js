(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-327eb9c8"],{"0485":function(e,t,n){},"0904":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"base-info-set",attrs:{loading:e.loading,"body-style":{height:"100%"}}},[n("div",{staticClass:"base-info-set__header"},[e._v("\n    配置基础信息\n  ")]),e._v(" "),n("create-sections",{attrs:{title:"基础信息"}},[n("wk-form",{ref:"wkBaseFrom",attrs:{model:e.fieldsForm,rules:e.fieldsRules,"field-from":e.fieldsForm,"field-list":e.fields,"label-position":"top"},on:{change:e.formChange},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.data,i=t.index;return[a&&"userDep"==a.formType?n("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":e.fieldsForm.userList,"dep-value":e.fieldsForm.deptList},on:{"update:userValue":function(t){e.$set(e.fieldsForm,"userList",t)},"update:depValue":function(t){e.$set(e.fieldsForm,"deptList",t)},change:function(t){e.userDepSelectChange(arguments,a,i)}}}):[e._t("default",null,{data:a})]]}}])})],1)],1)},i=[],o=(n("d3b7"),n("10ff")),r=n("5067"),s=n("b592"),l={name:"BaseInfoSet",components:{CreateSections:o["a"],WkForm:r["a"],WkUserDepDialogSelect:s["a"]},props:{fields:Array,fieldsForm:Object,fieldsRules:Object},data:function(){return{loading:!1}},computed:{form:function(){return this.$refs.wkBaseFrom.instance}},mounted:function(){},methods:{validate:function(){var e=this;return new Promise((function(t){e.form.validate((function(n){n||e.$message.error("请完善基本信息"),t(n)}))}))},formChange:function(e,t,n){this.$emit("change",e,t,n)},userDepSelectChange:function(e,t,n){var a=e[2],i=e[3];this.$emit("change",t,n,{userList:a,deptList:i})},hidenView:function(){this.$emit("hiden-view")}}},c=l,u=(n("13c8"),n("2877")),d=Object(u["a"])(c,a,i,!1,null,"5eb0fa10",null);t["a"]=d.exports},"13c8":function(e,t,n){"use strict";n("f52d")},3817:function(e,t,n){"use strict";n("caad"),n("d81d"),n("14d9"),n("b0c0"),n("e9f5"),n("7d54"),n("ab43"),n("d3b7"),n("2532"),n("159b"),n("ddb0");var a=n("612a");t["a"]={data:function(){return{wkRoleOption:[]}},methods:{getListInfo:function(e,t){var n=this;e.forEach((function(e){0===e.examineType?t.push(n.getConditonWrapInfo(e)):t.push(n.getNodeInfo(e))}))},getNodeInfo:function(e){var t=this,n={examineType:e.examineType,name:e.name,conditionList:[],deptList:e.deptList,examineErrorHandling:e.examineErrorHandling,roleId:e.roleId,type:e.type,userList:e.userList||[],chooseType:e.chooseType,rangeType:e.rangeType,parentLevel:1,tempParentLevel:1,overType:1,isError:!1};return 5===e.examineType?1===e.type?0===e.parentLevel?(n.overType=0,n.parentLevel=1):(n.overType=1,n.parentLevel=e.parentLevel):2===e.type&&(n.overType=0,n.parentLevel=1,n.tempParentLevel=e.parentLevel):n.parentLevel=e.parentLevel,e.roleId&&(n.roleObj={},this.getRoleList().then((function(a){t.getRoleObj(e.roleId,a,n)}))),n},getConditionNodeInfo:function(e,t){var n=this,a={conditionName:e.conditionName,sort:t+1,conditionDataList:[],examineDataList:[],isError:!1};return e.conditionDataList.forEach((function(e){var t={name:e.name,fieldName:e.fieldName,fieldId:e.fieldId,type:e.type,conditionType:e.conditionType};if(3===e.type||9===e.type)t.values=e.values,n.validateSetting(t);else if(6===e.conditionType)e.values&&4===e.values.length?(t.leftValue=parseInt(e.values[0]),t.leftCondition=parseInt(e.values[1]),t.rightCondition=parseInt(e.values[2]),t.rightValue=parseInt(e.values[3])):(t.leftValue=0,t.leftCondition=1,t.rightCondition=1,t.rightValue=0),t.values=0;else if(8===e.conditionType){var i=e.values;t.values={deptList:i.deptList.map((function(e){return e.deptId})),roleList:i.roleList,userList:i.userList.map((function(e){return e.userId}))},t.deptList=i.deptList,t.userList=i.userList,t.roleList=[],i.roleList.length>0&&n.getRoleList().then((function(e){n.getRoleItems(i.roleList,e,t.roleList)}))}else t.leftValue=0,t.leftCondition=1,t.rightCondition=1,t.rightValue=0,e.values&&e.values.length>0?t.values=e.values[0]:t.values=0;a.conditionDataList.push(t)})),a},validateSetting:function(e){},getRoleList:function(){var e=this;return new Promise((function(t,n){e.wkRoleOption.length>0?t(e.wkRoleOption):Object(a["n"])().then((function(n){e.wkRoleOption=n.data||[],t(e.wkRoleOption)})).catch((function(){}))}))},getRoleItems:function(e,t,n){for(var a=0;a<t.length;a++){var i=t[a];if(e.includes(i.roleId)&&(n.push(i),e.length===n.length))break;i.list&&this.getRoleItems(e,i.list,n)}},getRoleObj:function(e,t,n){for(var a=0;a<t.length;a++){var i=t[a];if(i.roleId===e){n.roleObj=i;break}i.list&&this.getRoleObj(e,i.list,n)}},getConditonWrapInfo:function(e){var t=this,n={examineType:e.examineType,name:e.name,conditionList:[]};return e.conditionList.forEach((function(e,a){var i=t.getConditionNodeInfo(e,a);n.conditionList.push(i),t.getListInfo(e.examineDataList,i.examineDataList)})),n}}}},4378:function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("flexbox",{staticClass:"wk-backgroud-tabs",staticStyle:{width:"auto"},attrs:{justify:"space-between",align:"center"}},[n("flexbox",{staticStyle:{"z-index":"1",width:"auto"},attrs:{align:"center"}},[e._t("left")],2),e._v(" "),n("div",{staticClass:"wk-tabs__wrap"},[n("div",{staticClass:"wk-tabs"},e._l(e.options,(function(t,a){return n("div",{key:a,staticClass:"wk-tabs__item",class:{active:t.value===e.value},on:{click:function(n){e.tabsClick(t,a)}}},[e._v("\n        "+e._s(t.label)),t.helpType?n("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":t.helpType,"data-id":t.helpId},on:{click:function(e){e.stopPropagation()}}}):e._e()])})))]),e._v(" "),n("flexbox",{staticStyle:{"z-index":"1",width:"auto"},attrs:{align:"center"}},[e._t("right")],2)],1)},i=[],o=(n("a9e3"),{name:"WkBackgroudTabs",components:{},props:{value:[String,Number],options:Array},data:function(){return{}},computed:{},watch:{},created:function(){},mounted:function(){},beforeDestroy:function(){},methods:{tabsClick:function(e,t){this.$emit("input",e.value),this.$emit("tab-click",e,t)}}}),r=o,s=(n("f5e9"),n("2877")),l=Object(s["a"])(r,a,i,!1,null,"8ec15ab4",null);t["a"]=l.exports},"575d":function(e,t,n){},"5a80":function(e,t,n){"use strict";n("a946")},"612a":function(e,t,n){"use strict";n.d(t,"k",(function(){return i})),n.d(t,"l",(function(){return o})),n.d(t,"m",(function(){return r})),n.d(t,"p",(function(){return s})),n.d(t,"o",(function(){return l})),n.d(t,"n",(function(){return c})),n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return p})),n.d(t,"d",(function(){return f})),n.d(t,"h",(function(){return h})),n.d(t,"i",(function(){return m})),n.d(t,"g",(function(){return v})),n.d(t,"t",(function(){return b})),n.d(t,"s",(function(){return g})),n.d(t,"r",(function(){return y})),n.d(t,"q",(function(){return L})),n.d(t,"j",(function(){return _})),n.d(t,"f",(function(){return O})),n.d(t,"e",(function(){return j}));n("e9f5"),n("7d54"),n("b64b"),n("d3b7"),n("159b");var a=n("b775");function i(e){return Object(a["a"])({url:"adminDept/deleteDept/"+e.id,method:"post"})}function o(e){return Object(a["a"])({url:"adminDept/setDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(e){return Object(a["a"])({url:"adminDept/addDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(e){return Object(a["a"])({url:"adminUser/setUser",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(e){return Object(a["a"])({url:"adminUser/addUser",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(e){return Object(a["a"])({url:"adminRole/getAllRoleList",method:"post",data:e})}function u(){return Object(a["a"])({url:"adminRole/queryDefaultRole",method:"post"})}function d(e){return Object(a["a"])({url:"adminRole/getRoleList",method:"post",data:e})}function p(e){return Object(a["a"])({url:"adminRole/queryAuthRole/".concat(e),method:"post"})}function f(e,t){return Object(a["a"])({url:"adminRole/updateAuthRole/".concat(e),method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(e){return Object(a["a"])({url:"adminUser/resetPassword",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(e){return Object(a["a"])({url:"adminUser/usernameEdit",method:"post",data:e})}function v(e){return Object(a["a"])({url:"adminUser/usernameEditByManager",method:"post",data:e})}function b(e){return Object(a["a"])({url:"adminUser/setUserStatus",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(e){return Object(a["a"])({url:"adminUser/downloadExcel",method:"post",data:e,responseType:"blob"})}function y(e){var t=new FormData;return Object.keys(e).forEach((function(n){t.append(n,e[n])})),Object(a["a"])({url:"adminUser/excelImport",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"},timeout:6e4})}function L(e){return Object(a["a"])({url:"adminUser/downExcel",method:"post",data:e,responseType:"blob"})}function _(e){return Object(a["a"])({url:"crmCall/authorize",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function O(e){return Object(a["a"])({url:"adminUser/setUserDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function j(){return Object(a["a"])({url:"adminUser/countNumOfUser",method:"post"})}},"94d4":function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-select",e._g(e._b({ref:"select",staticClass:"role-employee-select",on:{"visible-change":e.selectVisibleChange,change:e.selectChange},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}},"el-select",e.$attrs,!1),e.$listeners),[n("div",{staticClass:"role-employee-select__body"},[n("el-tabs",{ref:"roleTabs",class:{"el-tabs__header--hidden":e.config.onlyShowRole},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{ref:"roleTabPane",attrs:{label:"自选角色",name:"role"}},e._l(e.roleOption,(function(t){return n("div",{key:t.parentId,attrs:{label:t.name}},[n("div",{staticClass:"role-employee-select__title"},[e._v(e._s(t.name))]),e._v(" "),e._l(t.list,(function(e){return n("el-option",{key:e.roleId,staticStyle:{padding:"0 10px"},attrs:{label:e.roleName,value:e.roleId}})}))],2)}))),e._v(" "),n("el-tab-pane",{attrs:{label:"按员工复制角色",name:"employee"}},[n("el-input",{staticClass:"search-input",attrs:{placeholder:"搜索成员",size:"small","prefix-icon":"el-icon-search"},on:{input:e.userSearch},model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}}),e._v(" "),e._l(e.userOption,(function(t){return n("el-option",{directives:[{name:"show",rawName:"v-show",value:!t.isHide,expression:"!item.isHide"}],key:t.userId,staticStyle:{padding:"0 10px"},attrs:{label:t.realname,value:t.userId+"@"+t.roleId}},[n("flexbox",{staticClass:"cell"},[n("xr-avatar",{staticClass:"cell__img",attrs:{name:t.realname,size:24,src:t.img}}),e._v(" "),n("div",{staticClass:"cell__body"},[e._v(e._s(t.realname))]),e._v(" "),n("el-tooltip",{attrs:{content:t.roleName,effect:"dark",placement:"top"}},[n("div",{staticClass:"cell__footer text-one-line"},[e._v(e._s(t.roleName))])])],1)],1)}))],2)],1)],1)])},i=[],o=n("5530"),r=(n("e9f5"),n("7d54"),n("a9e3"),n("d3b7"),n("ac1f"),n("466d"),n("159b"),n("612a")),s=n("2934"),l=n("8122"),c=n("a318"),u=n.n(c),d=n("8ed6"),p={onlyShowRole:!1,roleRequest:null},f={name:"RoleEmployeeSelect",components:{},props:{props:{type:Object,default:function(){return{}}},value:[Array,Number,String]},data:function(){return{selectValue:[],activeName:"",roleOption:[],userOption:[],searchInput:""}},computed:{config:function(){return Object(d["a"])(Object(o["a"])({},p),this.props||{})},select:function(){return this.$refs.select}},watch:{value:{handler:function(){Object(l["valueEquals"])(this.value,this.selectValue)||(this.selectValue=this.value)},immediate:!0}},created:function(){this.getRoleList(),this.getUserList()},mounted:function(){},beforeDestroy:function(){},methods:{selectVisibleChange:function(e){""!==this.activeName&&"0"!==this.activeName||(this.activeName="role")},getRoleList:function(){var e=this,t=this.config.roleRequest||r["n"];t().then((function(t){e.roleOption=t.data||[]})).catch((function(){}))},getUserList:function(){var e=this;Object(s["w"])({pageType:0}).then((function(t){e.userOption=t.data.list||[]})).catch((function(){}))},selectChange:function(){this.$emit("input",this.selectValue)},userSearch:function(){var e=this;this.userOption.forEach((function(t){t.isHide=!u.a.match(t.realname,e.searchInput)}))}}},h=f,m=(n("5a80"),n("c128"),n("2877")),v=Object(m["a"])(h,a,i,!1,null,"6ca934ac",null);t["a"]=v.exports},a946:function(e,t,n){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},c128:function(e,t,n){"use strict";n("575d")},f52d:function(e,t,n){},f5e9:function(e,t,n){"use strict";n("0485")}}]);