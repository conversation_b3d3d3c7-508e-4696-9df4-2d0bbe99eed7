(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ba40a49a"],{"30f26":function(e,t,a){"use strict";a("b50b")},b0a8:function(e,t,a){"use strict";var i=a("5579");t["a"]={components:{WkEmpty:i["a"]},props:{},data:function(){return{currentPage:1,pageSize:15,pageSizes:[15,30,60,100],total:0,rowHeight:44,otherTableHeight:265,tableHeight:200}},computed:{},watch:{},created:function(){},mounted:function(){var e=this;window.onresize=function(){e.updateTableHeight()}},beforeDestroy:function(){},methods:{handleSizeChange:function(e){this.pageSize=e,this.getList()},handleCurrentChange:function(e){this.currentPage=e,this.getList()},updateTableHeight:function(){var e=document.documentElement.clientHeight-this.otherTableHeight,t=this.rowHeight*this.list.length+41;this.tableHeight=t>e?e:0===this.list.length?200:t}}}},b50b:function(e,t,a){},fe09:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("flexbox",{staticClass:"main-header",attrs:{justify:"space-between"}},[a("div",{staticClass:"main-header__left"},[a("span",{staticClass:"title"},[e._v(e._s(e.title))])])]),e._v(" "),a("flexbox",{staticClass:"main-header is-filter-header",attrs:{justify:"space-between"}},[a("div",{staticClass:"main-header__left"},[a("span",{staticClass:"tabs"},[a("span",{staticClass:"tabs-label"},[e._v("显示:")]),e._v(" "),e._l(e.tabs,(function(t,i){return a("el-button",{key:i,attrs:{type:t.name===e.tabsSelectValue?"selected":null},on:{click:function(a){e.tabsChange(t.name)}}},[e._v(e._s(t.label))])}))],2)])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],key:(e.tableHeight||"0")+"-"+e.pageSize,staticStyle:{width:"100%"},attrs:{size:"small","row-height":e.rowHeight,data:e.list,height:e.tableHeight,"cell-class-name":e.cellClassName,"use-virtual":"","row-key":"taskId","highlight-current-row":""},on:{"row-click":e.handleRowClick,"selection-change":e.handleSelectionChange}},e._l(e.tableHeaderFields,(function(t,i){return a("el-table-column",{key:i,attrs:{fixed:0==i,prop:t.field,label:t.name,"min-width":t.width,"show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row,n=a.column;a.$index;return[[e._v("\n          "+e._s(e.fieldFormatter(i,n,i[n.property],t))+"\n        ")]]}}])})}))),e._v(" "),a("div",{staticClass:"pagination-container"},[a("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("c-r-m-all-detail",{attrs:{id:e.rowID,visible:e.showCrmDetail,"crm-type":e.detailCrmType,"no-listener-class":["relate-cell","examine-content"]},on:{"update:visible":function(t){e.showCrmDetail=t},handle:e.getList}}),e._v(" "),a("examine-handle",{attrs:{id:e.rowID,show:e.showExamineHandle,"record-id":e.rowData.examineRecordId,detail:e.rowData,status:e.examineStatus,"examine-type":"crm_"+e.crmType},on:{close:function(t){e.showExamineHandle=!1},save:e.getList}})],1)},n=[],s=a("ffce"),l=a("c8fa"),r=a("4219"),o=a("b0a8"),c=a("7403"),h={name:"Index",components:{CRMAllDetail:l["a"],ExamineHandle:r["a"]},mixins:[o["a"],c["a"]],props:{},data:function(){return{crmType:"",tabsSelectValue:"1",tabs:[{label:"全部",name:"all"},{label:"待我审批的",name:"1"},{label:"我已审批的",name:"2"}],tableHeaderFields:[{name:"审批内容",field:"category",width:120,formType:"text"},{name:"创建人",field:"createUser",width:120,formType:"user"},{name:"状态",field:"examineStatus",width:80,formType:"examineStatus"},{name:"创建日期 ",field:"createTime",width:80}],list:[],loading:!1,detailIndex:0,rowID:"",rowData:{},detailCrmType:"",showCrmDetail:!1,showExamineHandle:!1,recordId:"",examineStatus:1}},computed:{title:function(){return{contract:"待我审批（合同）",receivables:"待我审批（回款）",invoice:"待我审批（发票）"}[this.crmType]}},watch:{},mounted:function(){this.crmType=this.$route.params.type,this.refreshList()},beforeRouteUpdate:function(e,t,a){this.crmType=e.params.type,this.tabsSelectValue="1",this.refreshList(),a()},beforeDestroy:function(){},methods:{cellClassName:function(e){e.row;var t=e.column;e.rowIndex,e.columnIndex;return"category"===t.property?"can-visit--underline":""},handleRowClick:function(e,t,a){this.detailIndex=this.getObjIndex(this.list,"categoryId",e.categoryId),this.detailCrmType=this.crmType,this.rowID=e.categoryId,this.showCrmDetail=!0},getObjIndex:function(e,t,a){for(var i=0;i<e.length;i++){var n=e[i];if(n[t]===a)return i}return null},handleSelectionChange:function(e){},refreshList:function(){this.currentPage=1,this.list=[],this.getList()},getList:function(){var e=this;this.loading=!0;var t={page:this.currentPage,limit:15,status:"all"==this.tabsSelectValue?"":this.tabsSelectValue};"contract"==this.crmType?t.label=1:"receivables"==this.crmType?t.label=2:"invoice"==this.crmType&&(t.label=3),Object(s["e"])(t).then((function(a){e.loading=!1;var i="all"==e.tabsSelectValue?"":e.tabsSelectValue;if(t.status==i){var n=a.data||{};e.list=n.list,e.total=n.totalRow,e.updateTableHeight()}else e.refreshList()})).catch((function(){e.loading=!1}))},fieldFormatter:function(e,t,a,i){return"createUser"===t.property?e.createUser?e.createUser.realname:"":"examineStatus"===t.property?this.getStatusName(e.examineStatus):a},tabsChange:function(e){this.tabsSelectValue=e,this.refreshList()},cellHandle:function(e,t,a){this.detailIndex=a,"detail"==e?(this.detailCrmType=this.crmType,this.rowID=t.categoryId,this.showCrmDetail=!0):"relate-detail"==e?(this.showDview=!1,this.rowID=t.id,this.detailCrmType=t.type,this.showCrmDetail=!0):"reject"!=e&&"pass"!=e&&"withdraw"!=e||(this.rowID=t.categoryId,this.examineStatus={pass:1,reject:2,withdraw:4}[e],this.rowData=t,this.showExamineHandle=!0)}}},d=h,u=(a("30f26"),a("2877")),m=Object(u["a"])(d,i,n,!1,null,"38707929",null);t["default"]=m.exports}}]);