(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-22e687b4"],{"23d3":function(e,t,n){"use strict";n("d77b")},"28f1":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("keep-alive",[n(e.componentName,{tag:"component",on:{"menu-select":e.menuSelect}})],1)},l=[],o=n("5530"),i=n("3f80"),s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("c-r-m-list-head",{attrs:{"crm-type":e.crmType,title:"名片线索管理"},on:{"on-handle":e.listHeadHandle,"on-export":e.exportInfos}},[e.menuItems.length>1?n("el-menu",{ref:"elMenu",attrs:{slot:"icon","default-active":e.crmType,mode:"horizontal","active-text-color":"#2362FB"},on:{select:e.menuSelect},slot:"icon"},e._l(e.menuItems,(function(t,a){return n("el-menu-item",{key:a,attrs:{index:t.path}},[n("img",{attrs:{src:t.icon}}),e._v(" "),n("span",[e._v(e._s(t.title))])])}))):e._e()],1),e._v(" "),n("div",{directives:[{name:"empty",rawName:"v-empty",value:!e.indexAuth,expression:"!indexAuth"}],staticClass:"crm-container",attrs:{"xs-empty-icon":"nopermission","xs-empty-text":"暂无权限"}},[n("c-r-m-table-head",{ref:"crmTableHead",attrs:{"crm-type":e.crmType,search:e.search,placeholder:"手机号/微信名称"},on:{"update:search":function(t){e.search=t},"on-search":e.crmSearch,filter:e.handleFilter,handle:e.handleHandle,scene:e.handleScene}},[n("template",{slot:"custom"},[n("div",[e._v("场景：")]),e._v(" "),n("el-select",{on:{change:e.selectApplet},model:{value:e.appletType,callback:function(t){e.appletType=t},expression:"appletType"}},e._l(e.appletOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})))],1)],2),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"n-table--border",class:e.crmTableClass,staticStyle:{width:"100%"},attrs:{id:"crm-table",size:"small",data:e.list,height:e.tableHeight,border:"","highlight-current-row":""},on:{"row-click":e.handleRowClick,"sort-change":e.sortChange,"header-dragend":e.handleHeaderDragend,"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{"show-overflow-tooltip":"",type:"selection",align:"center",width:"55"}}),e._v(" "),e._l(e.fieldList,(function(t,a){return n("el-table-column",{key:a,attrs:{prop:t.prop,label:t.label,width:t.width,sortable:"custom","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(a){return["weixinImg"==t.prop?[n("el-image",{staticClass:"image",attrs:{src:a.row.weixinImg}})]:"isTransform"==t.prop?[e._v("\n            "+e._s(1==a.row[t.prop]?"是":"否")+"\n          ")]:[e._v("\n            "+e._s(a.row[t.prop])+"\n          ")]]}}])})})),e._v(" "),n("el-table-column")],2),e._v(" "),n("div",{staticClass:"pagination-container"},[n("el-dropdown",{attrs:{trigger:"click",placement:"top"}},[n("el-button",{staticClass:"dropdown-btn"},[n("i",{staticClass:"el-icon-s-fold"})]),e._v(" "),n("el-dropdown-menu",{staticClass:"wk-table-style-dropdown-menu",attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",[n("span",{on:{click:function(e){e.stopPropagation()}}},[n("el-switch",{model:{value:e.tableStyleObj.rightBorderShow,callback:function(t){e.$set(e.tableStyleObj,"rightBorderShow",t)},expression:"tableStyleObj.rightBorderShow"}}),e._v("显示竖向分割线")],1)]),e._v(" "),n("el-dropdown-item",[n("span",{on:{click:function(e){e.stopPropagation()}}},[n("el-switch",{model:{value:e.tableStyleObj.bottomBorderShow,callback:function(t){e.$set(e.tableStyleObj,"bottomBorderShow",t)},expression:"tableStyleObj.bottomBorderShow"}}),e._v("显示横向分割线")],1)]),e._v(" "),n("el-dropdown-item",[n("span",{on:{click:function(e){e.stopPropagation()}}},[n("el-switch",{model:{value:e.tableStyleObj.stripe,callback:function(t){e.$set(e.tableStyleObj,"stripe",t)},expression:"tableStyleObj.stripe"}}),e._v("显示斑马纹")],1)])],1)],1),e._v(" "),n("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.showDview?n("applet-detail",{staticClass:"d-view",attrs:{id:e.rowID},on:{handle:e.handleHandle,"hide-view":function(t){e.showDview=!1}}}):e._e()],1)},r=[],c=(n("14d9"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e._v("详情")])}),p=[],d={},u=d,m=n("2877"),h=Object(m["a"])(u,c,p,!1,null,null,null),b=h.exports,v=n("e505"),f={name:"AppletIndex",components:{AppletDetail:b},mixins:[v["a"]],data:function(){return{crmType:"applet",appletType:0,appletOptions:[{label:"全部线索",value:0},{label:"我负责的线索",value:1},{label:"下属负责的线索",value:2}]}},computed:{menuItems:function(){var e=[];return this.crm&&this.crm.leads&&e.push({title:"线索管理",path:"leads",icon:n("2b84")}),this.crm&&this.crm.applet&&e.push({title:"名片线索",path:"applet",icon:n("ead7")}),e}},mounted:function(){},deactivated:function(){this.$refs.elMenu&&(this.$refs.elMenu.activeIndex=this.crmType)},methods:{menuSelect:function(e,t){this.$emit("menu-select",e,t)},selectApplet:function(e){this.appletType=e,this.currentPage=1,this.getList()}}},w=f,g=(n("23d3"),Object(m["a"])(w,s,r,!1,null,"d355ffce",null)),_=g.exports,y=n("2f62"),x={name:"LeadsAllIndex",components:{LeadsIndex:i["default"],AppletIndex:_},props:{},data:function(){return{componentName:""}},computed:Object(o["a"])({},Object(y["b"])(["crm"])),watch:{},mounted:function(){this.crm&&this.crm.leads?this.componentName="LeadsIndex":this.crm&&this.crm.applet&&(this.componentName="AppletIndex")},beforeDestroy:function(){},methods:{menuSelect:function(e,t){this.componentName={leads:"LeadsIndex",applet:"AppletIndex"}[e]}}},S=x,k=Object(m["a"])(S,a,l,!1,null,"0a714eba",null);t["default"]=k.exports},d77b:function(e,t,n){}}]);