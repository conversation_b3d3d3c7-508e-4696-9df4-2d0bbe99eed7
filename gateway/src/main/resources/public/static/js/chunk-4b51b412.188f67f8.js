(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b51b412"],{"08c2":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("flexbox",{staticClass:"filtrate-content",attrs:{justify:"flex-start"}},[a("flexbox",{staticClass:"title-box",attrs:{justify:"flex-start"}},[a("div",{staticClass:"icon-box"},[a("span",{staticClass:"wk wk-my-task icon"})]),t._v(" "),a("span",{staticClass:"text"},[t._v(t._s(t.title))])]),t._v(" "),t.showFilterView?[t.showYearSelect?t._e():a("time-type-select",{on:{change:t.timeTypeChange}}),t._v(" "),t.showYearSelect?a("el-date-picker",{attrs:{clearable:!1,"picker-options":t.pickerOptions,type:"year","value-format":"yyyy",placeholder:"选择年"},model:{value:t.yearValue,callback:function(e){t.yearValue=e},expression:"yearValue"}}):t._e(),t._v(" "),t._t("after-time"),t._v(" "),t.showSimpleChoose?[t.showUserSelect&&t.showDeptSelect?a("el-select",{model:{value:t.simpleChooseType,callback:function(e){t.simpleChooseType=e},expression:"simpleChooseType"}},t._l(t.simpleOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))):t._e(),t._v(" "),1===t.simpleChooseType&&t.showDeptSelect?a("wk-dept-dialog-select",{staticClass:"wk-dep-select",attrs:{radio:"",placeholder:"选择部门（默认为本部门及下属部门）"},model:{value:t.structuresSelectValue,callback:function(e){t.structuresSelectValue=e},expression:"structuresSelectValue"}}):t._e(),t._v(" "),2===t.simpleChooseType&&t.showUserSelect?a("wk-user-dialog-select",{staticClass:"wk-user-select",attrs:{radio:"",placeholder:"选择员工（默认为本人及下属）"},model:{value:t.userSelectValue,callback:function(e){t.userSelectValue=e},expression:"userSelectValue"}}):t._e()]:a("xr-radio-menu",{attrs:{"show-default":!1,options:t.dataTypeOptions,"user-checked-data":t.filterValue.userList,"dep-checked-data":t.filterValue.deptList,width:250},on:{select:t.radioMenuSelect},model:{value:t.filterDataType,callback:function(e){t.filterDataType=e},expression:"filterDataType"}},[a("el-input",{staticClass:"el-input--no-bg",attrs:{slot:"reference",readonly:!0},slot:"reference",model:{value:t.avatarData.realname,callback:function(e){t.$set(t.avatarData,"realname",e)},expression:"avatarData.realname"}},[a("i",{staticClass:"el-icon-arrow-up",attrs:{slot:"suffix"},slot:"suffix"})])],1),t._v(" "),t.showBusinessSelect?a("el-select",{attrs:{placeholder:"项目组"},model:{value:t.businessStatusValue,callback:function(e){t.businessStatusValue=e},expression:"businessStatusValue"}},t._l(t.businessOptions,(function(t){return a("el-option",{key:t.flowId,attrs:{label:t.flowName,value:t.flowId}})}))):t._e(),t._v(" "),t.showProductSelect?a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.productOptions,"show-all-levels":!1,props:{children:"children",label:"label",value:"categoryId"},"change-on-select":""},model:{value:t.productValue,callback:function(e){t.productValue=e},expression:"productValue"}}):t._e(),t._v(" "),t.showCustomSelect?a("el-select",{on:{change:t.customSelectChange},model:{value:t.customValue,callback:function(e){t.customValue=e},expression:"customValue"}},t._l(t.customOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})}))):t._e(),t._v(" "),t._t("append"),t._v(" "),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-search"},nativeOn:{click:function(e){return t.emitFilter(e)}}},[t._v("查询")]),t._v(" "),t._t("default")]:t._e()],2)},s=[],o=a("5530"),n=(a("99af"),a("4de4"),a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("1112")),r=a("ea20"),l=a("657f"),c=a("bfba"),u=a("8f81"),p=a("83f1"),h=a("2f62"),d={name:"FiltrateHandleView",components:{TimeTypeSelect:l["a"],WkDeptDialogSelect:c["a"],WkUserDialogSelect:u["a"],XrRadioMenu:p["a"]},props:{title:{type:String,default:""},showFilterView:{default:!0,type:Boolean},showYearSelect:{default:!1,type:Boolean},showBusinessSelect:{default:!1,type:Boolean},showProductSelect:{default:!1,type:Boolean},showCustomSelect:{default:!1,type:Boolean},customDefault:{type:String,default:""},customOptions:{default:function(){return[]},type:Array},showUserSelect:{default:void 0,type:Boolean},showDeptSelect:{default:void 0,type:Boolean}},data:function(){return{pickerOptions:{disabledDate:function(t){return t.getTime()>Date.now()}},yearValue:"",filterDataType:2,filterValue:{dataType:2,userList:[],deptList:[]},timeTypeValue:{},businessOptions:[],businessStatusValue:"",productValue:[],productOptions:[],customValue:"",dataTypeOptions:[{label:"仅本人",command:1},{label:"本人及下属",command:2},{label:"仅本部门",command:3},{label:"本部门及下属部门",command:4},{label:"自定义",command:"custom"}],simpleChooseType:1,simpleOptions:[{label:"部门",value:1},{label:"员工",value:2}],structuresSelectValue:"",userSelectValue:""}},computed:Object(o["a"])(Object(o["a"])({},Object(h["b"])(["userInfo"])),{},{showSimpleChoose:function(){return void 0!==this.showUserSelect&&void 0!==this.showDeptSelect},avatarData:function(){if("custom"===this.filterValue.dataType){var t=(this.filterValue.userList||[]).map((function(t){return t.realname})),e=(this.filterValue.deptList||[]).map((function(t){return t.name}));return{realname:t.concat(e).join(","),img:""}}return 1==this.filterValue.dataType?this.userInfo:{showIcon:!0,realname:{1:"仅本人",2:"本人及下属",3:"仅本部门",4:"本部门及下属部门"}[this.filterValue.dataType]}}}),mounted:function(){var t=this;this.showYearSelect&&(this.yearValue=this.$moment().year().toString()),void 0===this.showDeptSelect||this.showDeptSelect||(this.simpleChooseType=2),this.showCustomSelect&&(this.customValue=this.customDefault),this.$emit("load"),this.showBusinessSelect?this.getBusinessStatusList((function(){t.emitFilter()})):this.emitFilter(),this.showProductSelect&&this.getProductCategoryIndex()},methods:{getBusinessStatusList:function(t){var e=this;Object(n["r"])().then((function(a){e.businessOptions=a.data||[],e.businessOptions.length>0&&(e.businessStatusValue=e.businessOptions[0].flowId),t(!0)})).catch((function(){e.$emit("error")}))},getProductCategoryIndex:function(){var t=this;Object(r["T"])({type:"tree"}).then((function(e){t.productOptions=e.data})).catch((function(){}))},radioMenuSelect:function(t,e){this.filterValue.dataType=this.filterDataType,"custom"!=this.filterDataType?(this.filterValue.userList=[],this.filterValue.deptList=[]):(this.filterValue.userList=e.users,this.filterValue.deptList=e.strucs)},timeTypeChange:function(t){this.timeTypeValue=t},customSelectChange:function(){this.$emit("typeChange",this.customValue)},emitFilter:function(){var t=this,e={};this.showSimpleChoose?1===this.simpleChooseType?e.deptList=(this.structuresSelectValue||"").split(",").filter((function(t){return!!t})):e.userList=(this.userSelectValue||"").split(",").filter((function(t){return!!t})):"custom"!==this.filterValue.dataType?e.dataType=this.filterValue.dataType:(e.dataType=0,e.deptList=(this.filterValue.deptList||[]).map((function(t){return t.deptId})),e.userList=(this.filterValue.userList||[]).map((function(t){return t.userId}))),this.showYearSelect?(e.dateFilter="custom",e.startDate=this.yearValue+"-01-01"):"custom"===this.timeTypeValue.type?(e.startDate=this.timeTypeValue.startTime,e.endDate=this.timeTypeValue.endTime,e.dateFilter="custom"):e.dateFilter=this.timeTypeValue.value,this.showBusinessSelect&&(e.typeId=this.businessStatusValue,e.businessItem=this.businessOptions.map((function(e){if(e.flowId===t.businessStatusValue)return e}))),this.showProductSelect&&(e.categoryId=this.productValue.length>0?this.productValue[this.productValue.length-1]:""),this.$emit("change",e)}}},m=d,f=(a("965d"),a("2877")),b=Object(f["a"])(m,i,s,!1,null,"6d7c8f9a",null);e["a"]=b.exports},"965d":function(t,e,a){"use strict";a("c558")},bf0a:function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return o})),a.d(e,"e",(function(){return n})),a.d(e,"f",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"d",(function(){return c})),a.d(e,"g",(function(){return u})),a.d(e,"h",(function(){return p}));a("d3b7");var i=a("b775");function s(t){var e=t.tableType;if(!e)return Promise.reject();delete t.tableType;var a={count:"biEmployee/contractNumStats",money:"biEmployee/contractMoneyStats",back:"biEmployee/receivablesMoneyStats"}[e];return a?Object(i["a"])({url:a,method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}}):Promise.reject()}function o(t){var e=t.tableType;if(!e)return Promise.reject();delete t.tableType;var a={count:"biEmployee/contractNumStatsExport",money:"biEmployee/contractMoneyStatsExport",back:"biEmployee/receivablesMoneyStatsExport"}[e];return a?Object(i["a"])({url:a,method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}}):Promise.reject()}function n(t){return Object(i["a"])({url:"biEmployee/totalContract",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"biEmployee/totalContractExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"biEmployee/invoiceStats",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(i["a"])({url:"biEmployee/invoiceStatsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(i["a"])({url:"crmBiSearch/searchInvoicePageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(i["a"])({url:"crmBiSearch/searchReceivablesPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}},c558:function(t,e,a){},ceae:function(t,e,a){"use strict";var i=a("3835"),s=a("5530"),o=(a("99af"),a("caad"),a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("159b"),a("df55")),n=a("313e"),r=a("bf0a"),l=a("ef89"),c=a("c1df"),u=a.n(c);e["a"]={data:function(){return{axisOption:null,loading:!1,postParams:{},list:[],fieldList:[],type:"",typeName:"",typeUnit:"",reportListShow:!1,reportData:{title:"",placeholder:"",request:null,params:null}}},components:{},mixins:[o["a"]],props:{},computed:{},watch:{},mounted:function(){"back"===this.type?(this.typeName="回款金额",this.typeUnit="(元)"):"count"===this.type?(this.typeName="合同数量",this.typeUnit="（个）"):"money"===this.type&&(this.typeName="合同金额",this.typeUnit="(元)"),this.initAxis()},methods:{getDataList:function(t){var e=this;this.loading=!0,this.postParams=t,Object(r["a"])(Object(s["a"])(Object(s["a"])({},this.postParams),{},{tableType:this.type})).then((function(t){e.loading=!1;var a=[{name:"当月"+e.typeName+e.typeUnit,dateType:"days"},{name:"环比增长（%）",dateType:"month"},{name:"同比增长（%）",dateType:"year"}],i=[{field:"name",name:"日期"}];t.data.forEach((function(t,e){var s="value".concat(e);i.push({field:s,name:t.type});var o=["monthNum","pervMonthNum","prevYearNum"];o.forEach((function(e,i){a[i][s]=t[e]}))})),e.fieldList=i,e.list=a;for(var s=[],o=[],n=[],r=[],l=0;l<t.data.length;l++){var c=t.data[l];s.push(c.monthNum),o.push(c.pervMonthNum),n.push(c.prevYearNum),r.push(c.type)}e.axisOption.xAxis[0].data=r,e.axisOption.series[0].data=s,e.axisOption.series[1].data=o,e.axisOption.series[2].data=n,e.chartObj.setOption(e.axisOption,!0)})).catch((function(){e.loading=!1}))},initAxis:function(){this.chartObj=n["b"](document.getElementById("axismain")),this.axisOption={color:["#1890ff","#00B8D9","#36B37E"],toolbox:this.toolbox,tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:Object(s["a"])(Object(s["a"])({},this.chartDefaultOptions.legend),{},{data:["当月"+this.typeName,"环比增长","同比增长"]}),grid:Object(s["a"])(Object(s["a"])({},this.chartDefaultOptions.grid),{},{top:"50px"}),xAxis:[Object(s["a"])(Object(s["a"])({},this.chartXAxisStyle),{},{type:"category",data:[]})],yAxis:[Object(s["a"])(Object(s["a"])({},this.getChartYAxisStyle({axisLabel:{formatter:"{value}"}})),{},{type:"value",name:this.typeUnit}),Object(s["a"])(Object(s["a"])({},this.getChartYAxisStyle({axisLabel:{formatter:"{value}%"}})),{},{type:"value",name:""})],series:[{name:"当月"+this.typeName,type:"bar",yAxisIndex:0,barMaxWidth:10,label:this.chartDefaultBase.label,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},data:[]},Object(s["a"])(Object(s["a"])({},this.chartDefaultOptions.seriesLine),{},{name:"环比增长",type:"line",yAxisIndex:1,markLine:{data:[{type:"average",name:"平均值"}]},markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},data:[]}),Object(s["a"])(Object(s["a"])({},this.chartDefaultOptions.seriesLine),{},{name:"同比增长",type:"line",yAxisIndex:1,markLine:{data:[{type:"average",name:"平均值"}]},markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},data:[]})]},this.chartObj.setOption(this.axisOption,!0)},cellClassName:function(t){t.row,t.column,t.rowIndex;var e=t.columnIndex;return e?"can-visit--underline":""},handleRowClick:function(t,e,a){if("name"!==e.property){this.reportData.title="".concat(e.label,"详情");var s=e.label.split("-"),o=Object(i["a"])(s,2),n=o[0],c=o[1],p="",h=[];if("days"===t.dateType){var d=u()("".concat(n,"-").concat(c,"-01")).endOf("month").format("YYYY-MM-DD");h=[e.label,d]}else{p=u()(e.label).subtract(1,t.dateType).format("YYYY-MM-DD");var m=p.split("-"),f=Object(i["a"])(m,2),b=f[0],y=f[1],T=u()("".concat(b,"-").concat(y,"-01")).endOf("month").format("YYYY-MM-DD");h=[p,T],this.reportData.title="".concat(b,"-").concat(y,"-01详情")}var v={search:"",dataType:this.postParams.dataType};"back"===this.type?(this.reportData.crmType="receivables",v.type=7,this.reportData.request=r["h"]):(v.type=6,this.reportData.request=l["d"]);var x=["money","count"].includes(this.type)?"orderDate":"returnTime";v.searchList=[{formType:"date",name:x,type:14,values:h},{formType:"checkStatus",name:"checkStatus",type:1,values:[1,10]}],0==this.postParams.dataType&&(v.userList=this.postParams.userList,v.deptList=this.postParams.deptList),this.reportData.params=v,this.reportListShow=!0}},exportClick:function(){this.requestExportInfo(r["b"],Object(s["a"])(Object(s["a"])({},this.postParams),{},{tableType:this.type}))}},deactivated:function(){}}},df55:function(t,e,a){"use strict";var i=a("5530"),s=(a("d3b7"),a("08c2")),o=a("7a1a"),n=a("ed08"),r=a("a347"),l=a.n(r);e["a"]={data:function(){return{chartObj:null,chartOtherObj:null,chartColors:["#FF5630","#FFAB00","#36B37E","#00B8D9","#6554C0","#172B4D","#006644","#008DA6"],echartLineBarColors:["#505F79","#1890ff"],chartDefaultBase:{label:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},textColor:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},chartDefaultOptions:{tooltip:{textStyle:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight}},legend:{bottom:"0px",itemWidth:14,textStyle:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight}},grid:{top:"40px",left:"30px",right:"60px",bottom:"40px",containLabel:!0,borderColor:"#fff"},seriesLine:{symbol:"circle",symbolSize:8,smooth:!0,label:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight}}},chartXAxisStyle:{axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},axisLine:{lineStyle:{color:l.a.axisLineColor}},splitLine:{show:!1}},chartYAxisStyle:{minInterval:1,axisTick:{show:!1},axisLabel:{color:l.a.colorBlack,fontWeight:l.a.axisLabelFontWeight},axisLine:{show:!1},splitLine:{lineStyle:{color:l.a.axisLineColor}}},currentPage:1,pageSizes:[15,30,45,60],pageSize:0,total:0,toolbox:{showTitle:!1,feature:{saveAsImage:{show:!0,pixelRatio:2}}}}},components:{FiltrateHandleView:s["a"]},props:{},computed:{},watch:{},mounted:function(){var t=this;this.debouncedResize=Object(o["debounce"])(300,this.resizeFn),this.$nextTick((function(){window.addEventListener("resize",t.debouncedResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.debouncedResize)},methods:{resizeFn:function(){this.chartObj&&this.chartObj.resize(),this.chartOtherObj&&this.chartOtherObj.resize()},handleSizeChange:function(t){this.pageData.limit=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},handleCurrentChange:function(t){this.pageData.page=t,this.pageData.talkTime=this.talkTime,this.pageData.talkTimeCondition=this.talkTimeCondition,this.getList(this.pageData)},requestExportInfo:function(t,e){return new Promise((function(a,i){t(e).then((function(t){Object(n["g"])(t),a&&a(t)})).catch((function(t){i&&i(t)}))}))},getChartYAxisStyle:function(t){var e=Object(n["D"])(this.chartYAxisStyle);if(!t)return e;for(var a in t){var s=e[a],o=t[a];e[a]=s?Object(i["a"])(Object(i["a"])({},s),o):o}return e}},deactivated:function(){}}},ef89:function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return o})),a.d(e,"g",(function(){return n})),a.d(e,"h",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"e",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"d",(function(){return p})),a.d(e,"i",(function(){return h}));var i=a("b775");function s(t){return Object(i["a"])({url:"biAchievement/taskCompleteStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(t){return Object(i["a"])({url:"biAchievement/taskCompleteStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function n(t){return Object(i["a"])({url:"biProduct/productStatistics",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(t){return Object(i["a"])({url:"biProduct/productStatisticsExport",method:"post",data:t,responseType:"blob",headers:{"Content-Type":"application/json;charset=UTF-8"}})}function l(t){return Object(i["a"])({url:"biFunnel/sellFunnel",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(t){return Object(i["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function u(t){return Object(i["a"])({url:"crmBiSearch/productSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function p(t){return Object(i["a"])({url:"crmBiSearch/searchContractPageList",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function h(t){return Object(i["a"])({url:"crmBiSearch/employeesSatisfactionTable",method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}}}]);