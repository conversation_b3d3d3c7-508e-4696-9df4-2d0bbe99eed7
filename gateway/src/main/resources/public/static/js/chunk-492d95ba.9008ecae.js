(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-492d95ba"],{3359:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"role-authorization main"},[i("xr-header",{ref:"xrHeader",attrs:{label:"角色权限控制"}}),e._v(" "),i("div",{staticClass:"main-content-wrap",class:{"is-tabs":e.roleTabShow}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.roleMenuLoading,expression:"roleMenuLoading"}],staticClass:"main-nav"},[i("div",{staticClass:"main-nav__title"},[e._v("\n        "+e._s(e.title)),e.roleHelpObj?i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":e.roleHelpObj.type,"data-id":e.roleHelpObj.id}}):e._e(),e._v(" "),i("el-button",{staticClass:"add-btn",attrs:{type:"text",icon:"el-icon-plus"},on:{click:e.newRoleBtn}},[e._v("创建角色")])],1),e._v(" "),i("div",{staticClass:"main-nav__content"},[i("div",{staticClass:"nav-sections-wrap"},[e.roleTabShow?i("el-tabs",{on:{"tab-click":e.roleTabChange},model:{value:e.tabType,callback:function(t){e.tabType=t},expression:"tabType"}},[i("el-tab-pane",{attrs:{label:"管理员",name:"91"}}),e._v(" "),i("el-tab-pane",{attrs:{label:"上级",name:"92"}},[i("template",{slot:"label"},[e._v("\n                上级"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"22","data-id":"259"}})])],2)],1):e._e(),e._v(" "),i("div",{staticClass:"nav-section"},e._l(e.roleList,(function(t,a){return i("div",{key:a,staticClass:"menu-item",class:{"is-select":t.roleId==e.roleActive.roleId},on:{click:function(i){e.roleMenuSelect(t)}}},[i("div",{staticClass:"menu-item__content"},[e._v(e._s(t.roleName))]),e._v(" "),"admin"!=t.remark&&"project"!=t.remark?i("div",{staticClass:"icon-close",class:{"is-visible":t.visible}},[i("el-dropdown",{attrs:{trigger:"click"},on:{command:e.roleHandleClick},model:{value:t.visible,callback:function(i){e.$set(t,"visible",i)},expression:"item.visible"}},[i("el-button",{staticClass:"dropdown-btn menu-edit-btn",attrs:{icon:"wk wk-manage",size:"small"},on:{click:function(i){e.roleDropdownClick(t)}}}),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{command:"copy"}},[e._v("复制")]),e._v(" "),i("el-dropdown-item",{attrs:{command:"edit"}},[e._v("编辑")]),e._v(" "),i("el-dropdown-item",{attrs:{command:"delete"}},[e._v("删除")])],1)],1)],1):e._e()])})))],1)])]),e._v(" "),i("el-dialog",{attrs:{title:e.roleTitle,visible:e.newRoleVisible,"before-close":e.newRoleClose,"close-on-click-modal":!1,width:"30%"},on:{"update:visible":function(t){e.newRoleVisible=t}}},[i("label",{staticClass:"label-title"},[e._v("角色名称")]),e._v(" "),i("el-input",{staticClass:"input-role",attrs:{maxlength:100},model:{value:e.role.title,callback:function(t){e.$set(e.role,"title",t)},expression:"role.title"}}),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.newRoleSubmit}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.newRoleClose}},[e._v("取消")])],1)],1),e._v(" "),i("div",{staticClass:"main-content"},[i("el-tabs",{staticStyle:{height:"100%"},model:{value:e.mainMenuIndex,callback:function(t){e.mainMenuIndex=t},expression:"mainMenuIndex"}},[e.showRoleUser?i("el-tab-pane",{attrs:{label:"角色员工",name:"user"}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.userLoading,expression:"userLoading"}],staticClass:"content-table"},[i("flexbox",{staticClass:"content-table-header"},[i("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入内容"},on:{blur:e.headerSearch},nativeOn:{keyup:function(t){return!("button"in t)&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.headerSearch(t)}},model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}},[i("el-button",{attrs:{slot:"suffix",type:"icon",icon:"wk wk-sousuo"},nativeOn:{click:function(t){return e.headerSearch(t)}},slot:"suffix"})],1),e._v(" "),i("div",{staticClass:"content-table-header-reminder"},[e.showReminder?i("reminder",{attrs:{content:e.getReminderContent()}}):e._e()],1),e._v(" "),i("el-button",{attrs:{disabled:0===e.roleList.length},on:{click:e.addEmployees}},[e._v(" 关联员工 ")]),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"22","data-id":"183"}})],1),e._v(" "),i("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.tableData,stripe:e.WKConfig.tableStyle.stripe,height:e.tableHeight}},[i("el-table-column",{attrs:{prop:"realname",width:"150","show-overflow-tooltip":"",label:"姓名"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;t.column;return[i("span",{staticClass:"status-name"},[i("span",[e._v(e._s(a.realname))]),e._v(" "),0===a.userIdentity?i("el-tag",{attrs:{"disable-transitions":"",type:"warning"}},[e._v("主账号")]):e._e()],1)]}}])}),e._v(" "),e._l(e.tableList,(function(e,t){return i("el-table-column",{key:t,attrs:{prop:e.field,label:e.label,"show-overflow-tooltip":""}})})),e._v(" "),i("el-table-column",{attrs:{width:"160",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticClass:"table-edit-btn",attrs:{disabled:0===t.row.userIdentity,type:"primary-text",title:"编辑"},on:{click:function(i){e.employeeHandleClick("editRole",t.row)}}},[e._v("编辑")]),e._v(" "),i("el-button",{staticClass:"table-edit-btn",attrs:{type:"primary-text",title:"复制"},on:{click:function(i){e.employeeHandleClick("copyRole",t.row)}}},[e._v("复制")]),e._v(" "),i("el-button",{staticClass:"table-edit-btn",attrs:{disabled:0===t.row.userIdentity,type:"primary-text",title:"删除"},on:{click:function(i){e.employeeHandleClick("delete",t.row)}}},[e._v("删除")])]}}])})],2),e._v(" "),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":e.pageSizes,"page-size":e.pageSize,total:e.total,"pager-count":5,background:"",layout:"prev, pager, next, sizes, total, jumper"},on:{"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]):e._e(),e._v(" "),e.roleActive&&e.showRuleSet?i("el-tab-pane",{attrs:{name:"rule"}},[i("template",{slot:"label"},[e._v("角色权限"),i("i",{staticClass:"wk wk-icon-fill-help wk-help-tips",attrs:{"data-type":"22","data-id":"179"}})]),e._v(" "),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.ruleLoading,expression:"ruleLoading"}],staticClass:"jurisdiction-box"},[i("div",{staticClass:"jurisdiction-box-bar"},[e._l(e.ruleMenuList,(function(t){return i("el-button",{key:t.index,attrs:{type:e.ruleMenuIndex===t.index?"selected":null},on:{click:function(i){e.ruleMenuIndex=t.index}}},[e._v(e._s(t.label)+"\n              ")])})),e._v(" "),e.roleActive?i("el-button",{staticClass:"jurisdiction-edit",attrs:{disabled:0===e.roleList.length,size:"medium",type:"primary"},on:{click:e.ruleSubmit}},[e._v(" 保存 ")]):e._e()],2),e._v(" "),e._l(e.ruleMenuList,(function(t,a){return["tree"==t.type?i("div",{directives:[{name:"show",rawName:"v-show",value:e.ruleMenuIndex===t.index,expression:"ruleMenuIndex === item.index"}],key:a,staticClass:"jurisdiction-content",style:{height:e.treeHeight+"px"}},[i("div",{staticClass:"jurisdiction-content-checkbox"},[i("el-tree",{ref:"tree"+t.index,refInFor:!0,staticStyle:{height:"0"},attrs:{data:"92"==e.tabType?e.superTree:t.data,indent:0,"expand-on-click-node":!1,props:e.defaultProps,"show-checkbox":"","node-key":"menuId","empty-text":"","default-expand-all":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node;return i("span",{class:{"node-label":1==a.level||2==a.level,"common-node-label":"935"==a.data.menuId}},[e._v(e._s(a.label)),2==a.level&&e.canSetField(a.data.realm)?i("el-button",{attrs:{icon:"wk wk-manage",type:"primary-text"},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.fieldSetClick(a)}}},[e._v("字段授权")]):"935"==a.data.menuId?i("el-button",{attrs:{icon:"wk wk-manage",type:"primary-text"},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.checkRangeSetClick(a)}}},[e._v("配置查看范围")]):e._e()],1)}}])})],1)]):e._e(),e._v(" "),"tree"!==t.type?i("div",{directives:[{name:"show",rawName:"v-show",value:e.ruleMenuIndex===t.index,expression:"ruleMenuIndex === item.index"}],key:a,staticClass:"jurisdiction-content"},[i("div",{staticClass:"data-radio"},[i("el-radio-group",{model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}},[e.roleTabShow?e._e():i("el-radio",{attrs:{label:1}},[e._v("本人")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("本人及下属")]),e._v(" "),i("el-radio",{attrs:{label:3}},[e._v("本部门")]),e._v(" "),i("el-radio",{attrs:{label:4}},[e._v("本部门及下属部门")]),e._v(" "),i("el-radio",{attrs:{label:5}},[e._v("全部")])],1)],1)]):e._e()]}))],2)],2):e._e()],1)],1)],1),e._v(" "),i("relate-empoyee",{attrs:{visible:e.relateEmpoyeeShow,"role-id":e.roleId},on:{"update:visible":function(t){e.relateEmpoyeeShow=t},save:e.employeesSave}}),e._v(" "),i("field-set-dialog",{attrs:{visible:e.setFieldShow,"role-id":e.roleId,label:e.setFieldLabel},on:{"update:visible":function(t){e.setFieldShow=t}}}),e._v(" "),e.editRoleDialogShow?i("edit-role-dialog",{attrs:{"user-show":"copyRole"===e.editRoleType,"selection-list":e.selectionList,visible:e.editRoleDialogShow},on:{"update:visible":function(t){e.editRoleDialogShow=t},change:e.getUserList}}):e._e(),e._v(" "),e.setRoleRangeShow?i("role-range-set-dialog",{attrs:{visible:e.setRoleRangeShow,"role-id":e.roleId},on:{"update:visible":function(t){e.setRoleRangeShow=t}}}):e._e()],1)},n=[],l=(i("99af"),i("4de4"),i("caad"),i("14d9"),i("a434"),i("e9f5"),i("910d"),i("7d54"),i("a9e3"),i("d3b7"),i("ac1f"),i("25f0"),i("841c"),i("159b"),i("2934")),s=i("8d55"),o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,title:"关联员工",width:"600px"},on:{close:e.handleCancel}},[i("div",{staticClass:"handle-box"},[i("flexbox",{staticClass:"handle-item",attrs:{align:"stretch"}},[i("div",{staticClass:"handle-item-name",staticStyle:{"margin-top":"8px"}},[e._v("选择员工：")]),e._v(" "),i("wk-user-dialog-select",{staticClass:"handle-item-content",attrs:{radio:!1},model:{value:e.selectUsers,callback:function(t){e.selectUsers=t},expression:"selectUsers"}})],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(t){return e.handleConfirm(t)}}},[e._v("保存")]),e._v(" "),i("el-button",{nativeOn:{click:function(t){return e.handleCancel(t)}}},[e._v("取消")])],1)])},r=[],c=i("8f81"),u={name:"RelateEmpoyee",components:{WkUserDialogSelect:c["a"]},mixins:[],props:{visible:{type:Boolean,required:!0,default:!1},roleId:[Number,String]},data:function(){return{loading:!0,selectUsers:[]}},computed:{},watch:{visible:function(e){e&&(this.selectUsers=[])}},mounted:function(){},methods:{handleCancel:function(){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this;0==this.selectUsers.length?this.$message.error("请选择员工"):Object(s["c"])({userIds:this.selectUsers,roleIds:[this.roleId]}).then((function(t){e.$message.success("操作成功"),e.$emit("save")})).catch((function(){}))}}},d=u,h=(i("5a474"),i("2877")),p=Object(h["a"])(d,o,r,!1,null,"2bfa3c0a",null),f=p.exports,m=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,title:e.title,width:"700px"},on:{close:e.handleCancel}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"field-set-dialog"},[i("el-table",{class:e.WKConfig.tableStyle.class,staticStyle:{width:"100%"},attrs:{size:"small",data:e.list,stripe:e.WKConfig.tableStyle.stripe,height:"50vh"}},[i("el-table-column",{attrs:{label:"字段名称",prop:"name",width:"200"}}),e._v(" "),i("el-table-column",{attrs:{label:"字段权限"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("el-checkbox",{attrs:{disabled:!e.getCanOpreateRead(a.operateType)},on:{change:function(t){e.checkCheckboxChange(a)}},model:{value:a.canCheck,callback:function(t){e.$set(a,"canCheck",t)},expression:"row.canCheck"}},[e._v("可以查看")]),e._v(" "),i("el-checkbox",{attrs:{disabled:!e.getCanOpreateEdit(a.operateType)},on:{change:function(t){e.editCheckboxChange(a)}},model:{value:a.canEdit,callback:function(t){e.$set(a,"canEdit",t)},expression:"row.canEdit"}},[e._v("可以修改")]),e._v(" "),e.getMaskIsShow(a)?[i("el-checkbox",{on:{change:function(t){e.maskCheckboxChange(a)}},model:{value:a.canMask,callback:function(t){e.$set(a,"canMask",t)},expression:"row.canMask"}},[e._v("掩码显示")]),e._v(" "),i("el-button",{staticClass:"mask-button",attrs:{disabled:!a.canMask,type:"text"},on:{click:function(t){e.setMaskClick(a)}}},[e._v("设置掩码规则")])]:e._e()]}}])})],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticStyle:{float:"left"},attrs:{type:"text"},on:{click:e.getFieldList}},[e._v("重置")]),e._v(" "),i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),i("el-button",{on:{click:e.handleCancel}},[e._v("取消")])],1),e._v(" "),e.maskSetVisible?i("el-dialog",{attrs:{visible:e.maskSetVisible,"append-to-body":!0,"close-on-click-modal":!1,title:"设置掩码规则",width:"500px"},on:{close:function(t){e.maskSetVisible=!1}}},[i("div",{staticClass:"mask-wrap"},[i("div",{staticClass:"mask-wrap__des"},[e._v("\n        1、勾选该选项后，该字段的值在页面上将以掩码显示，比如 ：手机188****8888、邮箱wa*****@5kcrm.com、货币全部掩码显示、地址的详细地址全部掩码显示"),i("br"),e._v("\n        2、字段配置掩码后，若用户看到的是掩码显示，那该用户导出的也是掩码显示"),i("br"),e._v("\n        3、若有此字段的编辑权限，则在编辑页为非掩码显示\n      ")]),e._v(" "),i("div",{staticClass:"mask-wrap__body"},[i("div",[e._v("掩码显示的页面")]),e._v(" "),i("div",{staticClass:"handle"},[i("el-checkbox",{attrs:{disabled:""},model:{value:e.maskEditDetail.tableMask,callback:function(t){e.$set(e.maskEditDetail,"tableMask",t)},expression:"maskEditDetail.tableMask"}},[e._v("列表页面")]),e._v(" "),i("el-checkbox",{model:{value:e.maskEditDetail.viewMask,callback:function(t){e.$set(e.maskEditDetail,"viewMask",t)},expression:"maskEditDetail.viewMask"}},[e._v("详情页面")])],1)])]),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleMaskConfirm,expression:"handleMaskConfirm"}],attrs:{type:"primary"}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:function(t){e.maskSetVisible=!1}}},[e._v("取消")])],1)]):e._e()],1)},v=[],b=i("6683"),g=i("ed08"),k={name:"FieldSetDialog",components:{},mixins:[],props:{label:[String,Number],roleId:[String,Number],visible:{type:Boolean,required:!0,default:!1}},data:function(){return{loading:!1,list:[],rowDetail:null,maskEditDetail:null,maskSetVisible:!1}},computed:{title:function(){return"".concat(b["a"].convertTypeToName(this.label),"字段授权")}},watch:{visible:{handler:function(){this.visible&&this.getFieldList()},immediate:!0}},mounted:function(){},methods:{getFieldList:function(){var e=this;this.list=[],this.loading=!0,Object(s["i"])({label:this.label,roleId:this.roleId}).then((function(t){e.loading=!1;var i=t.data||[];i.forEach((function(e){e.canCheck=2==e.authLevel||3==e.authLevel,e.canEdit=3==e.authLevel,e.canMask=0!==e.maskType,e.tableMask=1==e.maskType||2==e.maskType,e.viewMask=2==e.maskType})),e.list=t.data||[]})).catch((function(){e.loading=!1}))},handleCancel:function(){this.$emit("update:visible",!1)},getCanOpreateRead:function(e){return 1==e||2==e},getCanOpreateEdit:function(e){return 1==e||3==e},getMaskIsShow:function(e){var t=e.type,i=e.fieldName;return(this.label!=b["a"].product||"price"!==i)&&((this.label!=b["a"].business||"money"!==i)&&((this.label!=b["a"].contract||"money"!==i)&&(!(this.label!=b["a"].receivablesPlan||!["real_received_money","unreceived_money"].includes(i))||(7==t||14==t||6==t||43==t))))},handleConfirm:function(){var e=this;this.loading=!0;var t=Object(g["D"])(this.list);t.forEach((function(e){var t=3;t=e.canCheck&&e.canEdit?3:e.canCheck?2:1,e.authLevel=t,delete e.canCheck,delete e.canEdit;var i=0;(e.tableMask||e.viewMask)&&(i=e.tableMask&&e.viewMask?2:1),e.maskType=i,delete e.canMask,delete e.tableMask,delete e.viewMask})),Object(s["j"])(t).then((function(t){e.loading=!1,e.$message.success("操作成功"),e.handleCancel()})).catch((function(){e.loading=!1}))},editCheckboxChange:function(e){e.canEdit&&this.$set(e,"canCheck",!0)},checkCheckboxChange:function(e){!e.canCheck&&e.canEdit&&this.$set(e,"canEdit",!1)},maskCheckboxChange:function(e){e.tableMask=e.canMask,e.viewMask=e.canMask},setMaskClick:function(e){this.rowDetail=e,this.maskEditDetail=Object(g["D"])(e),this.maskSetVisible=!0},handleMaskConfirm:function(){this.rowDetail.tableMask=this.maskEditDetail.tableMask,this.rowDetail.viewMask=this.maskEditDetail.viewMask,this.maskSetVisible=!1}}},_=k,y=(i("c715"),Object(h["a"])(_,m,v,!1,null,"4865b99e",null)),w=y.exports,C=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{ref:"wkDialog",attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,title:e.title,width:"700px"},on:{close:e.handleCancel}},[i("el-checkbox",{staticClass:"el-bold-checkbox",on:{change:e.allChange},model:{value:e.allChecked,callback:function(t){e.allChecked=t},expression:"allChecked"}},[e._v("全选")]),e._v(" "),i("el-checkbox-group",{on:{change:e.groupChange},model:{value:e.checkList,callback:function(t){e.checkList=t},expression:"checkList"}},e._l(e.treeList,(function(t,a){return i("div",{key:a,staticClass:"role-set-section"},[i("div",{staticClass:"role-set-section__title"},[e._v(e._s(t.name))]),e._v(" "),i("div",{staticClass:"role-set-section__list"},e._l(t.list,(function(t,a){return i("el-checkbox",{key:a,attrs:{label:t.roleId}},[e._v(e._s(t.roleName))])})))])}))),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"debounce",rawName:"v-debounce",value:e.handleConfirm,expression:"handleConfirm"}],attrs:{type:"primary"}},[e._v("保存")]),e._v(" "),i("el-button",{on:{click:e.handleCancel}},[e._v("取消")])],1)],1)},x=[],I=i("612a"),L=i("9dba"),R={name:"RoleRangeSetDialog",components:{},mixins:[L["a"]],props:{roleId:[String,Number],visible:{type:Boolean,required:!0,default:!1}},data:function(){return{loading:!1,allChecked:!1,treeList:[],checkList:[],allIds:[]}},computed:{title:function(){return"配置查看范围"}},mounted:function(){this.getRoleList()},methods:{getRoleRangeValue:function(){var e=this;this.loading=!0,Object(I["b"])(this.roleId).then((function(t){e.checkList=t.data||[],e.groupChange(),e.loading=!1})).catch((function(){e.loading=!1}))},getRoleList:function(){var e=this;this.loading=!0,Object(I["a"])().then((function(t){e.loading=!1;var i=t.data||[],a=[];i.forEach((function(e){e.list.forEach((function(e){a.push(e.roleId)}))})),e.allIds=a,e.treeList=i,e.getRoleRangeValue()})).catch((function(){e.loading=!1}))},handleCancel:function(){this.$emit("update:visible",!1)},allChange:function(){this.allChecked?this.checkList.length!==this.allIds.length&&(this.checkList=Object(g["D"])(this.allIds)):this.checkList.length===this.allIds.length&&(this.checkList=[])},groupChange:function(){this.checkList.length===this.allIds.length?this.allChecked||(this.allChecked=!0):this.allChecked&&(this.allChecked=!1)},handleConfirm:function(){var e=this;this.loading=!0,Object(I["d"])(this.roleId,this.checkList).then((function(t){e.loading=!1,e.$message.success("操作成功"),e.handleCancel()})).catch((function(){e.loading=!1}))}}},S=R,M=(i("7bf8"),Object(h["a"])(S,C,x,!1,null,"23386023",null)),T=M.exports,j=i("8f37"),O=i("f468"),E=i("4dd8"),D={components:{RelateEmpoyee:f,FieldSetDialog:w,RoleRangeSetDialog:T,Reminder:j["a"],XrHeader:O["a"],EditRoleDialog:E["a"]},data:function(){return{parentId:"",title:"",searchInput:"",tableData:[],tableHeight:document.documentElement.clientHeight-300,treeHeight:document.documentElement.clientHeight-230,currentPage:1,pageSize:15,pageSizes:[15,30,45,60],total:0,tableList:[{label:"部门",field:"deptName"},{label:"职位",field:"post"},{label:"角色",field:"roleName"}],newRoleVisible:!1,role:{},roleList:[],allRoleList:[],mainMenuIndex:"user",ruleMenuIndex:"data",ruleMenuList:[],defaultProps:{children:"childMenu",label:"menuName",disabled:!1},relateEmpoyeeShow:!1,roleActive:null,dropdownHandleRole:null,roleTitle:"",roleMenuLoading:!1,ruleLoading:!1,userLoading:!1,setFieldLabel:"",setFieldShow:!1,selectionList:[],editRoleType:"",editRoleDialogShow:!1,tabType:"91",superTree:[],setRoleRangeShow:!1}},computed:{roleId:function(){return this.roleActive?this.roleActive.roleId:""},showRuleSet:function(){return!!this.roleActive&&("admin"!=this.roleActive.remark&&"project"!=this.roleActive.remark)},showReminder:function(){return!!this.roleActive&&"project"==this.roleActive.remark},roleTabShow:function(){return 9==this.parentId},showRoleUser:function(){return 4!==Number(this.parentId)},roleHelpObj:function(){return{1:{type:"22",id:"172"},2:{type:"22",id:"176"},4:{type:"22",id:"178"},7:{type:"22",id:"173"},9:{type:"22",id:"177"}}[this.parentId]||null}},watch:{showRoleUser:{handler:function(){this.showRoleUser?this.mainMenuIndex="user":this.mainMenuIndex="rule"},deep:!0}},mounted:function(){var e=this;window.onresize=function(){e.tableHeight=document.documentElement.clientHeight-300,e.treeHeight=document.documentElement.clientHeight-230},this.parentId=this.$route.params.parentId,this.title=this.$route.params.title,this.getRulesList(),this.getRoleList()},beforeRouteUpdate:function(e,t,i){this.tabType="91",this.parentId=e.params.parentId,this.title=e.params.title,this.roleActive=null,this.roleList=[],this.mainMenuIndex="user",this.currentPage=1,this.total=0,this.tableData=[],this.$refs.xrHeader&&(this.$refs.xrHeader.search="",this.searchInput=""),this.getRulesList(),this.getRoleList(),i()},methods:{getRulesList:function(){var e=this;Object(s["k"])(this.parentId).then((function(t){var i=t.data||{};if(i.data){var a=[i.data];if(e.ruleMenuList=[{label:"模块功能",index:"data",type:"tree",value:[],data:a}],e.roleTabShow){var n=Object(g["D"])(a);e.addDisabledToTree(n),e.superTree=n}i.bi&&e.ruleMenuList.push({label:"数据分析",index:"bi",type:"tree",value:[],data:[i.bi]})}else e.ruleMenuList=[];e.getRoleRulesInfo()}))},addDisabledToTree:function(e){var t=this;e.forEach((function(e){e.disabled="label-92"!==e.remarks,e.childMenu&&t.addDisabledToTree(e.childMenu)}))},getDisabledDeleteStatus:function(e){var t=4===e.roleType&&"1"===e.remark;return!t},getRoleList:function(){var e=this;this.roleMenuLoading=!0,Object(s["h"])(this.parentId).then((function(t){var i=t.data||[];e.roleTabShow?(e.allRoleList=i,e.roleList=e.allRoleList.filter((function(t){return t.label==e.tabType}))):e.roleList=i;var a=!1;if(e.roleActive)for(var n=0;n<e.roleList.length;n++){var l=e.roleList[n];if(l.roleId==e.roleActive.roleId){e.roleActive=l,e.getRoleRulesInfo(),a=!0;break}}!a&&e.roleList.length&&(e.roleActive=e.roleList[0],e.getRoleRulesInfo()),e.refreshUserList(),e.roleMenuLoading=!1})).catch((function(){e.roleMenuLoading=!1}))},addEmployees:function(){this.relateEmpoyeeShow=!0},employeesSave:function(e){this.relateEmpoyeeShow=!1,this.getUserList()},employeeHandleClick:function(e,t){var i=this;"delete"===e?this.$confirm("此操作将永久删除是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i.userLoading=!0,Object(s["l"])({userId:t.userId,roleId:i.roleActive.roleId}).then((function(e){i.userLoading=!1,i.getUserList(),i.$message.success("删除成功")})).catch((function(){i.userLoading=!1}))})).catch((function(){i.$message({type:"info",message:"已取消删除"})})):"editRole"!==e&&"copyRole"!==e||(this.selectionList=[t],this.editRoleType=e,this.editRoleDialogShow=!0)},roleTabChange:function(){var e=this;this.roleList=this.allRoleList.filter((function(t){return t.label==e.tabType})),this.roleList.length?(this.roleActive=this.roleList[0],this.getRoleRulesInfo()):this.roleActive=null,this.refreshUserList()},newRoleClose:function(){this.newRoleVisible=!1},newRoleBtn:function(){this.roleTitle="新建角色",this.newRoleVisible=!0,this.role={}},roleDropdownClick:function(e){this.dropdownHandleRole=e},roleHandleClick:function(e){"edit"==e?this.roleEditBtn(this.dropdownHandleRole):"copy"==e?this.ticketsBtn(this.dropdownHandleRole):"delete"==e&&this.roleDelect(this.dropdownHandleRole)},getReminderContent:function(){return this.roleActive&&"project"==this.roleActive.remark?"项目管理员拥有“项目管理”模块所有权限，能看到并维护所有项目信息":""},roleEditBtn:function(e){this.roleTitle="编辑角色",this.role={title:e.roleName,parentId:e.roleType,id:e.roleId},this.newRoleVisible=!0},ticketsBtn:function(e){var t=this;this.$confirm("确定此操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["e"])({roleId:e.roleId}).then((function(e){t.$message.success("复制成功"),t.getRoleList()}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},roleDelect:function(e){var t=this;this.$confirm("此操作将永久删除是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["f"])({roleId:e.roleId}).then((function(e){t.roleList.length&&(t.roleActive=t.roleList[0],t.getRoleRulesInfo()),t.getRoleList(),t.$message.success("删除成功")}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},newRoleSubmit:function(){var e=this;if(this.role.title)if("新建角色"==this.roleTitle){var t={roleName:this.role.title,roleType:this.parentId};this.roleTabShow&&(t.label=this.tabType),Object(s["d"])(t).then((function(t){e.getRoleList(),e.$message.success("添加成功"),e.newRoleClose()}))}else Object(s["g"])({roleName:this.role.title,roleType:this.role.parentId,roleId:this.role.id}).then((function(t){e.getRoleList(),e.$message.success("编辑成功"),e.newRoleClose()}));else this.$message.error("角色名称不能为空")},roleMenuSelect:function(e){this.roleActive=e,"rule"!=this.mainMenuIndex||this.showRuleSet||(this.mainMenuIndex="user"),this.getRoleRulesInfo(),this.refreshUserList()},getRoleRulesInfo:function(){var e=this;if(this.roleActive&&this.ruleMenuList.length){if(2==this.parentId||10==this.parentId||9==this.parentId&&"92"==this.tabType){var t=this.ruleMenuList[this.ruleMenuList.length-1];"data"!=t.type&&this.ruleMenuList.push({label:"数据权限",index:"info",type:"data",value:this.roleActive.dataType})}else if(9==this.parentId&&"91"==this.tabType){var i=this.ruleMenuList[this.ruleMenuList.length-1];"tree"!=i.type&&(this.ruleMenuList=[this.ruleMenuList[0]]),this.ruleMenuIndex="data"}for(var a=function(){var t=e.ruleMenuList[n];"tree"==t.type?(t.rules=e.getRoleRules(e.roleActive.rules[t.index],t.data[0]),e.$nextTick((function(){var i=e.$refs["tree"+t.index];i&&("[object Array]"==Object.prototype.toString.call(i)?i.length&&i[0].setCheckedKeys(t.rules):i.setCheckedKeys(t.rules),e.setDisabledAuth())}))):t.value=e.roleActive.dataType},n=0;n<this.ruleMenuList.length;n++)a()}},setDisabledAuth:function(){if(4!==Number(this.parentId))this.$set(this.defaultProps,"disabled",!1);else{var e=this;this.$set(this.defaultProps,"disabled",(function(t,i){return 4===e.roleActive.label&&!["read","export","print"].includes(t.realm)}))}},getRoleRules:function(e,t){e||(e=[]);for(var i=!1,a=this.copyItem(e),n=0;n<t.childMenu.length;n++){var l=t.childMenu[n];if(!l.hasOwnProperty("children"))return l.length+1!=a.length&&this.removeItem(a,t.id),a;for(var s=0;s<e.length;s++){for(var o=e[s],r=[],c=0;c<l.childMenu.length;c++){var u=l.childMenu[c];u.id==o&&r.push(u)}r.length!=l.childMenu.length&&(i=!0,this.removeItem(a,l.id))}}i&&this.removeItem(a,t.id);for(var d=[],h=0;h<a.length;h++){var p=a[h];p&&d.push(parseInt(p))}return d},copyItem:function(e){for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t},removeItem:function(e,t){for(var i=-1,a=0;a<e.length;a++)if(t==e[a]){i=a;break}i>0&&e.splice(i,1)},containItem:function(e,t){for(var i=0;i<e.length;i++)if(t==e[i])return!0;return!1},headerSearch:function(){this.refreshUserList()},refreshUserList:function(){this.currentPage=1,this.getUserList()},getUserList:function(){var e=this;if(!this.roleActive)return this.tableData=[],void(this.total=0);this.userLoading=!0,Object(l["w"])({page:this.currentPage,limit:this.pageSize,roleId:this.roleActive.roleId,realname:this.searchInput}).then((function(t){e.tableData=t.data.list,e.total=t.data.totalRow,e.userLoading=!1})).catch((function(){e.userLoading=!1}))},handleSizeChange:function(e){this.pageSize=e,this.refreshUserList()},handleCurrentChange:function(e){this.currentPage=e,this.getUserList()},ruleSubmit:function(){var e=this;this.ruleLoading=!0;for(var t=[],i="",a=0;a<this.ruleMenuList.length;a++){var n=this.ruleMenuList[a];if("tree"==n.type){var l=this.$refs["tree"+n.index];l&&(t="[object Array]"==Object.prototype.toString.call(l)?t.concat(l[0].getCheckedKeys()):t.concat(l.getCheckedKeys()))}else i=n.value}Object(s["m"])({menuIds:t,dataType:i,roleId:this.roleActive.roleId,roleName:this.roleActive.roleName}).then((function(t){e.getRoleList(),e.$message.success("编辑成功"),e.ruleLoading=!1})).catch((function(){e.ruleLoading=!1}))},canSetField:function(e){return 10!=this.parentId&&(["leads","customer","contacts","business","contract","receivables","receivablesPlan","product","visit","invoice"].includes(e)&&"data"===this.ruleMenuIndex)},fieldSetClick:function(e){this.setFieldLabel=b["a"][e.data.realm],this.setFieldShow=!0},checkRangeSetClick:function(e){this.setRoleRangeShow=!0},getStatusColor:function(e){return 0==e?"#FF6767":1==e?"#46CDCF":2==e?"#CCCCCC":void 0}}},$=D,F=(i("f089"),Object(h["a"])($,a,n,!1,null,"4dc615fe",null));t["default"]=F.exports},"451c":function(e,t,i){},"4dd8":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{ref:"wkDialog",attrs:{visible:e.visible,"append-to-body":!0,"close-on-click-modal":!1,width:"500px"},on:{close:e.close}},[i("div",{staticClass:"el-dialog__title",attrs:{slot:"title"},slot:"title"},[e._v("\n    "+e._s(e.title)),e.userShow?i("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v("1、可以将员工角色复制给其他员工。"),i("br"),e._v("\n        2、若选择的员工已有角色，原角色会被覆盖。"),i("br"),e._v("\n        3、若选择部门，该部门所有员工的角色将相同，"),i("br"),e._v("\n             可保存后再对员工独立调整。\n      ")]),e._v(" "),i("i",{staticClass:"wk wk-help wk-help-tips",staticStyle:{"margin-left":"3px"}})]):e._e()],1),e._v(" "),i("el-form",{ref:"editRoleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px","label-position":"top"}},[e.userShow?i("el-form-item",{attrs:{label:"选择员工和部门",prop:"userIds"}},[i("wk-user-dep-dialog-select",{staticStyle:{width:"100%"},attrs:{"user-value":e.ruleForm.userIds,"dep-value":e.ruleForm.deptIds},on:{"update:userValue":function(t){e.$set(e.ruleForm,"userIds",t)},"update:depValue":function(t){e.$set(e.ruleForm,"deptIds",t)}}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"设置角色",prop:"roleList"}},[i("role-employee-select",{staticStyle:{width:"100%"},attrs:{props:e.roleSelectProps,multiple:""},model:{value:e.ruleForm.roleList,callback:function(t){e.$set(e.ruleForm,"roleList",t)},expression:"ruleForm.roleList"}})],1)],1),e._v(" "),i("div",{}),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.sureClick}},[e._v("确定")]),e._v(" "),i("el-button",{on:{click:e.close}},[e._v("取消")])],1)],1)},n=[],l=(i("d9e2"),i("99af"),i("a630"),i("caad"),i("d81d"),i("14d9"),i("e9f5"),i("7d54"),i("ab43"),i("d3b7"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("2532"),i("3ca3"),i("159b"),i("ddb0"),i("8d55")),s=i("612a"),o=i("94d4"),r=i("b592"),c=i("9dba"),u={name:"EditRoleDialog",components:{RoleEmployeeSelect:o["a"],WkUserDepDialogSelect:r["a"]},mixins:[c["a"]],props:{selectionList:Array,userShow:{type:Boolean,default:!0},visible:{type:Boolean,required:!0,default:!1}},data:function(){return{loading:!1,roleValue:[],ruleForm:{roleList:[],userIds:[],deptIds:[]}}},computed:{title:function(){return this.userShow?"复制角色":"编辑角色"},rules:function(){var e=this,t=function(t,i,a){e.ruleForm.userIds&&e.ruleForm.userIds.length>0||e.ruleForm.deptIds&&e.ruleForm.deptIds.length>0?a():a(new Error("请选择"))},i={roleList:[{required:!0,message:"请选择",trigger:"change"}]};return this.userShow&&(i.userIds=[{validator:t,trigger:""}]),i},roleSelectProps:function(){return{roleRequest:s["a"]}}},watch:{},created:function(){if(this.userShow&&this.selectionList.length>0||!this.userShow&&1===this.selectionList.length){var e=this.selectionList[0];this.ruleForm.roleList=e.roleId?this.selectionList[0].roleId.split(","):[]}},methods:{close:function(){this.$emit("update:visible",!1)},sureClick:function(){var e=this;this.$refs.editRoleForm.validate((function(t){if(!t)return!1;var i=[],a=[];e.ruleForm.roleList.forEach((function(e){if(e.includes("@")){var t=e.split("@");if(t.length>1){var n=t[1].split(",");a=a.concat(n)}}else i.push(e)}));var n=Array.from(new Set(i.concat(a))),s={roleIds:n};e.userShow?(s.userIds=e.ruleForm.userIds,s.deptIds=e.ruleForm.deptIds):s.userIds=e.selectionList.map((function(e){return e.userId})),Object(l["b"])(s).then((function(t){e.$message.success("操作成功"),e.$emit("change"),e.close()})).catch((function(){}))}))}}},d=u,h=(i("b5c2"),i("2877")),p=Object(h["a"])(d,a,n,!1,null,"2b9dc912",null);t["a"]=p.exports},"575d":function(e,t,i){},"5a474":function(e,t,i){"use strict";i("451c")},"5a80":function(e,t,i){"use strict";i("a946")},"612a":function(e,t,i){"use strict";i.d(t,"k",(function(){return n})),i.d(t,"l",(function(){return l})),i.d(t,"m",(function(){return s})),i.d(t,"p",(function(){return o})),i.d(t,"o",(function(){return r})),i.d(t,"n",(function(){return c})),i.d(t,"c",(function(){return u})),i.d(t,"a",(function(){return d})),i.d(t,"b",(function(){return h})),i.d(t,"d",(function(){return p})),i.d(t,"h",(function(){return f})),i.d(t,"i",(function(){return m})),i.d(t,"g",(function(){return v})),i.d(t,"t",(function(){return b})),i.d(t,"s",(function(){return g})),i.d(t,"r",(function(){return k})),i.d(t,"q",(function(){return _})),i.d(t,"j",(function(){return y})),i.d(t,"f",(function(){return w})),i.d(t,"e",(function(){return C}));i("e9f5"),i("7d54"),i("b64b"),i("d3b7"),i("159b");var a=i("b775");function n(e){return Object(a["a"])({url:"adminDept/deleteDept/"+e.id,method:"post"})}function l(e){return Object(a["a"])({url:"adminDept/setDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function s(e){return Object(a["a"])({url:"adminDept/addDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function o(e){return Object(a["a"])({url:"adminUser/setUser",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function r(e){return Object(a["a"])({url:"adminUser/addUser",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function c(e){return Object(a["a"])({url:"adminRole/getAllRoleList",method:"post",data:e})}function u(){return Object(a["a"])({url:"adminRole/queryDefaultRole",method:"post"})}function d(e){return Object(a["a"])({url:"adminRole/getRoleList",method:"post",data:e})}function h(e){return Object(a["a"])({url:"adminRole/queryAuthRole/".concat(e),method:"post"})}function p(e,t){return Object(a["a"])({url:"adminRole/updateAuthRole/".concat(e),method:"post",data:t,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function f(e){return Object(a["a"])({url:"adminUser/resetPassword",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function m(e){return Object(a["a"])({url:"adminUser/usernameEdit",method:"post",data:e})}function v(e){return Object(a["a"])({url:"adminUser/usernameEditByManager",method:"post",data:e})}function b(e){return Object(a["a"])({url:"adminUser/setUserStatus",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function g(e){return Object(a["a"])({url:"adminUser/downloadExcel",method:"post",data:e,responseType:"blob"})}function k(e){var t=new FormData;return Object.keys(e).forEach((function(i){t.append(i,e[i])})),Object(a["a"])({url:"adminUser/excelImport",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"},timeout:6e4})}function _(e){return Object(a["a"])({url:"adminUser/downExcel",method:"post",data:e,responseType:"blob"})}function y(e){return Object(a["a"])({url:"crmCall/authorize",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function w(e){return Object(a["a"])({url:"adminUser/setUserDept",method:"post",data:e,headers:{"Content-Type":"application/json;charset=UTF-8"}})}function C(){return Object(a["a"])({url:"adminUser/countNumOfUser",method:"post"})}},7037:function(e,t,i){},7523:function(e,t,i){},"7bf8":function(e,t,i){"use strict";i("7523")},"94d4":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-select",e._g(e._b({ref:"select",staticClass:"role-employee-select",on:{"visible-change":e.selectVisibleChange,change:e.selectChange},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}},"el-select",e.$attrs,!1),e.$listeners),[i("div",{staticClass:"role-employee-select__body"},[i("el-tabs",{ref:"roleTabs",class:{"el-tabs__header--hidden":e.config.onlyShowRole},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[i("el-tab-pane",{ref:"roleTabPane",attrs:{label:"自选角色",name:"role"}},e._l(e.roleOption,(function(t){return i("div",{key:t.parentId,attrs:{label:t.name}},[i("div",{staticClass:"role-employee-select__title"},[e._v(e._s(t.name))]),e._v(" "),e._l(t.list,(function(e){return i("el-option",{key:e.roleId,staticStyle:{padding:"0 10px"},attrs:{label:e.roleName,value:e.roleId}})}))],2)}))),e._v(" "),i("el-tab-pane",{attrs:{label:"按员工复制角色",name:"employee"}},[i("el-input",{staticClass:"search-input",attrs:{placeholder:"搜索成员",size:"small","prefix-icon":"el-icon-search"},on:{input:e.userSearch},model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}}),e._v(" "),e._l(e.userOption,(function(t){return i("el-option",{directives:[{name:"show",rawName:"v-show",value:!t.isHide,expression:"!item.isHide"}],key:t.userId,staticStyle:{padding:"0 10px"},attrs:{label:t.realname,value:t.userId+"@"+t.roleId}},[i("flexbox",{staticClass:"cell"},[i("xr-avatar",{staticClass:"cell__img",attrs:{name:t.realname,size:24,src:t.img}}),e._v(" "),i("div",{staticClass:"cell__body"},[e._v(e._s(t.realname))]),e._v(" "),i("el-tooltip",{attrs:{content:t.roleName,effect:"dark",placement:"top"}},[i("div",{staticClass:"cell__footer text-one-line"},[e._v(e._s(t.roleName))])])],1)],1)}))],2)],1)],1)])},n=[],l=i("5530"),s=(i("e9f5"),i("7d54"),i("a9e3"),i("d3b7"),i("ac1f"),i("466d"),i("159b"),i("612a")),o=i("2934"),r=i("8122"),c=i("a318"),u=i.n(c),d=i("8ed6"),h={onlyShowRole:!1,roleRequest:null},p={name:"RoleEmployeeSelect",components:{},props:{props:{type:Object,default:function(){return{}}},value:[Array,Number,String]},data:function(){return{selectValue:[],activeName:"",roleOption:[],userOption:[],searchInput:""}},computed:{config:function(){return Object(d["a"])(Object(l["a"])({},h),this.props||{})},select:function(){return this.$refs.select}},watch:{value:{handler:function(){Object(r["valueEquals"])(this.value,this.selectValue)||(this.selectValue=this.value)},immediate:!0}},created:function(){this.getRoleList(),this.getUserList()},mounted:function(){},beforeDestroy:function(){},methods:{selectVisibleChange:function(e){""!==this.activeName&&"0"!==this.activeName||(this.activeName="role")},getRoleList:function(){var e=this,t=this.config.roleRequest||s["n"];t().then((function(t){e.roleOption=t.data||[]})).catch((function(){}))},getUserList:function(){var e=this;Object(o["w"])({pageType:0}).then((function(t){e.userOption=t.data.list||[]})).catch((function(){}))},selectChange:function(){this.$emit("input",this.selectValue)},userSearch:function(){var e=this;this.userOption.forEach((function(t){t.isHide=!u.a.match(t.realname,e.searchInput)}))}}},f=p,m=(i("5a80"),i("c128"),i("2877")),v=Object(m["a"])(f,a,n,!1,null,"6ca934ac",null);t["a"]=v.exports},a946:function(e,t,i){e.exports={colorPrimary:"#1890ff",colorBlack:"#606266",colorSuccess:"#36B37E",colorWarning:"#FFAB00",colorDanger:"#FF5630",colorN40:"#DFE1E6",axisLineColor:"#DFE1E6",axisLabelFontWeight:"500",defaultTheme:"#409EFF"}},b5c2:function(e,t,i){"use strict";i("7037")},c128:function(e,t,i){"use strict";i("575d")},c715:function(e,t,i){"use strict";i("d671")},d20a:function(e,t,i){},d671:function(e,t,i){},f089:function(e,t,i){"use strict";i("d20a")}}]);