server:
  port: 8443
spring:
  application:
    name: gateway
  profiles:
    active: dev
  resources:
    cache:
      cachecontrol:
        no-cache: true
    static-locations: file:public/,classpath:public/,classpath:/static,classpath:/resources,classpath:/META-INF/resources,file:D:/upload/public
  mvc:
    throw-exception-if-no-handler-found: true
    favicon:
      enabled: false
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        prefix: gateway
        namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
      discovery:
        enabled: true
        server-addr: 127.0.0.1:8848
        namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 127.0.0.1:8079
      datasource:
        ds1:
          nacos:
            server-addr: 127.0.0.1:8848
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
            namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
    gateway:
      enabled: true
      discovery:
        locator:
          lower-case-service-id: true
          enabled: true
