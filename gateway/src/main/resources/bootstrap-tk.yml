server:
  port: 16710
spring:
  application:
    name: gateway
  profiles:
    active: dev
  resources:
    cache:
      cachecontrol:
        no-cache: true
    static-locations: file:public/,classpath:public/,classpath:/static,classpath:/resources,classpath:/META-INF/resources,file:D:/upload/public
  mvc:
    throw-exception-if-no-handler-found: true
    favicon:
      enabled: false
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: **************:8703
        file-extension: yaml
        prefix: gateway
        namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
      discovery:
        enabled: true
        server-addr: **************:8703
        namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 127.0.0.1:8079
      datasource:
        ds1:
          nacos:
            server-addr: **************:8703
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
            namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
    gateway:
      enabled: true
      discovery:
        locator:
          lower-case-service-id: true
          enabled: true
