package com.kakarote.oa.entity.VO;

import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.oa.entity.PO.OaEventNotice;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@NoArgsConstructor
@Data
public class QueryEventByIdVO {

    private LocalDateTime repeatStartTime;
    private String typeName;
    private Integer endType;
    private String color;
    private String endTypeConfig;
    private String batchId;
    private String repeatTime;
    private String createUserName;
    private String title;
    private String ownerUserIds;
    private LocalDateTime updateTime;
    private LocalDateTime repeatEndTime;
    private Long createUserId;
    private LocalDateTime createTime;
    private Long typeId;
    private LocalDateTime endTime;
    private LocalDateTime startTime;
    private Long eventId;
    private Object repeatRate;
    private Integer repetitionType;
    private List<OaEventNotice> noticeList;
    private List<SimpleUser> ownerUserList;
    private List<SimpleCrmEntity> businessList;
    private List<SimpleCrmEntity> contactsList;
    private List<SimpleCrmEntity> contractList;
    private List<SimpleCrmEntity> customerList;
}
