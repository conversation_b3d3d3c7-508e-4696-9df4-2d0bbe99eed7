package com.kakarote.oa.entity.VO;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Builder
public class ExamineLogUserVO {

    private LocalDateTime examineTime;

    private Integer examineStatus;

    private String realname;

    private Long userId;

    private String img;

    private ExamineUserVO examineUser;

    @Builder
    public static class ExamineUserVO{
        private Date examineTime;

        private Integer examineStatus;

        private String realname;

        private Long userId;

        private String img;

        @Override
        public String toString() {
            return "ExamineUserVO{" +
                    "examineTime=" + examineTime +
                    ", examineStatus=" + examineStatus +
                    ", realname='" + realname + '\'' +
                    ", userId=" + userId +
                    ", img='" + img + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "ExamineLogUserVO{" +
                "examineTime=" + examineTime +
                ", examineStatus=" + examineStatus +
                ", realname='" + realname + '\'' +
                ", userId=" + userId +
                ", img='" + img + '\'' +
                ", examineUser=" + examineUser +
                '}';
    }

}

