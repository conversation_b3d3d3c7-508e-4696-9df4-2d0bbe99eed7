package com.kakarote.oa.entity.BO;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QueryEventByIdBO {
    private Long eventId;
    private Long startTime;
    private Long endTime;

    @Override
    public String toString() {
        return "QueryEventByIdBO{" +
                "eventId=" + eventId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                '}';
    }
}
