package com.kakarote.oa.entity.BO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Getter
@Setter
public class SetExamineCategoryBO {

    @ApiModelProperty("审批类型id")
    private Long id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("备注")
    private String remarks;

    private String icon;

    private Integer examineType;

    private Set<Long> userIds;

    private Set<Long> deptIds;

    private List<Step> step;


    @Getter
    @Setter
    public static class Step{
        private Set<Long> checkUserId;

        private Integer stepType;

    }

    @Override
    public String toString() {
        return "SetExamineCategoryBO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", remarks='" + remarks + '\'' +
                ", icon='" + icon + '\'' +
                ", examineType=" + examineType +
                ", userIds=" + userIds +
                ", deptIds=" + deptIds +
                ", step=" + step +
                '}';
    }
}
