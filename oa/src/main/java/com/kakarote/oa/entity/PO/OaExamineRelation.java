package com.kakarote.oa.entity.PO;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 审批关联业务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-01
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("wk_oa_examine_relation")
@ApiModel(value = "OaExamineRelation对象", description = "审批关联业务表")
public class OaExamineRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "审批ID")
    private Long examineId;

    @ApiModelProperty(value = "关联id")
    private Long relationId;

    @ApiModelProperty(value = "类型 1客户id 2联系人id 3项目id 4合同id")
    private Integer type;

    @ApiModelProperty(value = "状态1可用")
    private Integer status;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUserId;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long updateUserId;


    @ApiModelProperty(value = "客户IDs")
    @TableField(exist = false)
    private String customerIds;

    @ApiModelProperty(value = "联系人IDs")
    @TableField(exist = false)
    private String contactsIds;

    @ApiModelProperty(value = "项目IDs")
    @TableField(exist = false)
    private String businessIds;

    @ApiModelProperty(value = "合同IDs")
    @TableField(exist = false)
    private String contractIds;

}
