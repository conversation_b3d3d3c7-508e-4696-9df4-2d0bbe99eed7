package com.kakarote.oa.entity.VO;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.feign.admin.entity.SimpleDept;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.oa.entity.PO.OaLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @program: wk_crm
 * @className OaLogVO
 * @description:
 * @author: jiao sir
 * @create: 2021-07-30 17:32
 **/
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "工作日志VO模型")
@Data
public class OaLogVO extends OaLog {

    @ApiModelProperty(value = "getBulletin")
    private Integer getBulletin;

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "用户名字")
    private String realname;

    @ApiModelProperty(value = "用户头像")
    private String userImg;

    @ApiModelProperty(value = "创建人信息")
    private SimpleUser createUser;

    @ApiModelProperty(value = "附件")
    private List<FileEntity> file;

    @ApiModelProperty(value = "图片")
    private List<FileEntity> img;

    @ApiModelProperty(value = "日志点赞用户列表")
    private List<SimpleUser> favourUser;

    @ApiModelProperty(value = "当前用户是否已经对日志点赞")
    private Boolean isFavour;

    @ApiModelProperty(value = "发送人列表")
    private List<SimpleUser> sendUserList;

    @ApiModelProperty(value = "发送部门列表")
    private List<SimpleDept> sendDeptList;

    @ApiModelProperty(value = "客户数量")
    private Integer customerCount;

    @ApiModelProperty(value = "项目数量")
    private Integer businessCount;

    @ApiModelProperty(value = "合同数量")
    private Integer contractCount;

    @ApiModelProperty(value = "回款金额")
    private BigDecimal receivablesMoney;

    @ApiModelProperty(value = "跟进记录数量")
    private Integer recordCount;

    @ApiModelProperty(value = "回复数量")
    private Object replyNum;

    @ApiModelProperty(value = "客户ids")
    private String customerIds;

    @ApiModelProperty(value = "联系人Ids")
    private String contactsIds;

    @ApiModelProperty(value = "合同Ids")
    private String contractIds;

    @ApiModelProperty(value = "项目ids")
    private String businessIds;

    @ApiModelProperty(value = "客户列表")
    private List<SimpleCrmEntity> customerList;

    @ApiModelProperty(value = "联系人列表")
    private List<SimpleCrmEntity> contactsList;

    @ApiModelProperty(value = "合同列表")
    private List<SimpleCrmEntity> contractList;

    @ApiModelProperty(value = "项目列表")
    private List<SimpleCrmEntity> businessList;

    @ApiModelProperty(value = "permission")
    private JSONObject permission;

    @ApiModelProperty(value = "公告")
    private OaLogRecordBulletin bulletin;


    @Data
    @ApiModel(value = "公告数据")
    @AllArgsConstructor
    public static class OaLogRecordBulletin {
        @ApiModelProperty(value = "合同数量")
        private Integer contractCount;

        @ApiModelProperty(value = "回款金额")
        private BigDecimal receivablesMoney;

        @ApiModelProperty(value = "记录数量")
        private Integer recordCount;

        @ApiModelProperty(value = "项目数量")
        private Integer businessCount;

        @ApiModelProperty(value = "客户数量")
        private Integer customerCount;
    }

}
