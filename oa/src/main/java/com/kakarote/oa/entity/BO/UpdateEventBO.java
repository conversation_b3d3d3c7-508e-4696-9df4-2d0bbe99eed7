package com.kakarote.oa.entity.BO;

import com.kakarote.oa.entity.PO.OaEvent;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UpdateEventBO {

    private Integer type;

    private Long time;

    private OaEvent event;

    @Override
    public String toString() {
        return "UpdateEventBO{" +
                "type=" + type +
                ", time=" + time +
                ", event=" + event +
                '}';
    }
}
