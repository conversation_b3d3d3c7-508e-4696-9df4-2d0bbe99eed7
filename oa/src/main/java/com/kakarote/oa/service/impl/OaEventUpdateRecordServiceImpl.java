package com.kakarote.oa.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.oa.entity.PO.OaEventUpdateRecord;
import com.kakarote.oa.mapper.OaEventUpdateRecordMapper;
import com.kakarote.oa.service.IOaEventUpdateRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 日程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-15
 */
@Service
public class OaEventUpdateRecordServiceImpl extends BaseServiceImpl<OaEventUpdateRecordMapper, OaEventUpdateRecord> implements IOaEventUpdateRecordService {

}
