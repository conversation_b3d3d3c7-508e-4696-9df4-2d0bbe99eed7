<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.oa.mapper.OaExamineFieldMapper">
    <delete id="deleteByChooseId">
      DELETE FROM wk_oa_examine_field WHERE field_id not in
      <foreach collection="ids" separator="," item="id" open="(" close=")" index="index">
          #{id}
      </foreach>
      and (field_type  = '0' or field_type  = '2')
        and examine_category_id = #{categoryId}
    </delete>
    <delete id="deleteByFieldValue">
    DELETE FROM wk_oa_examine_data WHERE field_id in
      (
      SELECT field_id FROM wk_oa_examine_field WHERE field_id not in
        <foreach collection="ids" separator="," item="id" open="(" close=")" index="index">
            #{id}
        </foreach>
      and (field_type  = '0' or field_type  = '2')
        and examine_category_id = #{categoryId}
      )
    </delete>
</mapper>
