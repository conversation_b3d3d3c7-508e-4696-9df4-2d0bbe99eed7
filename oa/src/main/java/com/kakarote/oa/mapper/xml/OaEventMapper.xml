<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.oa.mapper.OaEventMapper">

    <select id="queryList" resultType="com.kakarote.oa.entity.PO.OaEvent">
        select a.*,b.color
        from `wk_oa_event` a left join `wk_oa_calendar_type` b on a.type_id = b.type_id
        where 1 = 1
        <if test="queryEventListBO.userId != null">
            and find_in_set(#{queryEventListBO.userId},owner_user_ids)
        </if>
        <if test="queryEventListBO.startTime != null">
            and (repeat_end_time is null or
            case
            when repetition_type = 1 then unix_timestamp(repeat_start_time) * 1000  >= (#{queryEventListBO.startTime} - (unix_timestamp(end_time) * 1000 - unix_timestamp(start_time) * 1000)) and
            unix_timestamp(repeat_end_time) * 1000 &lt;= #{queryEventListBO.endTime} + (unix_timestamp(end_time) * 1000 - unix_timestamp(start_time) * 1000)
            else
            unix_timestamp(repeat_end_time)*1000 &gt;= #{queryEventListBO.startTime}
            end
            )
        </if>
        <if test="queryEventListBO.typeIds != null and queryEventBO.type.size() > 0">
            and a.type_id in
            <foreach collection="queryEventListBO.typeIds" index="index" item="typeId" separator="," open="(" close=")" >
                #{typeId}
            </foreach>
        </if>
    </select>
    <select id="queryRelationData" resultType="com.alibaba.fastjson.JSONObject">
        select a.relation_id id, a.`type`,
            (
            case `type`
            when 3 then (select `name` from `wk_crm_contacts` where contacts_id = a.relation_id)
            when 5 then (select business_name from `wk_crm_business` where business_id = a.relation_id)
            when 2 then (select `customer_name` from `wk_crm_customer` where customer_id = a.relation_id)
            when 4 then (select `name` from `wk_crm_product` where product_id = a.relation_id)
            when 6 then (select `name` from `wk_crm_contract` where contract_id = a.relation_id)
            end
            ) name
        from wk_oa_event_relation a where event_id = #{eventId}
    </select>
</mapper>
