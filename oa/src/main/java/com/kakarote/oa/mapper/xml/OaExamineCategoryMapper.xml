<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.oa.mapper.OaExamineCategoryMapper">

    <select id="queryAllExamineCategoryList" resultType="com.kakarote.oa.entity.PO.OaExamineCategory">
        select t.category_id,t.title,t.type,t.icon from wk_oa_examine_category t
        left join  wk_oa_examine_sort a
        on  t.category_id = a.category_id and a.user_id = #{userId}
        where t.is_deleted = 0 and t.status = 1
      <if test="!isAdmin">
          AND ( t.user_ids = '' OR t.user_ids LIKE concat( '%,', #{userId}, ',%' ))
          AND ( t.dept_ids = '' OR t.dept_ids LIKE concat( '%,', #{deptId}, ',%' ))
      </if>
      order by a.sort is null,a.sort,t.create_time
    </select>
</mapper>
