package com.kakarote.oa.common.repetitionstrategy;


import cn.hutool.core.date.LocalDateTimeUtil;
import com.kakarote.oa.constart.RepetitionType;
import com.kakarote.oa.entity.BO.OaEventDTO;
import com.kakarote.oa.entity.PO.OaEvent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component("NoRepetition")
public class NoRepetitionStrategy extends AbstractRepetitionStrategy {


    @Override
    public List<OaEventDTO> processQuery(List<OaEvent> oaEventList, long startTime, long endTime) {
        List<OaEventDTO> eventList = new ArrayList<>();
        for (OaEvent oaEvent : oaEventList) {
            LocalDateTime startDate = oaEvent.getRepeatStartTime();
            LocalDateTime endDate = oaEvent.getRepeatEndTime();
            OaEventDTO oaEventDTO = transfer(oaEvent);
            oaEventDTO.setStartTime(LocalDateTimeUtil.toEpochMilli(startDate));
            oaEventDTO.setEndTime(LocalDateTimeUtil.toEpochMilli(endDate));
            eventList.add(oaEventDTO);
        }
        return eventList;
    }

    @Override
    public OaEvent processTime(OaEvent oaEvent) {
        oaEvent.setRepetitionType(RepetitionType.NO_REPETITION.getType());
        oaEvent.setRepeatStartTime(oaEvent.getStartTime());
        oaEvent.setRepeatEndTime(oaEvent.getEndTime());
        return oaEvent;
    }

    @Override
    protected LocalDateTime processCountTime(LocalDateTime startTime, int offset) {
        return LocalDateTimeUtil.offset(startTime,offset, ChronoUnit.MONTHS);
    }

    /*@Override
    protected Date processCountTime(Date startTime, int offset) {
        return startTime;
    }*/
}
