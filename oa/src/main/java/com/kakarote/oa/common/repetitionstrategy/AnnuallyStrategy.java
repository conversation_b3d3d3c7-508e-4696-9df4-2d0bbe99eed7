package com.kakarote.oa.common.repetitionstrategy;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.kakarote.oa.entity.BO.OaEventDTO;
import com.kakarote.oa.entity.PO.OaEvent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 每年重复策略
 *
 * <AUTHOR>
 */
@Component("Annually")
public class  AnnuallyStrategy extends AbstractRepetitionStrategy {


    @Override
    public List<OaEventDTO> processQuery(List<OaEvent> oaEventList, long startTime, long endTime) {
        List<OaEventDTO> eventList = new ArrayList<>();
        //创建一个日期范围list
        List<DateTime> dateTimes = DateUtil.rangeToList(new Date(startTime), new Date(endTime), DateField.DAY_OF_YEAR);
        for (DateTime day : dateTimes) {
            for (OaEvent oaEvent : oaEventList) {
                LocalDateTime endDate = oaEvent.getRepeatEndTime();
                LocalDateTime offStartTime = DateUtil.parseLocalDateTime(day.year()+"-"+ DateUtil.format(oaEvent.getStartTime(),"MM-dd HH:mm:ss"));
                if (isContinue(oaEvent, offStartTime, endDate)) {
                    continue;
                }
                //日程开始时间大于当前天就跳过
                if (LocalDateTimeUtil.toEpochMilli(oaEvent.getStartTime()) > day.getTime()){
                    continue;
                }

                long betweenYear = LocalDateTimeUtil.between(oaEvent.getStartTime(),offStartTime, ChronoUnit.YEARS);
                Integer repeatRate = oaEvent.getRepeatRate();
                //余数不等于0修跳过
                if (betweenYear % repeatRate != 0) {
                    continue;
                }
                //不是同一天就跳过
                if (!DateUtil.isSameDay(day,Date.from(offStartTime.atZone(ZoneId.systemDefault()).toInstant()))){
                    continue;
                }
                long duration = LocalDateTimeUtil.toEpochMilli(oaEvent.getEndTime()) - LocalDateTimeUtil.toEpochMilli(oaEvent.getStartTime());
                OaEventDTO oaEventDTO = transfer(oaEvent);
                oaEventDTO.setStartTime(LocalDateTimeUtil.toEpochMilli(offStartTime));
                oaEventDTO.setEndTime(LocalDateTimeUtil.toEpochMilli(offStartTime) + duration);
                eventList.add(oaEventDTO);
            }
        }
        return eventList;
    }

    @Override
    protected LocalDateTime processCountTime(LocalDateTime startTime, int offset) {
        return LocalDateTimeUtil.offset(startTime,offset,ChronoUnit.YEARS);
    }


}
