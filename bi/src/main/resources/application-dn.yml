spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password:
    database: 12
    lettuce:
      pool:
        max-active: 300
  datasource:
    url: *************************************************************************************************************************************************************************************
    username: root
    password: abcd.1234
    druid:
      testWhileIdle: true
      validationQuery: select 1
mybatis-plus:
  mapper-locations: classpath:/mapper/${bi.xmlPath}/*.xml

bi: #mysql配置详见application-test.yml
  clickhouse: false
  xmlPath: xml
