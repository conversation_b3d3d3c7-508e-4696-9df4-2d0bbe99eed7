spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123456
    database: 12
    lettuce:
      pool:
        max-active: 300
  datasource:
    url: jdbc:mysql://**************:8701/wk_crm_table?characterEncoding=utf8&useSSL=false&zeroDateTimeBehavior=convertToNull&tinyInt1isBit=false&serverTimezone=Asia/Shanghai&useAffectedRows=true
    username: root
    password: Psu45@!Q=Ehu
    druid:
      testWhileIdle: true
      validationQuery: select 1
mybatis-plus:
  mapper-locations: classpath:/mapper/${bi.xmlPath}/*.xml
bi:
  clickhouse: false
  xmlPath: xml
