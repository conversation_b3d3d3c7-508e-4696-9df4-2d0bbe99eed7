package com.kakarote.bi.service;

import com.kakarote.bi.entity.VO.BiEmployeeStatsVO;
import com.kakarote.bi.entity.VO.BiTotalContractVO;
import com.kakarote.bi.entity.VO.BiTotalInvoiceVO;
import com.kakarote.core.feign.crm.entity.BiEntityParams;

import java.util.List;

public interface BiEmployeeService {
    /**
     * 员工业绩分析
     *
     * @param biParams params
     * @param type     1 合同数量分析  2 合同金额分析  3 回款金额分析
     * @return data
     */
    List<BiEmployeeStatsVO> contractNumStats(BiEntityParams biParams, Integer type);

    /**
     * 合同汇总表
     *
     * @param biParams params
     * @return data
     */
    List<BiTotalContractVO> totalContract(BiEntityParams biParams);

    /**
     * 发票统计
     *
     * @param biParams params
     * @return data
     */
    List<BiTotalInvoiceVO> invoiceStats(BiEntityParams biParams);
}
