package com.kakarote.bi.service.dwhImpl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.entity.VO.BiEmployeeStatsVO;
import com.kakarote.bi.entity.VO.BiTotalContractVO;
import com.kakarote.bi.entity.VO.BiTotalInvoiceVO;
import com.kakarote.bi.mapper.BiEmployeeMapper;
import com.kakarote.bi.service.BiEmployeeService;
import com.kakarote.core.common.enums.DateFilterEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "true")
public class BiEmployeeServiceImpl implements BiEmployeeService {

    @Autowired
    private BiEmployeeMapper biEmployeeMapper;

    /**
     * 合同数量分析
     *
     * @param biParams params
     * @param type     1 合同数量分析  2 合同金额分析  3 回款金额分析
     * @return data
     */
    @Override
    public List<BiEmployeeStatsVO> contractNumStats(BiEntityParams biParams, Integer type) {
        //只能按年筛选,让前端传过来一个开始时间即可
        if (!Objects.equals(biParams.getDateFilter(), DateFilterEnum.CUSTOM) || biParams.getStartDate() == null) {
            throw new CrmException();
        }
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        //开始时间往前偏移一年，因为需要计算同比环比
        biTimeEntity.setBeginDate(DateUtil.beginOfYear(biParams.getStartDate()).offset(DateField.YEAR, -1));
        biTimeEntity.setEndDate(DateUtil.endOfYear(biParams.getStartDate()));
        //筛选可查询用户
        BiParamsUtil.analyzeUser(biTimeEntity, biParams, BiMenuEnum.BI_EMPLOYEES_STATS.getMenuId());
        List<BiEmployeeStatsVO> biEmployeeStatsVOS = biEmployeeMapper.contractNumStats(biTimeEntity, type);
        if(biEmployeeStatsVOS.size() > 12){
            biEmployeeStatsVOS = ListUtil.sub(biEmployeeStatsVOS,0,12);
        }
        return ListUtil.reverse(biEmployeeStatsVOS);
    }

    /**
     * 合同汇总表
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiTotalContractVO> totalContract(BiEntityParams biParams) {
        //只能按年筛选,让前端传过来一个开始时间即可
        if (!Objects.equals(biParams.getDateFilter(), DateFilterEnum.CUSTOM) || biParams.getStartDate() == null) {
            throw new CrmException();
        }
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        biTimeEntity.setBeginDate(DateUtil.beginOfYear(biParams.getStartDate()));
        biTimeEntity.setEndDate(DateUtil.endOfYear(biParams.getStartDate()));
        //筛选可查询用户
        BiParamsUtil.analyzeUser(biTimeEntity, biParams, BiMenuEnum.BI_EMPLOYEES_STATS.getMenuId());
        return biEmployeeMapper.totalContractTable(biTimeEntity);
    }

    /**
     * 发票统计
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiTotalInvoiceVO> invoiceStats(BiEntityParams biParams) {
        //只能按年筛选,让前端传过来一个开始时间即可
        if (!Objects.equals(biParams.getDateFilter(), DateFilterEnum.CUSTOM) || biParams.getStartDate() == null) {
            throw new CrmException();
        }
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        biTimeEntity.setBeginDate(DateUtil.beginOfYear(biParams.getStartDate()));
        biTimeEntity.setEndDate(DateUtil.endOfYear(biParams.getStartDate()));
        //筛选可查询用户
        BiParamsUtil.analyzeUser(biTimeEntity, biParams, BiMenuEnum.BI_EMPLOYEES_STATS.getMenuId());
        return biEmployeeMapper.invoiceStatsTable(biTimeEntity);
    }
}
