package com.kakarote.bi.service;

import com.kakarote.bi.entity.VO.BiCustomerAnalyseVO;
import com.kakarote.bi.entity.VO.BiRankCountVO;
import com.kakarote.bi.entity.VO.BiRankMoneyVO;
import com.kakarote.core.feign.crm.entity.BiEntityParams;

import java.util.List;

public interface BiRankService {

    /**
     * 城市分布分析
     *
     * @param biParams params
     * @return data
     */
    List<BiCustomerAnalyseVO> addressAnalyse(BiEntityParams biParams);

    /**
     * 城市分布分析
     *
     * @param biParams params
     * @return data
     */
    List<BiCustomerAnalyseVO> portrait(BiEntityParams biParams);

    /**
     * 城市级别分析
     *
     * @param biParams params
     * @return data
     */
    List<BiCustomerAnalyseVO> portraitLevel(BiEntityParams biParams);

    /**
     * 城市来源分析
     *
     * @param biParams params
     * @return data
     */
    List<BiCustomerAnalyseVO> portraitSource(BiEntityParams biParams);

    /**
     * 合同金额排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankMoneyVO> contractRanKing(BiEntityParams biParams);

    /**
     * 回款金额排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankMoneyVO> receivablesRanKing(BiEntityParams biParams);

    /**
     * 签约合同排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> contractCountRanKing(BiEntityParams biParams);

    /**
     * 产品销量排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> productCountRanKing(BiEntityParams biParams);

    /**
     * 新增客户数排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> customerCountRanKing(BiEntityParams biParams);

    /**
     * 新增联系人排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> contactsCountRanKing(BiEntityParams biParams);

    /**
     * 跟进客户数排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> customerRecordCountRanKing(BiEntityParams biParams);

    /**
     * 跟进次数排行榜
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> recordCountRanKing(BiEntityParams biParams);

    /**
     * 出差次数排行
     *
     * @param biParams params
     * @return data
     */
    List<BiRankCountVO> travelCountRanKing(BiEntityParams biParams);


}
