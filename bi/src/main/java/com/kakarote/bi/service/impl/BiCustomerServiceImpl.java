package com.kakarote.bi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.kakarote.bi.common.BiConst;
import com.kakarote.bi.common.enums.BiCodeEnum;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.common.enums.BiMySqlDateEnum;
import com.kakarote.bi.common.utils.BiDataFormatUtil;
import com.kakarote.bi.entity.BO.BICrmInfoBO;
import com.kakarote.bi.entity.BO.CrmFieldBO;
import com.kakarote.bi.entity.VO.*;
import com.kakarote.bi.mapper.BiCustomerMapper;
import com.kakarote.bi.service.BiCustomerService;
import com.kakarote.core.common.Const;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.core.utils.UserCacheUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "false")
@Service
public class BiCustomerServiceImpl implements BiCustomerService {

    @Autowired
    private BiCustomerMapper biCustomerMapper;

    /**
     * 查询客户总量分析图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiTotalCustomerVO> totalCustomerStats(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        List<BiTotalCustomerVO> totalCustomerVOS = biCustomerMapper.totalCustomerStats(timeEntity);
        //格式化日期等信息
        BiDataFormatUtil.dateFormat(totalCustomerVOS, biParams.getDateFilter());
        return totalCustomerVOS;
    }


    /**
     * 查询客户总量分析图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiTotalCustomerVO> totalCustomerTable(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiTotalCustomerVO> totalCustomerVOS = biCustomerMapper.totalCustomerTable(timeEntity, biParams.getSortField(), sortType);
        //格式化日期等信息
        BiDataFormatUtil.dateFormat(totalCustomerVOS, biParams.getDateFilter());
        for (BiTotalCustomerVO customerVO : totalCustomerVOS) {
            if (customerVO.getDealCustomerNum() != 0) {
                //设置客户成交率
                customerVO.setDealCustomerRate(new BigDecimal(customerVO.getDealCustomerNum()).divide(new BigDecimal(customerVO.getCustomerNum()), 4, RoundingMode.HALF_UP).multiply(BiConst.PERCENTAGE));
            } else {
                customerVO.setDealCustomerRate(BigDecimal.ZERO);
            }

        }
        return totalCustomerVOS;
    }

    /**
     * 客户跟进次数分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRecordVO> customerRecordStats(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        List<BiRecordVO> recordVOS = biCustomerMapper.customerRecordStats(timeEntity);
        for (BiRecordVO recordVO : recordVOS) {
            if (recordVO.getCustomerNum() != 0) {
                //设置客户成交率
                recordVO.setRecordNumRate(new BigDecimal(recordVO.getRecordNum()).divide(new BigDecimal(recordVO.getCustomerNum()), 4, RoundingMode.HALF_UP));
            } else {
                recordVO.setRecordNumRate(BigDecimal.ZERO);
            }
        }
        return recordVOS;
    }

    /**
     * 客户跟进次数分析表
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRecordVO> customerRecordInfo(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiRecordVO> recordVOS = biCustomerMapper.customerRecordInfo(timeEntity, biParams.getSortField(), sortType);
        for (BiRecordVO recordVO : recordVOS) {
            if (recordVO.getCustomerNum() != 0) {
                //设置客户成交率
                recordVO.setRecordNumRate(new BigDecimal(recordVO.getRecordNum()).divide(new BigDecimal(recordVO.getCustomerNum()), 4, RoundingMode.HALF_UP));
            } else {
                recordVO.setRecordNumRate(BigDecimal.ZERO);
            }
            recordVO.setRealname(UserCacheUtil.getUserName(recordVO.getUserId()));
        }
        return recordVOS;
    }

    /**
     * 客户转化率分析表
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiTotalCustomerVO> customerConversionStats(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //有个查询限制，至少查询一个月的数据，后端未做限制，让前端处理了
        //分析表统计方式 1为 客户本月状态变更为已成交的数量 2为 客户本月新增的客户状态变为已成交的数量
        int type = Objects.equals(1, biParams.getType()) ? 1 : 2;
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        List<BiTotalCustomerVO> totalCustomerVOS;
        //客户本月新增的客户状态变为已成交的数量
        int newKnockdownNumberType = 2;
        if (type == newKnockdownNumberType) {
            totalCustomerVOS = biCustomerMapper.totalCustomerStats(timeEntity);
        } else {
            totalCustomerVOS = biCustomerMapper.customerConversionStats(timeEntity);
        }
        //格式化日期等信息
        BiDataFormatUtil.dateFormat(totalCustomerVOS, biParams.getDateFilter());
        for (BiTotalCustomerVO customerVO : totalCustomerVOS) {
            if (customerVO.getDealCustomerNum() != 0 && customerVO.getCustomerNum() != 0) {
                //设置客户成交率
                customerVO.setDealCustomerRate(new BigDecimal(customerVO.getDealCustomerNum()).divide(new BigDecimal(customerVO.getCustomerNum()), 4, RoundingMode.HALF_UP).multiply(BiConst.PERCENTAGE));
            } else {
                customerVO.setDealCustomerRate(BigDecimal.ZERO);
            }
        }
        return totalCustomerVOS;
    }

    /**
     * 公海客户分析图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiCustomerPoolVO> poolStats(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        return biCustomerMapper.poolStats(timeEntity);
    }

    /**
     * 公海客户分析表
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiCustomerPoolVO> poolTable(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiCustomerPoolVO> customerPoolVOS = biCustomerMapper.poolTable(timeEntity, biParams.getSortField(), sortType);
        //设置部门等信息
        for (BiCustomerPoolVO customerPoolVO : customerPoolVOS) {
            SimpleUser simpleUser = UserCacheUtil.getSimpleUser(customerPoolVO.getUserId());
            customerPoolVO.setRealname(simpleUser.getRealname());
            customerPoolVO.setDeptName(simpleUser.getDeptName());
        }
        return customerPoolVOS;
    }

    /**
     * 员工客户成交周期图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiEmployeeCycleVO> employeeCycle(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        //获取CK格式化数据信息
        timeEntity.setDateFormat(BiMySqlDateEnum.getMySqlDate(biParams.getDateFilter()));
        List<BiEmployeeCycleVO> employeeCycleVOS = biCustomerMapper.employeeCycle(timeEntity);
        for (BiEmployeeCycleVO employeeCycleVO : employeeCycleVOS) {
            if (employeeCycleVO.getCustomerNum() == 0) {
                employeeCycleVO.setCycle(0);
            } else {
                //因为在sql中没有进行天数运算，不保留小数，四舍五入
                employeeCycleVO.setCycle(new BigDecimal(employeeCycleVO.getCycle()).divide(new BigDecimal(employeeCycleVO.getCustomerNum()), 0, RoundingMode.HALF_UP).intValue());
            }
        }
        return employeeCycleVOS;
    }

    /**
     * 员工客户成交周期表
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiEmployeeCycleVO> employeeCycleInfo(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiEmployeeCycleVO> employeeCycleVOS = biCustomerMapper.employeeCycleInfo(timeEntity, biParams.getSortField(), sortType);
        for (BiEmployeeCycleVO employeeCycleVO : employeeCycleVOS) {
            if (employeeCycleVO.getCustomerNum() == 0) {
                employeeCycleVO.setCycle(0);
            } else {
                //因为在sql中没有进行天数运算，不保留小数，四舍五入
                employeeCycleVO.setCycle(new BigDecimal(employeeCycleVO.getCycle()).divide(new BigDecimal(employeeCycleVO.getCustomerNum()), 0, RoundingMode.HALF_UP).intValue());
            }
            SimpleUser simpleUser = UserCacheUtil.getSimpleUser(employeeCycleVO.getUserId());
            employeeCycleVO.setRealname(simpleUser.getRealname());
        }
        return employeeCycleVOS;
    }

    /**
     * 地区成交周期图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiEmployeeCycleVO> districtCycle(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiEmployeeCycleVO> districtCycleList = biCustomerMapper.districtCycle(timeEntity, biParams.getSortField(), sortType);
        for (BiEmployeeCycleVO districtCycle : districtCycleList) {
            if (districtCycle.getCustomerNum() == 0) {
                districtCycle.setCycle(0);
            } else {
                //因为在sql中没有进行天数运算，不保留小数，四舍五入
                districtCycle.setCycle(new BigDecimal(districtCycle.getCycle()).divide(new BigDecimal(districtCycle.getCustomerNum()), 0, RoundingMode.HALF_UP).intValue());
            }
        }
        return districtCycleList;
    }

    /**
     * 产品成交周期图
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiEmployeeCycleVO> productCycle(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        String sortType = Objects.equals(1, biParams.getType()) ? "desc" : "asc";
        List<BiEmployeeCycleVO> districtCycleList = biCustomerMapper.productCycle(timeEntity, biParams.getSortField(), sortType);
        for (BiEmployeeCycleVO districtCycle : districtCycleList) {
            if (districtCycle.getCustomerNum() == 0) {
                districtCycle.setCycle(0);
            } else {
                //因为在sql中没有进行天数运算，不保留小数，四舍五入
                districtCycle.setCycle(new BigDecimal(districtCycle.getCycle()).divide(new BigDecimal(districtCycle.getCustomerNum()), 0, RoundingMode.HALF_UP).intValue());
            }
        }
        return districtCycleList;
    }

    /**
     * 客户满意度分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<JSONObject> customerSatisfactionTable(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        BiParamsUtil.analyzeDate(biTimeEntity, biParams);
        BiParamsUtil.analyzeUserOrDept(biTimeEntity, biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        CrmFieldBO satisficing = biCustomerMapper.queryCrmFieldInfo(17, "satisficing");
        Map<String, Object> map = new HashMap<>();
        map.put("fieldId", satisficing.getFieldId());
        List<String> options = StrUtil.splitTrim(satisficing.getOptions(), Const.SEPARATOR);
        map.put("options", options);
        List<JSONObject> jsonObjects = biCustomerMapper.customerSatisfactionTable(biTimeEntity, map);
        jsonObjects.forEach(object -> {
            object.put("realname", UserCacheUtil.getUserName(object.getLong("ownerUserId")));
            addOptionNum(object, options);
        });
        return jsonObjects;
    }

    /**
     * 产品满意度分析
     *
     * @param biEntityParams params
     * @return data
     */
    @Override
    public List<JSONObject> productSatisfactionTable(BiEntityParams biEntityParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biEntityParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        if (CollectionUtil.isEmpty(biTimeEntity.getUserIds())) {
            return Collections.emptyList();
        }
        CrmFieldBO satisficing = biCustomerMapper.queryCrmFieldInfo(17, "satisficing");
        Map<String, Object> map = new HashMap<>();
        map.put("fieldId", satisficing.getFieldId());
        List<String> options = StrUtil.splitTrim(satisficing.getOptions(), Const.SEPARATOR);
        map.put("options", options);
        List<JSONObject> jsonObjects = biCustomerMapper.productSatisfactionTable(biTimeEntity, map);
        jsonObjects.forEach(item -> {
            addOptionNum(item, options);
        });
        return jsonObjects;
    }

    @Override
    public BasePage<JSONObject> queryProductTypeList(BICrmInfoBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        BiParamsUtil.analyzeDate(biTimeEntity, biParams);
        BiParamsUtil.analyzeUserOrDept(biTimeEntity, biParams, BiMenuEnum.BI_PRODUCT_STATS.getMenuId());
        Integer page = biParams.getPage() > 0 ? (biParams.getPage() - 1) * biParams.getLimit() : 0;
        List<JSONObject> list = biCustomerMapper.queryProductTypeList(biTimeEntity, biParams,page,biParams.getLimit());
        BasePage<JSONObject> basePage = new BasePage<>(biParams.getPage(), biParams.getLimit());
        basePage.setList(list);
        return basePage;
    }

    @Override
    public List<Long> queryProductCustomerList(BICrmInfoBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.CUSTOMER_LIST.getMenuId());
        return biCustomerMapper.queryProductCustomerList(biTimeEntity,biParams.getCategoryId());
    }

    @Override
    public List<Long> queryContendBusinessList(BICrmInfoBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = new BiParamsUtil.BiTimeEntity();
        BiParamsUtil.analyzeDate(biTimeEntity, biParams);
        BiParamsUtil.analyzeUserOrDept(biTimeEntity, biParams, BiMenuEnum.BUSINESS_LIST.getMenuId());
        List<Long> ids = biCustomerMapper.queryContendBusinessList(biTimeEntity,biParams);
        return ids;
    }

    @Override
    public BasePage<BiEmployeeCallVO> queryCallAnalysis(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CALL_ANALYSIS.getMenuId());
        BasePage<BiEmployeeCallVO> basePage = biCustomerMapper.queryCallAnalysis(biParams.parse(),biTimeEntity,biParams);
        for (BiEmployeeCallVO vo : basePage.getList()) {
            setCalculate(vo);
            SimpleUser simpleUser = UserCacheUtil.getSimpleUser(vo.getOwnerUserId());
            vo.setUserInfo(simpleUser);
        }
        return basePage;
    }

    @Override
    public BasePage<BiCallListVO> queryCallList(BICrmInfoBO biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CALL_INDEX.getMenuId());
        Long talkTime = biParams.getTalkTime();
        String talkTimeCondition = biParams.getTalkTimeCondition();
        if (talkTime != null && StrUtil.isEmpty(talkTimeCondition)){
            throw new CrmException(BiCodeEnum.BI_CALL_DATA_QUERY_ERROR,"筛选条件");
        }
        BasePage<BiCallListVO> basePage = biCustomerMapper.queryCallList(biParams.parse(), biTimeEntity, talkTime, talkTimeCondition);
        Map<String,List<Long>> modelMap = new HashMap<>(4,1.0F);
        for (BiCallListVO listVO : basePage.getList()) {
            listVO.setOwnerUserName(UserCacheUtil.getUserName(listVO.getOwnerUserId()));
            if(!modelMap.containsKey(listVO.getModel())) {
                modelMap.put(listVO.getModel(),new ArrayList<>());
            }
            modelMap.get(listVO.getModel()).add(listVO.getModelId());
        }
        Map<Long, Map<String,Object>> modelDataMap = biCustomerMapper.queryModelName(modelMap);
        for (BiCallListVO listVO : basePage.getList()) {
            if(modelDataMap.containsKey(listVO.getModelId())){
                listVO.setModelName(TypeUtils.castToString(modelDataMap.get(listVO.getModelId()).get("modelName")));
            }
        }
        return basePage;
    }

    private void addOptionNum(JSONObject jsonObject, List<String> options) {
        int allNum = TypeUtils.castToInt(jsonObject.get("allNum"));
        int num = 0;
        if (allNum > 0) {
            for (String option : options) {
                num += TypeUtils.castToInt(jsonObject.get(option));
            }
        }
        jsonObject.put("其他", allNum - num);
    }
}
