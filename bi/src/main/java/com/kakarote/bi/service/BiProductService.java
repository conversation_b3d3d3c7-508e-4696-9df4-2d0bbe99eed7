package com.kakarote.bi.service;

import com.kakarote.bi.entity.VO.BiProductStatisticsVO;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;

import java.util.List;

public interface BiProductService {

    /**
     * 查询产品销售情况统计
     * @param biParams biParams
     * @return data
     */
    public BasePage<BiProductStatisticsVO> queryProductSell(BiEntityParams biParams);

    /**
     * 产品分类销量分析
     *
     * @param biParams params
     * @return data
     */
    public List<BiProductStatisticsVO> contractProductRanKing(BiEntityParams biParams);
}
