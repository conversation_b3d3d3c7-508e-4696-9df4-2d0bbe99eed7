package com.kakarote.bi.service;

import com.kakarote.bi.entity.BO.BiAchievementBO;
import com.kakarote.bi.entity.VO.BiAchievementVO;

import java.util.List;
import java.util.Map;

public interface BiAchievementService {


    /**
     * 完成工作统计
     *
     * @param biAchievementBO:业绩目标完成
     * @return 业绩目标分析VO
     */
    public List<BiAchievementVO> taskCompleteStatistics(BiAchievementBO biAchievementBO);

    /**
     * 业绩完成统计导出
     *
     * @param biAchievementBO
     * @return data
     */
    public List<Map<String, Object>> taskCompleteStatisticsExport(BiAchievementBO biAchievementBO);
}
