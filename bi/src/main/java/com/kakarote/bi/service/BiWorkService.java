package com.kakarote.bi.service;


import com.kakarote.bi.entity.BO.BiExamineInfoBO;
import com.kakarote.bi.entity.VO.BiExamineStatisticsVO;
import com.kakarote.bi.entity.VO.BiWorkLogStatisticsVO;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.feign.oa.entity.ExamineVO;
import com.kakarote.core.feign.oa.entity.OaLogVO;

import java.util.List;

public interface BiWorkService {

    /**
     * 查询日志统计信息
     *
     * @param biParams:bi查询相关参数
     * @return 日志分析VO
     */
    public List<BiWorkLogStatisticsVO> logStatistics(BiEntityParams biParams);

    /**
     * 查询审批统计信息
     *
     * @param biParams:bi查询相关参数
     * @return 办公审批分析
     */
    public BiExamineStatisticsVO examineStatistics(BiEntityParams biParams);

    /**
     * 查询审批详情
     *
     * @param biEntityParams:bi查询相关参数
     * @return 审批VO
     */
    BasePage<ExamineVO> examineInfo(BiExamineInfoBO biEntityParams);

    /**
     * 查询评论日志列表
     * @param biEntityParams
     */
    BasePage<OaLogVO> queryOaComment(BiExamineInfoBO biEntityParams);

    /**
     * 查询未评论日志列表
     * @param biEntityParams
     * @return
     */
    BasePage<OaLogVO> queryOaNoComment(BiExamineInfoBO biEntityParams);
}
