package com.kakarote.bi.service;

import com.kakarote.bi.entity.BO.BiSellFunnelBO;
import com.kakarote.bi.entity.VO.BiSellFunnelVO;
import com.kakarote.core.feign.crm.entity.BiEntityParams;

import java.util.List;

public interface BiFunnelService {
    /**
     * 销售漏斗
     * @param biParams params
     * @return data
     */
    public List<BiSellFunnelVO> sellFunnel(BiSellFunnelBO biParams);

    /**
     * 新增项目分析图
     * @param biParams params
     * @return data
     */
    public List<BiSellFunnelVO> addBusinessAnalyze(BiEntityParams biParams);

    /**
     * 项目转化率分析
     * @param biParams params
     * @return data
     */
    public List<BiSellFunnelVO> win(BiEntityParams biParams);

}
