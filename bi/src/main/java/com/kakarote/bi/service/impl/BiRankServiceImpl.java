package com.kakarote.bi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.bi.common.BiConst;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.common.enums.MonthEnum;
import com.kakarote.bi.common.utils.BiDataFormatUtil;
import com.kakarote.bi.entity.BO.CrmFieldBO;
import com.kakarote.bi.entity.VO.BiCustomerAnalyseVO;
import com.kakarote.bi.entity.VO.BiRankCountVO;
import com.kakarote.bi.entity.VO.BiRankMoneyVO;
import com.kakarote.bi.mapper.BiCustomerMapper;
import com.kakarote.bi.mapper.BiRankMapper;
import com.kakarote.bi.service.BiRankService;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.enums.DateFilterEnum;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.core.utils.UserCacheUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(prefix = "bi", name = "clickhouse", havingValue = "false")
public class BiRankServiceImpl implements BiRankService {

    @Autowired
    private BiRankMapper biRankMapper;

    @Autowired
    private BiCustomerMapper biCustomerMapper;

    /**
     * 城市分布分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiCustomerAnalyseVO> addressAnalyse(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_ANALYSE_STATS.getMenuId());
        if (CollectionUtil.isEmpty(timeEntity.getUserIds())) {
            return Collections.emptyList();
        }
        return biRankMapper.addressAnalyse(timeEntity);
    }

    /**
     * 客户行业分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiCustomerAnalyseVO> portrait(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_ANALYSE_STATS.getMenuId());
        if (CollectionUtil.isEmpty(timeEntity.getUserIds())) {
            return Collections.emptyList();
        }
        CrmFieldBO fieldBO = biCustomerMapper.queryCrmFieldInfo(2, "industry");
        List<String> optionsList = StrUtil.splitTrim(fieldBO.getOptions(), Const.SEPARATOR);
        List<BiCustomerAnalyseVO> analyseVOS = biRankMapper.portrait(timeEntity, fieldBO.getFieldId());
        return buildOptions(optionsList, analyseVOS);
    }

    /**
     * 城市级别分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiCustomerAnalyseVO> portraitLevel(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_ANALYSE_STATS.getMenuId());
        if (CollectionUtil.isEmpty(timeEntity.getUserIds())) {
            return Collections.emptyList();
        }
        CrmFieldBO fieldBO = biCustomerMapper.queryCrmFieldInfo(2, "level");
        List<String> optionsList = StrUtil.splitTrim(fieldBO.getOptions(), Const.SEPARATOR);
        List<BiCustomerAnalyseVO> analyseVOS = biRankMapper.portrait(timeEntity, fieldBO.getFieldId());
        return buildOptions(optionsList, analyseVOS);
    }

    /**
     * 城市来源分析
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiCustomerAnalyseVO> portraitSource(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_ANALYSE_STATS.getMenuId());
        if (CollectionUtil.isEmpty(timeEntity.getUserIds())) {
            return Collections.emptyList();
        }
        //字段不会为空
        CrmFieldBO fieldBO = biCustomerMapper.queryCrmFieldInfo(2, "source");
        List<String> optionsList = StrUtil.splitTrim(fieldBO.getOptions(), Const.SEPARATOR);
        List<BiCustomerAnalyseVO> analyseVOS = biRankMapper.portrait(timeEntity, fieldBO.getFieldId());
        return buildOptions(optionsList, analyseVOS);
    }

    private List<BiCustomerAnalyseVO> buildOptions(List<String> optionsList, List<BiCustomerAnalyseVO> analyseVOS) {
        BiCustomerAnalyseVO customerAnalyseVO = new BiCustomerAnalyseVO("其他", 0, 0);
        analyseVOS.removeIf(data -> {
            if (optionsList.contains(data.getType())) {
                return false;
            }
            customerAnalyseVO.setAllCustomer(customerAnalyseVO.getAllCustomer() + data.getAllCustomer());
            customerAnalyseVO.setDealCustomer(customerAnalyseVO.getDealCustomer() + data.getDealCustomer());
            return true;
        });
        analyseVOS.add(customerAnalyseVO);
        return analyseVOS;
    }

    /**
     * 合同金额排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankMoneyVO> contractRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankMoneyVO> moneyVOS = biRankMapper.contractRanKing(timeEntity);
        BiDataFormatUtil.rankUserMoneySetting(moneyVOS);
        if (CollectionUtil.isNotEmpty(moneyVOS)) {
            calculateRate(timeEntity.getBeginDate(), timeEntity.getEndDate(), timeEntity.getUserIds(), 1, biParams.getDateFilter(), moneyVOS);
        }
        return moneyVOS;
    }

    /**
     * 回款金额排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankMoneyVO> receivablesRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankMoneyVO> moneyVOS = biRankMapper.receivablesRanKing(timeEntity);
        BiDataFormatUtil.rankUserMoneySetting(moneyVOS);
        if (CollectionUtil.isNotEmpty(moneyVOS)) {
            calculateRate(timeEntity.getBeginDate(), timeEntity.getEndDate(), timeEntity.getUserIds(), 2, biParams.getDateFilter(), moneyVOS);
        }
        return moneyVOS;
    }

    /**
     * 签约合同排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> contractCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> countVOS = biRankMapper.contractCountRanKing(timeEntity);
        BiDataFormatUtil.rankUserCountSetting(countVOS);
        return countVOS;
    }

    /**
     * 产品销量排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> productCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> countVOS = biRankMapper.productCountRanKing(timeEntity);
        BiDataFormatUtil.rankUserCountSetting(countVOS);
        return countVOS;
    }

    /**
     * 新增客户数排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> customerCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> countVOS = biRankMapper.customerCountRanKing(timeEntity);
        BiDataFormatUtil.rankUserCountSetting(countVOS);
        return countVOS;
    }

    /**
     * 新增联系人排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> contactsCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> countVOS = biRankMapper.contactsCountRanKing(timeEntity);
        BiDataFormatUtil.rankUserCountSetting(countVOS);
        return countVOS;
    }

    /**
     * 跟进客户数排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> customerRecordCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> countVOS = biRankMapper.customerRecordCountRanKing(timeEntity);
        BiDataFormatUtil.rankUserCountSetting(countVOS);
        return countVOS;
    }

    /**
     * 跟进次数排行榜
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> recordCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> countVOS = biRankMapper.recordCountRanKing(timeEntity);
        BiDataFormatUtil.rankUserCountSetting(countVOS);
        return countVOS;
    }

    /**
     * 出差次数排行
     *
     * @param biParams params
     * @return data
     */
    @Override
    public List<BiRankCountVO> travelCountRanKing(BiEntityParams biParams) {
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_RANK_STATS.getMenuId());
        List<BiRankCountVO> biRankCountVOS = biRankMapper.travelCountRanKing(biTimeEntity);
        Long examineType = biRankMapper.findExamineType(3);
        int i = 0;
        for (BiRankCountVO biRankCountVO : biRankCountVOS) {
            SimpleUser simpleUser = UserCacheUtil.getSimpleUser(biRankCountVO.getUserId());
            biRankCountVO.setOrder(++i);
            biRankCountVO.setDeptName(simpleUser.getDeptName());
            biRankCountVO.setRealname(simpleUser.getRealname());
            biRankCountVO.setImg(simpleUser.getImg());
            biRankCountVO.setTypeId(examineType);
        }
        return biRankCountVOS;
    }

    private void calculateRate(Date startDate, Date endDate, List<Long> userIds, Integer module, DateFilterEnum dateFilterEnum, List<BiRankMoneyVO> biRankMoneyVOS) {
        Map<String, Object> map = new HashMap<>();
        map.put("module", module);
        int startYear = DateUtil.year(startDate);
        int endYear = DateUtil.year(endDate);
        if (dateFilterEnum == DateFilterEnum.QUARTER || dateFilterEnum == DateFilterEnum.LAST_QUARTER || dateFilterEnum == DateFilterEnum.NEXT_QUARTER) {
            int quarter = DateUtil.quarter(startDate);
            if (dateFilterEnum == DateFilterEnum.LAST_QUARTER) {
                quarter = quarter - 1;
                if (quarter == 0) {
                    quarter = 4;
                    startYear = startYear - 1;
                }
            } else if (dateFilterEnum == DateFilterEnum.NEXT_QUARTER) {
                quarter = quarter + 1;
                // quarter=当前时间所属的季度5就是下一年
                int nextYear = 5;
                if (quarter == nextYear) {
                    quarter = 1;
                    startYear = startYear + 1;
                }
            }
            List<MonthEnum> quarterMonth = MonthEnum.parseQuarterMonth(quarter);
            map.put("queryField", quarterMonth.stream().map(MonthEnum::getName).collect(Collectors.toList()));
        } else if (dateFilterEnum == DateFilterEnum.YEAR || dateFilterEnum == DateFilterEnum.LAST_YEAR || dateFilterEnum == DateFilterEnum.NEXT_YEAR) {
            map.put("queryField", Arrays.stream(MonthEnum.values()).map(MonthEnum::getName).collect(Collectors.toList()));
        } else {
            MonthEnum startMonthEnum = MonthEnum.valueOf(DateUtil.month(startDate) + 1);
            MonthEnum endMonthEnum = MonthEnum.valueOf(DateUtil.month(endDate) + 1);
            map.put("queryField", Collections.singleton(startMonthEnum.getName()));
            if (!Objects.equals(startMonthEnum.getValue(), endMonthEnum.getValue())) {
                map.put("endMonthField", Collections.singleton(endMonthEnum.getName()));
            }
        }
        map.put("startYear", startYear);
        if (startYear != endYear) {
            map.put("endYear", endYear);
        }
        Map<Long, JSONObject> result = biRankMapper.queryUserCalaulateRate(map, userIds);
        BigDecimal oneHundredDecimal = BiConst.PERCENTAGE;
        for (BiRankMoneyVO biRankMoneyVO : biRankMoneyVOS) {
            JSONObject jsonObject = result.get(biRankMoneyVO.getUserId());
            if (null != jsonObject) {
                BigDecimal achievemen = jsonObject.getBigDecimal("money");
                BigDecimal money = biRankMoneyVO.getMoney();
                if (null == money) {
                    biRankMoneyVO.setRate("0");
                } else if (achievemen.compareTo(money) == 0) {
                    biRankMoneyVO.setRate("100");
                } else if (BigDecimal.ZERO.compareTo(achievemen) == 0) {
                    biRankMoneyVO.setRate(biRankMoneyVO.getMoney().multiply(oneHundredDecimal).toString());
                } else {
                    biRankMoneyVO.setRate(biRankMoneyVO.getMoney().multiply(oneHundredDecimal).divide(achievemen, 2, RoundingMode.HALF_UP).toString());
                }
            } else {
                biRankMoneyVO.setRate("暂无目标");
            }
        }
    }
}
