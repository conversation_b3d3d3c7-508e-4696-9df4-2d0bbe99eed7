package com.kakarote.bi.service;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.bi.entity.BO.BICrmInfoBO;
import com.kakarote.bi.entity.VO.*;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public interface BiCustomerService {

    /**
     * 查询客户总量分析图
     *
     * @param biParams params
     * @return data
     */
    List<BiTotalCustomerVO> totalCustomerStats(BiEntityParams biParams);

    /**
     * 查询客户总量分析表格
     *
     * @param biParams params
     * @return data
     */
    List<BiTotalCustomerVO> totalCustomerTable(BiEntityParams biParams);

    /**
     * 客户跟进次数分析
     *
     * @param biParams params
     * @return data
     */
    List<BiRecordVO> customerRecordStats(BiEntityParams biParams);

    /**
     * 客户跟进次数分析表
     *
     * @param biParams params
     * @return data
     */
    List<BiRecordVO> customerRecordInfo(BiEntityParams biParams);

    /**
     * 客户转化率分析图
     *
     * @param biParams params
     * @return data
     */
    List<BiTotalCustomerVO> customerConversionStats(BiEntityParams biParams);

    /**
     * 公海客户分析图
     *
     * @param biParams params
     * @return data
     */
    List<BiCustomerPoolVO> poolStats(BiEntityParams biParams);

    /**
     * 公海客户分析表
     *
     * @param biParams params
     * @return data
     */
    List<BiCustomerPoolVO> poolTable(BiEntityParams biParams);

    /**
     * 员工客户成交周期图
     *
     * @param biParams params
     * @return data
     */
    List<BiEmployeeCycleVO> employeeCycle(BiEntityParams biParams);

    /**
     * 员工客户成交周期表
     *
     * @param biParams params
     * @return data
     */
    List<BiEmployeeCycleVO> employeeCycleInfo(BiEntityParams biParams);

    /**
     * 地区成交周期图
     *
     * @param biParams params
     * @return data
     */
    List<BiEmployeeCycleVO> districtCycle(BiEntityParams biParams);

    /**
     * 产品成交周期图
     *
     * @param biParams params
     * @return data
     */
    List<BiEmployeeCycleVO> productCycle(BiEntityParams biParams);

    /**
     * 客户满意度分析
     *
     * @param biParams params
     * @return data
     */
    List<JSONObject> customerSatisfactionTable(BiEntityParams biParams);

    /**
     * 产品满意度分析
     *
     * @param biEntityParams params
     * @return data
     */
    List<JSONObject> productSatisfactionTable(BiEntityParams biEntityParams);

    /**
     * 查看产品类别销售列表
     *
     * @param biParams
     */
    BasePage<JSONObject> queryProductTypeList(BICrmInfoBO biParams);

    /**
     * 查看产品成交客户列表
     *
     * @param biParams
     * @return
     */
    List<Long> queryProductCustomerList(BICrmInfoBO biParams);

    /**
     * 查看项目赢单数据
     *
     * @param biParams
     * @return
     */
    List<Long> queryContendBusinessList(BICrmInfoBO biParams);

    /**
     * 查看员工通话统计分析
     *
     * @param biParams
     */
    BasePage<BiEmployeeCallVO> queryCallAnalysis(BiEntityParams biParams);

    /**
     * 查看员工通话记录
     *
     * @param biParams
     * @return
     */
    BasePage<BiCallListVO> queryCallList(BICrmInfoBO biParams);


    /**
     * 判断两个值是否可以合法做除法运算
     *
     * @param a 值a
     * @param b 值b
     * @return true为可以
     */
    default boolean isCanCalculate(Integer a, Integer b) {
        boolean dataA = a != null && a != 0;
        boolean dataB = b != null && b != 0;
        return dataA && dataB;
    }

    default void setCalculate(BiEmployeeCallVO vo) {
        //接通率
        if (isCanCalculate(vo.getTotalCountCalls(), vo.getTotalCountAnswer())) {
            vo.setRateAnswer(BigDecimal.valueOf(vo.getTotalCountAnswer()).divide(new BigDecimal(vo.getTotalCountCalls()), 2, RoundingMode.HALF_UP));
        } else {
            vo.setRateAnswer(BigDecimal.ZERO);
        }
        //外呼接通率
        if (isCanCalculate(vo.getTotalCountCallsOut(), vo.getTotalCountAnswerOut())) {
            vo.setRateAnswerOut(BigDecimal.valueOf(vo.getTotalCountAnswerOut()).divide(new BigDecimal(vo.getTotalCountCallsOut()), 2, RoundingMode.HALF_UP));
        } else {
            vo.setRateAnswerOut(BigDecimal.ZERO);
        }
        //外呼通话平均时长
        if (NumberUtil.isNumber(vo.getTotalTimeCallsOut()) && isCanCalculate(Integer.parseInt(vo.getTotalTimeCallsOut()), vo.getTotalCountAnswerOut())) {
            vo.setAverageTimeCallOut(new BigDecimal(vo.getTotalTimeCallsOut()).divide(new BigDecimal(vo.getTotalCountAnswerOut()), 2, RoundingMode.HALF_UP).toString());
        } else {
            vo.setAverageTimeCallOut("0");
        }
        //呼入通话平均时长
        if (NumberUtil.isNumber(vo.getTotalTimeCallsIn()) && isCanCalculate(Integer.parseInt(vo.getTotalTimeCallsIn()), vo.getTotalCountAnswerIn())) {
            vo.setAverageTimeCallIn(new BigDecimal(vo.getTotalTimeCallsIn()).divide(new BigDecimal(vo.getTotalCountAnswerIn()), 2, RoundingMode.HALF_UP).toString());
        } else {
            vo.setAverageTimeCallIn("0");
        }
        //呼入接通率
        if (isCanCalculate(vo.getTotalCountAnswerIn(), vo.getTotalCountCallsIn())) {
            vo.setRateAnswerIn(BigDecimal.valueOf(vo.getTotalCountAnswerIn()).divide(new BigDecimal(vo.getTotalCountCallsIn()), 2, RoundingMode.HALF_UP));
        } else {
            vo.setRateAnswerIn(BigDecimal.ZERO);
        }
    }
}
