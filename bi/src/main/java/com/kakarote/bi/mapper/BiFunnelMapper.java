package com.kakarote.bi.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.VO.BiSellFunnelVO;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface BiFunnelMapper {

    /**
     * 销售漏斗查询
     * @param biTimeEntity timeEntity
     * @param sortField 排序字段
     * @param sortType 排序方式
     * @param typeId 阶段流程ID
     * @return data
     */
    @SqlParser(filter = true)
    List<BiSellFunnelVO> sellFunnel(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("sortField") String sortField, @Param("sortType") String sortType, @Param("typeId") Long typeId);

    /**
     * 新增项目分析
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiSellFunnelVO> addBusinessAnalyze(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 项目转化率分析
     * 显示筛选时间内签订合同的项目转化率（赢单项目数/项目总数）
     * @param biTimeEntity timeEntity
     * @return data
     */
    @SqlParser(filter = true)
    List<BiSellFunnelVO> win(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);

    /**
     * 查询结尾项目
     * @param  timeEntity:
     * @param  typeId:类型id
     * @return data
    */
    @SqlParser(filter = true)
    @MapKey("isEnd")
    Map<Integer,BiSellFunnelVO> queryEndBusiness(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity, @Param("typeId")  Long typeId);
}
