<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.bi.mapper.BiFunnelMapper">
    <select id="sellFunnel" resultType="com.kakarote.bi.entity.VO.BiSellFunnelVO">
        SELECT
            a.businessNum as businessNum,
            ifnull(a.money,0) as businessMoney,
            b.name as settingName,
            a.status_id as settingId,
            0 as isEnd
        FROM (
            SELECT
                status_id,
                count(1) as businessNum,
                sum(money) as money
            FROM wk_crm_business
            where type_id = #{typeId} and status != 3 and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            GROUP by status_id
        ) as a JOIN wk_crm_flow_setting  as b on a.status_id = b.setting_id
        <if test="sortField != null and sortField != ''">
            order by ${sortField} ${sortType}
        </if>
    </select>

    <select id="addBusinessAnalyze" resultType="com.kakarote.bi.entity.VO.BiSellFunnelVO">
        select
            a.dateType as type,
            ifnull(b.businessNum,0) as businessNum,
            ifnull(b.money,0) as businessMoney
        from (
        select
            ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
            max(create_time) as lastTime
            from
            wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by
            dateType
            order by
            lastTime asc
        ) as a left join (
            SELECT
            ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as type,
            count(1) as businessNum,
            sum(money) as money
            FROM wk_crm_business
            where status!='3' and owner_user_id in
            <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by type
        ) as b on a.dateType = b.type  order by a.dateType asc
    </select>

    <select id="win" resultType="com.kakarote.bi.entity.VO.BiSellFunnelVO">
        select
            a.dateType as type,
            ifnull(b.businessNum,0) as businessNum,
            ifnull(b.winBusinessNum,0) as winBusinessNum
        from (
            select
                ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as dateType,
                max(create_time) as lastTime
            from
            wk_temp_date
            where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
            group by
            dateType
            order by
            lastTime asc
        ) as a left join (
        SELECT
            ${@com.kakarote.bi.common.enums.BiMySqlDateEnum@parseSql(timeEntity.dateFormat,'create_time')} as type,
            count(1) as businessNum,
            count(if(is_end=1,true,null)) as winBusinessNum
        FROM wk_crm_business
        where status!='3' and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        group by type
        ) as b on a.dateType = b.type order by a.dateType asc
    </select>

    <select id="queryEndBusiness" resultType="com.kakarote.bi.entity.VO.BiSellFunnelVO">
        SELECT
        is_end,
        count(1) as businessNum,
        ifnull(sum(money),0) as businessMoney,
        (case when is_end = 1 then '赢单'
                else  '输单' end) as settingName
        FROM wk_crm_business
        where  type_id = #{typeId} and status != 3 and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and is_end in (1,2)
        group by is_end
    </select>
</mapper>
