<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.bi.mapper.BiRankMapper">
    <select id="addressAnalyse" resultType="com.kakarote.bi.entity.VO.BiCustomerAnalyseVO">
        select
        a.city_name as type,
        b.customerNum as allCustomer,
        b.dealCustomer as dealCustomer
        from (
        SELECT city_name FROM wk_crm_area WHERE parent_id ='100000'
        ) as a left join
        (
        select
        substr(address,1,locate(address,',')-1) as type,
        count() as customerNum,
        countIf(deal_status==1) as dealCustomer
        from wk_crm_customer
        where status !='3' and notEmpty(address)
        and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        group by type
        ) as b on a.city_name = b.type
    </select>

    <select id="portrait" resultType="com.kakarote.bi.entity.VO.BiCustomerAnalyseVO">
        SELECT
        ifnull(value,'') as type,
        count() as allCustomer,
        countIf(b.deal_status == 1) as dealCustomer
        FROM
        wk_crm_customer_data as a
        join (
        select
        batch_id,
        deal_status
        from
        wk_crm_customer
        where status !='3' and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and status != '3'
        ) as b on a.batch_id =b.batch_id
        where field_id = #{fieldId}
        group by type
    </select>

    <select id="contractRanKing" resultType="com.kakarote.bi.entity.VO.BiRankMoneyVO">
        SELECT IFNULL(SUM(a.money), 0) as money,a.owner_user_id as userId
        FROM wk_crm_contract as a
        WHERE a.check_status in (1,10)
        and a.owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and a.order_date between #{timeEntity.beginDate} and #{timeEntity.endDate}
        GROUP BY a.owner_user_id
        ORDER BY money desc
    </select>
    <select id="receivablesRanKing" resultType="com.kakarote.bi.entity.VO.BiRankMoneyVO">
        SELECT IFNULL(SUM(a.money), 0) as money,a.owner_user_id as userId
        FROM wk_crm_receivables as a
        WHERE a.check_status in (1,10)
        and a.owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and a.return_time between #{timeEntity.beginDate,jdbcType=DATE} and #{timeEntity.endDate,jdbcType=DATE}
        GROUP BY a.owner_user_id
        ORDER BY money desc
    </select>
    <select id="contractCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        SELECT count() as count,
        a.owner_user_id as userId,
        IFNULL(SUM(a.money), 0) as money
        FROM wk_crm_contract as a
        WHERE a.check_status in (1,10)
        and a.owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and a.order_date between #{timeEntity.beginDate} and #{timeEntity.endDate}
        GROUP BY a.owner_user_id
        ORDER BY money desc
    </select>
    <select id="productCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        SELECT
        sum(num) as productCount,
        b.owner_user_id as userId
        FROM
        wk_crm_contract_product as a
        join wk_crm_contract as b on a.contract_id = b.contract_id
        WHERE
        b.order_date between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and b.check_status in (1,10)
        and b.owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by b.owner_user_id
        order by productCount desc
    </select>

    <select id="customerCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        SELECT
        count() as count,
        a.owner_user_id as userId
        FROM
        wk_crm_customer as a
        WHERE
        status !='3'
        and create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by owner_user_id
        order by count desc
    </select>
    <select id="contactsCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        SELECT
        count() as count,
        a.owner_user_id as userId
        FROM
        wk_crm_contacts as a
        WHERE
        create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and owner_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by owner_user_id
        order by count desc
    </select>
    <select id="customerRecordCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        select create_user_id as userId,
        groupBitmap(activity_type_id) as count
        from wk_crm_activity as a
        where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and create_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and activity_type in (1,2)
        and type='1' and activity_id = activity_type_id
        group by create_user_id
        order by count desc
    </select>
    <select id="recordCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        select create_user_id as userId,
        count() as count
        from wk_crm_activity as a
        where create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and create_user_id in
        <foreach collection="timeEntity.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and type='1' and activity_id = activity_type_id
        group by create_user_id
        order by count desc
    </select>
    <select id="travelCountRanKing" resultType="com.kakarote.bi.entity.VO.BiRankCountVO">
        select count(1) as `count`,a.create_user_id as user_id
        from wk_oa_examine_travel as a
         join (
        select a.examine_id
        from wk_oa_examine as a
        join (
        select examine_id from wk_examine where oa_type =3
        ) as b on toUInt64(a.category_id) = b.examine_id
        where a.examine_status = 1 and a.create_user_id in
         <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
        #{userId}
        </foreach>
        ) as b on a.examine_id = b.examine_id
        where a.start_time between #{timeEntity.beginDate} and
        #{timeEntity.endDate}
        group by user_id order by count desc
    </select>

    <select id="findExamineType" resultType="java.lang.Long">
        select examine_id from wk_examine where oa_type =#{type} and status =  '1' limit 1
    </select>

    <select id="contractRanKing1" resultType="com.alibaba.fastjson.JSONObject">
        SELECT IFNULL(SUM(cct.money), 0) as money,(TRUNCATE ( IFNULL((IFNULL(SUM(cct.money), 0)/SUM(achievement)),0)*100
        ,2)) as rate,
        cau.realname,cau.user_id,cau.img,cct.owner_user_id,cad.name as structureName
        FROM
        (select sum(money) as money, owner_user_id from wk_crm_contract where check_status in ('1','10')
        and owner_user_id in
        <foreach collection="data.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and order_date between #{data.beginDate} and #{data.endDate} group by owner_user_id) as cct
        LEFT JOIN wk_admin_user as cau on cau.user_id = cct.owner_user_id
        left join wk_admin_dept as cad on cad.dept_id = cau.dept_id
        left join ( select sum(x.achievement) as achievement,x.user_id from (
        <foreach collection="monthMap" item="x" separator="union all">
            select ${x.month} as achievement,obj_id as user_id from wk_crm_achievement where obj_id in
            <foreach collection="data.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and year = #{x.year} and type = 3 and status = 1
        </foreach>
        ) as x group by user_id) as y ON y.user_id = cct.owner_user_id
        where 1=1
        GROUP BY cct.owner_user_id
        ORDER BY money DESC
    </select>

    <select id="receivablesRanKing1" resultType="com.alibaba.fastjson.JSONObject">
        SELECT IFNULL(SUM(cct.money), 0) as money,(TRUNCATE ( IFNULL((IFNULL(SUM(cct.money), 0)/SUM(achievement)),0)*100
        ,2)) as rate,
        cau.realname,cau.user_id,cau.img,cad.name as structureName
        FROM
        (select sum(money) as money, owner_user_id from wk_crm_receivables where check_status in ('1','10')
        and owner_user_id in
        <foreach collection="data.userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and return_time between #{z} and #{timeEntity.endDate,jdbcType=DATE} group by owner_user_id) as cct
        LEFT JOIN wk_admin_user as cau on cau.user_id = cct.owner_user_id
        left join wk_admin_dept as cad on cad.dept_id = cau.dept_id
        left join ( select sum(x.achievement) as achievement,x.user_id from (
        <foreach collection="monthMap" item="x" separator="union all">
            select ${x.month} as achievement,obj_id as user_id from wk_crm_achievement where obj_id in
            <foreach collection="data.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and year = #{x.year} and type = 3 and status = 2
        </foreach>
        ) as x group by user_id) as y ON y.user_id = cct.owner_user_id
        where 1=1
        GROUP BY cct.owner_user_id
        ORDER BY money DESC
    </select>

    <select id="queryUserCalaulateRate" resultType="com.alibaba.fastjson.JSONObject">
        select a.user_id,(
        <foreach collection="map.queryField" item="month" separator="+">
            sum(${month})
        </foreach>
        ) as money from (
        select obj_id as user_id,
        <foreach collection="map.queryField" item="month" separator=",">
            ${month}
        </foreach>
        from wk_crm_achievement
        where toUInt64(year) = #{map.startYear}
        and `type` = 3
        and status = #{map.module}
        and obj_id in
        <foreach collection="userIds" item="objId" separator="," open="(" close=")">
            #{objId}
        </foreach>

        <if test="map.endYear != null">
            union all
            select obj_id as user_id,
            <foreach collection="map.endMonthField" item="month" separator=",">
                ${month}
            </foreach>
            from wk_crm_achievement
            where toUInt64(year) = #{map.endYear}
            and `type` = 3
            and status = #{map.module}
            and obj_id in
            <foreach collection="userIds" item="objId" separator="," open="(" close=")">
                #{objId}
            </foreach>
        </if>
        ) as a group by a.user_id

    </select>
</mapper>
