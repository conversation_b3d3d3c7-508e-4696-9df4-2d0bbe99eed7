<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.bi.mapper.BiWorkMapper">
    <select id="logStatistics" resultType="com.kakarote.bi.entity.VO.BiWorkLogStatisticsVO">
        select wol.create_user_id as user_id,
               IFNULL(sum(commentCount),0) as commentCount ,
               count(1) as count from wk_oa_log as wol
        left join
        (
        select type_id, count(distinct type_id) as commentCount
        from wk_work_task_comment where type = '2' and type_id in(
        select log_id from wk_oa_log as temp_wol
        where temp_wol.create_time
        between #{timeEntity.beginDate}
        and #{timeEntity.endDate} and temp_wol.create_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        )
        group by type_id
        ) as temp on wol.log_id = temp.type_id
        where wol.create_time
        between #{timeEntity.beginDate}
        and #{timeEntity.endDate} and wol.create_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by user_id
    </select>

    <select id="queryExamineCategory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT category_id, title, type
        FROM wk_oa_examine_category
        WHERE is_deleted = 0
    </select>

    <select id="examineStatistics" resultType="com.alibaba.fastjson.JSONObject">
        select b.create_user_id as user_id,
        <foreach collection="examineIds" item="examineId" separator=",">
            count(if(b.category_id=${examineId},true,null)) as count_${examineId}
        </foreach>
        from wk_oa_examine as b where b.create_time between #{timeEntity.beginDate} and #{timeEntity.endDate}
        and b.create_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and category_id in
        <foreach collection="examineIds" item="examineId" open="(" separator="," close=")">
            #{examineId}
        </foreach>
        and b.examine_status = 1
        GROUP BY user_id
    </select>

    <select id="myInitiate" resultType="com.kakarote.core.feign.oa.entity.ExamineVO">
        select a.*
        from wk_oa_examine a
        where  a.examine_status = 1
        and a.create_user_id = #{data.userId}
          and a.category_id = #{data.categoryId}
        order by a.create_time desc
    </select>

    <select id="queryExamineCount" resultType="com.alibaba.fastjson.JSONObject">
        SELECT IFNULL(SUM(money), 0) as money, IFNULL(SUM(duration), 0) as duration
        FROM wk_oa_examine as a
        WHERE  a.category_id = #{data.categoryId}
          and a.create_user_id = #{data.userId}
          and a.examine_status =1
            limit 1
    </select>

    <select id="queryOaComment" resultType="com.kakarote.core.feign.oa.entity.OaLogVO">
        select wol.log_id,wol.category_id,wol.title,wol.content,wol.tomorrow,wol.question,wol.create_user_id,wol.create_time from wk_oa_log as wol
        join
        (
        select type_id
        from wk_work_task_comment where type = '2' and type_id in(
        select log_id from wk_oa_log as temp_wol
        where temp_wol.create_time
        between #{timeEntity.beginDate}
        and #{timeEntity.endDate} and temp_wol.create_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        )
        group by type_id
        ) as temp on wol.log_id = temp.type_id
        where wol.create_time
        between #{timeEntity.beginDate}
        and #{timeEntity.endDate} and wol.create_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        limit ${page},${size}
    </select>

    <select id="queryOaNoComment" resultType="com.kakarote.core.feign.oa.entity.OaLogVO">
        select wol.log_id,wol.category_id,wol.title,wol.content,wol.tomorrow,wol.question,wol.create_time,wol.create_user_id from wk_oa_log as wol
        where wol.create_time
        between #{timeEntity.beginDate}
        and #{timeEntity.endDate} and wol.create_user_id in
        <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and wol.log_id not in
        (
            select type_id
            from wk_work_task_comment where type = '2' and type_id in(
                select log_id from wk_oa_log as temp_wol
                where temp_wol.create_time
                between #{timeEntity.beginDate}
                and #{timeEntity.endDate} and temp_wol.create_user_id in
                <foreach collection="timeEntity.userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
        )
        group by type_id
        )
        limit ${page},${size}
    </select>
</mapper>
