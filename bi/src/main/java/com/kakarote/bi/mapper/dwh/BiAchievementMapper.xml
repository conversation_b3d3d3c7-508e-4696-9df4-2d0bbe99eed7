<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.bi.mapper.BiAchievementMapper">

    <select id="taskCompleteStatistics" resultType="com.alibaba.fastjson.JSONObject">
        select obj_id,
        january,
        february,
        march,
        april,
        may,
        june,
        july,
        august,
        september,
        october,
        november,
        december
        from wk_crm_achievement
        where toUInt64(year) = #{year} and `type` = #{data.type} and `status` = #{data.module}
        <choose>
            <when test="data.objId != null">
                and obj_id = #{data.objId}
            </when>
            <otherwise>
                and obj_id in
                <if test="data.userList != null and data.userList.size > 0">
                    <foreach collection="data.userList" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                </if>
                <if test="data.deptList != null and data.deptList.size > 0">
                    <foreach collection="data.deptList" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="taskCompleteStatisticsRate" resultType="com.kakarote.bi.entity.VO.BiMonthReceivedMoneyVO">
        select td_month.create_time as month,tem.money as receivedMoney,tem.owner_user_id from (
        select toMonth(create_time) as create_time from wk_temp_date as td where td.create_time between #{data.startDate}
        and
        #{data.endDate} group by create_time
        ) as td_month left join (
        select
        <if test="data.module == 1">
            toMonth(order_date)
        </if>
        <if test="data.module == 2">
            toMonth(return_time)
        </if>
        as obj_time , owner_user_id,sum(money) as money
        from
        <if test="data.module == 1">
            wk_crm_contract
        </if>
        <if test="data.module == 2">
            wk_crm_receivables
        </if>
        as obj where obj.
        <if test="data.module == 1">
            order_date between #{data.startDate} and #{data.endDate}
        </if>
        <if test="data.module == 2">
            return_time between #{data.startDate,jdbcType=DATE} and #{data.endDate,jdbcType=DATE}
        </if>
         and owner_user_id in
        <foreach collection="data.userList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by obj_time,owner_user_id
        ) as tem on td_month.create_time = tem.obj_time
    </select>
    <!--
        select toMonth(td.create_time), received_money,tem.owner_user_id
        from wk_temp_date as td
        left join (
        select sum(obj.received_money) as received_money, obj.owner_user_id, toMonth(obj.create_time) as create_time
        from
        <if test="data.status == 1">
            wk_crm_contract
        </if>
        <if test="data.status == 2">
            wk_crm_receivables
        </if>
        as obj
        where obj.create_time between #{data.startDate} and #{data.endDate} and obj.owner_user_id in
        <foreach collection="data.userList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by obj.create_time, obj.owner_user_id
        ) tem on tem.create_time = toMonth(td.create_time) where td.create_time between #{data.startDate} and
        #{data.endDate}-->
</mapper>
