package com.kakarote.bi.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.VO.BiCustomerAnalyseVO;
import com.kakarote.bi.entity.VO.BiRankCountVO;
import com.kakarote.bi.entity.VO.BiRankMoneyVO;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface BiRankMapper {

    /**
     * 住址分析
     *
     * @param timeEntity
     * @return bi客户画像分析
     */
    @SqlParser(filter = true)
    List<BiCustomerAnalyseVO> addressAnalyse(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * portrait
     *
     * @param timeEntity:BiTimeEntity
     * @param fieldId:字段id
     * @return bi客户画像分析
     */
    @SqlParser(filter = true)
    List<BiCustomerAnalyseVO> portrait(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity, @Param("fieldId") Long fieldId);

    /**
     * 排名合同
     *
     * @param timeEntity:
     * @return 排行榜金额排行VO
     */
    @SqlParser(filter = true)
    List<BiRankMoneyVO> contractRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名票据
     *
     * @param timeEntity:
     * @return 排行榜金额排行VO
     */
    @SqlParser(filter = true)
    List<BiRankMoneyVO> receivablesRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名合同数量
     *
     * @param timeEntity:
     * @return 排行榜数量排行VO
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> contractCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名产品数量
     *
     * @param timeEntity:
     * @return 排行榜数量排行VO
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> productCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名客户数量
     *
     * @param timeEntity:
     * @return 排行榜数量排行VO
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> customerCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名联系人数量
     *
     * @param timeEntity:
     * @return 排行榜数量排行VO
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> contactsCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名客户记录数量
     *
     * @param timeEntity:
     * @return 排行榜数量排行VO
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> customerRecordCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名记录数量
     *
     * @param timeEntity:
     * @return 排行榜数量排行VO
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> recordCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 排名出行数量
     *
     * @param timeEntity:
     * @return
     */
    @SqlParser(filter = true)
    List<BiRankCountVO> travelCountRanKing(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity);

    /**
     * 根据类型查询OA审批
     * @param type
     * @return
     */
    Long findExamineType(@Param("type") Integer type);

    /**
     * 合同排名1
     *
     * @param toMap:
     * @param monthMap:
     * @return data
     */
    @SqlParser(filter = true)
    List<JSONObject> contractRanKing1(@Param("data") Map<String, Object> toMap, @Param("monthMap") List<Map<String, String>> monthMap);

    /**
     * 应收账款排名
     *
     * @param toMap
     * @param monthMap
     * @return data
     */
    @SqlParser(filter = true)
    List<JSONObject> receivablesRanKing1(@Param("data") Map<String, Object> toMap, @Param("monthMap") List<Map<String, String>> monthMap);

    /**
     * 》》》》》》
     *
     * @param map
     * @param userIds:用户id
     * @return
     */
    @MapKey("userId")
    Map<Long, JSONObject> queryUserCalaulateRate(@Param("map") Map<String, Object> map, @Param("userIds") List<Long> userIds);
}
