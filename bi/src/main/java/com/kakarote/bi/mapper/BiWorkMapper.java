package com.kakarote.bi.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.bi.entity.BO.BiExamineInfoBO;
import com.kakarote.bi.entity.VO.BiWorkLogStatisticsVO;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.feign.oa.entity.ExamineVO;
import com.kakarote.core.feign.oa.entity.OaLogVO;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BiWorkMapper {

    /**
     * 记录统计
     *
     * @param timeEntity
     * @param biParams
     * @return 日志分析VO
     */
    @SqlParser(filter = true)
    List<BiWorkLogStatisticsVO> logStatistics(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity,
                                              @Param("data") BiEntityParams biParams);

    /**
     * 查询审批分类
     *
     * @param
     * @return
     */
    public List<JSONObject> queryExamineCategory();

    /**
     * 审批统计
     *
     * @param timeEntity:
     * @param biParams:
     * @param examineIds:
     * @return
     */
    @SqlParser(filter = true)
    public List<JSONObject> examineStatistics(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity, @Param("data") BiEntityParams biParams,
                                              @Param("examineIds") List<String> examineIds);

    /**
     * myInitiate
     *
     * @param timeEntity
     * @param biEntityParams
     * @return
     */
    @SqlParser(filter = true)
    List<ExamineVO> myInitiate(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity, @Param("data") BiExamineInfoBO biEntityParams);

    /**
     * 查询审批数量
     *
     * @param timeEntity
     * @param biEntityParams
     * @return
     */
    @SqlParser(filter = true)
    JSONObject queryExamineCount(@Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity, @Param("data") BiExamineInfoBO biEntityParams);

    /**
     * 查询评论日志列表
     * @param timeEntity
     * @param biEntityParams
     * @return
     */
    @SqlParser(filter = true)
    List<OaLogVO> queryOaComment(@Param("timeEntity")BiParamsUtil.BiTimeEntity timeEntity,
                                 @Param("data")BiExamineInfoBO biEntityParams, @Param("page") Integer page, @Param("size") Integer size);

    /**
     * 查询没有评论日志列表
     * @param timeEntity
     * @param biEntityParams
     * @return
     */
    @SqlParser(filter = true)
    List<OaLogVO> queryOaNoComment(@Param("timeEntity")BiParamsUtil.BiTimeEntity timeEntity,
                                          @Param("data")BiExamineInfoBO biEntityParams,@Param("userId") Long userId,
                                          @Param("page") Integer page, @Param("size") Integer size);

}
