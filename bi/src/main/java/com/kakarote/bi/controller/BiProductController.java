package com.kakarote.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.kakarote.bi.entity.VO.BiProductStatisticsVO;
import com.kakarote.bi.service.BiProductService;
import com.kakarote.core.common.Result;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.ExcelParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/biProduct")
@Api(tags = "商业智能产品分析接口")
public class BiProductController {

    @Autowired
    private BiProductService productService;

    @PostMapping("/productStatistics")
    @ApiOperation("产品销售情况统计")
    public Result<BasePage<BiProductStatisticsVO>> productStatistics(@RequestBody BiEntityParams biParams) {
        biParams.setPageType(1);
        BasePage<BiProductStatisticsVO> objectList = productService.queryProductSell(biParams);
        return Result.ok(objectList);
    }

    @PostMapping("/productStatisticsExport")
    @ApiOperation("产品销售情况统计导出")
    public void productStatisticsExport(@RequestBody BiEntityParams biParams) {
        biParams.setPageType(0);
        BasePage<BiProductStatisticsVO> objectList = productService.queryProductSell(biParams);
        List<Map<String, Object>> list = objectList.getList().stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("categoryName", "产品分类"));
        dataList.add(ExcelParseUtil.toEntity("productName", "产品名称"));
        dataList.add(ExcelParseUtil.toEntity("contractNum", "合同数"));
        dataList.add(ExcelParseUtil.toEntity("num", "数量合计"));
        dataList.add(ExcelParseUtil.toEntity("total", "订单产品小计"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "产品销售情况";
            }
        }, dataList);
    }


    @ApiOperation("产品分类销量分析")
    @PostMapping("/contractProductRanKing")
    public Result<List<BiProductStatisticsVO>> contractProductRanKing(@RequestBody BiEntityParams biParams) {
        List<BiProductStatisticsVO> objectList = productService.contractProductRanKing(biParams);
        return Result.ok(objectList);
    }
}
