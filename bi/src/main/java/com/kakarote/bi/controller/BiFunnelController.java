package com.kakarote.bi.controller;

import com.kakarote.bi.entity.BO.BiSellFunnelBO;
import com.kakarote.bi.entity.VO.BiSellFunnelVO;
import com.kakarote.bi.service.BiFunnelService;
import com.kakarote.core.common.Result;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/biFunnel")
@Api(tags = "销售漏斗分析")

public class BiFunnelController {

    @Autowired
    private BiFunnelService biFunnelService;


    @ApiOperation("销售漏斗")
    @PostMapping("/sellFunnel")
    public Result<List<BiSellFunnelVO>> sellFunnel(@RequestBody BiSellFunnelBO biParams) {
        List<BiSellFunnelVO> objectList = biFunnelService.sellFunnel(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("新增项目分析图")
    @PostMapping("/addBusinessAnalyze")
    public Result<List<BiSellFunnelVO>> addBusinessAnalyze(@RequestBody BiEntityParams biParams) {
        List<BiSellFunnelVO> sellFunnelVOS = biFunnelService.addBusinessAnalyze(biParams);
        return Result.ok(sellFunnelVOS);
    }

    @ApiOperation("项目转化率分析")
    @PostMapping("/win")
    public Result<List<BiSellFunnelVO>> win(@RequestBody BiEntityParams biParams) {
        List<BiSellFunnelVO> objectList = biFunnelService.win(biParams);
        return Result.ok(objectList);
    }
}
