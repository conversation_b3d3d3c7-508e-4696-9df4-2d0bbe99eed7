package com.kakarote.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.kakarote.bi.entity.VO.BiEmployeeStatsVO;
import com.kakarote.bi.entity.VO.BiTotalContractVO;
import com.kakarote.bi.entity.VO.BiTotalInvoiceVO;
import com.kakarote.bi.service.BiEmployeeService;
import com.kakarote.core.common.Result;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.utils.ExcelParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/biEmployee")
@Api(tags = "员工业绩分析模块")
public class BiEmployeeController {

    @Autowired
    private BiEmployeeService biEmployeeService;


    @ApiOperation("合同数量分析")
    @PostMapping("/contractNumStats")
    public Result<List<BiEmployeeStatsVO>> contractNumStats(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeStatsVO> employeeStatsVOS = biEmployeeService.contractNumStats(biParams, 1);
        return Result.ok(employeeStatsVOS);
    }

    @ApiOperation("合同数量分析导出")
    @PostMapping("/contractNumStatsExport")
    public void contractNumStatsExport(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeStatsVO> employeeStatsVOS = biEmployeeService.contractNumStats(biParams, 1);
        List<Map<String, Object>> recordList = employeeStatsVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("type", "日期"));
        dataList.add(ExcelParseUtil.toEntity("monthNum", "当月合同数量"));
        dataList.add(ExcelParseUtil.toEntity("pervMonthNum", "环比增长"));
        dataList.add(ExcelParseUtil.toEntity("prevYearNum", "同比增长"));
        ExcelParseUtil.exportExcel(recordList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "合同数量分析";
            }
        }, dataList);
    }

    @ApiOperation("合同金额分析")
    @PostMapping("/contractMoneyStats")
    public Result<List<BiEmployeeStatsVO>> contractMoneyStats(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeStatsVO> employeeStatsVOS = biEmployeeService.contractNumStats(biParams, 2);
        return Result.ok(employeeStatsVOS);
    }

    @ApiOperation("合同金额分析导出")
    @PostMapping("/contractMoneyStatsExport")
    public void contractMoneyStatsExport(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeStatsVO> employeeStatsVOS = biEmployeeService.contractNumStats(biParams, 2);
        List<Map<String, Object>> recordList = employeeStatsVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("type", "日期"));
        dataList.add(ExcelParseUtil.toEntity("monthNum", "当月合同金额"));
        dataList.add(ExcelParseUtil.toEntity("pervMonthNum", "环比增长"));
        dataList.add(ExcelParseUtil.toEntity("prevYearNum", "同比增长"));
        ExcelParseUtil.exportExcel(recordList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "合同金额分析";
            }
        }, dataList);
    }

    @ApiOperation("回款金额分析")
    @PostMapping("/receivablesMoneyStats")
    public Result<List<BiEmployeeStatsVO>> receivablesMoneyStats(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeStatsVO> employeeStatsVOS = biEmployeeService.contractNumStats(biParams, 3);
        return Result.ok(employeeStatsVOS);
    }

    @ApiOperation("回款金额分析导出")
    @PostMapping("/receivablesMoneyStatsExport")
    public void receivablesMoneyStatsExport(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeStatsVO> employeeStatsVOS = biEmployeeService.contractNumStats(biParams, 3);
        List<Map<String, Object>> recordList = employeeStatsVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("type", "日期"));
        dataList.add(ExcelParseUtil.toEntity("monthNum", "当月回款金额"));
        dataList.add(ExcelParseUtil.toEntity("pervMonthNum", "环比增长"));
        dataList.add(ExcelParseUtil.toEntity("prevYearNum", "同比增长"));
        ExcelParseUtil.exportExcel(recordList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "回款金额分析";
            }
        }, dataList);
    }


    @ApiOperation("合同汇总表")
    @PostMapping("/totalContract")
    public Result<List<BiTotalContractVO>> totalContract(@RequestBody BiEntityParams biParams) {
        List<BiTotalContractVO> biTotalContractVOS = biEmployeeService.totalContract(biParams);
        return Result.ok(biTotalContractVOS);
    }

    @ApiOperation("合同汇总表导出")
    @PostMapping("/totalContractExport")
    public void totalContractExport(@RequestBody BiEntityParams biParams) {
        List<BiTotalContractVO> biTotalContractVOS = biEmployeeService.totalContract(biParams);
        List<Map<String, Object>> recordList = biTotalContractVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("type", "日期"));
        dataList.add(ExcelParseUtil.toEntity("contractNum", "签约合同数（个）"));
        dataList.add(ExcelParseUtil.toEntity("contractMoney", "签约合同金额（元）"));
        dataList.add(ExcelParseUtil.toEntity("receivablesMoney", "回款金额（元）"));
        ExcelParseUtil.exportExcel(recordList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "合同汇总";
            }
        }, dataList);
    }

    @ApiOperation("发票统计")
    @PostMapping("/invoiceStats")
    public Result<List<BiTotalInvoiceVO>> invoiceStats(@RequestBody BiEntityParams biParams) {
        List<BiTotalInvoiceVO> totalInvoiceVOS = biEmployeeService.invoiceStats(biParams);
        return Result.ok(totalInvoiceVOS);
    }

    @ApiOperation("发票统计导出")
    @PostMapping("/invoiceStatsExport")
    public void invoiceStatsExport(@RequestBody BiEntityParams biParams) {
        List<BiTotalInvoiceVO> totalInvoiceVOS = biEmployeeService.invoiceStats(biParams);
        List<Map<String, Object>> recordList = totalInvoiceVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("type", "日期"));
        dataList.add(ExcelParseUtil.toEntity("receivablesMoney", "回款金额（元）"));
        dataList.add(ExcelParseUtil.toEntity("invoiceMoney", "开票金额（元）"));
        dataList.add(ExcelParseUtil.toEntity("receivablesNoInvoice", "已回款未开票（元）"));
        dataList.add(ExcelParseUtil.toEntity("invoiceNoReceivables", "已开票未回款（元）"));
        ExcelParseUtil.exportExcel(recordList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "发票统计";
            }
        }, dataList);
    }
}
