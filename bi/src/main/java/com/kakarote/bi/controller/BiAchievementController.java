package com.kakarote.bi.controller;

import com.kakarote.bi.entity.BO.BiAchievementBO;
import com.kakarote.bi.entity.VO.BiAchievementVO;
import com.kakarote.bi.service.BiAchievementService;
import com.kakarote.core.common.Result;
import com.kakarote.core.utils.ExcelParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/biAchievement")
@Api(tags = "商业智能业绩目标接口")
public class BiAchievementController {

    @Autowired
    private BiAchievementService biAchievementService;


    @PostMapping("/taskCompleteStatistics")
    @ApiOperation("获取商业智能业绩目标完成情况")
    public Result< List<BiAchievementVO>> taskCompleteStatistics(@RequestBody BiAchievementBO biAchievementBO) {
        List<BiAchievementVO> objectList = biAchievementService.taskCompleteStatistics(biAchievementBO);
        return Result.ok(objectList);
    }

    @PostMapping("/taskCompleteStatisticsExport")
    @ApiOperation("获取商业智能业绩目标完成情况导出")
    public void taskCompleteStatisticsExport(@RequestBody BiAchievementBO biAchievementBO) {
        List<Map<String, Object>> list = biAchievementService.taskCompleteStatisticsExport(biAchievementBO);
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("name", "名称"));
        dataList.add(ExcelParseUtil.toEntity("monthName", "月份"));
        dataList.add(ExcelParseUtil.toEntity("achievement", "目标"));
        dataList.add(ExcelParseUtil.toEntity("money", "完成"));
        dataList.add(ExcelParseUtil.toEntity("rate", "完成率"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "业绩目标完成情况";
            }
        }, dataList);
    }
}
