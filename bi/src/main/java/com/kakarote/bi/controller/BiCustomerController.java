package com.kakarote.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.bi.common.enums.BiMenuEnum;
import com.kakarote.bi.entity.BO.BICrmInfoBO;
import com.kakarote.bi.entity.BO.CrmFieldBO;
import com.kakarote.bi.entity.VO.*;
import com.kakarote.bi.mapper.BiCustomerMapper;
import com.kakarote.bi.service.BiCustomerService;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.Result;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.core.utils.ExcelParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("biCustomer")
@Api(tags = "商业智能客户分析模块")
public class BiCustomerController {

    @Autowired
    private BiCustomerService biCustomerService;

    @Autowired
    private BiCustomerMapper biCustomerMapper;

    @ApiOperation("客户总量分析图")
    @PostMapping("/totalCustomerStats")
    public Result<List<BiTotalCustomerVO>> totalCustomerStats(@RequestBody BiEntityParams biParams) {
        List<BiTotalCustomerVO> totalCustomerVOS = biCustomerService.totalCustomerStats(biParams);
        return Result.ok(totalCustomerVOS);
    }

    @ApiOperation("客户总量分析表")
    @PostMapping("/totalCustomerTable")
    public Result<List<BiTotalCustomerVO>> totalCustomerTable(@RequestBody BiEntityParams biParams) {
        List<BiTotalCustomerVO> totalCustomerVOS = biCustomerService.totalCustomerTable(biParams);
        return Result.ok(totalCustomerVOS);
    }

    @ApiOperation("客户总量分析图导出")
    @PostMapping("/totalCustomerTableExport")
    public void totalCustomerTableExport(@RequestBody BiEntityParams biParams) {
        List<BiTotalCustomerVO> totalCustomerVOS = biCustomerService.totalCustomerTable(biParams);
        List<Map<String, Object>> mapList = totalCustomerVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("realname", "员工姓名"));
        dataList.add(ExcelParseUtil.toEntity("customerNum", "新增客户数"));
        dataList.add(ExcelParseUtil.toEntity("dealCustomerNum", "成交客户数"));
        dataList.add(ExcelParseUtil.toEntity("dealCustomerRate", "客户成交率(%)"));
        dataList.add(ExcelParseUtil.toEntity("contractMoney", "合同总金额"));
        dataList.add(ExcelParseUtil.toEntity("receivablesMoney", "回款金额"));
        ExcelParseUtil.exportExcel(mapList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "客户总量分析";
            }
        }, dataList);
    }

    @ApiOperation("客户跟进次数分析")
    @PostMapping("/customerRecordStats")
    public Result<List<BiRecordVO>> customerRecordStats(@RequestBody BiEntityParams biParams) {
        List<BiRecordVO> objectList = biCustomerService.customerRecordStats(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("客户跟进次数分析表")
    @PostMapping("/customerRecordInfo")
    public Result<List<BiRecordVO>> customerRecordInfo(@RequestBody BiEntityParams biParams) {
        List<BiRecordVO> objectList = biCustomerService.customerRecordInfo(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("客户跟进次数分析表导出")
    @PostMapping("/customerRecordInfoExport")
    public void customerRecordInfoExport(@RequestBody BiEntityParams biParams) {
        List<BiRecordVO> objectList = biCustomerService.customerRecordInfo(biParams);
        List<Map<String, Object>> list = objectList.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("realname", "员工姓名"));
        dataList.add(ExcelParseUtil.toEntity("recordNum", "跟进次数"));
        dataList.add(ExcelParseUtil.toEntity("outSignRecordNum", "外勤签到次数"));
        dataList.add(ExcelParseUtil.toEntity("customerNum", "跟进客户数"));
        dataList.add(ExcelParseUtil.toEntity("recordNumRate", "客户跟进占比"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "客户跟进次数分析";
            }
        }, dataList);
    }


    @ApiOperation("客户转化率分析")
    @PostMapping("/customerConversionStats")
    public Result<List<BiTotalCustomerVO>> customerConversionStats(@RequestBody BiEntityParams biParams) {
        List<BiTotalCustomerVO> customerVOS = biCustomerService.customerConversionStats(biParams);
        return Result.ok(customerVOS);
    }

    @ApiOperation("公海客户分析图")
    @PostMapping("/poolStats")
    public Result<List<BiCustomerPoolVO>> poolStats(@RequestBody BiEntityParams biParams) {
        List<BiCustomerPoolVO> customerPoolVOS = biCustomerService.poolStats(biParams);
        return Result.ok(customerPoolVOS);
    }

    @ApiOperation("公海客户分析表")
    @PostMapping("/poolTable")
    public Result<List<BiCustomerPoolVO>> poolTable(@RequestBody BiEntityParams biParams) {
        List<BiCustomerPoolVO> customerPoolVOS = biCustomerService.poolTable(biParams);
        return Result.ok(customerPoolVOS);
    }

    @ApiOperation("公海客户分析表导出")
    @PostMapping("/poolTableExport")
    public void poolTableExport(@RequestBody BiEntityParams biParams) {
        List<BiCustomerPoolVO> customerPoolVOS = biCustomerService.poolTable(biParams);
        List<Map<String, Object>> list = customerPoolVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("realname", "姓名"));
        dataList.add(ExcelParseUtil.toEntity("deptName", "部门"));
        dataList.add(ExcelParseUtil.toEntity("receiveNum", "公海池领取客户数"));
        dataList.add(ExcelParseUtil.toEntity("putInNum", "进入公海客户数"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "公海客户分析";
            }
        }, dataList);
    }


    @ApiOperation("员工客户成交周期图")
    @PostMapping("/employeeCycle")
    public Result<List<BiEmployeeCycleVO>> employeeCycle(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> employeeCycleVOS = biCustomerService.employeeCycle(biParams);
        return Result.ok(employeeCycleVOS);
    }

    @ApiOperation("员工客户成交周期表")
    @PostMapping("/employeeCycleInfo")
    public Result<List<BiEmployeeCycleVO>> employeeCycleInfo(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> employeeCycleVOS = biCustomerService.employeeCycleInfo(biParams);
        return Result.ok(employeeCycleVOS);
    }

    @ApiOperation("员工客户成交周期表导出")
    @PostMapping("/employeeCycleInfoExport")
    public void employeeCycleInfoExport(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> employeeCycleVOS = biCustomerService.employeeCycleInfo(biParams);
        List<Map<String, Object>> list = employeeCycleVOS.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("realname", "姓名"));
        dataList.add(ExcelParseUtil.toEntity("cycle", "成交周期（天）"));
        dataList.add(ExcelParseUtil.toEntity("customerNum", "成交客户数"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "员工客户成交周期分析";
            }
        }, dataList);
    }


    @ApiOperation("地区成交周期图")
    @PostMapping("/districtCycle")
    public Result<List<BiEmployeeCycleVO>> districtCycle(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> districtCycle = biCustomerService.districtCycle(biParams);
        return Result.ok(districtCycle);
    }

    @ApiOperation("地区成交周期图导出")
    @PostMapping("/districtCycleExport")
    public void districtCycleExport(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> districtCycle = biCustomerService.districtCycle(biParams);
        List<Map<String, Object>> list = districtCycle.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("type", "地区"));
        dataList.add(ExcelParseUtil.toEntity("cycle", "成交周期（天）"));
        dataList.add(ExcelParseUtil.toEntity("customerNum", "成交客户数"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "地区成交周期分析";
            }
        }, dataList);
    }

    @ApiOperation("产品成交周期")
    @PostMapping("/productCycle")
    public Result<List<BiEmployeeCycleVO>> productCycle(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> productCycle = biCustomerService.productCycle(biParams);
        return Result.ok(productCycle);
    }

    @ApiOperation("产品成交周期导出")
    @PostMapping("/productCycleExport")
    public void productCycleExport(@RequestBody BiEntityParams biParams) {
        List<BiEmployeeCycleVO> productCycle = biCustomerService.productCycle(biParams);
        List<Map<String, Object>> list = productCycle.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        dataList.add(ExcelParseUtil.toEntity("productName", "产品名称"));
        dataList.add(ExcelParseUtil.toEntity("cycle", "成交周期（天）"));
        dataList.add(ExcelParseUtil.toEntity("customerNum", "成交客户数"));
        ExcelParseUtil.exportExcel(list, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "产品成交周期分析";
            }
        }, dataList);
    }

    @ApiOperation("客户满意度分析")
    @PostMapping("/customerSatisfactionTable")
    public Result<List<JSONObject>> customerSatisfactionTable(@RequestBody BiEntityParams biParams) {
        List<JSONObject> objectList = biCustomerService.customerSatisfactionTable(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("客户满意度分析导出")
    @PostMapping("/customerSatisfactionExport")
    public void customerSatisfactionExport(@RequestBody BiEntityParams biParams) {
        List<JSONObject> objectList = biCustomerService.customerSatisfactionTable(biParams);
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        CrmFieldBO satisficing = biCustomerMapper.queryCrmFieldInfo(17, "satisficing");
        List<String> optionList = StrUtil.splitTrim(satisficing.getOptions(), Const.SEPARATOR);
        dataList.add(ExcelParseUtil.toEntity("realname", "员工姓名"));
        dataList.add(ExcelParseUtil.toEntity("visitContractNum", "回访合同总数"));
        optionList.forEach(option -> dataList.add(ExcelParseUtil.toEntity(option, option)));
        ExcelParseUtil.exportExcel(objectList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "客户满意度分析";
            }
        }, dataList);
    }

    @ApiOperation("产品满意度分析")
    @PostMapping("/productSatisfactionTable")
    public Result<List<JSONObject>> productSatisfactionTable(@RequestBody BiEntityParams biParams) {
        List<JSONObject> objectList = biCustomerService.productSatisfactionTable(biParams);
        return Result.ok(objectList);
    }

    @ApiOperation("产品满意度分析导出")
    @PostMapping("/productSatisfactionExport")
    public void productSatisfactionExport(@RequestBody BiEntityParams biParams) {
        List<JSONObject> objectList = biCustomerService.productSatisfactionTable(biParams);
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        CrmFieldBO satisficing = biCustomerMapper.queryCrmFieldInfo(17, "satisficing");
        List<String> optionList = StrUtil.splitTrim(satisficing.getOptions(), Const.SEPARATOR);
        dataList.add(ExcelParseUtil.toEntity("productName", "产品名称"));
        dataList.add(ExcelParseUtil.toEntity("visitNum", "回访合同总数"));
        optionList.forEach(option -> dataList.add(ExcelParseUtil.toEntity(option, option)));
        ExcelParseUtil.exportExcel(objectList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "产品满意度分析";
            }
        }, dataList);
    }

    @PostMapping("queryContactsByCustomerSatisfaction")
    @ApiOperation("查看回访客户满意度关联数据--供crm调用")
    public Result<List<String>> queryContactsByCustomerSatisfaction(@RequestBody @ApiParam("BiSearchBO类的转换") Map<String, Object> map){
        CrmFieldBO satisficing = biCustomerMapper.queryCrmFieldInfo(17, "satisficing");
        map.put("fieldId", satisficing.getFieldId());
        BiEntityParams biEntityParams = BeanUtil.copyProperties(map, BiEntityParams.class);
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biEntityParams, BiMenuEnum.CUSTOMER_LIST.getMenuId());
        List<String> list = biCustomerMapper.queryContactsByCustomerSatisfaction(biTimeEntity,map);
        return Result.ok(list);
    }

    @PostMapping("queryCallAnalysis")
    @ApiOperation("查看员工通话统计分析")
    public Result<BasePage<BiEmployeeCallVO>> queryCallAnalysis(@RequestBody BiEntityParams biParams){
        BasePage<BiEmployeeCallVO> basePage = biCustomerService.queryCallAnalysis(biParams);
        return Result.ok(basePage);
    }

    @PostMapping("queryCallList")
    @ApiOperation("查看员工通话记录")
    public Result<BasePage<BiCallListVO>> queryCallList(@RequestBody BICrmInfoBO biParams){
        BasePage<BiCallListVO> basePage = biCustomerService.queryCallList(biParams);
        return Result.ok(basePage);
    }

    @PostMapping("queryContractsByProductSatisfaction")
    @ApiOperation("查看回访产品满意度关联数据--供crm调用")
    public Result<List<String>> queryContractsByProductSatisfaction(@RequestBody @ApiParam("BiSearchBO类的转换") Map<String, Object> map){
        CrmFieldBO satisficing = biCustomerMapper.queryCrmFieldInfo(17, "satisficing");
        map.put("fieldId", satisficing.getFieldId());
        BiEntityParams biEntityParams = BeanUtil.copyProperties(map, BiEntityParams.class);
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biEntityParams, BiMenuEnum.PRODUCT_LIST.getMenuId());
        List<String> list = biCustomerMapper.queryContractsByProductSatisfaction(biTimeEntity,map);
        return Result.ok(list);
    }

    @PostMapping("queryRecordCustomerList")
    @ApiOperation("查看客户跟进数据")
    public Result<List<Long> > queryRecordCustomerList(@RequestBody Map<String, Object> map){
        BiEntityParams biParams = BeanUtil.copyProperties(map, BiEntityParams.class);
        BiParamsUtil.BiTimeEntity timeEntity = BiParamsUtil.analyzeType(biParams, BiMenuEnum.BI_CUSTOMER_STATS.getMenuId());
        return Result.ok(ApplicationContextHolder.getBean(BiCustomerMapper.class).customerRecordIds(timeEntity,biParams.getType(),map.get("category")));
    }

    @PostMapping("queryProductTypeList")
    @ApiOperation("查看产品类别销售列表")
    public Result<BasePage<JSONObject>> queryProductTypeList(@RequestBody BICrmInfoBO biParams){
        return Result.ok(biCustomerService.queryProductTypeList(biParams));
    }

    @PostMapping("queryProductCustomerList")
    @ApiOperation("查看产品成交客户列表")
    public Result<List<Long>> queryProductCustomerList(@RequestBody BICrmInfoBO biParams){
        return Result.ok(biCustomerService.queryProductCustomerList(biParams));
    }

    @PostMapping("queryContendBusinessList")
    @ApiOperation("查看项目赢单数据")
    public Result<List<Long>> queryContendBusinessList(@RequestBody BICrmInfoBO biParams){
        return Result.ok(biCustomerService.queryContendBusinessList(biParams));
    }
}
