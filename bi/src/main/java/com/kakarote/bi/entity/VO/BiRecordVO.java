package com.kakarote.bi.entity.VO;

import com.kakarote.bi.common.utils.BiDataFormatUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("客户总量分析VO")
@Getter
@Setter
public class BiRecordVO implements BiDataFormatUtil.BaseDateTypeSetting, BiDataFormatUtil.BaseUserSetting,Serializable {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("员工姓名")
    private String realname;

    @ApiModelProperty("跟进次数")
    private Integer recordNum;

    @ApiModelProperty("外勤签到次数")
    private Integer outSignRecordNum;

    @ApiModelProperty("跟进客户数")
    private Integer customerNum;

    @ApiModelProperty("客户跟进次数比")
    private BigDecimal recordNumRate;

    @ApiModelProperty("类型")
    private String type;

    @Override
    public String toString() {
        return "BiRecordVO{" +
                "userId=" + userId +
                ", realname='" + realname + '\'' +
                ", recordNum=" + recordNum +
                ", outSignRecordNum=" + outSignRecordNum +
                ", customerNum=" + customerNum +
                ", recordNumRate=" + recordNumRate +
                ", type='" + type + '\'' +
                '}';
    }
}
