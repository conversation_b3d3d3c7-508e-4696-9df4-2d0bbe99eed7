package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("业绩目标BO")
@NoArgsConstructor
@AllArgsConstructor
public class BiPerformanceVO implements Serializable {

    @ApiModelProperty("业绩目标金额")
    private BigDecimal achievementMoneys;

    @ApiModelProperty("业绩目标完成率")
    private BigDecimal proportion;

    @ApiModelProperty("完成金额")
    private BigDecimal money;
}
