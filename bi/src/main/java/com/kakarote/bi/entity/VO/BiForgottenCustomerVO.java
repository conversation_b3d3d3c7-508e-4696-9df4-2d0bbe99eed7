package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@ApiModel("仪表盘客户遗忘提醒VO")
public class BiForgottenCustomerVO implements Serializable {

    @ApiModelProperty("七天未联系客户")
    private Integer sevenDays;

    @ApiModelProperty("超过15天未联系的客户")
    private Integer fifteenDays;

    @ApiModelProperty("超过30天未联系的客户")
    private Integer oneMonth;

    @ApiModelProperty("超过3个月未联系的客户")
    private Integer threeMonth;

    @ApiModelProperty("超过6个月未联系的客户")
    private Integer sixMonth;

    @ApiModelProperty("逾期未联系的客户")
    private Integer unContactCustomerCount;

    public BiForgottenCustomerVO() {
        this.sevenDays = 0;
        this.fifteenDays = 0;
        this.oneMonth = 0;
        this.threeMonth = 0;
        this.sixMonth = 0;
        this.unContactCustomerCount = 0;
    }

    public BiForgottenCustomerVO(Integer sevenDays, Integer fifteenDays, Integer oneMonth, Integer threeMonth, Integer sixMonth, Integer unContactCustomerCount) {
        this.sevenDays = sevenDays;
        this.fifteenDays = fifteenDays;
        this.oneMonth = oneMonth;
        this.threeMonth = threeMonth;
        this.sixMonth = sixMonth;
        this.unContactCustomerCount = unContactCustomerCount;
    }

    @Override
    public String toString() {
        return "BiForgottenCustomerVO{" +
                "sevenDays=" + sevenDays +
                ", fifteenDays=" + fifteenDays +
                ", oneMonth=" + oneMonth +
                ", threeMonth=" + threeMonth +
                ", sixMonth=" + sixMonth +
                ", unContactCustomerCount=" + unContactCustomerCount +
                '}';
    }
}
