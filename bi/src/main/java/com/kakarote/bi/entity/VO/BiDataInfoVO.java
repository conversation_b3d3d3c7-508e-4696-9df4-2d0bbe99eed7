package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("数据汇总模块VO")
public class BiDataInfoVO implements Serializable {

    @ApiModelProperty("新增客户")
    private Integer allCustomer;

    @ApiModelProperty("放入公海客户")
    private Integer putInPoolNum;

    @ApiModelProperty("转成交客户")
    private Integer dealCustomer;

    @ApiModelProperty("公海池领取客户")
    private Integer receiveNum;

    @ApiModelProperty("新增项目")
    private Integer allBusiness;

    @ApiModelProperty("赢单项目")
    private Integer endBusiness;

    @ApiModelProperty("输单项目")
    private Integer loseBusiness;

    @ApiModelProperty("项目金额")
    private BigDecimal businessMoney;

    @ApiModelProperty("签约合同")
    private Integer allContract;

    @ApiModelProperty("即将到期的合同")
    private Integer expireContract;

    @ApiModelProperty("已到期的合同")
    private Integer endContract;

    @ApiModelProperty("合同金额")
    private BigDecimal contractMoney;

    @ApiModelProperty("跟进")
    private Integer activityNum;

    @ApiModelProperty("新增未跟进")
    private Integer activityRealNum;

    @ApiModelProperty("回款金额")
    private BigDecimal receivablesMoney;

    @ApiModelProperty("预计回款金额")
    private BigDecimal planMoney;

    @Override
    public String toString() {
        return "BiDataInfoVO{" +
                "allCustomer=" + allCustomer +
                ", putInPoolNum=" + putInPoolNum +
                ", dealCustomer=" + dealCustomer +
                ", receiveNum=" + receiveNum +
                ", allBusiness=" + allBusiness +
                ", endBusiness=" + endBusiness +
                ", loseBusiness=" + loseBusiness +
                ", businessMoney=" + businessMoney +
                ", allContract=" + allContract +
                ", expireContract=" + expireContract +
                ", endContract=" + endContract +
                ", contractMoney=" + contractMoney +
                ", activityNum=" + activityNum +
                ", activityRealNum=" + activityRealNum +
                ", receivablesMoney=" + receivablesMoney +
                ", planMoney=" + planMoney +
                '}';
    }
}
