package com.kakarote.bi.entity.BO;

import com.kakarote.core.feign.crm.entity.BiEntityParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * crm
 * @description:
 * @author: zyy
 * @date: 2022-02-28
 */
@Data
@ApiModel("crm查询BO")
public class BICrmInfoBO extends BiEntityParams {

    private Long categoryId;

    @ApiModelProperty("通话时长(秒)   员工通话记录使用")
    private Long talkTime;

    @ApiModelProperty("比较类型  员工通话记录使用")
    private String talkTimeCondition;
}
