package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("排行榜金额排行VO")
@Getter
@Setter
public class BiRankMoney<PERSON> implements Serializable {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户昵称")
    private String realname;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("头像")
    private String img;

    @ApiModelProperty("金额")
    private BigDecimal money;

    @ApiModelProperty("排序")
    private Integer order;

    @ApiModelProperty("完成率")
    private String rate;

    @Override
    public String toString() {
        return "BiRankMoneyVO{" +
                "userId=" + userId +
                ", realname='" + realname + '\'' +
                ", deptName='" + deptName + '\'' +
                ", img='" + img + '\'' +
                ", money=" + money +
                ", order=" + order +
                ", rate='" + rate + '\'' +
                '}';
    }
}
