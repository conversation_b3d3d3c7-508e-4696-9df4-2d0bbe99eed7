package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("仪表盘销售简报VO")
@Getter
@Setter
public class BiBulletinVO implements Serializable {

    @ApiModelProperty("客户数量")
    private Integer customerCount;

    @ApiModelProperty("联系人数量")
    private Integer contactsCount;

    @ApiModelProperty("项目数量")
    private Integer businessCount;

    @ApiModelProperty("项目金额")
    private BigDecimal businessMoney;

    @ApiModelProperty("合同数量")
    private Integer contractCount;

    @ApiModelProperty("合同金额")
    private BigDecimal contractMoney;

    @ApiModelProperty("回款金额")
    private BigDecimal receivablesMoney;

    @ApiModelProperty("跟进记录数量")
    private Integer recordCount;

    @ApiModelProperty("type")
    private String type;

    @Override
    public String toString() {
        return "BiBulletinVO{" +
                "customerCount=" + customerCount +
                ", contactsCount=" + contactsCount +
                ", businessCount=" + businessCount +
                ", businessMoney=" + businessMoney +
                ", contractCount=" + contractCount +
                ", contractMoney=" + contractMoney +
                ", receivablesMoney=" + receivablesMoney +
                ", recordCount=" + recordCount +
                ", type='" + type + '\'' +
                '}';
    }
}
