package com.kakarote.bi.entity.VO;

import com.kakarote.core.feign.admin.entity.SimpleUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 员工通话记录分析
 *
 * @author: zyy
 * @date: 2022-03-08
 */
@Getter
@Setter
@ToString
@ApiModel("员工通话记录分析VO")
public class BiEmployeeCallVO implements Serializable {

    @ApiModelProperty(value = "员工信息")
    private SimpleUser userInfo;

    @ApiModelProperty(value = "总通话数")
    private Integer totalCountCalls;

    @ApiModelProperty(value = "接通数")
    private Integer totalCountAnswer;

    @ApiModelProperty(value = "接通率")
    private Object rateAnswer;

    @ApiModelProperty(value = "总通话时长")
    private Integer totalTimeCalls;

    @ApiModelProperty(value = "外呼总数")
    private Integer totalCountCallsOut;

    @ApiModelProperty(value = "外呼接通数")
    private Integer totalCountAnswerOut;

    @ApiModelProperty(value = "外呼接通率")
    private Object rateAnswerOut;

    @ApiModelProperty(value = "外呼通话时长")
    private String totalTimeCallsOut;

    @ApiModelProperty(value = "外呼通话平均时长")
    private String averageTimeCallOut;

    @ApiModelProperty(value = "呼入通话总数")
    private Integer totalCountCallsIn;

    @ApiModelProperty(value = "呼入通话接通数")
    private Integer totalCountAnswerIn;

    @ApiModelProperty(value = "呼入通话总时长")
    private String totalTimeCallsIn;

    @ApiModelProperty(value = "呼入通话平均时长")
    private String averageTimeCallIn;

    @ApiModelProperty(value = "呼入接通率")
    private BigDecimal rateAnswerIn;

    @ApiModelProperty("负责人ID")
    private Long ownerUserId;
}
