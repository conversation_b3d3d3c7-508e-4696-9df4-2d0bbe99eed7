package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel("员工业绩分析VO")
@Getter
@Setter
public class BiEmployeeCycleVO implements Serializable {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("员工姓名")
    private String realname;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("客户数量")
    private Integer customerNum;

    @ApiModelProperty("成交周期")
    private Integer cycle;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品ID")
    private Object productId;

    @Override
    public String toString() {
        return "BiEmployeeCycleVO{" +
                "userId=" + userId +
                ", realname='" + realname + '\'' +
                ", type='" + type + '\'' +
                ", customerNum=" + customerNum +
                ", cycle=" + cycle +
                ", productName='" + productName + '\'' +
                ", productId='" + productId + '\'' +
                '}';
    }
}
