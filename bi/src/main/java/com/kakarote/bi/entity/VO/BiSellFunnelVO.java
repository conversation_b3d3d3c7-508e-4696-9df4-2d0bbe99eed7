package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("销售漏斗VO")
@Getter
@Setter
public class BiSellFunnelVO implements Serializable {

    @ApiModelProperty("流程名称")
    private String settingName;

    @ApiModelProperty("项目金额")
    private BigDecimal businessMoney;

    @ApiModelProperty("项目数量")
    private Integer businessNum;

    @ApiModelProperty("赢单项目数量")
    private Integer winBusinessNum;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("项目转化率")
    private BigDecimal businessConversion;

    @ApiModelProperty("阶段id")
    private Long settingId;

    @ApiModelProperty("项目状态")
    private Integer isEnd;

    @Override
    public String toString() {
        return "BiSellFunnelVO{" +
                "settingName='" + settingName + '\'' +
                ", businessMoney=" + businessMoney +
                ", businessNum=" + businessNum +
                ", winBusinessNum=" + winBusinessNum +
                ", type='" + type + '\'' +
                ", businessConversion=" + businessConversion +
                ", settingId=" + settingId +
                ", isEnd=" + isEnd +
                '}';
    }
}
