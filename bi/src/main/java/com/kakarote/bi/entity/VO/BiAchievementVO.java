package com.kakarote.bi.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("业绩目标分析VO")
public class BiAchievementVO implements Serializable {

    @ApiModelProperty("业务对象名称")
    private String name;

    @ApiModelProperty("具体的月份目标完成信息")
    List<BiMonthReceivedMoneyVO> biMonthReceivedMoneyVOS;
}
