package com.kakarote.bi.common.enums;

/**
 * 枚举菜单ID
 *
 * <AUTHOR>
 */
public enum BiMenuEnum {
    /*
        线索列表
     */
    LEADS_LIST(20L),
    /*
        客户列表
     */
    CUSTOMER_LIST(28L),
    /*
        联系人列表
     */
    CONTACTS_LIST(42L),
    /*
        产品列表
     */
    PRODUCT_LIST(67L),
    /*
        项目列表
     */
    BUSINESS_LIST(48L),
    /*
        合同列表
     */
    CONTRACT_LIST(55L),

    /*
        回款列表
     */
    RECEIVABLES_LIST(62L),

    /*
        回访列表
     */
    VISIT_LIST(403L),
    /*
        跟进记录列表
     */
    RECORD_LIST(441L),

    /**
     * 业绩目标完成
     */
    BI_ACHIEVEMENT(102L),

    /*
        销售漏斗分析
     */
    BI_SELL_FUNNEL(103L),
    /*
        员工业绩分析
     */
    BI_EMPLOYEES_STATS(106L),

    /*
        客户总量分析
     */
    BI_CUSTOMER_STATS(104L),

    /*
        产品销售分析
     */
    BI_PRODUCT_STATS(118L),

    /*
        排行榜分析
     */
    BI_RANK_STATS(126L),

    /*
        客户画像分析
     */
    BI_CUSTOMER_ANALYSE_STATS(124L),
    /**
     * 办公分析
     */
    BI_WORK_STATS(147L),

    /*
        查询通话记录
     */
    BI_CALL_INDEX(194L),

    /*
        通话记录分析
     */
    BI_CALL_ANALYSIS(195L),

    ;
    private final Long menuId;

    BiMenuEnum(Long menuId) {
        this.menuId = menuId;
    }

    public Long getMenuId() {
        return menuId;
    }
}
