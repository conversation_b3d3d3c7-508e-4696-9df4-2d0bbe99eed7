package com.kakarote.bi.common.enums;

import com.kakarote.core.common.ResultCode;

/**
 * <AUTHOR>
 * 管理后台响应错误代码枚举类
 */

public enum BiCodeEnum implements ResultCode {
    //客户模块管理
    BI_DATE_PARSE_ERROR(3101, "日期解析错误！"),
    BI_CALL_DATA_QUERY_ERROR(3102,"%s不正确！"),
    ;

    BiCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final int code;
    private final String msg;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
