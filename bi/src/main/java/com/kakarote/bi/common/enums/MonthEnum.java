package com.kakarote.bi.common.enums;

import cn.hutool.core.date.Quarter;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

public enum MonthEnum {
    /**
     * 一月
     */
    JANUARY("january", "一月", "01"),
    /**
     * 二月
     */
    FEBRUARY("february", "二月", "02"),
    /**
     * 三月
     */
    MARCH("march", "三月", "03"),
    /**
     * 四月
     */
    APRIL("april", "四月", "04"),
    /**
     * 五月
     */
    MAY("may", "五月", "05"),
    /**
     * 六月
     */
    JUNE("june", "六月", "06"),
    /**
     * 七月
     */
    JULY("july", "七月", "07"),
    /**
     * 八月
     */
    AUGUST("august", "八月", "08"),
    /**
     * 九月
     */
    SEPTEMBER("september", "九月", "09"),
    /**
     * 十月
     */
    OCTOBER("october", "十月", "10"),
    /**
     * 十一月
     */
    NOVEMBER("november", "十一月", "11"),
    /**
     * 十二月
     */
    DECEMBER("december", "十二月", "12"),

    /**
     * 整年
     */
    YEAR("yeartarget", "全年", "0");

    MonthEnum(String name, String remark, String value) {
        this.name = name;
        this.remark = remark;
        this.value = value;
    }

    public static List<MonthEnum> parseQuarterMonth(int quarter) {
        switch (Quarter.of(quarter)) {
            case Q1:
                return Arrays.asList(JANUARY,FEBRUARY,MARCH);
            case Q2:
                return Arrays.asList(APRIL,MAY,JUNE);
            case Q3:
                return Arrays.asList(JULY,AUGUST,SEPTEMBER);
            case Q4:
                return Arrays.asList(OCTOBER,NOVEMBER,DECEMBER);
            default:
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
    }

    public String getName() {
        return name;
    }

    public String getRemark() {
        return remark;
    }

    public String getValue() {
        return value;
    }

    private final String name;

    private final String remark;

    private final String value;

    public static MonthEnum valueOf(int mouth) {
        String mouthe = mouth >= 10 ? (mouth + "") : ("0" + mouth);
        for (MonthEnum monthEnum : MonthEnum.values()) {
            if (monthEnum.getValue().equals(mouthe)) {
                return monthEnum;
            }
        }
        throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
    }

    public static MonthEnum parseName(String name) {
        for (MonthEnum monthEnum : MonthEnum.values()) {
            if (monthEnum.getName().equals(name)) {
                return monthEnum;
            }
        }
        throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
    }
}
