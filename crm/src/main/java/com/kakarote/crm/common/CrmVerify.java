package com.kakarote.crm.common;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmModelSaveBO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 保存CRM数据时验证数据格式
 */
public class CrmVerify {

    private final CrmEnum crmEnum;

    public CrmVerify(CrmEnum crmEnum) {
        this.crmEnum = crmEnum;
    }

    public Result verify(CrmModelSaveBO model) {
        switch (crmEnum) {
            case LEADS:
            case CUSTOMER:
            case CONTACTS:
                return verifyLeads(model);
            case PRODUCT:
                return verifyProduct(model);
            default:
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
    }

    /**
     * 验证字段的正确性
     * 目前线索，客户，联系人需要验证的逻辑是相同的
     *
     * @param model json数据
     * @return 验证结果
     */
    private Result verifyLeads(CrmModelSaveBO model) {
        JSONObject entity = new JSONObject(model.getEntity());
        String mobile = "mobile";
        if (StrUtil.isNotEmpty(entity.getString(mobile))) {
            if (!ReUtil.isMatch("^(\\+?0?\\d{2,4}\\-?)?\\d{6,11}$", entity.getString("mobile"))) {
                return R.error(CrmCodeEnum.CRM_PHONE_FORMAT_ERROR);
            }
        }
        if (StrUtil.isNotEmpty(entity.getString("next_time"))) {
            try {
                entity.put("next_time", parseDateTime(entity.get("next_time")));
            } catch (Exception ex) {
                return Result.error(CrmCodeEnum.CRM_DATETIME_FORMAT_ERROR);
            }
        }
        Result result = verifyField(model);
        if (!result.hasSuccess()) {
            return result;
        }
        return R.ok();
    }

    /**
     * 验证字段的正确性
     *
     * @param model json数据
     * @return 验证结果
     */
    private Result verifyProduct(CrmModelSaveBO model) {
        JSONObject entity = new JSONObject(model.getEntity());
        if (StrUtil.isNotEmpty(entity.getString("price"))) {
            if (!ReUtil.isMatch(PatternPool.MONEY, entity.getString("price")) && !ReUtil.isMatch(PatternPool.NUMBERS, entity.getString("price"))) {
                return R.error(CrmCodeEnum.CRM_PRICE_FORMAT_ERROR);
            }
        }
        Result result = verifyField(model);
        if (!result.hasSuccess()) {
            return result;
        }
        return R.ok();
    }

    /**
     * 验证自定义字段的正确性
     *
     * @param modelSaveBO json数据
     * @return 验证结果
     */
    private Result verifyField(CrmModelSaveBO modelSaveBO) {
        List<CrmModelFiledVO> array = modelSaveBO.getField();
        for (CrmModelFiledVO object : array) {
            int type = object.getType();
            Object val = object.getValue();
            if (ObjectUtil.isEmpty(val)) {
                continue;
            }
            if (type == 4) {
                try {
                    object.setValue(parseDate(val));
                } catch (Exception ex) {
                    return Result.error(CrmCodeEnum.CRM_DATE_FORMAT_ERROR);
                }
            } else if (type == 5) {
                if (!ReUtil.isMatch(PatternPool.NUMBERS, val.toString())) {
                    return R.error(CrmCodeEnum.CRM_PRICE_FORMAT_ERROR);
                }
            } else if (type == 7) {
                if (!ReUtil.isMatch("^(\\+?0?\\d{2,4}\\-?)?\\d{6,11}$", val.toString())) {
                    return R.error(CrmCodeEnum.CRM_PHONE_FORMAT_ERROR);
                }
            } else if (type == 13) {
                try {
                    object.setValue(parseDateTime(val));
                } catch (Exception ex) {
                    return Result.error(CrmCodeEnum.CRM_DATETIME_FORMAT_ERROR);
                }
            }
        }
        return R.ok();
    }

    private static String parseDate(Object val) {
        if (val instanceof Date) {
            return DateUtil.formatDate((Date) val);
        } else if (val instanceof LocalDate) {
            return LocalDateTimeUtil.format((LocalDate) val, DatePattern.NORM_DATE_FORMATTER);
        } else {
            DateTime dateTime = DateUtil.parse(val.toString());
            return dateTime.toDateStr();
        }
    }

    private static String parseDateTime(Object val) {
        if (val instanceof Date) {
            return DateUtil.formatDateTime((Date) val);
        } else if (val instanceof LocalDate) {
            return LocalDateTimeUtil.format((LocalDate) val, DatePattern.NORM_DATETIME_FORMATTER);
        } else {
            DateTime dateTime = DateUtil.parse(val.toString());
            return dateTime.toString(DatePattern.NORM_DATE_FORMAT);
        }
    }
}
