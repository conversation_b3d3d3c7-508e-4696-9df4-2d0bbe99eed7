package com.kakarote.crm.constant;

/**
 * <AUTHOR>
 * @since 2021-12-15
 */

public enum CrmActivityTypeEnum {
    /**
     * 跟进记录
     */
    ACTIVITY(1),

    /**
     * 创建记录
     */
    CREATE(2),

    /**
     * 项目阶段变更
     */
    BUSINESS_CHANGE(3),

    /**
     * 外勤签到
     */
    OUTWORK(4),

    /**
     * 呼叫记录
     */
    CALL_RECORD(5);

    CrmActivityTypeEnum(Integer type) {
        this.type = type;
    }

    private final Integer type;

    public static CrmActivityTypeEnum parse(Integer type) {
        switch (type) {
            case 1:
                return ACTIVITY;
            case 2:
                return CREATE;
            case 3:
                return BUSINESS_CHANGE;
            case 4:
                return OUTWORK;
            case 5:
                return CALL_RECORD;
            default:
                return null;
        }
    }


    public int getType() {
        return type;
    }

}
