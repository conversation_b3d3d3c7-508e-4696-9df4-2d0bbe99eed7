package com.kakarote.crm.constant;


/**
 * <AUTHOR>
 * crm模块枚举
 */

public enum CrmDatabaseEnum {

    WK_CRM_ACTIVITY("CRM活动表"),
    WK_CRM_ACTIVITY_RELATION("活动关联项目联系人表"),
    WK_CRM_BUSINESS("项目表"),
    //WK_CRM_BUSINESS_CHANGE("项目阶段变化表"),
    WK_CRM_BUSINESS_DATA("项目扩展字段数据表"),
    WK_CRM_BUSINESS_PRODUCT("项目产品关系表"),
    WK_CRM_BUSINESS_USER_STAR("用户项目标星关系表"),
    WK_CRM_CONTACTS("联系人表"),
    WK_CRM_CONTACTS_DATA("联系人扩展字段数据表"),
    WK_CRM_CONTACTS_BUSINESS("项目联系人关联表"),
    WK_CRM_CONTACTS_USER_STAR("用户联系人标星关系表"),
    WK_CRM_CONTRACT("合同表"),
    WK_CRM_CONTRACT_DATA("合同扩展字段数据表"),
    WK_CRM_CONTRACT_PRODUCT("合同产品关系表"),
    WK_CRM_CUSTOMER("客户表"),
    WK_CRM_CUSTOMER_DATA("客户扩展字段数据表"),
    WK_CRM_CUSTOMER_SETTING("员工拥有以及锁定客户数限制"),
    WK_CRM_CUSTOMER_SETTING_USER("员工拥有以及锁定客户员工关联表"),
    WK_CRM_CUSTOMER_USER_STAR("用户客户标星关系表"),
    WK_CRM_FIELD_SORT("字段排序表"),
    WK_CRM_INVOICE("发票表"),
    WK_CRM_INVOICE_DATA("发票自定义字段存值表"),
    WK_CRM_INVOICE_INFO("发票信息表"),
    WK_CRM_LEADS("线索表"),
    WK_CRM_LEADS_DATA("线索自定义字段存值表"),
    WK_CRM_LEADS_USER_STAR("用户线索标星关系表"),
    WK_CRM_OWNER_RECORD("负责人变更记录表"),
    WK_CRM_PRINT_RECORD("打印记录表"),
    WK_CRM_PRODUCT("产品表"),
    WK_CRM_PRODUCT_DATA("产品自定义字段存值表"),
    WK_CRM_PRODUCT_DETAIL_IMG("产品详情图片"),
    WK_CRM_PRODUCT_USER("产品员工小程序显示关联表"),
    WK_CRM_RECEIVABLES("回款表"),
    WK_CRM_RECEIVABLES_DATA("回款自定义字段存值表"),
    WK_CRM_RECEIVABLES_PLAN("回款计划表"),
    WK_CRM_RETURN_VISIT("回访表"),
    WK_CRM_RETURN_VISIT_DATA("回访表自定义字段存值表"),
    WK_CRM_CUSTOMER_POOL_RELATION("客户公海关联表"),
    WK_CRM_CUSTOMER_POOL_FIELD_SETTING("公海列表页字段设置表"),
    WK_CRM_CUSTOMER_POOL_FIELD_SORT("公海列表页字段排序表"),
    WK_CRM_CUSTOMER_POOL_FIELD_STYLE("公海列表页字段样式表"),
    WK_CRM_CUSTOMER_POOL_RULE("公海收回规则表"),
    WK_CRM_MARKETING("营销表"),
    WK_CRM_MARKETING_INFO("营销数据表"),
    WK_CRM_MARKETING_FIELD("活动字段表"),
    WK_CRM_MARKETING_FORM("活动表单表"),
    WK_CRM_ACTION_RECORD("字段操作记录表"),
    WK_CRM_BACK_LOG_DEAL("待办事项相关表"),
    WK_CRM_CUSTOMER_POOL("公海表"),
    WK_CRM_FIELD_NUMBER_DATA("自定义编号字段存值表"),
    WK_CRM_CUSTOMER_ADDRESS("客户地址表"),
    WK_CRM_CUSTOMER_SUPERIOR("上级客户下级客户关联表"),
    WK_CRM_ACHIEVEMENT("业绩目标"),
    WK_CRM_FLOW_COMMENT("流程阶段评论表"),
    WK_CRM_FLOW_DATA("阶段数据表"),
    WK_CRM_INSTRUMENT_SORT("仪表盘排序表"),
    WK_CRM_MARKETING_FIELD_EXTEND("市场活动自定义字段明细表"),
    WK_CRM_MENUS("CRM列表菜单"),
    WK_CRM_RECEIVABLES_PLAN_DATA("回款计划自定义字段存值表"),
    WK_CRM_ROLE_FIELD("角色字段授权表"),
    WK_CRM_SCENE("场景"),
    WK_CRM_SCENE_DEFAULT("场景默认关系表"),
    WK_CRM_SEARCH_DEFAULT("查询条件默认值"),
    WK_CRM_SEARCH_EXPERT("高级筛选外漏查询条件"),
    WK_CRM_TEAM_MEMBERS("CRM团队成员表");

    private final String tableName;

    CrmDatabaseEnum(String tableName) {
        this.tableName = tableName;
    }

    public String getTableName() {
        return tableName;
    }
}
