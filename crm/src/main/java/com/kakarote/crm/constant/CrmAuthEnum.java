package com.kakarote.crm.constant;


/**
 * crm模块操作权限枚举
 */
public enum CrmAuthEnum {

    /**
     * 新增
     */
    ADD(1),
    /**
     * 编辑
     */
    EDIT(2),
    /**
     * 列表
     */
    LIST(3),
    /**
     * 详情
     */
    READ(4),
    /**
     * 删除
     */
    DELETE(5),

    /**
     * 转移
     */
    TRANSFER(6),
    ;


    CrmAuthEnum(int value) {
        this.value = value;
    }

    private final int value;

    public Integer getValue() {
        return value;
    }

    /**
     * 根据功能和权限获取菜单ID，在CRM模块中菜单ID是完全固定的常量
     * 9 线索
     * 17 新建 18 修改 19 列表 20 详情 23 删除
     * 10 客户
     * 26 新建 27 修改 28 列表 29 详情 32 删除
     * 11 联系人
     * 40 新建 41 修改 42 列表 43 详情 44 删除
     * 12 项目
     * 46 新建 47 修改 48 列表 49 详情 50 删除
     * 13 合同
     * 53 新建 54 修改 55 列表 56 详情 57 删除
     * 14 回款
     * 60 新建 61 修改 62 列表 63 详情 64 删除
     * 15 产品
     * 65 新建 66 修改 67 列表 68 详情 211 删除
     * 440 跟进记录
     * 442 新建 443 修改 441 列表 441 详情 444 删除
     * 936 跟进记录
     * 937 新建 938 修改 939 列表 940 详情 941 删除
     * 400 跟进记录
     * 401 新建 402 修改 403 列表 404 详情 405 删除
     *
     * @param crmEnum label
     * @return menuId
     */
    public Long getMenuId(CrmEnum crmEnum) {
        /*
            跟进记录
         */
        if (crmEnum == null || crmEnum == CrmEnum.NULL) {
            switch (this) {
                case ADD:
                    return 442L;
                case EDIT:
                    return 443L;
                case LIST:
                case READ:
                    return 441L;
                case DELETE:
                    return 444L;
                default:
                    return 0L;
            }
        }
        //公海只有一个放入公海权限
        if (crmEnum == CrmEnum.CUSTOMER_POOL) {
            return 34L;
        }
        //转移权限不太规律，单独判断
        if (this == TRANSFER) {
            switch (crmEnum) {
                case LEADS:
                    return 24L;
                case CUSTOMER:
                    return 33L;
                case CONTACTS:
                    return 45L;
                case PRODUCT:
                    return 70L;
                case BUSINESS:
                    return 51L;
                case CONTRACT:
                    return 58L;
                case RECEIVABLES:
                    return 71L;
                case INVOICE:
                    return 426L;
                default:
                    return 0L;
            }
        }

        long start = 0;
        switch (crmEnum) {
            case LEADS:
                start = 16L;
            case CUSTOMER: {
                if (crmEnum == CrmEnum.CUSTOMER) {
                    start = 25L;
                }
                if (this == DELETE) {
                    return start + value + 2L;
                } else {
                    return start + value;
                }
            }
            case CONTACTS:
                start = 39L;
            case PRODUCT:
                if (CrmEnum.PRODUCT == crmEnum) {
                    start = 64L;
                }
            case BUSINESS:
                if (CrmEnum.BUSINESS == crmEnum) {
                    start = 45;
                }
            case CONTRACT:
                if (CrmEnum.CONTRACT == crmEnum) {
                    start = 52;
                }
            case RECEIVABLES:
                if (CrmEnum.RECEIVABLES == crmEnum) {
                    start = 59;
                }
                if (CrmEnum.PRODUCT == crmEnum && this == DELETE) {
                    return 211L;
                }
            case RETURN_VISIT:
                if (CrmEnum.RETURN_VISIT == crmEnum) {
                    start = 400L;
                }
            case INVOICE:
                if (CrmEnum.INVOICE == crmEnum) {
                    start = 420L;
                }
            case RECEIVABLES_PLAN:
                if (CrmEnum.RECEIVABLES_PLAN == crmEnum) {
                    start = 936L;
                }
                return start + value;
        }
        return 0L;
    }
}
