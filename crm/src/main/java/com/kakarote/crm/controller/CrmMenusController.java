package com.kakarote.crm.controller;


import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.crm.entity.BO.CrmMenusSaveBo;
import com.kakarote.crm.service.ICrmMenusService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * crm列表菜单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@RestController
@RequestMapping("/crmMenus")
public class CrmMenusController {

    @Autowired
    private ICrmMenusService crmMenusService;


    @PostMapping("/save")
    @ApiOperation("保存菜单")
    public Result save(@RequestBody CrmMenusSaveBo saveBo) {
        crmMenusService.saveAndUpdate(saveBo);
        return R.ok();
    }

    @PostMapping("/queryMenus")
    @ApiOperation("获取当前登录人菜单")
    public Result<CrmMenusSaveBo> queryMenus() {
        CrmMenusSaveBo saveBo = crmMenusService.queryMenus();
        return R.ok(saveBo);
    }

}

