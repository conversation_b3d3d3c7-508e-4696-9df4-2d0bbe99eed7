package com.kakarote.crm.controller;

import com.kakarote.core.common.Result;
import com.kakarote.crm.entity.BO.MarketingFieldBO;
import com.kakarote.crm.entity.PO.CrmMarketingField;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.service.ICrmMarketingFieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
@RestController
@RequestMapping("/crmMarketingField")
@Api(tags = "活动表单字段接口")
public class CrmMarketingFieldController {

    @Autowired
    private ICrmMarketingFieldService crmMarketingFieldService;

    /**
     * <AUTHOR>
     * 查询新增或编辑字段
     */
    @ApiOperation("查询新增或编辑字段")
    @PostMapping("/queryField/{formId}")
    public Result<List<CrmMarketingField>> queryField(@PathVariable Long formId){
        List<CrmMarketingField> list = crmMarketingFieldService.queryField(formId);
        return Result.ok(list);
    }

    @ApiOperation("保存自定义字段")
    @PostMapping("/saveField")
    public Result saveField(@RequestBody MarketingFieldBO marketingFieldBO){
        crmMarketingFieldService.saveField(marketingFieldBO);
        return Result.ok();
    }

    /**
     * <AUTHOR>
     * 新查询新增或编辑字段
     */
    @ApiOperation("查询新增或编辑字段")
    @PostMapping("/queryNewField/{formId}")
    public Result<List<List<CrmModelFiledVO>>> queryFormPositionField(@PathVariable Long formId){
        List<List<CrmModelFiledVO>>list = crmMarketingFieldService.queryFormPositionField(formId);
        return Result.ok(list);
    }
}
