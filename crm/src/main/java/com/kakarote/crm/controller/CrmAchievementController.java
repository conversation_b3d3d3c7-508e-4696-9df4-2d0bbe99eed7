package com.kakarote.crm.controller;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.crm.entity.BO.AchievementBO;
import com.kakarote.crm.entity.PO.CrmAchievement;
import com.kakarote.crm.service.ICrmAchievementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 业绩目标 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
@RestController
@RequestMapping("/crmAchievement")
@Api(tags = "业绩目标控制器")
public class CrmAchievementController {

    @Autowired
    private ICrmAchievementService achievementService;

    @PostMapping("/queryAchievementList")
    @ApiOperation("查询业绩目标")
    public Result<List<CrmAchievement>> queryAchievementList(AchievementBO crmAchievement) {
        List<CrmAchievement> crmAchievements = achievementService.queryAchievementList(crmAchievement);
        return Result.ok(crmAchievements);
    }

    @PostMapping("/addAchievement")
    @ApiOperation("保存业绩目标")
    public Result addAchievement(@RequestBody CrmAchievement crmAchievement) {
        achievementService.addAchievement(crmAchievement);
        return Result.ok();
    }

    @PostMapping("/setAchievement")
    @ApiOperation("修改业绩目标")
    public Result setAchievement(@RequestBody List<CrmAchievement> crmAchievement) {
        achievementService.verifyCrmAchievementData(crmAchievement);
        achievementService.updateBatchById(crmAchievement);
        return Result.ok();
    }

    @PostMapping("/deleteAchievement")
    @ApiOperation("删除业绩目标")
    public Result deleteAchievement(@RequestParam("achievementId") Long achievementId){
        CrmAchievement byId = achievementService.getById(achievementId);
        if(byId!=null){
            achievementService.removeById(achievementId);
        }
        return Result.ok();
    }

    @PostMapping("/downloadExcel")
    @ApiOperation("下载导入模板")
    public void downloadExcel(HttpServletResponse response,@RequestParam("type") Integer type) throws IOException {
        achievementService.downloadExcel(response,type);
    }

    @PostMapping("/excelImport")
    @ApiOperation("excel导入业绩目标")
    public Result<JSONObject> excelImport(@RequestParam("file") MultipartFile file,
                                          @RequestParam("type") Integer type) {
        JSONObject object = achievementService.excelImport(file,type);
        return R.ok(object);
    }

    @PostMapping("/downExcel")
    @ApiOperation("excel下载错误数据")
    public void downExcel(@RequestParam("token") String token, HttpServletResponse response) {
        String path = FileUtil.getTmpDirPath() + "/" + token;
        if (FileUtil.exist(path)) {
            File file = FileUtil.file(path);
            final String fileName = file.getName();
            final String contentType = ObjectUtil.defaultIfNull(FileUtil.getMimeType(fileName), "application/octet-stream");
            BufferedInputStream in = null;
            try {
                in = FileUtil.getInputStream(file);
                ServletUtil.write(response, in, contentType, "import_error.xls");
            } finally {
                IoUtil.close(in);
            }
            FileUtil.del(path);
        }
    }

}

