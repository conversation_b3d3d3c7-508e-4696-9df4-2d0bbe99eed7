package com.kakarote.crm.controller;

import com.kakarote.core.common.Result;
import com.kakarote.core.entity.BasePage;
import com.kakarote.crm.entity.BO.BiSearchBO;
import com.kakarote.crm.service.CrmBiSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 *
 * Bi模块筛选统计数据控制器
 *
 * @author: zyy
 * @date: 2022-02-14
 */
@RestController
@RequestMapping("/crmBiSearch")
@Api(tags = "Bi模块筛选统计数据")
public class CrmBiSearchController {

    @Autowired
    CrmBiSearchService crmBiSearchService;

    @PostMapping("searchCustomerPageList")
    @ApiOperation("客户相关筛选")
    public Result<BasePage<Map<String, Object>>> searchCustomerPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchCustomerPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchBusinessPageList")
    @ApiOperation("项目相关筛选")
    public Result<BasePage<Map<String, Object>>> searchBusinessPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchBusinessPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchContractPageList")
    @ApiOperation("合同相关筛选")
    public Result<BasePage<Map<String, Object>>> searchContractPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchContractPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchContactsPageList")
    @ApiOperation("联系人相关筛选")
    public Result<BasePage<Map<String, Object>>> searchContactsPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchContactsPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchInvoicePageList")
    @ApiOperation("发票相关筛选")
    public Result<BasePage<Map<String, Object>>> searchInvoicePageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchInvoicePageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchProductPageList")
    @ApiOperation("产品相关筛选")
    public Result<BasePage<Map<String, Object>>> searchProductPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchProductPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchReceivablesPageList")
    @ApiOperation("回款相关筛选")
    public Result<BasePage<Map<String, Object>>> searchReceivablesPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchReceivablesPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchReceivablesPlanPageList")
    @ApiOperation("回款计划相关筛选")
    public Result<BasePage<Map<String, Object>>> searchReceivablesPlanPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchReceivablesPlanPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("searchPoolCustomerPageList")
    @ApiOperation("公海相关筛选")
    public Result<BasePage<Map<String, Object>>> searchPoolCustomerPageList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.searchPoolCustomerPageList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("queryCustomerRecordList")
    @ApiOperation("客户跟进数量列表")
    public Result<BasePage<Map<String, Object>>> queryCustomerRecordList(@RequestBody BiSearchBO biSearchBO){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.queryCustomerRecordList(biSearchBO);
        return Result.ok(basePage);
    }

    @PostMapping("queryVisitContractPageList")
    @ApiOperation("查看回访合同数")
    public Result<BasePage<Map<String,Object>>> queryVisitContractPageList(@RequestBody BiSearchBO biSearchBO){
        return Result.ok(crmBiSearchService.queryVisitContractPageList(biSearchBO));
    }

    @PostMapping("employeesSatisfactionTable")
    @ApiOperation("员工满意度分析")
    public Result<BasePage<Map<String, Object>>> employeesSatisfactionTable(@RequestBody BiSearchBO biEntityParams){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.employeesSatisfactionTable(biEntityParams);
        return Result.ok(basePage);
    }

    @PostMapping("productSatisfactionTable")
    @ApiOperation("产品满意度分析")
    public Result productSatisfactionTable(@RequestBody BiSearchBO biEntityParams){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.productSatisfactionTable(biEntityParams);
        return Result.ok(basePage);
    }

    @PostMapping("queryProductSucceedCustomerList")
    @ApiOperation("产品成交客户")
    public Result queryProductSucceedCustomerList(@RequestBody BiSearchBO biEntityParams){
        BasePage<Map<String, Object>> basePage =crmBiSearchService.queryProductSucceedCustomerList(biEntityParams);
        return Result.ok(basePage);
    }
}
