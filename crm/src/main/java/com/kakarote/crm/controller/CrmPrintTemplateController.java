package com.kakarote.crm.controller;


import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.LoginFromCookie;
import com.kakarote.core.utils.BaseUtil;
import com.kakarote.crm.entity.BO.CrmPrintTemplateBO;
import com.kakarote.crm.entity.PO.CrmPrintRecord;
import com.kakarote.crm.entity.PO.CrmPrintTemplate;
import com.kakarote.crm.entity.VO.CrmPrintFieldVO;
import com.kakarote.crm.service.ICrmPrintTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 打印模板表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@RestController
@RequestMapping("/crmPrint")
@Api(tags = "打印模板")
@Slf4j
public class CrmPrintTemplateController {

    @Autowired
    private ICrmPrintTemplateService printTemplateService;

    @ApiOperation("查询打印模板列表")
    @PostMapping("/queryPrintTemplateList")
    public Result<BasePage<CrmPrintTemplate>> queryPrintTemplateList(@RequestBody CrmPrintTemplateBO printTemplateBO) {
        BasePage<CrmPrintTemplate> adminPrintTemplateBasePage = printTemplateService.queryPrintTemplateList(printTemplateBO);
        return Result.ok(adminPrintTemplateBasePage);
    }

    @ApiOperation("跟进ID查询打印模板")
    @PostMapping("/queryPrintTemplateById")
    public Result<CrmPrintTemplate> queryPrintTemplateById(@RequestParam("templateId") Long templateId) {
        CrmPrintTemplate byId = printTemplateService.getById(templateId);
        return Result.ok(byId);
    }

    @ApiOperation("新增模板")
    @PostMapping("/addPrintTemplate")
    public Result addPrintTemplate(@RequestBody CrmPrintTemplate adminPrintTemplate) {
        adminPrintTemplate.setUpdateTime(LocalDateTimeUtil.now());
        printTemplateService.save(adminPrintTemplate);
        return Result.ok();
    }

    @ApiOperation("修改模板")
    @PostMapping("/updatePrintTemplate")
    public Result updatePrintTemplate(@RequestBody CrmPrintTemplate adminPrintTemplate) {
        printTemplateService.updateById(adminPrintTemplate);
        return Result.ok();
    }

    @ApiOperation("删除模板")
    @PostMapping("/deletePrintTemplate")
    public Result deletePrintTemplate(@RequestParam("templateId") Long templateId) {
        printTemplateService.deletePrintTemplate(templateId);
        return Result.ok();
    }

    @ApiOperation("查询字段")
    @PostMapping("/queryFields")
    public Result<CrmPrintFieldVO> queryFields(@RequestParam("type") Integer type) {
        CrmPrintFieldVO crmPrintFieldVO = printTemplateService.queryFields(type);
        List<Integer> types = Arrays.asList(FieldEnum.FIELD_GROUP.getType(), FieldEnum.BOOLEAN_VALUE.getType(), FieldEnum.HANDWRITING_SIGN.getType(), FieldEnum.DESC_TEXT.getType(), FieldEnum.DETAIL_TABLE.getType(), FieldEnum.ATTENTION.getType(), FieldEnum.TAG.getType());
        crmPrintFieldVO.getContacts().removeIf(field-> types.contains(field.getType()));
        crmPrintFieldVO.getContacts().removeIf(field-> types.contains(field.getType()));
        crmPrintFieldVO.getBusiness().removeIf(field-> types.contains(field.getType()));
        crmPrintFieldVO.getContract().removeIf(field-> types.contains(field.getType()));
        crmPrintFieldVO.getCustomer().removeIf(field-> types.contains(field.getType()));
        crmPrintFieldVO.getProduct().removeIf(field-> types.contains(field.getType()));
        crmPrintFieldVO.getReceivables().removeIf(field-> types.contains(field.getType()));
        return Result.ok(crmPrintFieldVO);
    }

    @ApiOperation("打印")
    @PostMapping("/print")
    public Result<String> print(@RequestParam("templateId") Long templateId, @RequestParam("id") Long id) {
        String print = printTemplateService.print(templateId, id);
        return Result.ok(print);
    }

    @ApiOperation("预览")
    @PostMapping("/preview")
    public Result<String> preview(@RequestParam("content") String content, @RequestParam("type") String type) {
        String s = printTemplateService.preview(content, type);
        return Result.ok(s);
    }

    @ApiOperation("下载文件")
    @RequestMapping("/down")
    @LoginFromCookie
    public void down(@RequestParam("type") Integer type, @RequestParam("key") String key, HttpServletResponse response) {
        String object = BaseUtil.getRedis().get(CrmCacheKey.CRM_PRINT_TEMPLATE_CACHE_KEY + key);
        if (StrUtil.isNotEmpty(object)) {
            JSONObject parse = JSON.parseObject(object);
            String path;
            int two=2;
            if (type == two) {
                path = parse.getString("word");
            } else {
                path = parse.getString("pdf");
            }
            if (FileUtil.exist(path)) {
                ServletUtil.write(response, FileUtil.file(path));
                return;
            }
        }
        ServletUtil.write(response, Result.ok().toJSONString(), "text/plain");
    }

    @ApiOperation("iframe")
    @RequestMapping("/preview.pdf")
    @LoginFromCookie
    public void preview(String key, HttpServletResponse response) {
        String object = BaseUtil.getRedis().get(CrmCacheKey.CRM_PRINT_TEMPLATE_CACHE_KEY + key);
        if (StrUtil.isNotEmpty(object)) {
            JSONObject parse = JSON.parseObject(object);
            String path = parse.getString("pdf");
            if (FileUtil.exist(path)) {
                File file = FileUtil.file(path);
                BufferedInputStream in = null;
                ServletOutputStream out = null;
                try {
                    in = FileUtil.getInputStream(file);
                    response.setContentType("application/pdf");
                    IoUtil.copy(in,response.getOutputStream());
                } catch (Exception ex) {
                    log.error("导出错误",ex);
                } finally {
                    IoUtil.close(in);
                    IoUtil.close(out);
                }
                return;
            }
        }
        ServletUtil.write(response, Result.ok().toJSONString(), "text/plain");
    }

    @ApiOperation("复制模板")
    @PostMapping("/copyTemplate")
    public Result copyTemplate(@RequestParam("templateId") Long templateId) {
        printTemplateService.copyTemplate(templateId);
        return Result.ok();
    }

    @ApiOperation("保存打印记录")
    @PostMapping("/savePrintRecord")
    public Result savePrintRecord(@RequestBody CrmPrintRecord crmPrintRecord) {
        printTemplateService.savePrintRecord(crmPrintRecord);
        return Result.ok();
    }

    @ApiOperation("查询打印记录")
    @PostMapping("/queryPrintRecord")
    public Result<List<CrmPrintRecord>> queryPrintRecord(@RequestParam("crmType") Integer crmType, @RequestParam("typeId") Long typeId) {
        List<CrmPrintRecord> crmPrintRecords = printTemplateService.queryPrintRecord(crmType, typeId);
        return Result.ok(crmPrintRecords);
    }

    @ApiOperation("查询打印记录")
    @PostMapping("/queryPrintRecordById")
    public Result<CrmPrintRecord> queryPrintRecordById(@RequestParam("recordId") Long recordId) {
        CrmPrintRecord crmPrintRecord = printTemplateService.queryPrintRecordById(recordId);
        return Result.ok(crmPrintRecord);
    }

}

