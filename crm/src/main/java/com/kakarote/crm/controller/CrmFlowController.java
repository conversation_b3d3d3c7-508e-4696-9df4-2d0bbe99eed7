package com.kakarote.crm.controller;


import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.exception.CrmException;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.constant.CrmAuthEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmFlowPageBO;
import com.kakarote.crm.entity.BO.CrmFlowSaveBO;
import com.kakarote.crm.entity.PO.CrmFlow;
import com.kakarote.crm.entity.PO.CrmFlowComment;
import com.kakarote.crm.entity.PO.CrmFlowData;
import com.kakarote.crm.entity.VO.CrmFlowDataVO;
import com.kakarote.crm.entity.VO.CrmFlowInfoVO;
import com.kakarote.crm.service.ICrmFlowCommentService;
import com.kakarote.crm.service.ICrmFlowDataService;
import com.kakarote.crm.service.ICrmFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 阶段流程主信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@RestController
@RequestMapping("/crmFlow")
@Api(tags = "阶段流程配置")
public class CrmFlowController {

    @Autowired
    private ICrmFlowService crmFlowService;

    @Autowired
    private ICrmFlowDataService flowDataService;

    @Autowired
    private ICrmFlowCommentService flowCommentService;

    @PostMapping("/queryList")
    @ApiOperation(value = "管理后台查询阶段流程列表")
    public Result<BasePage<CrmFlow>> queryList(@RequestBody CrmFlowPageBO crmFlowPageBO) {
        BasePage<CrmFlow> basePage = crmFlowService.queryList(crmFlowPageBO);
        return Result.ok(basePage);
    }

    @PostMapping("/save")
    @ApiOperation(value = "管理后台保存阶段流程")
    public Result saveFlow(@RequestBody CrmFlowSaveBO flowSaveBO) {
        crmFlowService.saveFlow(flowSaveBO);
        return Result.ok();
    }

    @PostMapping("/updateStatus/{flowId}")
    @ApiOperation(value = "管理后台修改流程状态")
    public Result updateStatus(@ApiParam("流程ID") @PathVariable("flowId") Long flowId, @ApiParam("状态 1 启用 2 停用 3 删除") @RequestParam("status") Integer status) {
        crmFlowService.updateFlowStatus(flowId, status);
        return Result.ok();
    }

    @PostMapping("/flowInfo/{flowId}")
    @ApiOperation("管理后台阶段流程详情")
    public Result<CrmFlowInfoVO> flowInfo(@ApiParam("流程ID") @PathVariable("flowId") Long flowId) {
        CrmFlowInfoVO flowInfoVO = crmFlowService.flowInfo(flowId);
        return Result.ok(flowInfoVO);
    }

    @PostMapping("/queryFlowSettingList/{label}")
    @ApiOperation("数据详情页查询阶段列表")
    public Result<CrmFlowDataVO> queryFlowSettingList(@ApiParam("类型") @PathVariable("label") Integer label, @ApiParam("对应主键ID") @RequestParam("typeId") Long typeId) {
        CrmFlowDataVO flowDataVO = crmFlowService.queryFlowSettingList(label, typeId);
        return Result.ok(flowDataVO);
    }

    @PostMapping("/queryFlowDataInfo")
    @ApiOperation("数据详情页查询详细的阶段信息")
    public Result<CrmFlowData> queryFlowDataInfo(@RequestParam("dataId") Long dataId) {
        CrmFlowData crmFlowData = flowDataService.queryFlowDataInfo(dataId);
        if(crmFlowData == null) {
            return Result.ok(null);
        }
        crmFlowData.setCommentNum(flowCommentService.lambdaQuery().eq(CrmFlowComment::getDataId, crmFlowData.getId()).count());
        return Result.ok(crmFlowData);
    }

    @PostMapping("/saveFlowData")
    @ApiOperation("数据详情页保存阶段信息")
    public Result saveFlowData(@RequestBody CrmFlowData crmFlowData) {
        flowDataService.saveFlowData(crmFlowData);
        return Result.ok();
    }


    @PostMapping("/queryCommentList/{id}")
    @ApiOperation("数据详情页查询评论列表")
    public Result<List<CrmFlowComment>> queryCommentList(@ApiParam("阶段流程信息ID") @PathVariable("id") Long dataId) {
        List<CrmFlowComment> flowCommentList = flowCommentService.queryCommentList(dataId);
        return Result.ok(flowCommentList);
    }

    /**
     * @param comment 评论对象
     * <AUTHOR>
     * 添加评论或者修改
     */
    @PostMapping("/setComment")
    @ApiOperation("数据详情页添加或修改评论")
    public Result<CrmFlowComment> setComment(@RequestBody CrmFlowComment comment) {
        flowCommentService.setComment(comment);
        return R.ok(comment);
    }

    @PostMapping("/deleteComment")
    @ApiOperation("数据详情页删除评论")
    public Result deleteComment(@RequestParam("commentId") Long commentId) {
        CrmFlowComment comment = flowCommentService.getById(commentId);
        if (comment != null) {
            flowCommentService.removeById(commentId);
            flowCommentService.lambdaUpdate().eq(CrmFlowComment::getMainId, commentId).remove();
        }
        return R.ok();
    }

    @PostMapping("/updateFlowDataStatus/{dataId}")
    @ApiOperation("修改流程阶段")
    public Result updateFlowDataStatus(@ApiParam("阶段流程信息ID") @PathVariable("dataId") Long dataId
            , @ApiParam("最终状态") @RequestParam(name = "finalStatus", required = false) Integer finalStatus
            , @RequestParam(name = "remark",required = false) String remark
    ) {
        flowDataService.updateFlowDataStatus(dataId, finalStatus, remark);
        return Result.ok();
    }

    @PostMapping("/queryBusinessSetting")
    @ApiOperation("查询项目可用阶段组")
    public Result<List<Object>> queryBusinessSetting() {
        List<Object> crmFlowList = crmFlowService.queryBusinessSetting();
        return Result.ok(crmFlowList);
    }

    @PostMapping("/queryActiveFlowName/{label}")
    @ApiOperation("查询当前类型下是否存在启用的流程")
    public Result<String> queryActiveFlowName(@PathVariable("label") Integer label, @RequestParam(value = "flowId", required = false) Long flowId) {
        LambdaQueryChainWrapper<CrmFlow> chainWrapper = crmFlowService.lambdaQuery().eq(CrmFlow::getLabel, label).eq(CrmFlow::getStatus, 1);
        if (flowId != null) {
            chainWrapper.ne(CrmFlow::getFlowId, flowId);
        }
        Optional<CrmFlow> crmFlow = chainWrapper.last("limit 1").oneOpt();
        return crmFlow.map(flow -> Result.ok(flow.getFlowName())).orElseGet(() -> Result.ok(null));
    }
}

