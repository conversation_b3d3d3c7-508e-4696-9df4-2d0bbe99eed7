package com.kakarote.crm.controller;


import com.kakarote.core.common.ApiExplain;
import com.kakarote.core.common.Result;
import com.kakarote.core.feign.crm.entity.SimpleCrmInfo;
import com.kakarote.core.feign.examine.entity.ExamineConditionDataBO;
import com.kakarote.core.feign.examine.entity.ExamineMessageBO;
import com.kakarote.crm.service.ICrmExamineRecordService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * 审核记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@RequestMapping("/crmExamineRecord")
@Api(tags = "审批记录")
public class CrmExamineRecordController {

    @Autowired
    private ICrmExamineRecordService examineRecordService;

    @PostMapping("/queryConditionData")
    @ApiExplain("获取条件数据")
    public Result<Map<String, Object>> getDataMapForNewExamine(@RequestBody ExamineConditionDataBO examineConditionDataBO) {
        return Result.ok(examineRecordService.getDataMapForNewExamine(examineConditionDataBO));
    }


    @PostMapping("/updateCheckStatusByNewExamine")
    @ApiExplain("修改状态")
    public Result<Boolean> updateCheckStatusByNewExamine(@RequestBody ExamineConditionDataBO examineConditionDataBO){
        return Result.ok(examineRecordService.updateCheckStatusByNewExamine(examineConditionDataBO));
    }

    @PostMapping("/addMessageForNewExamine")
    @ApiExplain("发送消息")
    public Result addMessageForNewExamine(@RequestBody ExamineMessageBO examineMessageBO){
        examineRecordService.addMessageForNewExamine(examineMessageBO);
        return Result.ok();
    }


    @PostMapping("/getCrmSimpleInfo")
    @ApiExplain("发送消息")
    public Result<SimpleCrmInfo> getCrmSimpleInfo(@RequestBody ExamineConditionDataBO examineConditionDataBO){
        return Result.ok(examineRecordService.getCrmSimpleInfo(examineConditionDataBO));
    }
}

