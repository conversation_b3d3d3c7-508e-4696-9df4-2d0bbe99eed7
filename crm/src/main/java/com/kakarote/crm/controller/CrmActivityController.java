package com.kakarote.crm.controller;


import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.ApiExplain;
import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.PageEntity;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.crm.entity.CrmActivityBO;
import com.kakarote.core.utils.ExcelParseUtil;
import com.kakarote.crm.common.AuthUtil;
import com.kakarote.crm.constant.CrmActivityEnum;
import com.kakarote.crm.constant.CrmAuthEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.BO.CrmActivitySaveBO;
import com.kakarote.crm.entity.VO.CrmActivityVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.service.ICrmActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * crm活动表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
@Slf4j
@RestController
@RequestMapping("/crmActivity")
@Api(tags = "跟进记录模块")
public class CrmActivityController {

    @Autowired
    private ICrmActivityService crmActivityService;

    private static final int TWO=2;
    private static final int THREE=3;
    private static final int FIVE=5;
    private static final int SIX=6;

    @PostMapping("/getCrmActivityPageList")
    @ApiOperation("查询跟进记录列表")
    public Result<BasePage<CrmActivityVO>> getCrmActivityPageList(@RequestBody CrmActivityQueryBO CrmActivityQueryBO) {
        BasePage<CrmActivityVO> pageList = crmActivityService.getCrmActivityPageList(CrmActivityQueryBO);
        return Result.ok(pageList);
    }

    @PostMapping("/addCrmActivityRecord")
    @ApiOperation("添加跟进记录")
    public Result addCrmActivityRecord(@RequestBody CrmActivitySaveBO crmActivityBO) {
        boolean auth = AuthUtil.isCrmAuth(CrmEnum.parse(crmActivityBO.getActivityType()), crmActivityBO.getActivityTypeId(), CrmAuthEnum.READ);
        if (auth) {
            return R.noAuth();
        }
        crmActivityService.addCrmActivityRecord(crmActivityBO);
        return R.ok();
    }

    @PostMapping("/addActivity")
    @ApiExplain("添加活动记录(内部使用)")
    public Result addActivity(@RequestParam("type") Integer type, @RequestParam("activityType") Integer activityType, @RequestParam("activityTypeId") Long activityTypeId) {
        crmActivityService.addActivity(type, CrmActivityEnum.parse(activityType), activityTypeId);
        return R.ok();
    }

    @PostMapping("/addRelationActivity")
    @ApiExplain("生成关联业务活动记录")
    Result addRelationActivity(@RequestBody CrmActivityBO crmActivityBO){
        crmActivityService.addRelationActivity(crmActivityBO);
        return R.ok();
    }

    /**
     * 删除跟进记录
     *
     * @param activityId
     */
    @PostMapping("/deleteCrmActivityRecord/{activityId}")
    @ApiOperation("删除跟进记录")
    public Result deleteCrmActivityRecord(@PathVariable("activityId") Long activityId) {
        crmActivityService.deleteCrmActivityRecord(activityId);
        return Result.ok();
    }

    /**
     * 修改跟进记录
     */
    @PostMapping("/updateActivityRecord")
    @ApiOperation("修改跟进记录")
    public Result updateActivityRecord(@RequestBody CrmActivitySaveBO crmActivity) {
        crmActivityService.updateActivityRecord(crmActivity);
        return Result.ok();
    }

    @PostMapping("/outworkSign")
    @ApiOperation("外勤签到")
    public Result outworkSign(@RequestBody CrmActivitySaveBO crmActivity) {
        crmActivityService.outworkSign(crmActivity);
        return R.ok();
    }


    @PostMapping("/queryOutworkStats")
    @ApiOperation("app外勤统计")
    public Result<BasePage<JSONObject>> queryOutworkStats(PageEntity pageEntity, String startTime, String endTime) {
        BasePage<JSONObject> basePage = crmActivityService.queryOutworkStats(pageEntity, startTime, endTime);
        return R.ok(basePage);
    }

    @PostMapping("/queryOutworkList")
    @ApiOperation("app外勤详情")
    public Result<BasePage<CrmActivityVO>> queryOutworkList(@RequestBody CrmActivityQueryBO CrmActivityQueryBO) {
        BasePage<CrmActivityVO> basePage = crmActivityService.queryOutworkList(CrmActivityQueryBO);
        //BasePage<CrmActivity> basePage = crmActivityService.queryOutworkList(pageEntity, startTime, endTime, userId);
        return R.ok(basePage);
    }


    @PostMapping("/queryPictureSetting")
    @ApiOperation("app查询签到照片上传设置")
    public Result<Integer> queryPictureSetting() {
        Integer integer = crmActivityService.queryPictureSetting();
        return R.ok(integer);
    }


    @PostMapping("/setPictureSetting")
    @ApiOperation("app设置签到照片上传")
    public Result setPictureSetting(Integer status) {
        crmActivityService.setPictureSetting(status);
        return R.ok();
    }


    @PostMapping("/deleteOutworkSign")
    @ApiOperation("删除外勤签到")
    public Result deleteOutworkSign(@RequestParam("id") Long activityId) {
        crmActivityService.deleteOutworkSign(activityId);
        return R.ok();
    }

    @PostMapping("/importRecordList")
    @ApiOperation("excel导入跟进记录")
    public Result<JSONObject> importRecordList(@RequestParam("file") MultipartFile file, @RequestParam("crmType") Integer crmType) {
        JSONObject object = crmActivityService.importRecordList(file,crmType);
        return R.ok(object);
    }


    @PostMapping("/downloadRecordExcel")
    @ApiOperation("获取导入模板")
    public void downloadExcel(HttpServletResponse response,@RequestParam("crmType") Integer crmType) {
        //客户 ：跟进内容（必填）、创建人（必填）、所属客户（必填）、跟进时间、跟进方式、相关联系人、相关项目
        //非客户：跟进内容（必填）、创建人（必填）、所属线索/联系人/项目/合同（必填）、跟进时间、跟进方式
        if (!Arrays.asList(1,TWO,THREE,FIVE,SIX).contains(crmType)){
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
        CrmEnum crmEnum = CrmEnum.parse(crmType);
        List<CrmModelFiledVO> filedList = new ArrayList<>();
        filedList.add(new CrmModelFiledVO("content", FieldEnum.TEXT, "*跟进内容", 1));
        filedList.add(new CrmModelFiledVO("createUserName", FieldEnum.TEXT, "*创建人", 1));
        if (crmType == TWO){
            filedList.add(new CrmModelFiledVO("crmTypeName", FieldEnum.TEXT, "*所属客户", 1));
        }else {
            filedList.add(new CrmModelFiledVO("crmTypeName", FieldEnum.TEXT, "*所属" + crmEnum.getRemarks(), 1));
        }
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATE, "跟进时间-例:2020-2-1", 1));
        filedList.add(new CrmModelFiledVO("category", FieldEnum.TEXT, "跟进方式", 1));
        if (crmType == TWO){
            filedList.add(new CrmModelFiledVO("contactsNames", FieldEnum.TEXT, "相关联系人", 1));
            filedList.add(new CrmModelFiledVO("businessNames", FieldEnum.TEXT, "相关项目", 1));
        }
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet("跟进记录导入表");
        sheet.setDefaultRowHeight((short) 400);
        CellStyle textStyle = wb.createCellStyle();
        DataFormat format = wb.createDataFormat();
        textStyle.setDataFormat(format.getFormat("@"));
        for (int i = 0; i < filedList.size(); i++) {
            CrmModelFiledVO crmModelFiledVO = filedList.get(i);
            sheet.setDefaultColumnStyle(i, textStyle);
            sheet.setColumnWidth(i, 20 * 256);
        }
        try {
            HSSFRow hintRow = sheet.createRow(0);
            String desc = CrmEnum.CONTRACT.equals(crmEnum) ? "编号" : "名称";
            hintRow.createCell(0).setCellValue("注意事项：\n" +
                    "1、表头标“*”的红色字体为必填项\n" +
                    "2、跟进时间：推荐格式为2020-2-1\n" +
                    "3、若相关数据有多条时用“/”区分例如：杭州科技有限公司／卡卡罗特软件科技有限公司\n" +
                    "4、所属" + crmEnum.getRemarks() + "中的" + crmEnum.getRemarks() + "需要存在系统中，" +
                    "且填写的所属" + crmEnum.getRemarks() + desc +"与系统中的" + crmEnum.getRemarks() + desc +"必须保持一致否则会导入失败\n" +
                    "5、创建人为系统员工，请填写系统员工“姓名”，若匹配不到系统员工，则会导致导入失败\n" +
                    "6、如果系统中存在多个" + desc + "重复的情况，会默认导入到最新的数据中");
            hintRow.setHeight((short) 2100);
            CellStyle hintStyle = wb.createCellStyle();
            hintStyle.setWrapText(true);
            hintStyle.setAlignment(HorizontalAlignment.LEFT);
            hintRow.getCell(0).setCellStyle(hintStyle);
            CellRangeAddress hintRegion = new CellRangeAddress(0, 0, 0, filedList.size() - 1);
            sheet.addMergedRegion(hintRegion);

            HSSFRow row = sheet.createRow(1);
            row.setHeight((short) 400);
            for (int i = 0; i < filedList.size(); i++) {
                CrmModelFiledVO crmModelFiledVO = filedList.get(i);
                // 在第一行第一个单元格，插入选项
                HSSFCell cell = row.createCell(i);
                if (crmModelFiledVO.getName().contains("*")){
                    HSSFRichTextString richString = new HSSFRichTextString(crmModelFiledVO.getName());
                    HSSFFont wbFont = wb.createFont();
                    wbFont.setColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
                    richString.applyFont(wbFont);
                    cell.setCellValue(richString);
                    continue;
                }
                // 普通写入操作
                String cellValue = crmModelFiledVO.getName();
                cell.setCellValue(cellValue);
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("UTF-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(crmEnum.getRemarks()+"跟进记录导入模板", "utf-8") + ".xls" );
            wb.write(response.getOutputStream());

        } catch (Exception e) {
            log.error("error",e);
        } finally {
            try {
                wb.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }



    @PostMapping("/exportRecordList")
    @ApiOperation("导出跟进记录")
    public void exportRecordList(@RequestBody CrmActivityQueryBO biParams, HttpServletResponse response){
        Integer crmType = biParams.getActivityType();
        if (!Arrays.asList(1,TWO,THREE,FIVE,SIX).contains(crmType)){
            throw new CrmException(SystemCodeEnum.SYSTEM_NO_VALID);
        }
        CrmEnum crmEnum = CrmEnum.parse(crmType);
        List<Map<String,Object>> objectList = crmActivityService.exportRecordList(biParams);
        List<ExcelParseUtil.ExcelDataEntity> dataList = new ArrayList<>();
        if (crmType == TWO) {
            dataList.add(ExcelParseUtil.toEntity("activityTypeName", "所属客户"));
        }
        dataList.add(ExcelParseUtil.toEntity("content", "跟进内容"));
        dataList.add(ExcelParseUtil.toEntity("createUserName", "跟进人"));
        if (crmType != TWO) {
            dataList.add(ExcelParseUtil.toEntity("activityTypeName", "所属" + crmEnum.getRemarks()));
        }
        dataList.add(ExcelParseUtil.toEntity("createTime", "跟进时间"));
        dataList.add(ExcelParseUtil.toEntity("category", "跟进方式"));
        dataList.add(ExcelParseUtil.toEntity("nextTime", "下次联系时间"));
        if (crmType == TWO) {
            dataList.add(ExcelParseUtil.toEntity("contactsNames", "相关联系人"));
            dataList.add(ExcelParseUtil.toEntity("businessNames", "相关项目"));
        }
        ExcelParseUtil.exportExcel(objectList, new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "跟进记录";
            }
        }, dataList);

    }

}

