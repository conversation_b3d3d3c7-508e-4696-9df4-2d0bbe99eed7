package com.kakarote.crm.controller;


import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.crm.entity.BO.CrmProductCategoryBO;
import com.kakarote.crm.entity.PO.CrmProductCategory;
import com.kakarote.crm.service.ICrmProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 产品分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@RestController
@RequestMapping("/crmProductCategory")
@Api(tags = "产品类型模块接口")
public class CrmProductCategoryController {

    @Autowired
    private ICrmProductCategoryService crmProductCategoryService;

    @PostMapping("/queryList")
    @ApiOperation("查询产品分类列表")
    public Result<List<CrmProductCategoryBO>> queryList(@ApiParam("type") String type) {
        List<CrmProductCategoryBO> list = crmProductCategoryService.queryList(type);
        return R.ok(list);
    }

    @PostMapping("/queryById/{id}")
    @ApiOperation("根据ID查询")
    public Result<CrmProductCategory> queryById(@PathVariable("id") @ApiParam(name = "id", value = "id") Long productId) {
        CrmProductCategory category = crmProductCategoryService.queryById(productId);
        return R.ok(category);
    }

    @PostMapping("/save")
    @ApiOperation("保存")
    public Result save(@RequestBody CrmProductCategory productCategory) {
        crmProductCategoryService.saveAndUpdate(productCategory);
        return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation("保存")
    public Result update(@RequestBody CrmProductCategory productCategory) {
        crmProductCategoryService.saveAndUpdate(productCategory);
        return R.ok();
    }

    @PostMapping("/deleteById/{id}")
    @ApiOperation("删除")
    public Result deleteById(@PathVariable("id") Long id) {
        crmProductCategoryService.deleteById(id);
        return R.ok();
    }
}

