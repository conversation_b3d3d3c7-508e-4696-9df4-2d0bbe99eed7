package com.kakarote.crm.controller;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.BO.CrmInstrumentQueryBO;
import com.kakarote.crm.entity.BO.CrmSearchParamsBO;
import com.kakarote.crm.entity.VO.CrmActivityVO;
import com.kakarote.crm.service.CrmInstrumentService;
import com.kakarote.crm.service.ICrmInstrumentSortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * CRM仪表盘 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
@Slf4j
@RestController
@RequestMapping("/crmInstrument/")
@Api(tags = "仪表盘相关接口")
public class CrmInstrumentController {

    @Autowired
    private ICrmInstrumentSortService sortService;

    @Autowired
    private CrmInstrumentService instrumentService;



    @PostMapping("/queryModelSort")
    @ApiOperation("查询模块排序")
    public Result<JSONObject> queryModelSort() {
        JSONObject object = sortService.queryModelSort();
        return Result.ok(object);
    }

    @PostMapping("/setModelSort")
    @ApiOperation("修改模块排序")
    public Result setModelSort(@RequestBody JSONObject jsonObject) {
        sortService.setModelSort(jsonObject);
        return R.ok();
    }

    @PostMapping("/queryBulletinInfo")
    @ApiOperation("查询销售简报详情")
    public Result<BasePage<Map<String, Object>>> queryBulletinInfo(@RequestBody CrmInstrumentQueryBO biParams) {
        BasePage<Map<String, Object>> basePage = instrumentService.queryBulletinInfo(biParams);
        return R.ok(basePage);
    }


    @PostMapping("/sellFunnelBusinessList")
    @ApiOperation("销售漏斗项目状态列表")
    public Result<BasePage<Map<String, Object>>> sellFunnelBusinessList(@RequestBody CrmSearchParamsBO crmSearchParamsBO) {
        BasePage<Map<String, Object>> mapBasePage = instrumentService.sellFunnelBusinessList(crmSearchParamsBO);
        return R.ok(mapBasePage);
    }


    @PostMapping("/queryRecordCount")
    @ApiOperation("查询销售简报的跟进记录统计")
    public Result<List<JSONObject>> queryRecordCount(@RequestBody BiEntityParams biParams) {
        List<JSONObject> data = instrumentService.queryRecordCount(biParams);
        return Result.ok(data);
    }

    @PostMapping("/queryRecordList")
    @ApiOperation("查询跟进记录统计列表")
    public Result<BasePage<CrmActivityVO>> queryRecordList(@RequestBody CrmActivityQueryBO biParams){
        BasePage<CrmActivityVO> page = instrumentService.queryRecordList(biParams);
        return Result.ok(page);
    }

    @PostMapping("forgottenCustomerPageList")
    @ApiOperation("客户遗忘列表")
    public Result<BasePage<Map<String, Object>>> forgottenCustomerPageList(@RequestBody CrmInstrumentQueryBO biParams){
        BasePage<Map<String, Object>> basePage = instrumentService.forgottenCustomerPageList(biParams);
        return Result.ok(basePage);
    }

    @PostMapping("unContactCustomerPageList")
    @ApiOperation("未联系客户列表")
    public Result<BasePage<Map<String, Object>>> unContactCustomerPageList(@RequestBody CrmInstrumentQueryBO biParams){
        BasePage<Map<String, Object>> basePage = instrumentService.unContactCustomerPageList(biParams);
        return Result.ok(basePage);
    }

    @PostMapping("queryNoRecordCustomerList")
    @ApiOperation("未跟进客户列表")
    public Result<BasePage<Map<String, Object>>> queryNoRecordCustomerList(@RequestBody CrmInstrumentQueryBO biParams){
        BasePage<Map<String, Object>> basePage = instrumentService.queryNoRecordCustomerList(biParams);
        return Result.ok(basePage);
    }

    @PostMapping("queryplanMoneyList")
    @ApiOperation("预计回款")
    public Result queryplanMoneyList(@RequestBody CrmInstrumentQueryBO biParams){
        BasePage<Map<String, Object>> basePage = instrumentService.queryPlanMoneyList(biParams);
        return Result.ok(basePage);
    }

    @PostMapping("queryContendBusinessList")
    @ApiOperation("项目输赢单列表")
    public Result<BasePage<Map<String, Object>> > queryContendBusinessList(@RequestBody CrmInstrumentQueryBO biParams){
        BasePage<Map<String, Object>> basePage = instrumentService.queryContendBusinessList(biParams);
        return Result.ok(basePage);
    }
}
