<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmFlowMapper">

    <select id="queryCrmFlow" parameterType="java.util.Map" resultType="com.kakarote.crm.entity.PO.CrmFlow">
        SELECT
            flow_id,
            flow_name,
            success_name,
            failed_name
        FROM
            wk_crm_flow
        WHERE
           status = 1 and label = #{label}
           AND (
               JSON_CONTAINS( user_ids, '${userId}')
           OR JSON_CONTAINS( dept_ids, '${deptId}')
           OR (JSON_LENGTH( dept_ids )= 0 AND JSON_LENGTH( user_ids )= 0)
               )
            limit 0,1
    </select>
    <select id="queryBusinessSetting" parameterType="java.util.Map" resultType="com.kakarote.crm.entity.PO.CrmFlow">
        SELECT
            flow_id,
            flow_name
        FROM
            wk_crm_flow
        WHERE
            status = 1 and label = '5'
          AND (
                JSON_CONTAINS( user_ids, '${userId}')
                OR JSON_CONTAINS( dept_ids, '${deptId}')
                OR (JSON_LENGTH( dept_ids )= 0 AND JSON_LENGTH( user_ids )= 0)
            )
    </select>

    <select id="queryCrmFlowCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            wk_crm_flow
        WHERE
            status = 1 and label = #{label}
          AND (
                JSON_CONTAINS( user_ids, '${userId}')
                OR JSON_CONTAINS( dept_ids, '${deptId}')
                OR (JSON_LENGTH( dept_ids )= 0 AND JSON_LENGTH( user_ids )= 0)
            )
    </select>
</mapper>
