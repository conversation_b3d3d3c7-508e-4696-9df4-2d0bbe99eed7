package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CrmPrintRecord;
import com.kakarote.crm.entity.PO.CrmPrintTemplate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 打印模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface CrmPrintTemplateMapper extends BaseMapper<CrmPrintTemplate> {

    public void removePrintRecord(@Param("templateId") Long templateId);

    @Select("SELECT a.product_id,a.price,a.sales_price,a.num AS sales_num,a.discount,a.subtotal,b.main_file_ids FROM wk_crm_business_product AS a LEFT JOIN wk_crm_product_detail_img AS b ON a.product_id=b.product_id WHERE business_id=#{id}")
    public List<JSONObject> queryBusinessProduct(@Param("id") Long id);

    @Select("SELECT a.product_id,a.price,a.sales_price,a.num AS sales_num,a.discount,a.subtotal,b.main_file_ids FROM wk_crm_contract_product AS a LEFT JOIN wk_crm_product_detail_img AS b ON a.product_id=b.product_id WHERE contract_id=#{id}")
    public List<JSONObject> queryContractProduct(@Param("id") Long id);

    public List<CrmPrintRecord> queryPrintRecord(@Param("crmType") Integer crmType, @Param("typeId") Long typeId);

}
