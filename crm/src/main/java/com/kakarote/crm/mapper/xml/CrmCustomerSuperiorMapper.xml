<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmCustomerSuperiorMapper">

    <select id="querySuperiorCustomerId" resultType="java.lang.Long">
        select superior_customer_id
        from wk_crm_customer_superior
        where subordinate_customer_id = #{customerId}
    </select>
    <select id="querySubordinateCustomerId" resultType="java.lang.Long">
        select subordinate_customer_id
        from wk_crm_customer_superior
        where superior_customer_id = #{customerId}
    </select>
</mapper>
