package com.kakarote.crm.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CrmCustomerPool;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 公海表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface CrmCustomerPoolMapper extends BaseMapper<CrmCustomerPool> {

    List<Long> queryPoolIdByUserId(@Param("userId") Long userId,@Param("deptId") Long deptId);

    @SqlParser(filter = true)
    Set<Long> putInPoolByRecord(@Param("data") Map<String, Object> record);

    @SqlParser(filter = true)
    Set<Long> putInPoolByBusiness(@Param("data") Map<String, Object> record);

    @SqlParser(filter = true)
    Set<Long> putInPoolByDealStatus(@Param("data") Map<String, Object> record);

}
