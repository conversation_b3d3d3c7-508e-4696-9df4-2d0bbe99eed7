package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.PO.CrmContract;
import com.kakarote.crm.entity.PO.CrmField;
import com.kakarote.crm.entity.PO.CrmReceivablesPlan;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 合同表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface CrmContractMapper extends BaseMapper<CrmContract> {

    /**
     * 通过id查询项目数据
     *
     * @param id     id
     * @param userId 用户ID
     * @return data
     */
    public CrmModel queryById(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询详情页数量
     *
     * @param map map
     * @return crm详情页数量VO
     */
    @SqlParser(filter = true)
    public CrmInfoNumVO queryNum(Map<String, Object> map);

    /**
     * 查询应收账款计划根据合同id
     *
     * @param contractId:合同id
     * @return 回款计划表
     */
    List<CrmReceivablesPlan> queryReceivablesPlansByContractId(Long contractId);

    /**
     * 查询应收账单金钱
     *
     * @param contractId:合同id
     * @return
     */
    public BigDecimal queryReceivablesMoney(@Param("contractId") Long contractId);

    /**
     * 查询回款计划表
     *
     * @param receivablesId:应收账单id
     * @return 回款计划表
     */
    CrmReceivablesPlan queryReceivablesPlansByReceivablesId(Long receivablesId);

    /**
     * 根据合同查询合计
     *
     * @param contractId:合同id
     * @return data
     */
    JSONObject querySubtotalByContractId(@Param("contractId") Long contractId);

    /**
     * 查询产品分页
     *
     * @param contractId:合同id
     * @param parse:合同id
     * @return data
     */
    BasePage<JSONObject> queryProductPageList(BasePage<Object> parse, @Param("contractId") Long contractId);
    /**
     * 查询产品列表
     *
     * @param contractId:合同id
     * @return data
     */
    List<JSONObject> selectPrudctList(@Param("contractId") Long contractId);

    /**
     * 查询返回访问
     *
     * @param parse
     * @param contractId
     * @param conditions
     * @param nameList
     * @return
     */
    BasePage<JSONObject> queryReturnVisit(BasePage<Object> parse, @Param("contractId") Long contractId, @Param("conditions") String conditions, @Param("nameList") List<CrmField> nameList);

    /**
     * 结束合同
     *
     * @param crmEventBO
     * @return
     */
    @SqlParser(filter = true)
    List<String> endContract(@Param("data") CrmEventBO crmEventBO);

    /**
     * 接收合同
     *
     * @param crmEventBO
     * @return data
     */
    @SqlParser(filter = true)
    List<String> receiveContract(@Param("data") CrmEventBO crmEventBO);

    /**
     * 合同结束list
     *
     * @param userId:用户id
     * @param time:时间
     * @param expiringDay:到期天数
     * @return
     */
    List<Long> endContractList(@Param("userId") Long userId, @Param("time") Date time, @Param("expiringDay") Integer expiringDay);

    /**
     * 接收合同list
     *
     * @param userId:用户id
     * @param time:时间
     * @return data
     */
    List<JSONObject> receiveContractList(@Param("userId") Long userId, @Param("time") Date time);

    /**
     * 查询领导
     * @param userId
     * @return
     */
    String selectLeader(@Param("userId") String userId);
}
