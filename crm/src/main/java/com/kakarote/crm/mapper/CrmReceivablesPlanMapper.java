package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.PO.CrmReceivablesPlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 回款计划表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface CrmReceivablesPlanMapper extends BaseMapper<CrmReceivablesPlan> {

    /**
     * 通过id查询回款计划数据
     *
     * @param id id
     * @return data
     */
    public CrmModel queryById(@Param("id") Long id);


    /**
     * 根据合同ID查询回款计划
     * @param contractId 合同ID
     * @return data
     */
    public List<CrmReceivablesPlan> queryReceivablesPlansByContractId(@Param("contractId") Long contractId);

    /**
     * 根据合同ID查询回款计划
     * @param contractId 合同ID
     * @return data
     */
    public BasePage<CrmReceivablesPlan> queryReceivablesPlanListByContractId(BasePage<JSONObject> page,@Param("contractId") Long contractId,@Param("userId")Long userId,@Param("isAdmin") boolean isAdmin);

    /**
     * 批量修改状态
     */
    public List<CrmReceivablesPlan> queryReceivablesPlansList();

    /**
     * 查看回款计划数据ID
     * @param biTimeEntity
     * @return
     */
    List<String> queryPlanMoneyList(@Param("timeEntity") BiParamsUtil.BiTimeEntity biTimeEntity);
}
