package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.utils.BiParamsUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface CrmInstrumentMapper {

    @SqlParser(filter = true)
    public List<String> sellFunnelBusinessList(Map<String,Object> map);

    @SqlParser(filter = true)
    public List<JSONObject> queryRecordCount(@Param("data") BiParamsUtil.BiTimeEntity timeEntity);
}
