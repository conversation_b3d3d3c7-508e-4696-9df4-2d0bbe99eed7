package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.BO.CrmDataCheckBO;
import com.kakarote.crm.entity.BO.CrmRelationPageBO;
import com.kakarote.crm.entity.PO.CrmBusiness;
import com.kakarote.crm.entity.PO.CrmContacts;
import com.kakarote.crm.entity.PO.CrmCustomer;
import com.kakarote.crm.entity.PO.CrmField;
import com.kakarote.crm.entity.VO.CrmDataCheckVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.kakarote.crm.entity.PO.CrmBusiness;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface CrmCustomerMapper extends BaseMapper<CrmCustomer> {
    /**
     * 通过id查询客户数据
     *
     * @param id     id
     * @param userId 用户ID
     * @return data
     */
    public CrmModel queryById(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询是否是只读成员
     *
     * @param id     id
     * @param userId 用户ID
     * @return data
     */
    public Integer queryIsRoUser(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询是否是只读成员
     *
     * @param id id
     * @return data
     */
    @Select("select city_name from wk_crm_area where parent_id = #{id}")
    public List<String> queryCityList(@Param("id") Integer id);

    /**
     * 查询联系人列表
     * @param crmContactsBaseMapper
     * @param customerId
     * @param search
     * @param userIds
     * @param atUserId
     * @param crmAuthEnumValue
     * @param readOnly
     * @return
    */
    public BasePage<CrmModel> queryContacts(BasePage<CrmContacts> crmContactsBaseMapper, @Param("customerId") Long customerId, @Param("search") String search
            , @Param("userIds") List<Long> userIds, @Param("atUserId") Long atUserId,
                                            @Param("crmAuthEnumValue") Integer crmAuthEnumValue, @Param("readOnly") Integer readOnly);

    /**
     * 查询项目
     * @param crmContactsBaseMapper
     * @param customerId
     * @param search
     * @param userIds
     * @param atUserId
     * @param crmAuthEnumValue
     * @param readOnly
     * @return data
    */
    public BasePage<Map<String, Object>> queryBusiness(BasePage<Map<String, Object>> crmContactsBaseMapper, @Param("customerId") Long customerId,
                                                       @Param("search") String search, @Param("userIds") List<Long> userIds, @Param("atUserId") Long atUserId,
                                                       @Param("crmAuthEnumValue") Integer crmAuthEnumValue, @Param("readOnly") Integer readOnly);
    /**
     * 通过客户查询项目信息
     * @return business
     */
    @Select("SELECT count(1) as status,max(create_time) as create_time FROM wk_crm_business WHERE customer_id =#{customerId}")
    public CrmBusiness queryBusinessData(@Param("customerId") Long customerId);
    /**
     * 查询合同
     * @param crmContactsBaseMapper
     * @param customerId
     * @param search
     * @param checkStatus
     * @param userIds
     * @param atUserId
     * @param crmAuthEnumValue
     * @param readOnly
     * @return data
    */
    public BasePage<Map<String, Object>> queryContract(BasePage<Map<String, Object>> crmContactsBaseMapper, @Param("customerId") Long customerId,
                                                       @Param("search") String search, @Param("checkStatus") Integer checkStatus , @Param("userIds") List<Long> userIds, @Param("atUserId") Long atUserId,
                                                       @Param("crmAuthEnumValue") Integer crmAuthEnumValue, @Param("readOnly") Integer readOnly);

    /**
     * 查询详情页数量
     *
     * @param map map
     * @return crm详情页数量VO
     */
    @SqlParser(filter = true)
    public CrmInfoNumVO queryNum(Map<String, Object> map);

    /**
     * 负责数量
     * @param ids
     * @param ownerUserId:负责人id
     * @return
    */
    Integer ownerNum(@Param("ids") List<Long> ids, @Param("ownerUserId") Long ownerUserId);

    /**
     * data检查
     * @param dataCheckBO
     * @return
    */
    @SqlParser(filter = true)
    List<CrmDataCheckVO> dataCheck(@Param("data") CrmDataCheckBO dataCheckBO);

    /**
     * 查询公海根据客户id
     * @param customerId:客户id
     * @return data
    */
    @SqlParser(filter = true)
    String queryPoolIdsByCustomer(@Param("customerId") Long customerId);

    /**
     * 查询应收票据计划
     * @param page
     * @param customerId
     * @param userIds
     * @param atUserId
     * @param crmAuthEnumValue
     * @param readOnly
     * @return data
    */
    BasePage<JSONObject> queryReceivablesPlan(BasePage<JSONObject> page, @Param("customerId") Long customerId, @Param("userIds") List<Long> userIds, @Param("atUserId") Long atUserId,
                                              @Param("crmAuthEnumValue") Integer crmAuthEnumValue, @Param("readOnly") Integer readOnly);


    /**
     * 查询应收账款
     * @param page
     * @param customerId
     * @param userIds
     * @param atUserId
     * @param crmAuthEnumValue
     * @param readOnly
     * @return
    */
    BasePage<JSONObject> queryReceivables(BasePage<JSONObject> page, @Param("customerId") Long customerId, @Param("userIds") List<Long> userIds, @Param("atUserId") Long atUserId,
                                          @Param("crmAuthEnumValue") Integer crmAuthEnumValue, @Param("readOnly") Integer readOnly);

    /**
     * 查询返回访问
     * @param page
     * @param customerId
     * @param userIds
     * @param nameList
     * @return
    */
    BasePage<JSONObject> queryReturnVisit(BasePage<JSONObject> page, @Param("customerId") Long customerId,@Param("userIds") List<Long> userIds, @Param("nameList") List<CrmField> nameList);

    /**
     * 查询账单
     * @param page
     * @param customerId
     * @param userIds
     * @return data
    */
    BasePage<JSONObject> queryInvoice(BasePage<JSONObject> page, @Param("customerId") Long customerId,  @Param("userIds") List<Long> userIds);

    /**
     * 查询账单信息
     * @param page
     * @param customerId
     * @return data
    */
    BasePage<JSONObject> queryInvoiceInfo(BasePage<JSONObject> page, @Param("customerId") Long customerId);

    /**
     * 查询打电话记录
     * @param page
     * @param customerId
     * @return data
    */
    BasePage<JSONObject> queryCallRecord(BasePage<JSONObject> page, @Param("customerId") Long customerId);

    /**
     * 查询输出天数
     * @param customerId
     * @param userId
     * @return
    */
    public Integer queryOutDays(@Param("customerId") Long customerId, @Param("userId") Long userId);

    /**
     * 查询附近的客户
     * @param lng
     * @param lat
     * @param type
     * @param radius
     * @param poolIdList
     * @param ownerUserId
     * @param authUserIdList
     * @return data
    */
    @SqlParser(filter = true)
    List<JSONObject> nearbyCustomer(@Param("lng") String lng, @Param("lat") String lat, @Param("type") Integer type,
                                    @Param("radius") Integer radius, @Param("ownerUserId") Long ownerUserId, @Param("userIds") List<Long> authUserIdList,
                                    @Param("poolIdList") List<Long> poolIdList);

    /**
     * 查询活动客户
     * @param crmEventBO
     * @return data
    */
    @SqlParser(filter = true)
    List<String> eventCustomer(@Param("data") CrmEventBO crmEventBO);

    /**
     * 查询活动客户list
     * @param userId
     * @param time
     * @return
    */
    List<Long> eventCustomerList(@Param("userId") Long userId, @Param("time") Date time);

    /**
     * 遗忘的客户
     * @param day
     * @param userIds
     * @param search
     * @return
    */
    List<Long> forgottenCustomer(@Param("day") Integer day, @Param("userIds") List<Long> userIds, @Param("search") String search);

    /**
     * 非联系人客户
     * @param search
     * @param userIds
     * @return
    */
    List<Long> unContactCustomer(@Param("search") String search, @Param("userIds") List<Long> userIds);

    /**
     * 根据客户id，获取客户摘要
     *
     * @param id 主键ID
     * @return data
     */
    public CrmModel queryDigestById(@Param("customerId") Long id);

    /**
     * 根据客户id，查询客户费用
     *
     * @param page                  page
     * @param crmRelationPageBO     业务参数
     * @param oaExamineRelationType 审批关系类型
     * @return com.kakarote.core.entity.BasePage<com.alibaba.fastjson.JSONObject>
     * <AUTHOR> sir
     * @date 2021/11/19
     */
    BasePage<JSONObject> queryApplyExamine(BasePage<JSONObject> page,
                                           @Param("data") CrmRelationPageBO crmRelationPageBO,
                                           @Param("oaExamineRelationType") int oaExamineRelationType);

    /**
     * 客户费用信息
     *
     * @param id                    客户id
     * @param oaExamineRelationType 审批关系类型
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> sir
     * @return
     */
    JSONObject queryApplyExamineMoney(@Param("customerId") Long id,
                                      @Param("data") CrmRelationPageBO crmRelationPageBO,
                                      @Param("oaExamineRelationType") int oaExamineRelationType);

    /**
     * 根据公司id删除
     * @param tableName
     * @return
    */
    void removeAllData(@Param("tableName") String tableName);

    /**
     * 查看未跟进客户
     * @param userIds
     * @param beginDate
     * @param endDate
     * @return
     */
    List<String> queryNoRecordCustomerList(@Param("userIds") List<Long> userIds, @Param("beginDate") Date beginDate, @Param("endDate") Date endDate);

}
