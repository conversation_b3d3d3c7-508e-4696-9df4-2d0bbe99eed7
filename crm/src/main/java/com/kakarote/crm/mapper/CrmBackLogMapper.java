package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.crm.entity.PO.CrmReceivablesPlan;
import com.kakarote.crm.entity.VO.CrmReceivablesPlanVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface CrmBackLogMapper {

    /**
     * 查询今天客户数量
     *
     * @param map:data
     * @return
     */
    public Integer todayCustomerNum(Map<String, Object> map);

    /**
     * 今天销售数量
     *
     * @param paras:data
     * @return
     */
    Integer todayLeadsNum(Map<String, Object> paras);

    /**
     * 今天线索数量
     *
     * @param paras:data
     * @return
     */
    Integer todayBusinessNum(Map<String, Object> paras);

    /**
     * 今日需联系中，已经逾期的数据数量
     *
     * @param paras 参数
     * @return 数量
     */
    public Integer todayOvertimeNum(Map<String, Object> paras);

    /**
     * 跟进客户数量
     *
     * @param map:data
     * @return
     */
    public Integer followCustomerNum(Map<String, Object> map);

    /**
     * 放入公海记录
     *
     * @param map:data
     * @return data
     */
    public List<JSONObject> putInPoolByRecord(Map<String, Object> map);

    /**
     * 放入公海项目
     *
     * @param map:data
     * @return data
     */
    public List<JSONObject> putInPoolByBusiness(Map<String, Object> map);

    /**
     * 放入公海交易状态
     *
     * @param map:data
     * @return data
     */
    @SqlParser(filter = true)
    public List<JSONObject> putInPoolByDealStatus(Map<String, Object> map);

    /**
     * 跟进销售线索数量
     *
     * @param map:data
     * @return
     */
    public Integer followLeadsNum(Map<String, Object> map);

    /**
     * 合同截止数量
     *
     * @param map:data
     * @return
     */
    public Integer endContractNum(Map<String, Object> map);

    /**
     * 访问提醒数量
     *
     * @param map:data
     * @return
     */
    public Integer returnVisitRemindNum(Map<String, Object> map);

    /**
     * 应收账单计划数量
     *
     * @param map:data
     * @return
     */
    public Integer remindReceivablesPlanNum(Map<String, Object> map);

    /**
     * 提醒应收账款到期数量
     *
     * @param date:时间
     * @param userId:用户id
     * @return
     */
    public Integer remindReceivablesOvertimeNum(@Param("date") Date date, @Param("userId") Long userId);

    /**
     * 提醒应收账款
     *
     * @param parse:
     * @param type:类型
     * @param ids:
     * @param userId:用户id
     * @return crm需要的自定义字段对象
     */
    @SqlParser(filter = true)
    public BasePage<CrmReceivablesPlanVO> remindReceivables(BasePage<CrmReceivablesPlan> parse, @Param("type") Integer type, @Param("ids") List<String> ids, @Param("userId") Long userId);

    /**
     * 查询今天的客户id
     *
     * @param userId:用户id
     * @return
     */
    @Select("select customer_id from wk_crm_customer where to_days(next_time) = to_days(now()) and last_time < next_time and owner_user_id = #{userId} and status != 3")
    public List<Long> queryTodayCustomerId(@Param("userId") Long userId);

    /**
     * 查询跟进销售线索
     *
     * @param userId:用户id
     * @return
     */
    @Select("select leads_id from wk_crm_leads where followup = 0 and is_transform = 0  and owner_user_id = #{userId}")
    public List<Long> queryFollowLeadsId(@Param("userId") Long userId);

    /**
     * 查询跟进的用户id
     *
     * @param userId:用户id
     * @return
     */
    @Select("select customer_id from wk_crm_customer where is_receive = 1 and followup = 0 and status = 1 and owner_user_id = #{userId}")
    public List<Long> queryFollowCustomerId(@Param("userId") Long userId);

    /**
     * 查询交易id根据公海id
     *
     * @param userId:用户id
     * @param type:类型
     * @param model:模块
     * @param poolId:公海id
     * @return
     */
    @Select("select type_id from wk_crm_back_log_deal where create_user_id = #{userId} and crm_type = #{type} and model = #{model} and pool_id = #{poolId}")
    public List<Long> queryDealIdByPoolId(@Param("userId") Long userId, @Param("type") Integer type, @Param("model") Integer model, @Param("poolId") Long poolId);

    /**
     * 查询提醒应收账单计划id
     *
     * @param userId:用户id
     * @return
     */
    @Select("select a.receivables_plan_id from wk_crm_receivables_plan as a inner join wk_crm_customer as b on a.customer_id = b.customer_id\n" +
            "  inner join wk_crm_contract as c on a.contract_id = c.contract_id\n" +
            "  where to_days(a.return_date) >= to_days(now()) and to_days(a.return_date) <= to_days(now())+a.remind and receivables_id is null and c.owner_user_id = #{userId}")
    public List<Long> queryRemindReceivablesPlanId(@Param("userId") Long userId);

    /**
     * 查询结束合同id
     *
     * @param userId:用户id
     * @param remindDay:提醒天数
     * @return
     */
    @Select("select a.contract_id from wk_crm_contract as a inner join wk_crm_customer as b on a.customer_id = b.customer_id where check_status = 1 and to_days(a.end_time) >= to_days(now()) and to_days(a.end_time) <= to_days(now()) + IFNULL(#{remindDay},0) and a.owner_user_id = #{userId}")
    public List<Long> queryEndContractId(@Param("userId") Long userId, @Param("remindDay") Integer remindDay);

    /**
     * 查询返回访问合同id
     *
     * @param userId:用户id
     * @param remindDay:提醒天数
     * @return
     */
    @Select("select contract_id from wk_crm_contract where check_status = 1 and owner_user_id = #{userId} and to_days(now()) - to_days(start_time) >= #{remindDay}")
    public List<Long> queryReturnVisitContractId(@Param("userId") Long userId, @Param("remindDay") Integer remindDay);

    /**
     * 返回访问提醒
     *
     * @param userIds:用户id
     * @param remindDay:提醒天数
     * @return data
     */
    @Select("select contract_id from wk_crm_contract where check_status = 1 and owner_user_id in (${userIds}) and to_days(now()) - to_days(start_time) >= #{remindDay} and not exists (SELECT visit_id FROM wk_crm_return_visit WHERE contract_id = wk_crm_contract.contract_id)")
    public List<String> returnVisitRemind(@Param("userIds") String userIds, @Param("remindDay") String remindDay);

    /**
     * 查询今天销售线索id
     *
     * @param userId:用户id
     * @return
     */
    @Select("select leads_id from wk_crm_leads where to_days(next_time) = to_days(now()) and last_time < next_time and owner_user_id = #{userId}")
    List<Long> queryTodayLeadsId(Long userId);

    /**
     * 查询今天项目id
     *
     * @param userId:用户id
     * @return
     */
    @Select("select business_id from wk_crm_business where to_days(next_time) = to_days(now()) and last_time < next_time and owner_user_id = #{userId} and status != 3")
    List<Long> queryTodayBusinessId(Long userId);

    /**
     * 获取审批id
     *
     * @param dataMap
     * @return
     */
    Long selectExamineRecordId(Map<String, Object> dataMap);
}
