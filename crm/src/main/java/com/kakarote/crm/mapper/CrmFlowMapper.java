package com.kakarote.crm.mapper;

import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CrmFlow;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 阶段流程主信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface CrmFlowMapper extends BaseMapper<CrmFlow> {

    public CrmFlow queryCrmFlow(Map<String, Object> map);

    public List<CrmFlow> queryBusinessSetting(@Param("userId") Long userId, @Param("deptId") Integer deptId);

    public Integer queryCrmFlowCount(@Param("label") Integer label, @Param("userId") Long userId, @Param("deptId") Integer deptId);
}
