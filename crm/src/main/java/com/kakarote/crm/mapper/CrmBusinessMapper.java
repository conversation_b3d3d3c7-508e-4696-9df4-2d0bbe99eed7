package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.PO.CrmBusiness;
import com.kakarote.crm.entity.PO.CrmContacts;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface CrmBusinessMapper extends BaseMapper<CrmBusiness> {
    /**
     * 通过id查询项目数据
     *
     * @param id     id
     * @param userId 用户ID
     * @return data
     */
    public CrmModel queryById(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询联系人
     *
     * @param crmContactsBaseMapper
     * @param businessId:项目id
     * @return
     */
    public BasePage<CrmModel> queryContacts(BasePage<CrmContacts> crmContactsBaseMapper, @Param("businessId") Long businessId);

    /**
     * 查询详情页数量
     *
     * @param map map
     * @return crm详情页数量VO
     */
    @SqlParser(filter = true)
    public CrmInfoNumVO queryNum(Map<String, Object> map);

    /**
     * 根据项目id查询合计
     *
     * @param businessId:项目id
     * @return data
     */
    JSONObject querySubtotalByBusinessId(Long businessId);

    /**
     * 查询产品
     *
     * @param parse
     * @param businessId:项目id
     * @return data
     */
    BasePage<JSONObject> queryProduct(BasePage<Object> parse, @Param("businessId") Long businessId);

    /**
     * 查询合同
     *
     * @param parse
     * @param businessId:项目id
     * @param userIds:用户id
     * @param atUserId
     * @param crmAuthEnumValue
     * @param readOnly
     * @return data
     */
    BasePage<JSONObject> queryContract(BasePage<Object> parse, @Param("businessId") Long businessId, @Param("userIds") List<Long> userIds, @Param("atUserId") Long atUserId,
                                       @Param("crmAuthEnumValue") Integer crmAuthEnumValue, @Param("readOnly") Integer readOnly);

    /**
     * 时间交易项目
     *
     * @param crmEventBO
     * @return
     */
    @SqlParser(filter = true)
    List<String> eventDealBusiness(@Param("data") CrmEventBO crmEventBO);

    /**
     * 时间交易项目列表
     *
     * @param userId:用户id
     * @param time:时间
     * @return
     */
    List<Long> eventDealBusinessPageList(@Param("userId") Long userId, @Param("time") Date time);

    /**
     * 项目事件
     *
     * @param crmEventBO
     * @return
     */
    @SqlParser(filter = true)
    List<String> eventBusiness(@Param("data") CrmEventBO crmEventBO);

    /**
     * 事件项目列表
     *
     * @param userId:用户id
     * @param time:时间
     * @return
     */
    List<Long> eventBusinessPageList(@Param("userId") Long userId, @Param("time") Date time);
}
