package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CallRecord;
import com.kakarote.crm.entity.VO.CrmCallRecordVO;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通话记录管理
 *
 * <AUTHOR>
 */
public interface CallRecordMapper extends BaseMapper<CallRecord> {

    /**
     * 查询通话记录
     *
     * @param number:手机哈
     * @param startTime:起始时间
     * @param ownerUserId:负责人id
     * @return 通话记录表
     */
    CallRecord queryRecord(@Param("number") String number, @Param("startTime") LocalDateTime startTime, @Param("ownerUserId") Long ownerUserId);

    /**
     * 查询通话记录列表
     *
     * @param page:分页信息
     * @param sqlDateFormat:时间
     * @param userIds:用户ids
     * @param talkTime:交流时间
     * @param talkTimeCondition:交流状态
     * @param beginTime:开始时间
     * @param finalTime:结束时间
     * @return data
     */
    @SqlParser(filter = true)
    BasePage<JSONObject> pageCallRecordList(BasePage<JSONObject> page, @Param("sqlDateFormat") String sqlDateFormat,
                                            @Param("userIds") List<Long> userIds, @Param("talkTime") Long talkTime,
                                            @Param("talkTimeCondition") String talkTimeCondition,
                                            @Param("beginTime") Integer beginTime, @Param("finalTime") Integer finalTime);

    /**
     * 查询线索自定义字段
     *
     * @param leadsId:线索id
     * @return data
     */
    List<JSONObject> searchFieldValueByLeadsId(@Param("leadsId") Serializable leadsId);

    /**
     * 查询联系人自定义字段
     *
     * @param contactsId:联系人id
     * @return data
     */
    List<JSONObject> searchFieldValueByContactsId(@Param("contactsId") Long contactsId);

    public CrmCallRecordVO queryCallRecordInfo(@Param("recordId") Long recordId);


    /**
     * 查询电话号name
     *
     * @param model:电话号
     * @param id
     * @return data
     */
    public String queryModelName(@Param("model") String model, @Param("id") Integer id);
}
