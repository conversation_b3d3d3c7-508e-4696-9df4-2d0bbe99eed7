package com.kakarote.crm.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.PO.CrmContacts;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 联系人表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
public interface CrmContactsMapper extends BaseMapper<CrmContacts> {

    /**
     * 通过id查询联系人数据
     *
     * @param id     id
     * @param userId 用户ID
     * @return data
     */
    public CrmModel queryById(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询详情页数量
     *
     * @param map map
     * @return crm详情页数量VO
     */
    @SqlParser(filter = true)
    public CrmInfoNumVO queryNum(Map<String, Object> map);

    /**
     * 查询项目
     *
     * @param parse:data
     * @param contactsId:联系人id
     * @return data
     */
    BasePage<Map<String, Object>> queryBusiness(BasePage<Object> parse, @Param("contactsId") Long contactsId);
}
