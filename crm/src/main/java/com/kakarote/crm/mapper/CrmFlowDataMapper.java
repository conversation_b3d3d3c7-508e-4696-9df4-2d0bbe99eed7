package com.kakarote.crm.mapper;

import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CrmFlowData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 阶段数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
public interface CrmFlowDataMapper extends BaseMapper<CrmFlowData> {

    /**
     * 查询全部数据
     * @param label 数据类型
     * @return data
     */
    List<CrmFlowData> queryAllFlowDataList(@Param("label") Integer label);
}
