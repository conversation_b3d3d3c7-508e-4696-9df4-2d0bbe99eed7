package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.crm.entity.PO.CrmMarketing;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 营销表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface CrmMarketingMapper extends BaseMapper<CrmMarketing> {

    /**
     * 分页查询数据
     *
     * @param parse    分页
     * @param userIds  userIds
     * @param deptIds  部门ids
     * @param crmType  crmType
     * @param search   搜索条件
     * @param isAdmin  是否是管理员
     * @param timeType **
     * @param status   **
     * @return com.kakarote.core.entity.BasePage<com.kakarote.crm.entity.PO.CrmMarketing>
     * @date 2021/11/19
     */
    BasePage<CrmMarketing> queryPageList(BasePage<Object> parse,
                                         @Param("userIds") List<Long> userIds,
                                         @Param("deptIds") List<Long> deptIds,
                                         @Param("crmType") Long crmType,
                                         @Param("search") String search,
                                         @Param("isAdmin") boolean isAdmin,
                                         @Param("timeType") Integer timeType,
                                         @Param("status") Integer status
    );

    BasePage<JSONObject> census(BasePage<Object> parse, @Param("marketingId") Long marketingId, @Param("userIds") List<Long> userIds, @Param("status") Integer status);
}
