<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmReceivablesPlanMapper">
    <select id="queryById" resultType="com.kakarote.crm.common.CrmModel">
        SELECT
        a.*,b.customer_name,c.num as contractNum
        FROM
        wk_crm_receivables_plan AS a
        LEFT JOIN wk_crm_customer as b on a.customer_id = b.customer_id
        LEFT JOIN wk_crm_contract as c on a.contract_id = c.contract_id
        WHERE
        receivables_plan_id = #{id}
    </select>

    <select id="queryReceivablesPlansByContractId" resultType="com.kakarote.crm.entity.PO.CrmReceivablesPlan">
        select a.receivables_plan_id,a.num,a.money,a.return_type,date(a.return_date) as return_date
        from wk_crm_receivables_plan as a join wk_crm_receivables as b on a.receivables_id = b.receivables_id
        where b.receivables_id is null and a.contract_id = #{contractId}
    </select>
    <select id="queryReceivablesPlanListByContractId" resultType="com.kakarote.crm.entity.PO.CrmReceivablesPlan">
        select scrp.receivables_plan_id,scrp.num,scc.customer_id,scc.customer_name ,scco.contract_id,scco.num as contract_num ,scrp.remind,
     scrp.money,scrp.return_date,return_type,
     scrp.remind,scrp.remark,(select receivables_id from wk_crm_receivables where receivables_plan_id = scrp.receivables_plan_id) as receivables_id
                     from wk_crm_receivables_plan as scrp
                    LEFT JOIN wk_crm_customer as scc on scc.customer_id = scrp.customer_id
                     LEFT JOIN wk_crm_contract as scco on scco.contract_id = scrp.contract_id
                    where scrp.contract_id = #{contractId}
                     <if test="isAdmin">
                         and scrp.owner_user_id = #{userId}
                     </if>
    </select>
    <select id="queryReceivablesPlansList" resultType="com.kakarote.crm.entity.PO.CrmReceivablesPlan">
        select *
        from wk_crm_receivables_plan
        where unreceived_money &gt; 0
        and received_status in (0,2)
        and to_days(return_date) &lt; to_days(return_date)
    </select>
    <select id="queryPlanMoneyList" resultType="java.lang.String">
        select
        receivables_plan_id
        from wk_crm_receivables_plan as a
        join wk_crm_contract as b on a.contract_id = b.contract_id
        where b.check_status in (0,1,3,10)
        and a.return_date BETWEEN #{timeEntity.beginDate} and #{timeEntity.endDate}
        AND a.owner_user_id in
        <foreach collection="timeEntity.userIds" index="i" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
