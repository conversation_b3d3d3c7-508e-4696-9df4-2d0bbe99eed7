<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmBackLogMapper">
    <select id="todayCustomerNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_customer
        where to_days(next_time) = to_days(now()) and last_time &lt; next_time and owner_user_id = #{userId} and status != 3
        and customer_id not in (select type_id from wk_crm_back_log_deal where model = 1 and crm_type = 2 and create_user_id = #{userId})
    </select>

    <select id="todayLeadsNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_leads
        where to_days(next_time) = to_days(now()) and last_time &lt; next_time and owner_user_id = #{userId}
        and leads_id not in (select type_id from wk_crm_back_log_deal where model = 11 and crm_type = 1 and create_user_id = #{userId})
        and is_transform = 0
    </select>

    <select id="todayBusinessNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_business
        where to_days(next_time) = to_days(now()) and last_time &lt; next_time and owner_user_id = #{userId}
        and business_id not in (select type_id from wk_crm_back_log_deal where model = 12 and crm_type = 5 and create_user_id = #{userId})
    </select>

    <select id="todayOvertimeNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_${table}
        where next_time &lt; now() and last_time &lt; next_time and owner_user_id = #{userId}
        <if test="type == 1">
            and is_transform = '0'
        </if>
        <if test="type == 2">
            and status != 3
        </if>
        and ${table}_id not in (select type_id from wk_crm_back_log_deal where model = #{model} and crm_type = #{type} and create_user_id = #{userId})
    </select>

    <select id="followCustomerNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_customer as a
        where is_receive = 1 and followup = 0 and status = 1
        and owner_user_id = #{userId}
        and customer_id not in (select type_id from wk_crm_back_log_deal where model = 3 and crm_type = 2 and create_user_id = #{userId})
    </select>

    <select id="putInPoolByRecord" resultType="com.alibaba.fastjson.JSONObject">
        select a.customer_id,(#{limitDay} - (to_days(now()) - to_days(if(a.last_time &gt; a.receive_time,a.last_time,a.receive_time)))) as poolDay
        from wk_crm_customer as a
        <if test="customerLevelSetting == 2">
            left join wk_crm_customer_data as c on a.batch_id = c.batch_id and c.name = 'level'
        </if>
        where (to_days(now()) - to_days(if(a.last_time &gt; a.receive_time,a.last_time,a.receive_time)))
        between (#{limitDay} - #{remindDay}) and #{limitDay}
        and a.status = 1 and a.owner_user_id in
        <foreach collection="ids" index="i" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="dealHandle == 0">
            and a.deal_status = 0
        </if>
        <if test="customerLevelSetting == 2">
            and c.value = #{level}
        </if>
        <if test="businessHandle == 0">
            and (select count(1) from wk_crm_business d where a.customer_id = d.customer_id) = 0
        </if>
    </select>

    <select id="putInPoolByBusiness" resultType="com.alibaba.fastjson.JSONObject">
        select a.customer_id,(#{limitDay} - (to_days(now()) - to_days(a.receive_time))) as poolDay
        from wk_crm_customer as a left join wk_crm_business as b on a.customer_id = b.customer_id and b.status = 1
        <if test="customerLevelSetting != 1">
            left join wk_crm_customer_data as c on a.batch_id = c.batch_id and c.name = 'level'
        </if>
        where (to_days(now()) - to_days(a.receive_time)) between (#{limitDay} - #{remindDay}) and #{limitDay}
        and a.status = 1  and b.business_id is null
        and a.owner_user_id in
        <foreach collection="ids" index="i" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="dealHandle == 0">
            and a.deal_status = 0
        </if>
        <if test="customerLevelSetting == 2">
            and c.value = #{level}
        </if>
    </select>

    <select id="putInPoolByDealStatus" resultType="com.alibaba.fastjson.JSONObject">
        select a.customer_id,(#{limitDay} - (to_days(now()) - to_days(a.receive_time))) as poolDay
        from wk_crm_customer as a
        <if test="customerLevelSetting != 1">
            left join wk_crm_customer_data as c on a.batch_id = c.batch_id and c.name = 'level'
        </if>
        where (to_days(now()) - to_days(a.receive_time)) between (#{limitDay} - #{remindDay}) and #{limitDay}
        and a.status = 1 and a.deal_status = 0
        and a.owner_user_id in
        <foreach collection="ids" index="i" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="customerLevelSetting == 2">
            and c.value = #{level}
        </if>
        <if test="businessHandle == 0">
            and (select count(1) from wk_crm_business d where a.customer_id = d.customer_id) = 0
        </if>
    </select>

    <select id="followLeadsNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_leads as a
        where followup = 0
        and is_transform = 0  and owner_user_id = #{userId} and is_receive = 1
        and leads_id not in (select type_id from wk_crm_back_log_deal where model = 2 and crm_type = 1 and create_user_id = #{userId})
    </select>

    <select id="endContractNum" resultType="java.lang.Integer">
        select count(*) from wk_crm_contract as a inner join wk_crm_customer as b on a.customer_id = b.customer_id
        where check_status = 1 and to_days(a.end_time) &gt;= to_days(now()) and to_days(a.end_time) &lt;= to_days(now()) + IFNULL(#{remindDay},0) and a.owner_user_id = #{userId}
        and contract_id not in (select type_id from wk_crm_back_log_deal where model = 8 and crm_type = 6 and create_user_id = #{userId})
    </select>

    <select id="returnVisitRemindNum" resultType="java.lang.Integer">
        select count(1) from wk_crm_contract
        where check_status = 1 and owner_user_id = #{userId} and to_days(now()) - to_days(start_time) &gt;= #{remindDay}
        and  not exists (SELECT visit_id FROM wk_crm_return_visit WHERE contract_id = wk_crm_contract.contract_id)
        and contract_id not in (select type_id from wk_crm_back_log_deal where model = 9 and crm_type = 6 and create_user_id = #{userId})
    </select>

    <select id="remindReceivablesPlanNum" resultType="java.lang.Integer">
        select count(*)
        from wk_crm_receivables_plan as a inner join wk_crm_customer as b on a.customer_id = b.customer_id
        inner join wk_crm_contract as c on a.contract_id = c.contract_id
        left join wk_crm_receivables d on a.receivables_id = d.receivables_id
        where to_days(a.return_date) &gt;= to_days(now()) and to_days(a.return_date) &lt;= to_days(now())+a.remind
        and (a.receivables_id is null or d.check_status != 1) and c.owner_user_id = #{userId}
        and a.receivables_plan_id not in (select type_id from wk_crm_back_log_deal where model = 7 and crm_type = 8 and create_user_id = #{userId})
    </select>

    <select id="remindReceivables" resultType="com.kakarote.crm.entity.VO.CrmReceivablesPlanVO">
        select a.receivables_plan_id,a.num,a.customer_id,a.contract_id,
               c.num as contractNum,a.money,a.return_date,a.return_type,a.remind,a.remark,
               (select realname from wk_admin_user where user_id = a.owner_user_id) as owner_user_name,
                (select realname from wk_admin_user where user_id = a.create_user_id) as create_user_name,
               a.create_time,
               a.real_received_money,a.real_return_date,a.unreceived_money,
               (case
                when a.received_status = 0 then '待回款'
                when a.received_status = 1 then '回款完成'
                when a.received_status = 2 then '部分回款'
                when a.received_status = 3 then '作废'
                when a.received_status = 4 then '逾期'
                else '待生效' end) as received_status,a.update_time
        from wk_crm_receivables_plan as a inner join wk_crm_contract as c on a.contract_id = c.contract_id
        left join wk_crm_receivables d on a.receivables_id = d.receivables_id
        where 1=1
        <choose>
            <when test="type == 1">
                and to_days(a.return_date) &gt;= to_days(now()) and to_days(a.return_date) &lt;= to_days(now())+a.remind and (a.receivables_id is null or d.check_status != 1)
                <if test="ids.size > 0">
                    and a.receivables_plan_id not in
                    <foreach collection="ids" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
          </when>
            <when test="type == 2">
                and a.receivables_id is not null and d.check_status = 1
            </when>
            <when test="type == 3">
                and to_days(a.return_date) &lt; to_days(now()) and (a.receivables_id is null or d.check_status != 1) and a.received_status = 0
            </when>
            <otherwise>
                and receivables_plan_id='0'
            </otherwise>
        </choose>
        and c.owner_user_id = #{userId}
    </select>

    <select id="remindReceivablesOvertimeNum" resultType="java.lang.Integer">
        select count(1)
        from wk_crm_receivables_plan as a
        inner join wk_crm_contract as c on a.contract_id = c.contract_id
        left join wk_crm_receivables d on a.receivables_id = d.receivables_id
        where a.return_date &lt; #{date} and (a.receivables_id is null or d.check_status != 1)
        and c.owner_user_id = #{userId} and a.received_status = 0
    </select>

    <select id="selectExamineRecordId" resultType="java.lang.Long">
        select examine_record_id
        from wk_crm_${tableName}
        where
        ${idName} = #{id}

    </select>

</mapper>
