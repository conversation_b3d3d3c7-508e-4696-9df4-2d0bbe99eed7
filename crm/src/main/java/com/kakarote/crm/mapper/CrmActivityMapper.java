package com.kakarote.crm.mapper;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseMapper;
import com.kakarote.core.utils.BiParamsUtil;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.PO.CrmActivity;
import com.kakarote.crm.entity.VO.CrmActivityVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * crm活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
public interface CrmActivityMapper extends BaseMapper<CrmActivity> {

    /**
     * 更新下次联系时间
     *
     * @param map map
     */
    public void updateNextTime(Map<String, Object> map);

    /**
     * 更新业务关联表的下次联系时间
     *
     * @param map map
     */
    public void updateRelationNextTime(Map<String, Object> map);

    /**
     * 查询crm记录列表
     *
     * @param basePage:活动记录VO
     * @param timeEntity:BiTimeEntity
     * @param queryBO:活动记录保存BO
     * @return
     */
    public BasePage<CrmActivityVO> getCrmActivityPageList(BasePage<CrmActivityVO> basePage, @Param("timeEntity") BiParamsUtil.BiTimeEntity timeEntity, @Param("data") CrmActivityQueryBO queryBO);

    /**
     * 查询户外状态
     *
     * @param parse:data
     * @param startTime:开始时间
     * @param endTime:结束时间
     * @param userIds:用户ids
     * @return data
     */
    public BasePage<JSONObject> queryOutworkStats(BasePage<JSONObject> parse, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("userIds") List<Long> userIds);

    /**
     * 查询户外list
     *
     * @param parse:crm活动表
     * @param startTime:开始时间
     * @param endTime:结束时间
     * @param userId:用户id
     * @return 活动记录VO
     */
    public BasePage<CrmActivityVO> queryOutworkList(BasePage<CrmActivity> parse, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("userId") Long userId);

    /**
     * 查询关系数据
     *
     * @param activityId:活动id
     * @return
     */
    List<JSONObject> queryRelationData(@Param("activityId") Long activityId);

    /**
     * 查询项目下的产品
     *
     * @param activityTypeId
     * @return
     * <AUTHOR>
     */
    List<JSONObject> queryBusinessProducts(@Param("id") Long activityTypeId);

    /**
     * 查看活动任务下的子任务
     *
     * @param activityTypeId
     * @return
     * <AUTHOR>
     */
    List<JSONObject> querySubtasks(@Param("id") Long activityTypeId);

    /**
     * 查询记录
     *
     * @param parse:data
     * @param biTimeEntity:BiTimeEntity
     * @param biParams:活动记录保存BO
     * @return 活动记录VO
     */
    BasePage<CrmActivityVO> queryRecordList(BasePage<Object> parse, @Param("biTimeEntity") BiParamsUtil.BiTimeEntity biTimeEntity, @Param("data") CrmActivityQueryBO biParams);

}
