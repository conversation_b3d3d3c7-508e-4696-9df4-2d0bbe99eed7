<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmFieldAttentionDataMapper">
    <update id="updateDate">
        update ${data.tableDataName} set `value` =  #{data.value} where id = #{data.dataId}
    </update>

    <select id="queryBatchId" resultType="com.alibaba.fastjson.JSONObject">
        select update_time,batch_id from ${data.tableName}  where ${data.paramName} = #{data.id}
    </select>
    <select id="queryData" resultType="com.alibaba.fastjson.JSONObject">
        select * from ${data.tableDataName} where field_id = #{data.fieldId} and batch_id = #{data.batchId}
            limit 0,1
    </select>
</mapper>
