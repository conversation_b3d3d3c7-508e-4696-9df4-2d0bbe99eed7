<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.crm.mapper.CrmBusinessProductMapper">
    <select id="queryList" resultType="com.kakarote.crm.entity.PO.CrmBusinessProduct">
        select b.product_id,c.name as name,d.name as category_name,b.unit,b.price,b.sales_price,b.num,b.discount,b.subtotal
        from wk_crm_business as a inner join wk_crm_business_product as b on a.business_id = b.business_id
        inner join wk_crm_product as c on b.product_id = c.product_id  and c.status != 3
        inner join wk_crm_product_category as d on c.category_id = d.category_id
        where a.business_id = #{businessId}
    </select>
</mapper>
