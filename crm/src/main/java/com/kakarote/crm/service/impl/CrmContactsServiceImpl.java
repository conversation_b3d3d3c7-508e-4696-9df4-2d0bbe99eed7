package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.*;
import com.kakarote.crm.common.ActionRecordUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.common.ElasticUtil;
import com.kakarote.crm.common.ResourcesUtil;
import com.kakarote.crm.constant.CrmActivityEnum;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.*;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmContactsMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 联系人表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Service
@Slf4j
public class CrmContactsServiceImpl extends BaseServiceImpl<CrmContactsMapper, CrmContacts> implements ICrmContactsService, CrmPageService {

    @Autowired
    private ICrmFieldService crmFieldService;

    @Autowired
    private ICrmContactsDataService crmContactsDataService;

    @Autowired
    private ICrmActivityService crmActivityService;

    @Autowired
    private ICrmActionRecordService crmActionRecordService;

    @Autowired
    private ICrmContactsBusinessService crmContactsBusinessService;

    @Autowired
    private ICrmContactsUserStarService crmContactsUserStarService;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    private ICrmBusinessService crmBusinessService;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private FieldService fieldService;

    private static final String CUSTOMER_ID = "customerId";

    private static final String PARENT_CONTACTS_ID = "parentContactsId";

    private static final String FIELD_TYPE = "fieldType";

    private static final String FIELD_NAME = "fieldName";


    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        return queryField(id, false);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            if (crmModel.get(CUSTOMER_ID) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            List<JSONObject> pContactsList = new ArrayList<>();
            if (crmModel.get(PARENT_CONTACTS_ID) != null && !Objects.equals(crmModel.get(PARENT_CONTACTS_ID), 0)) {
                JSONObject customer = new JSONObject();
                CrmContacts crmContacts = getById(TypeUtils.castToLong(crmModel.get("parentContactsId")));
                if (!ObjectUtil.isEmpty(crmContacts)) {
                    pContactsList.add(customer.fluentPut("contactsId", crmModel.get("parentContactsId")).fluentPut("contactsName", crmContacts.getName()));
                }
            }
            crmModel.put("parentContactsId", pContactsList);
        }
        List<CrmModelFiledVO> filedVOS = crmFieldService.queryField(crmModel);
        if (appendInformation) {
            List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
            filedVOS.addAll(modelFiledVOS);
        }
       /* List<CrmModelFiledVO> modelFiledVOS = new ArrayList<>();
        modelFiledVOS.add(new CrmModelFiledVO("parentContactsId", FieldEnum.CONTACTS, "直属上级", 1).setValue(crmModel.get("parentContactsId")).setSysInformation(1));
        filedVOS.addAll(modelFiledVOS);*/
        return filedVOS;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            if (crmModel.get(CUSTOMER_ID) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            List<JSONObject> contactsList = new ArrayList<>();
            if (crmModel.get(PARENT_CONTACTS_ID) != null && StrUtil.isNotEmpty(crmModel.get(PARENT_CONTACTS_ID).toString()) &&
                    !crmModel.get("parentContactsId").equals(0)) {
                JSONObject contacts = new JSONObject();
                CrmContacts crmContacts = getById((Serializable) crmModel.get("parentContactsId"));
                if (!ObjectUtil.isEmpty(crmContacts)) {
                    contactsList.add(contacts.fluentPut("contactsId", crmModel.get("parentContactsId")).fluentPut("contactsName", crmContacts.getName()));
                }
            }
            crmModel.put("parentContactsId", contactsList);
            // 去除编辑掩码
            crmModel.put("update", true);
        }
        List<List<CrmModelFiledVO>> vos = crmFieldService.queryFormPositionFieldVO(crmModel);
                /*List<CrmModelFiledVO> modelFiledVOS = new ArrayList<>();
        modelFiledVOS.add(new CrmModelFiledVO("parentContactsId", FieldEnum.CONTACTS, "直属上级", 1)
                .setValue(crmModel.get("parentContactsId")).setSysInformation(1).setStylePercent(100).setIsNull(0));
        vos.add(3,modelFiledVOS);*/
        return vos;
    }

    /**
     * 分页查询
     *
     * @param search search
     * @return data
     */
    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        BasePage<Map<String, Object>> basePage = queryList(search, false);
        Long userId = UserUtil.getUserId();
        List<Long> starIds = crmContactsUserStarService.starList(userId);
        basePage.getList().forEach(map -> {
            map.put("star", starIds.contains(map.get("contactsId")) ? 1 : 0);
        });
        return basePage;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public CrmModel queryById(Long id) {
        CrmModel crmModel;
        if (id != null) {
            crmModel = getBaseMapper().queryById(id, UserUtil.getUserId());
            if (crmModel == null) {
                throw new CrmException(CrmCodeEnum.CRM_DATA_DELETED, "联系人");
            }
            if (crmModel.get(PARENT_CONTACTS_ID) != null) {
                crmModel.put("parentContactsId", Long.valueOf(crmModel.get("parentContactsId").toString()).equals(0L) ? null : crmModel.get("parentContactsId"));
            }
            crmModel.setLabel(CrmEnum.CONTACTS.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            crmContactsDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            stringList.forEach(crmModel::remove);
        } else {
            crmModel = new CrmModel(CrmEnum.CONTACTS.getType());
        }
        return crmModel;
    }

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.contacts, action = CrmMsgActionEnum.save)
    public void addOrUpdate(CrmContactsSaveBO crmModel, boolean isExcel) {
        setData(crmModel.getEntity());
        CrmContacts crmContacts = BeanUtil.copyProperties(crmModel.getEntity(), CrmContacts.class);
        String batchId = StrUtil.isNotEmpty(crmContacts.getBatchId()) ? crmContacts.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_contacts_data"));
        crmContactsDataService.saveData(crmModel.getField(), batchId);
        if (StrUtil.isEmpty(crmContacts.getEmail())) {
            crmContacts.setEmail(null);
        }
        // 是否新建
        Boolean isAdd = false;
        if (crmContacts.getContactsId() != null) {
            actionRecordUtil.updateRecord(BeanUtil.beanToMap(getById(crmContacts.getContactsId())), BeanUtil.beanToMap(crmContacts), CrmEnum.CONTACTS, crmContacts.getName(), crmContacts.getContactsId());
            crmContacts.setUpdateTime(LocalDateTimeUtil.now());
            // 上级不能为本人和下级
            if (crmContacts.getParentContactsId() != null) {
                List<Long> list = RecursionUtil.getChildList(list(), "parentContactsId", crmContacts.getContactsId(), "contactsId", "contactsId");
                list.add(crmContacts.getContactsId());
                if (list.contains(crmContacts.getParentContactsId())) {
                    throw new CrmException(CrmCodeEnum.CRM_CONTACTS_REPET_ERROR);
                }
            }

            crmContacts.setParentContactsId(crmContacts.getParentContactsId() == null ? 0 : crmContacts.getParentContactsId());
            updateById(crmContacts);
            crmContacts = getById(crmContacts);
            ElasticUtil.batchUpdateEsData(elasticsearchRestTemplate.getClient(), "contacts", crmContacts.getContactsId().toString(), crmContacts.getName());
        } else {
            isAdd = true;
            crmContacts.setCreateTime(LocalDateTimeUtil.now());
            crmContacts.setUpdateTime(LocalDateTimeUtil.now());
            crmContacts.setCreateUserId(UserUtil.getUserId());
            if (crmContacts.getOwnerUserId() == null) {
                crmContacts.setOwnerUserId(UserUtil.getUserId());
            }
            crmContacts.setBatchId(batchId);
            if (crmContacts.getCustomerId() == null) {
                throw new CrmException(CrmCodeEnum.CRM_CONTACTS_DATA_ERROR);
            }

            save(crmContacts);
            if (crmModel.getBusinessId() != null) {
                crmContactsBusinessService.save(crmModel.getBusinessId(), crmContacts.getContactsId());
            }
            crmActivityService.addActivity(2, CrmActivityEnum.CONTACTS, crmContacts.getContactsId());
            actionRecordUtil.addRecord(crmContacts.getContactsId(), CrmEnum.CONTACTS, crmContacts.getName());
        }
        ICrmCustomerService bean = ApplicationContextHolder.getBean(ICrmCustomerService.class);
        CrmCustomer customer = bean.getById(crmContacts.getCustomerId());
        if (customer != null && customer.getContactsId() == null) {
            customer.setContactsId(crmContacts.getContactsId());
            bean.updateById(customer);
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmContacts));
        savePage(crmModel, crmContacts.getContactsId(), isExcel);

        if (!isExcel && isAdd) {
            // 发送消息
            MsgBodyBO msgBody = new MsgBodyBO();
            msgBody.setMsgKey(IdUtil.simpleUUID());
            msgBody.setMsgTag(getMsgLabelEnum().name());
            msgBody.setAction(CrmMsgActionEnum.save.name());
            msgBody.setCurrentUser(UserUtil.getUser());
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmContacts.getContactsId());
            operateObject.put("name", crmContacts.getName());
            operateObject.put("ownerUserId", crmContacts.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(crmContacts.getOwnerUserId()));
            String title = ResourcesUtil.getMessage("m2", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), "@NAME");
            msgBody.setTitle(title);
            msgBody.setOperateObject(Arrays.asList(operateObject));
            AdminMessageUtil.setMsgBody(msgBody);
        }
    }


    /**
     * 判断类型
     */
    private void setData(Map<String, Object> entity) {
        List<CrmField> fieldList = crmFieldService.lambdaQuery().eq(CrmField::getLabel, getLabel().getType())
                .eq(CrmField::getFieldType, 1).list();
        for (CrmField crmField : fieldList) {
            if (entity.get(crmField.getFieldName()) != null) {
                String value = fieldService.convertObjectValueToString(crmField.getType(),
                        entity.get(crmField.getFieldName()), entity.get(crmField.getFieldName()).toString());
                entity.put(crmField.getFieldName(), value);
            }
        }
    }

    @Override
    public void setOtherField(Map<String, Object> map) {
        String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
        map.put("ownerUserName", ownerUserName);
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);
        String customerName = crmCustomerService.getCustomerName((Long) map.get("customerId"));
        map.put("customerName", customerName);
        Long parentContactsId = TypeUtils.castToLong(map.get("parentContactsId"));
        if (parentContactsId != null && parentContactsId > 0L) {
            CrmContacts crmContacts = getById(parentContactsId);
            if (crmContacts != null) {
                map.put("parentContactsName", crmContacts.getName());
            } else {
                map.put("parentContactsName", null);
            }
        } else {
            map.put("parentContactsName", null);
        }
    }

    @Override
    public Dict getSearchTransferMap() {
        return Dict.create().set("customerId", "customerName").set("customer_id", "customerId").set("parentContactsId", "parentContactsName").set("parent_contacts_id", "parentContactsName");
    }

    /**
     * 删除联系人数据
     *
     * @param ids ids
     */
    @Override
    public void deleteByIds(List<Long> ids) {
        for (Long id : ids) {
            Integer count = ApplicationContextHolder.getBean(ICrmContractService.class).lambdaQuery().eq(CrmContract::getContactsId, id).ne(CrmContract::getCheckStatus, 7).count();
            if (count > 0) {
                throw new CrmException(CrmCodeEnum.CRM_DATA_JOIN_ERROR);
            }
            CrmContacts contacts = getById(id);
            if (contacts != null) {
                //删除自定义字段
                crmContactsBusinessService.removeByContactsId(id);
                LambdaUpdateWrapper<CrmCustomer> wrapper = new LambdaUpdateWrapper<>();
                wrapper.set(CrmCustomer::getContactsId, null).eq(CrmCustomer::getContactsId, contacts.getContactsId());
                ApplicationContextHolder.getBean(ICrmCustomerService.class).update(wrapper);
                LambdaUpdateWrapper<CrmBusiness> businessLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                businessLambdaUpdateWrapper.set(CrmBusiness::getContactsId, null).eq(CrmBusiness::getContactsId, contacts.getContactsId());
                ApplicationContextHolder.getBean(ICrmBusinessService.class).update(businessLambdaUpdateWrapper);
                //删除跟进记录
                crmActivityService.deleteActivityRecord(Collections.singletonList(contacts.getContactsId()));
                //删除字段操作记录
                crmActionRecordService.deleteActionRecord(CrmEnum.CONTACTS, Collections.singletonList(contacts.getContactsId()));
                //删除联系人项目关联
                ApplicationContextHolder.getBean(ICrmContactsBusinessService.class).removeByContactsId(id);
                //删除自定义字段
                crmContactsDataService.deleteByBatchId(Collections.singletonList(contacts.getBatchId()));
                removeById(id);
            }
        }
        deletePage(ids);
    }

    /**
     * 修改联系人负责人
     *
     * @param changeOwnerUserBO 负责人变更BO
     */
    @Override
    @Message(label = CrmMsgLabelEnum.contacts, action = CrmMsgActionEnum.transfer)
    public void changeOwnerUser(CrmChangeOwnerUserBO changeOwnerUserBO) {
        Long newOwnerUserId = changeOwnerUserBO.getOwnerUserId();
        List<Long> ids = changeOwnerUserBO.getIds();
        // 操作对象数据
        List<JSONObject> operateObjects = new ArrayList<>();
        String ownerUserName = UserCacheUtil.getUserName(newOwnerUserId);
        for (Long id : ids) {
            CrmContacts contacts = getById(id);
            actionRecordUtil.addConversionRecord(id, CrmEnum.CONTACTS, newOwnerUserId, contacts.getName());
            // 构建操作对象数据
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", contacts.getContactsId());
            operateObject.put("name", contacts.getName());
            operateObject.put("ownerUserId", newOwnerUserId);
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(newOwnerUserId));
            operateObjects.add(operateObject);
            if (2 == changeOwnerUserBO.getTransferType() && !newOwnerUserId.equals(contacts.getOwnerUserId())) {
                ApplicationContextHolder.getBean(ICrmTeamMembersService.class).addSingleMember(getLabel(), contacts.getContactsId(), contacts.getOwnerUserId(), changeOwnerUserBO.getPower(), changeOwnerUserBO.getExpiresTime(), contacts.getName());
            }
            ApplicationContextHolder.getBean(ICrmTeamMembersService.class).deleteMember(getLabel(), new CrmMemberSaveBO(id, newOwnerUserId));
        }
        LambdaUpdateWrapper<CrmContacts> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CrmContacts::getContactsId, ids);
        wrapper.set(CrmContacts::getOwnerUserId, newOwnerUserId);
        update(wrapper);
        //修改es
        Map<String, Object> map = new HashMap<>();
        map.put("ownerUserId", newOwnerUserId);
        map.put("ownerUserName", ownerUserName);
        updateField(map, ids);

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.transform.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m3", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(),
                "@NAME", ownerUserName);
        msgBody.setTitle(title);
        msgBody.setOperateObject(operateObjects);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    /**
     * 下载导入模板
     *
     * @param response 联系人id
     * @throws IOException exception
     */
    @Override
    public void downloadExcel(HttpServletResponse response) throws IOException {
        List<CrmModelFiledVO> crmModelFiledList = queryField(null);
        int k = 0;
        for (int i = 0; i < crmModelFiledList.size(); i++) {
            if ("name".equals(crmModelFiledList.get(i).getFieldName())) {
                k = i;
                break;
            }
        }
        crmModelFiledList.add(k + 1, new CrmModelFiledVO("ownerUserId", FieldEnum.TEXT, "负责人", 1).setIsNull(1));
        ExcelParseUtil.importExcel(new ExcelParseUtil.ExcelParseService() {
            @Override
            public String getExcelName() {
                return "联系人";
            }
        }, crmModelFiledList, response, "crm");
    }

    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    @Override
    @Message(label = CrmMsgLabelEnum.contacts, action = CrmMsgActionEnum.excelExport)
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls) {
        List<CrmFieldSortVO> headList = crmFieldService.queryListHead(getLabel().getType(), sortIds);
        exportExcel(search, headList, response, isXls, null);
        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.excelExport.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), headList.size());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    /**
     * 标星
     *
     * @param id 联系人id
     */
    @Override
    public void star(Long id) {
        LambdaQueryWrapper<CrmContactsUserStar> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmContactsUserStar::getContactsId, id);
        wrapper.eq(CrmContactsUserStar::getUserId, UserUtil.getUserId());
        CrmContactsUserStar star = crmContactsUserStarService.getOne(wrapper);
        if (star == null) {
            star = new CrmContactsUserStar();
            star.setContactsId(id);
            star.setUserId(UserUtil.getUserId());
            crmContactsUserStarService.save(star);
        } else {
            crmContactsUserStarService.removeById(star.getId());
        }
    }

    @Override
    public List<CrmModelFiledVO> information(Long contactsId) {
        return queryField(contactsId, true);
    }

    /**
     * 查询文件数量
     *
     * @param contactsId id
     * @return data
     */
    @Override
    public CrmInfoNumVO num(Long contactsId) {
        CrmContacts crmContacts = getById(contactsId);
        if (crmContacts == null) {
            throw new CrmException(CrmCodeEnum.CRM_DATA_DELETED, "联系人");
        }
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        List<CrmField> crmFields = crmFieldService.queryFileField();
        List<String> batchIdList = new ArrayList<>();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmContactsData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmContactsData::getValue);
            wrapper.eq(CrmContactsData::getBatchId, crmContacts.getBatchId());
            wrapper.in(CrmContactsData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            batchIdList.addAll(crmContactsDataService.listObjs(wrapper, Object::toString));
        }
        batchIdList.add(crmContacts.getBatchId());
        batchIdList.addAll(crmActivityService.queryFileBatchId(crmContacts.getCustomerId(), getLabel().getType()));
        Map<String, Object> map = new HashMap<>();
        map.put("contactsId", contactsId);
        CrmInfoNumVO infoNumVO = getBaseMapper().queryNum(map);
        infoNumVO.setFileCount(fileService.queryNum(batchIdList).getData());
        infoNumVO.setMemberCount(ApplicationContextHolder.getBean(ICrmTeamMembersService.class).queryMemberCount(getLabel(), crmContacts.getContactsId(), crmContacts.getOwnerUserId()));
        return infoNumVO;
    }

    /**
     * 查询文件列表
     *
     * @param contactsId id
     * @return file
     */
    @Override
    public List<FileEntity> queryFileList(Long contactsId) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        CrmContacts crmContacts = getById(contactsId);
        AdminFileService fileService = ApplicationContextHolder.getBean(AdminFileService.class);
        fileService.queryFileList(crmContacts.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmContactsData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmContactsData::getValue);
            wrapper.eq(CrmContactsData::getBatchId, crmContacts.getBatchId());
            wrapper.in(CrmContactsData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            List<FileEntity> data = fileService.queryFileList(crmContactsDataService.listObjs(wrapper, Object::toString)).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("联系人详情");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        List<String> stringList = crmActivityService.queryFileBatchId(crmContacts.getContactsId(), getLabel().getType());
        if (stringList.size() > 0) {
            List<FileEntity> data = fileService.queryFileList(stringList).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("跟进记录");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        return fileEntityList;
    }

    @Override
    public List<SimpleCrmEntity> querySimpleEntity(List<Long> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        List<CrmContacts> list = lambdaQuery().select(CrmContacts::getContactsId, CrmContacts::getName).in(CrmContacts::getContactsId, ids).list();
        return list.stream().map(crmContacts -> {
            SimpleCrmEntity simpleCrmEntity = new SimpleCrmEntity();
            simpleCrmEntity.setId(crmContacts.getContactsId());
            simpleCrmEntity.setName(crmContacts.getName());
            return simpleCrmEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"name", "telephone", "mobile"};
    }

    /**
     * 获取crm列表类型
     *
     * @return data
     */
    @Override
    public CrmEnum getLabel() {
        return CrmEnum.CONTACTS;
    }

    /**
     * 查询所有字段
     *
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("lastTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("ownerUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("teamMemberIds", FieldEnum.USER, 0));
        return filedList;
    }

    @Override
    public String getContactsName(Long contactsId) {
        return lambdaQuery().select(CrmContacts::getName).eq(CrmContacts::getContactsId, contactsId).oneOpt()
                .map(CrmContacts::getName).orElse("");
    }

    @Override
    public Long getContactsIdByName(String contactsName) {
        CrmContacts crmContacts = lambdaQuery().eq(CrmContacts::getName, contactsName).last("limit 1").one();
        if (crmContacts != null) {
            return crmContacts.getContactsId();
        } else {
            return null;
        }
    }


    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long contactsId = updateInformationBO.getId();
        String parent_contacts_id = "parent_contacts_id";
        int two = 2;
        updateInformationBO.getList().forEach(record -> {
            CrmContacts oldContacts = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldContactsMap = BeanUtil.beanToMap(oldContacts);
            if (record.getInteger(FIELD_TYPE) == 1) {

                //上级限制；不能为本人和下级
                if (parent_contacts_id.equals(StrUtil.toUnderlineCase(record.getString(FIELD_NAME)))) {   //直属上级
                    List<Long> list = RecursionUtil.getChildList(list(), "parentContactsId", contactsId, "contactsId", "contactsId");
                    list.add(contactsId);
                    String value = "value";
                    if (list.contains(record.getLong(value))) {
                        throw new CrmException(CrmCodeEnum.CRM_CONTACTS_REPET_ERROR);
                    }
                }

                Map<String, Object> crmContactsMap = new HashMap<>(oldContactsMap);
                crmContactsMap.put(record.getString("fieldName"), record.get("value"));
                CrmContacts crmContacts = BeanUtil.toBeanIgnoreCase(crmContactsMap, CrmContacts.class, true);
                String address = "address";
                if (address.equals(StrUtil.toUnderlineCase(record.getString(FIELD_NAME)))) {
                    crmContactsMap.put("address", JSONObject.toJSONString(record.get("value")));
                    update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), JSONObject.toJSONString(record.get("value"))).eq("contacts_id", updateInformationBO.getId()).update();
                } else {
                    // 20220223 wwl 删除直接上级提交后，该字段为 '' 空字符串，没有上级，那就设上级为0
                    Object value = record.get("value");
                    if (null == value || "".equals(value)) {
                        value = 0;
                    }
                    update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), value).eq("contacts_id", updateInformationBO.getId()).update();
                }
                actionRecordUtil.updateRecord(oldContactsMap, crmContactsMap, CrmEnum.CONTACTS, crmContacts.getName(), crmContacts.getContactsId());
                String name = "name";
                if (name.equals(record.getString(FIELD_NAME))) {
                    ElasticUtil.batchUpdateEsData(elasticsearchRestTemplate.getClient(), "contacts", crmContacts.getContactsId().toString(), crmContacts.getName());
                }
            } else if (record.getInteger(FIELD_TYPE) == 0 || record.getInteger(FIELD_TYPE) == two) {
                CrmContactsData contactsData = crmContactsDataService.lambdaQuery()
                        .select(CrmContactsData::getValue, CrmContactsData::getId).eq(CrmContactsData::getFieldId, record.get("fieldId"))
                        .eq(CrmContactsData::getBatchId, batchId).one();
                String value = contactsData != null ? contactsData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.CONTACTS, BehaviorEnum.UPDATE, contactsId, oldContacts.getName(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));
                CrmContactsData crmContactsData = new CrmContactsData();
                crmContactsData.setId(contactsData != null ? contactsData.getId() : null);
                crmContactsData.setFieldId(record.getLong("fieldId"));
                crmContactsData.setName(record.getString("fieldName"));
                crmContactsData.setValue(newValue);
                crmContactsData.setCreateTime(LocalDateTimeUtil.now());
                crmContactsData.setBatchId(batchId);
                crmContactsDataService.saveOrUpdate(crmContactsData);

            }
            updateField(record, contactsId);
        });
        this.lambdaUpdate().set(CrmContacts::getUpdateTime, new Date()).eq(CrmContacts::getContactsId, contactsId).update();
    }

    @Override
    public BasePage<Map<String, Object>> queryBusiness(CrmBusinessPageBO businessPageBO) {
        return getBaseMapper().queryBusiness(businessPageBO.parse(), businessPageBO.getContactsId());
    }

    @Override
    public void relateBusiness(CrmRelateBusinessBO relateBusinessBO) {
        crmContactsBusinessService.lambdaUpdate().eq(CrmContactsBusiness::getContactsId, relateBusinessBO.getContactsId()).remove();
        for (Long businessId : relateBusinessBO.getBusinessIds()) {
            CrmContactsBusiness crmContactsBusiness = new CrmContactsBusiness();
            crmContactsBusiness.setContactsId(relateBusinessBO.getContactsId());
            crmContactsBusiness.setBusinessId(businessId);
            crmContactsBusinessService.save(crmContactsBusiness);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unrelateBusiness(CrmRelateBusinessBO relateBusinessBO) {
        //delete from 72crm_crm_contacts_business where contacts_id = #para(contactsId) and business_id in (#fori(ids))
        crmContactsBusinessService.lambdaUpdate().eq(CrmContactsBusiness::getContactsId, relateBusinessBO.getContactsId())
                .in(CrmContactsBusiness::getBusinessId, relateBusinessBO.getBusinessIds()).remove();
        //UPDATE `72crm_crm_business` SET `contacts_id` = NULL WHERE contacts_id = #para(contactsId) and `business_id`  in (#fori(ids))
        crmBusinessService.lambdaUpdate().set(CrmBusiness::getContactsId, null)
                .eq(CrmBusiness::getContactsId, relateBusinessBO.getContactsId())
                .in(CrmBusiness::getBusinessId, relateBusinessBO.getBusinessIds()).update();
    }

}
