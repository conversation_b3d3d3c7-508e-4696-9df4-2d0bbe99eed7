package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.BiEntityParams;
import com.kakarote.crm.entity.BO.CrmActivityQueryBO;
import com.kakarote.crm.entity.BO.CrmInstrumentQueryBO;
import com.kakarote.crm.entity.BO.CrmSearchParamsBO;
import com.kakarote.crm.entity.VO.CrmActivityVO;

import java.util.List;
import java.util.Map;

public interface CrmInstrumentService {

    /**
     * 销售简报的详情
     * @param biParams 参数
     * @return data
     */
    public BasePage<Map<String, Object>> queryBulletinInfo( CrmInstrumentQueryBO biParams);

    /**
     * 销售漏斗项目状态列表
     * @param crmSearchParamsBO
     * @return
     */
    BasePage<Map<String, Object>> sellFunnelBusinessList(CrmSearchParamsBO crmSearchParamsBO);

    /**
     * 查询销售简报的跟进记录统计
     * @param biParams
     * @return
     */
    List<JSONObject> queryRecordCount(BiEntityParams biParams);

    /**
     * 查询跟进记录统计列表
     * @param biParams
     * @return
     */
    BasePage<CrmActivityVO> queryRecordList(CrmActivityQueryBO biParams);

    /**
     * 客户遗忘列表
     * @param biParams
     * @return
     */
    BasePage<Map<String, Object>> forgottenCustomerPageList(CrmInstrumentQueryBO biParams);

    /**
     * 未联系客户列表
     * @param biParams
     * @return
     */
    BasePage<Map<String, Object>> unContactCustomerPageList(CrmInstrumentQueryBO biParams);

    /**
     * 未跟进客户列表
     * @param biParams
     * @return
     */
    BasePage<Map<String, Object>> queryNoRecordCustomerList(CrmInstrumentQueryBO biParams);

    /**
     * 预计回款
     * @param biParams
     * @return
     */
    BasePage<Map<String, Object>> queryPlanMoneyList(CrmInstrumentQueryBO biParams);

    /**
     * 项目输赢单列表
     * @param biParams
     * @return
     */
    BasePage<Map<String, Object>> queryContendBusinessList(CrmInstrumentQueryBO biParams);
}
