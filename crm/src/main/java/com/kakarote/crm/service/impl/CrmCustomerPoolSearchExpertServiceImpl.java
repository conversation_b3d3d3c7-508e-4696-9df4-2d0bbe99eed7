package com.kakarote.crm.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.entity.PO.CrmCustomerPoolSearchExpert;
import com.kakarote.crm.mapper.CrmCustomerPoolSearchExpertMapper;
import com.kakarote.crm.service.ICrmCustomerPoolSearchExpertService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 公海高级筛选外漏查询条件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Service
public class CrmCustomerPoolSearchExpertServiceImpl extends BaseServiceImpl<CrmCustomerPoolSearchExpertMapper, CrmCustomerPoolSearchExpert> implements ICrmCustomerPoolSearchExpertService {


    @Override
    public void saveAndUpdate(CrmCustomerPoolSearchExpert crmSearchExpert) {
        CrmCustomerPoolSearchExpert searchExpert = lambdaQuery()
        .eq(CrmCustomerPoolSearchExpert::getCreateUserId, UserUtil.getUserId()).eq(CrmCustomerPoolSearchExpert::getPoolId,crmSearchExpert.getPoolId()).one();
        if (searchExpert == null){
            crmSearchExpert.setCreateUserId(UserUtil.getUserId());
            save(crmSearchExpert);
        }else {
            searchExpert.setDefaultValue(crmSearchExpert.getDefaultValue());
            updateById(searchExpert);
        }
    }

}
