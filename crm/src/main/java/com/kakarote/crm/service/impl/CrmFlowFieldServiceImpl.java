package com.kakarote.crm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.kakarote.core.common.Const;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.entity.PO.CrmFlowField;
import com.kakarote.crm.mapper.CrmFlowFieldMapper;
import com.kakarote.crm.service.ICrmFieldConfigService;
import com.kakarote.crm.service.ICrmFlowFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 自定义字段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Service
public class CrmFlowFieldServiceImpl extends BaseServiceImpl<CrmFlowFieldMapper, CrmFlowField> implements ICrmFlowFieldService {

    @Autowired
    private ICrmFieldConfigService fieldConfigService;

    @Autowired
    private FieldService fieldService;

    /**
     * 保存阶段流程表单信息
     *
     * @param settingId settingId
     * @param fieldList 字段列表
     */

    @Override
    public Map<String,String> saveFlowField(Long settingId, List<CrmFlowField> fieldList) {
        if (fieldList.isEmpty()) {
            return Collections.emptyMap();
        }
        int i = 0;
        Map<String,String> fieldMap = new HashMap<>(fieldList.size(),1.0F);
        List<String> fieldNameList = new ArrayList<>(fieldList.size());
        for (CrmFlowField crmFlowField : fieldList) {
            if (crmFlowField.getIsHidden().equals(1) && crmFlowField.getIsNull().equals(1)) {
                throw new CrmException(CrmCodeEnum.REQUIRED_OPTIONS_CANNOT_BE_HIDDEN);
            }
            crmFlowField.setSettingId(settingId);
            crmFlowField.setSorting(i++);
            crmFlowField.setFieldId(null);
            Object value = crmFlowField.getDefaultValue();
            if(value instanceof Iterable) {
                crmFlowField.setDefaultValue(StrUtil.join(Const.SEPARATOR,value));
            }
            String nextFieldName = fieldConfigService.getNextFieldName(20, crmFlowField.getType(), fieldNameList, Const.AUTH_DATA_RECURSION_NUM, false);
            fieldNameList.add(nextFieldName);
            fieldMap.put(crmFlowField.getName(),nextFieldName);
            crmFlowField.setFieldName(nextFieldName);
        }
        saveBatch(fieldList);
        return fieldMap;
    }

    /**
     * 查询阶段流程表单信息
     *
     * @param settingId settingId
     * @return data
     */
    @Override
    public List<List<CrmFlowField>> queryFieldList(Long settingId, boolean queryExamine) {
        List<CrmFlowField> flowFields;
        if (queryExamine) {
            flowFields = lambdaQuery().eq(CrmFlowField::getSettingId, settingId).orderByAsc(CrmFlowField::getSorting).list();
        }else {
            flowFields = lambdaQuery().eq(CrmFlowField::getSettingId, settingId)
                    .eq(CrmFlowField::getIsHidden, 0).orderByAsc(CrmFlowField::getSorting).list();
        }
        return fieldService.convertFormPositionFieldList(flowFields, CrmFlowField::getXAxis, CrmFlowField::getYAxis, CrmFlowField::getSorting);
    }
}
