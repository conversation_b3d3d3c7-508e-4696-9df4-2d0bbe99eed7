package com.kakarote.crm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.PO.CrmField;
import com.kakarote.crm.entity.PO.CrmFieldSort;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.mapper.CrmFieldSortMapper;
import com.kakarote.crm.service.ICrmFieldService;
import com.kakarote.crm.service.ICrmFieldSortService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 字段排序表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-19
 */
@Service
public class CrmFieldSortServiceImpl extends BaseServiceImpl<CrmFieldSortMapper, CrmFieldSort> implements ICrmFieldSortService {

    @Autowired
    private ICrmFieldService crmFieldService;

    /**
     * 查询模块字段列表
     *
     * @param label label
     * @return data
     */
    @Override
    public List<CrmFieldSortVO> queryListHead(Integer label) {
        Long userId = UserUtil.getUserId();
        QueryWrapper<CrmFieldSort> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("label", label);
        int number = count(wrapper);
        if (number == 0) {
            saveUserFieldSort(label, userId);
        }
        List<CrmFieldSortVO> crmFieldSortVOS = getBaseMapper().queryListHead(label, userId);
        //加工是否锁定排序
        List<CrmFieldSortVO> collect = crmFieldSortVOS.stream().sorted(Comparator.comparing(CrmFieldSortVO::getIsLock, Comparator.nullsFirst(Integer::compareTo)).reversed()).collect(Collectors.toList());
        List<CrmFieldSortVO> collect1 = crmFieldSortVOS.stream().filter(e -> "项目个数".equals(e.getName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect1)){
            collect = crmFieldSortVOS.stream().filter(e -> !"项目个数".equals(e.getName())).collect(Collectors.toList());
            CrmFieldSortVO crmFieldSortVO = collect1.get(0);
            crmFieldSortVO.setIsLock(1);
            collect.add(0,crmFieldSortVO);
        }
        return collect;
    }

    /**
     * 查询模块字段列表
     *
     * @param label label
     * @return data
     */
    @Override
    public List<CrmFieldSortVO> queryListHead(Integer label, List<Long> ids) {
        Long userId = UserUtil.getUserId();
        QueryWrapper<CrmFieldSort> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("label", label).in("id", ids);
        int number = count(wrapper);
        if (number == 0) {
            saveUserFieldSort(label, userId);
        }
        return getBaseMapper().queryListHeadByIds(label, userId, ids);
    }

    @Override
    public List<CrmFieldSort> queryAllFieldSortList(Integer label, Long userId) {
        List<CrmField> crmFieldList = crmFieldService.list(label, false);
        CrmEnum crmEnum = CrmEnum.parse(label);
        //需要初始化时锁定的字段
        List<String> isLockStrNames = new ArrayList<>();
        switch (crmEnum) {
            case CUSTOMER:
                crmFieldList.add(1,new CrmField("num", "项目个数", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("status", "锁定状态", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("dealStatus", "成交状态", FieldEnum.SELECT));
                crmFieldList.add(new CrmField("lastTime", "最后跟进时间", FieldEnum.DATETIME));
                crmFieldList.add(new CrmField("lastContent", "最后跟进记录", FieldEnum.TEXTAREA));
                crmFieldList.add(new CrmField("detailAddress", "详细地址", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("address", "地区定位", FieldEnum.MAP_ADDRESS));
                crmFieldList.add(new CrmField("poolDay", "距进入公海天数", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("teamMemberIds", "相关团队", FieldEnum.USER));
                crmFieldList.add(new CrmField("contactsName", "首要联系人", FieldEnum.TEXT));
                isLockStrNames.add("customerName");
                break;
            case CONTACTS:
                //crmFieldList.add(new CrmField("parentContactsName", "直属上级", FieldEnum.TEXT));
                isLockStrNames.add("name");
            case BUSINESS:
                crmFieldList.add(new CrmField("lastTime", "最后跟进时间", FieldEnum.DATETIME));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("teamMemberIds", "相关团队", FieldEnum.USER));
                crmFieldList.add(new CrmField("content", "最后跟进记录", FieldEnum.USER));
                isLockStrNames.add("businessName");
                break;
            case CONTRACT:
                crmFieldList.add(new CrmField("checkStatus", "审核状态", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("receivedMoney", "已收款金额", FieldEnum.FLOATNUMBER));
                crmFieldList.add(new CrmField("unreceivedMoney", "未收款金额", FieldEnum.FLOATNUMBER));
                crmFieldList.add(new CrmField("lastTime", "最后跟进时间", FieldEnum.DATETIME));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("teamMemberIds", "相关团队", FieldEnum.USER));
                crmFieldList.add(new CrmField("contractNum", "甲方合同编号", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("isConfidential", "相关团队", FieldEnum.TEXT));
                isLockStrNames.add("name");
                isLockStrNames.add("num");
                break;
            case RECEIVABLES:
                crmFieldList.add(new CrmField("checkStatus", "审核状态", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("contractMoney", "合同金额", FieldEnum.FLOATNUMBER));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("teamMemberIds", "相关团队", FieldEnum.USER));
                isLockStrNames.add("number");
                break;
            case RECEIVABLES_PLAN:
                int three = 3;
                if (crmFieldList.size() > three) {
                    crmFieldList.add(2, new CrmField("num", "期数", FieldEnum.TEXT));
                } else {
                    crmFieldList.add(new CrmField("num", "期数", FieldEnum.TEXT));
                }
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("realReceivedMoney", "实际回款金额", FieldEnum.FLOATNUMBER));
                crmFieldList.add(new CrmField("realReturnDate", "实际回款时间", FieldEnum.DATETIME));
                crmFieldList.add(new CrmField("unreceivedMoney", "未回款金额", FieldEnum.FLOATNUMBER));
                crmFieldList.add(new CrmField("receivedStatus", "回款状态", FieldEnum.SELECT));
                isLockStrNames.add("customerName");
                break;
            case LEADS:
                crmFieldList.add(new CrmField("lastTime", "最后跟进时间", FieldEnum.DATE));
                crmFieldList.add(new CrmField("lastContent", "最后跟进记录", FieldEnum.TEXTAREA));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                isLockStrNames.add("leadsName");
                break;
            case PRODUCT:
                crmFieldList.add(new CrmField("status", "是否上下架", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                isLockStrNames.add("name");
                break;
            case CUSTOMER_POOL:
                crmFieldList.add(new CrmField("lastContent", "最后跟进记录", FieldEnum.TEXTAREA));
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                isLockStrNames.add("customerName");
                break;
            case RETURN_VISIT:
                isLockStrNames.add("visitNumber");
                break;
            case INVOICE:
                crmFieldList.add(new CrmField("ownerUserName", "负责人", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("checkStatus", "审核状态", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("invoiceStatus", "开票状态", FieldEnum.NUMBER));
                crmFieldList.add(new CrmField("invoiceNumber", "发票号码", FieldEnum.TEXT));
                crmFieldList.add(new CrmField("realInvoiceDate", "实际开票日期", FieldEnum.DATE));
                crmFieldList.add(new CrmField("logisticsNumber", "物流单号", FieldEnum.TEXT));
                isLockStrNames.add("invoiceApplyNumber");
            default:
                break;
        }
        if (CrmEnum.RECEIVABLES_PLAN != crmEnum && CrmEnum.RETURN_VISIT != crmEnum) {
            crmFieldList.add(new CrmField("ownerDeptName", "所属部门", FieldEnum.TEXT));
        }
        crmFieldList.add(new CrmField("updateTime", "更新时间", FieldEnum.DATETIME));
        crmFieldList.add(new CrmField("createTime", "创建时间", FieldEnum.DATETIME));
        crmFieldList.add(new CrmField("createUserName", "创建人", FieldEnum.TEXT));

        if (crmEnum == CrmEnum.BUSINESS) {
            crmFieldList.add(new CrmField("flowName", "项目状态组", FieldEnum.TEXT));
            crmFieldList.add(new CrmField("settingName", "项目阶段", FieldEnum.TEXT));
        } else {
            crmFieldList.add(new CrmField("flowName", "流程名称", FieldEnum.TEXT, 1));
            crmFieldList.add(new CrmField("settingName", "阶段名称", FieldEnum.TEXT, 1));
        }

        List<CrmFieldSort> fieldSortList = new ArrayList<>();
        for (int i = 0; i < crmFieldList.size(); i++) {
            CrmFieldSort fieldSort = new CrmFieldSort();
            fieldSort.setFieldId(crmFieldList.get(i).getFieldId());
            fieldSort.setFieldName(parseFieldName(crmFieldList.get(i).getFieldName()));
            fieldSort.setName(crmFieldList.get(i).getName());
            fieldSort.setSort(i);
            fieldSort.setUserId(userId);
            if (crmFieldList.get(i).getIsHidden() == null) {
                fieldSort.setIsHide(0);
            } else {
                fieldSort.setIsHide(crmFieldList.get(i).getIsHidden());
            }
            if (isLockStrNames.contains(fieldSort.getFieldName())) {
                fieldSort.setIsLock(1);
            }
            fieldSort.setLabel(label);
            fieldSort.setType(crmFieldList.get(i).getType());
            fieldSortList.add(fieldSort);
        }
        return fieldSortList;
    }

    /**
     * 保存用户排序
     *
     * @param label  label
     * @param userId 用户ID
     */
    private void saveUserFieldSort(Integer label, Long userId) {
        List<CrmFieldSort> fieldSortList = queryAllFieldSortList(label, userId);
        saveBatch(fieldSortList, Const.BATCH_SAVE_SIZE);
    }

    private String parseFieldName(String fieldName) {
        String contract_id = "contract_id";
        String receivables_plan_id = "receivables_plan_id";
        String _id = "_id";
        if (contract_id.equals(fieldName)) {
            return "contractNum";
        }
        if (receivables_plan_id.equals(fieldName)) {
            return "planNum";
        }
        if (fieldName.endsWith(_id)) {
            fieldName = fieldName.substring(0, fieldName.lastIndexOf("_id")).concat("_name");
        }
        return StrUtil.toCamelCase(fieldName);
    }
}
