package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.BO.CrmCensusBO;
import com.kakarote.crm.entity.BO.CrmMarketingPageBO;
import com.kakarote.crm.entity.BO.CrmSyncDataBO;
import com.kakarote.crm.entity.PO.CrmMarketing;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 营销表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
public interface ICrmMarketingService extends BaseService<CrmMarketing> {

    byte[] BYTES = "2dc587a2016c4bad9f30c1dab32a121a".getBytes();

    long LEADS = 1L, CUSTOMER = 2L;

    String LEADS_STR = "线索", CUSTOMER_STR = "客户";

    /**
     * crmType 1线索 2客户
     */
    List<Long> FIXED_CRM_TYPE = Arrays.asList(LEADS, CUSTOMER);

    void addOrUpdate(CrmMarketing crmMarketing);

    BasePage<CrmMarketing> queryPageList(CrmMarketingPageBO crmMarketingPageBO, Integer status);

    JSONObject queryById(Long marketingId, String device);

    void deleteByIds(List<Long> marketingIds);

    List<CrmModelFiledVO> queryField(Long marketingId);

    void updateStatus(String marketingIds, Integer status);

    void updateShareNum(Long marketingId, Integer num);

    BasePage<JSONObject> census(CrmCensusBO crmCensusBO);

    JSONObject queryAddField(String marketingId);

    void saveMarketingInfo(JSONObject data);

    void syncData(CrmSyncDataBO syncDataBO);
}
