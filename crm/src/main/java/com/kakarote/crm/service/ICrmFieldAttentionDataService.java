package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.PO.CrmFieldAttentionData;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import java.util.Map;

/**
 * <p>
 * 关注度字段存值表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
public interface ICrmFieldAttentionDataService extends BaseService<CrmFieldAttentionData> {

    /**
     * 生成关注度数据
     *
     * @param dataMap 字段值列表
     * @param filedVO 字段信息
     */
    public void saveOrUpdate(CrmEnum crmEnum, CrmModelFiledVO filedVO, Map<String,Object> dataMap, Object id);

    /**
     * 根据规则降低关注度
     */
    public void carryOutAttention();


}
