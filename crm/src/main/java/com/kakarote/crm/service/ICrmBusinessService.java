package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.feign.crm.entity.QueryEventCrmPageBO;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.BO.*;
import com.kakarote.crm.entity.PO.CrmBusiness;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface ICrmBusinessService extends BaseService<CrmBusiness> {

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    public List<CrmModelFiledVO> queryField(Long id);

    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id);
    /**
     * 查询所有数据
     *
     * @param search 搜索对象
     * @return data
     */
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search);

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    public CrmModel queryById(Long id);

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    public void addOrUpdate(CrmBusinessSaveBO crmModel);

    /**
     * 删除项目数据
     *
     * @param ids ids
     */
    public void deleteByIds(List<Long> ids);

    /**
     * 修改项目负责人
     *
     * @param changOwnerUserBO       data
     */
    public void changeOwnerUser(CrmChangeOwnerUserBO changOwnerUserBO);

    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    public void exportExcel(HttpServletResponse response, CrmSearchBO search,List<Long> sortIds,Integer isXls);

    /**
     * 查询项目下联系人
     * @param pageEntity entity
     * @return data
     */
    public BasePage<CrmModel> queryContacts(CrmContactsPageBO pageEntity);

    /**
     * 查询详情信息
     * @param businessId 项目id
     * @return data
     */
    public List<CrmModelFiledVO> information(Long businessId);

    /**
     * 标星
     * @param businessId 项目id
     */
    public void star(Long businessId);

    /**
     * 查询文件数量
     *
     * @param businessId id
     * @return data
     */
    public CrmInfoNumVO num(Long businessId);

    /**
     * 查询文件列表
     * @param businessId id
     * @return file
     */
    public List<FileEntity> queryFileList(Long businessId);

    /**
     * 设置首要联系人
     * @param contactsBO  data
     */
    public void setContacts(CrmFirstContactsBO contactsBO);

    /**
     * 项目关联联系人
     * @param relevanceBusinessBO 业务对象
     */
    public void relateContacts(CrmRelevanceBusinessBO relevanceBusinessBO);

    /**
     * 项目解除+关联联系人
     * @param relevanceBusinessBO 业务对象
     */
    public void unrelateContacts(CrmRelevanceBusinessBO relevanceBusinessBO);

    public List<SimpleCrmEntity> querySimpleEntity(List<Long> ids);

    String getBusinessName(Long businessId);

    void updateInformation(CrmUpdateInformationBO updateInformationBO);

    JSONObject queryProduct(CrmBusinessQueryRelationBO businessQueryProductBO);

    BasePage<JSONObject> queryContract(CrmBusinessQueryRelationBO businessQueryRelationBO);

    List<String> eventDealBusiness(CrmEventBO crmEventBO);

    BasePage<Map<String, Object>> eventDealBusinessPageList(QueryEventCrmPageBO eventCrmPageBO);

    List<String> eventBusiness(CrmEventBO crmEventBO);

    BasePage<Map<String, Object>> eventBusinessPageList(QueryEventCrmPageBO eventCrmPageBO);

    default CrmMsgLabelEnum getMsgLabelEnum() {
        return CrmMsgLabelEnum.business;
    }

    public void updateEs(Map<String,Object> map,Long businessId);

}
