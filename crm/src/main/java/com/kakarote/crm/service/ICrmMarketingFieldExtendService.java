package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmMarketingFieldExtend;

import java.util.List;

/**
 * <p>
 * 市场活动自定义字段明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
public interface ICrmMarketingFieldExtendService extends BaseService<CrmMarketingFieldExtend> {

    /**
     * 查询自定义字段扩展表
     * */
    List<CrmMarketingFieldExtend> queryCrmFieldExtend(Long parentFieldId);


    /**
     * 保存或修改自定义字段扩展表
     * */
    boolean saveOrUpdateCrmFieldExtend(List<CrmMarketingFieldExtend> crmMarketingFieldExtendList, Long parentFieldId,boolean isUpdate);


    /**
     * 删除或添加自定义字段扩展表
     * */
    boolean deleteCrmFieldExtend(Long parentFieldId);

}
