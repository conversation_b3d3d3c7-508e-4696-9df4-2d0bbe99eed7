package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmSearchExpert;

/**
 * <p>
 * 高级筛选外漏查询条件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
public interface ICrmSearchExpertService extends BaseService<CrmSearchExpert> {

    /**
     * 新建默认值
     */
    public void saveAndUpdate(CrmSearchExpert crmSearchExpert);

    /**
     * 根据类型，获取当前默认值
     */
    public CrmSearchExpert queryByLabel(Integer label);

}
