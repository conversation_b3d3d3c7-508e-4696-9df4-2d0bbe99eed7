package com.kakarote.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.entity.PO.CrmContactsBusiness;
import com.kakarote.crm.mapper.CrmContactsBusinessMapper;
import com.kakarote.crm.service.ICrmContactsBusinessService;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * <p>
 * 项目联系人关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Service
public class CrmContactsBusinessServiceImpl extends BaseServiceImpl<CrmContactsBusinessMapper, CrmContactsBusiness> implements ICrmContactsBusinessService {

    /**
     * 保存
     *
     * @param business   项目ID
     * @param contactsId 联系人ID
     */
    @Override
    public void save(Long business, Long contactsId) {
        lambdaUpdate().eq(CrmContactsBusiness::getBusinessId, business).eq(CrmContactsBusiness::getContactsId, contactsId).remove();
//        if (count == 0){
            CrmContactsBusiness contactsBusiness = new CrmContactsBusiness();
            contactsBusiness.setBusinessId(business);
            contactsBusiness.setContactsId(contactsId);
            save(contactsBusiness);
//        }

    }

    /**
     * 根据联系人ID删除联系人项目关联
     *
     * @param contactsId 联系人ID
     */
    @Override
    public void removeByContactsId(Long contactsId) {
        LambdaQueryWrapper<CrmContactsBusiness> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CrmContactsBusiness::getContactsId, contactsId);
        remove(wrapper);
    }

    /**
     * 根据项目ID删除联系人项目关联
     *
     * @param businessId 项目ID
     */
    @Override
    public void removeByBusinessId(Long... businessId) {
        LambdaQueryWrapper<CrmContactsBusiness> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CrmContactsBusiness::getBusinessId, Arrays.asList(businessId));
        remove(wrapper);
    }
}
