package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmSearchDefault;

/**
 * <p>
 * 查询条件默认值 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
public interface ICrmSearchDefaultService extends BaseService<CrmSearchDefault> {

    /**
     * 新建默认值
     */
    public void saveAndUpdate(CrmSearchDefault crmDefault);

    /**
     * 根据类型，获取当前默认值
     */
    public CrmSearchDefault queryByType(CrmSearchDefault crmDefault);

}
