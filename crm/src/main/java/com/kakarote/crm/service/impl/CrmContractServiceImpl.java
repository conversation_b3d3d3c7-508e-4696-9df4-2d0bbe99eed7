package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kakarote.core.common.Const;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.annotation.Message;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.CrmMsgActionEnum;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.common.enums.FieldEnum;
import com.kakarote.core.common.log.BehaviorEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.MsgBodyBO;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminConfig;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.service.AdminFileService;
import com.kakarote.core.feign.admin.service.AdminService;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.feign.crm.entity.QueryEventCrmPageBO;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.feign.examine.entity.ExamineRecordReturnVO;
import com.kakarote.core.feign.examine.entity.ExamineRecordSaveBO;
import com.kakarote.core.feign.examine.service.ExamineService;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.core.utils.*;
import com.kakarote.crm.common.*;
import com.kakarote.crm.constant.*;
import com.kakarote.crm.entity.BO.*;
import com.kakarote.crm.entity.PO.*;
import com.kakarote.crm.entity.VO.CrmFieldSortVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmContractMapper;
import com.kakarote.crm.mapper.CrmReceivablesPlanMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.kakarote.core.servlet.ApplicationContextHolder.getBean;

/**
 * <p>
 * 合同表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Service
@Slf4j
public class CrmContractServiceImpl extends BaseServiceImpl<CrmContractMapper, CrmContract> implements ICrmContractService, CrmPageService {

    @Autowired
    private ICrmFieldService crmFieldService;

    @Value("${product-send}")
    private String url;

    @Autowired
    private ICrmContractDataService crmContractDataService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private ICrmActionRecordService crmActionRecordService;

    @Autowired
    private ICrmActivityService crmActivityService;

    @Autowired
    private ICrmBackLogDealService crmBackLogDealService;

    @Autowired
    private ICrmContractProductService crmContractProductService;

    @Autowired
    private ICrmBusinessProductService crmBusinessProductService;

    @Autowired
    private ICrmReceivablesPlanService crmReceivablesPlanService;

    @Autowired
    private ICrmCustomerService crmCustomerService;

    @Autowired
    private ActionRecordUtil actionRecordUtil;

    @Autowired
    private AdminFileService adminFileService;

    @Autowired
    private ICrmReceivablesService crmReceivablesService;

    @Autowired
    private ExamineService examineService;

    @Autowired
    private FieldService fieldService;
    @Autowired
    private ICrmExamineRecordService crmExamineRecordService;


    @Autowired
    private Redis redis;

    /**
     * 大的搜索框的搜索字段
     *
     * @return fields
     */
    @Override
    public String[] appendSearch() {
        return new String[]{"name", "num", "customerName"};
    }

    /**
     * 获取crm列表类型
     *
     * @return data
     */
    @Override
    public CrmEnum getLabel() {
        return CrmEnum.CONTRACT;
    }

    /**
     * 查询所有字段
     *
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryDefaultField() {
        List<CrmModelFiledVO> filedList = crmFieldService.queryField(getLabel().getType());
        filedList.add(new CrmModelFiledVO("lastTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("updateTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("createTime", FieldEnum.DATETIME, 1));
        filedList.add(new CrmModelFiledVO("ownerUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("createUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("companyUserId", FieldEnum.USER, 1));
        filedList.add(new CrmModelFiledVO("checkStatus", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("contractId", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("receivedMoney", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("unreceivedMoney", FieldEnum.TEXT, 1));
        filedList.add(new CrmModelFiledVO("teamMemberIds", FieldEnum.USER, 0));
        return filedList;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public List<CrmModelFiledVO> queryField(Long id) {
        return queryField(id, false);
    }

    private List<CrmModelFiledVO> queryField(Long id, boolean appendInformation) {
        CrmModel crmModel = queryById(id);
        if (Objects.nonNull(id)&&Objects.nonNull(crmModel)) {
            List<JSONObject> customerList = new ArrayList<>();
            String customerId = "customerId";
            if (crmModel.get(customerId) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            String businessId = "businessId";
            if (crmModel.get(businessId) != null) {
                crmModel.put("businessId", Collections.singletonList(new JSONObject().fluentPut("businessId", crmModel.get("businessId")).fluentPut("businessName", crmModel.get("businessName"))));
            } else {
                crmModel.put("businessId", new ArrayList<>());
            }
            String contactsId = "contactsId";
            if (crmModel.get(contactsId) != null) {
                crmModel.put("contactsId", Collections.singletonList(new JSONObject().fluentPut("contactsId", crmModel.get("contactsId")).fluentPut("name", crmModel.get("contactsName"))));
            } else {
                crmModel.put("contactsId", new ArrayList<>());
            }
        }
        List<CrmModelFiledVO> vos = crmFieldService.queryField(crmModel);
        JSONObject object = new JSONObject();
        if (Objects.nonNull(crmModel)){
            object.fluentPut("discountRate", crmModel.get("discountRate")).fluentPut("product", crmContractProductService.queryList(id)).fluentPut("totalPrice", crmModel.get("totalPrice"));
            if (appendInformation) {
                List<CrmModelFiledVO> modelFiledVOS = appendInformation(crmModel);
                vos.addAll(modelFiledVOS);
            }
        }
        vos.add(new CrmModelFiledVO().setFieldName("product").setName("产品").setValue(object).setFormType("product").setSetting(new ArrayList<>()).setIsNull(0).setFieldType(1));
        return vos;
    }

    @Override
    public List<List<CrmModelFiledVO>> queryFormPositionField(Long id) {
        CrmModel crmModel = queryById(id);
        if (id != null) {
            List<JSONObject> customerList = new ArrayList<>();
            String customerId = "customerId";
            if (crmModel.get(customerId) != null) {
                JSONObject customer = new JSONObject();
                customerList.add(customer.fluentPut("customerId", crmModel.get("customerId")).fluentPut("customerName", crmModel.get("customerName")));
            }
            crmModel.put("customerId", customerList);
            String businessId = "businessId";
            if (crmModel.get(businessId) != null) {
                crmModel.put("businessId", Collections.singletonList(new JSONObject().fluentPut("businessId", crmModel.get("businessId")).fluentPut("businessName", crmModel.get("businessName"))));
            } else {
                crmModel.put("businessId", new ArrayList<>());
            }
            String contactsId = "contactsId";
            if (crmModel.get(contactsId) != null) {
                crmModel.put("contactsId", Collections.singletonList(new JSONObject().fluentPut("contactsId", crmModel.get("contactsId")).fluentPut("name", crmModel.get("contactsName"))));
            } else {
                crmModel.put("contactsId", new ArrayList<>());
            }
            //去除编辑掩码
            crmModel.put("update", true);
        }
        List<List<CrmModelFiledVO>> vos = crmFieldService.queryFormPositionFieldVO(crmModel);
        JSONObject object = new JSONObject();
        object.fluentPut("discountRate", crmModel.get("discountRate")).fluentPut("product", crmContractProductService.queryList(id)).fluentPut("totalPrice", crmModel.get("totalPrice"));
        CrmModelFiledVO crmModelFiledVO = new CrmModelFiledVO().setFieldName("product").setName("产品").setValue(object).setFormType("product").setSetting(new ArrayList<>()).setIsNull(0).setFieldType(1);
        crmModelFiledVO.setStylePercent(100);
        vos.add(ListUtil.toList(crmModelFiledVO));
        return vos;
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    @Override
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search) {
        CrmSearchBO search1 = ObjectUtil.cloneByStream(search);
        BasePage<Map<String, Object>> basePage = queryList(search, false);
        for (Map<String, Object> map : basePage.getList()) {
            Double contractMoney = StrUtil.isNotEmpty(map.get("money").toString()) ? Double.parseDouble(map.get("money").toString()) : 0D;
            BigDecimal receivedProgress = new BigDecimal(100);
            if (!contractMoney.equals(0D)) {
                receivedProgress = BigDecimal.valueOf(map.get("receivedMoney") != null ? Double.parseDouble(map.get("receivedMoney").toString()) : 0D).divide(new BigDecimal(contractMoney), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            }
            map.put("receivedProgress", receivedProgress);
        }
        SearchRequest searchRequest = new SearchRequest(getIndex());
        searchRequest.types(getDocType());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder queryBuilder = createQueryBuilder(search1);
        queryBuilder.must(QueryBuilders.termsQuery("checkStatus", Arrays.asList(1, 10)));
        sourceBuilder.aggregation(AggregationBuilders.sum("contractMoney").field("money"))
                .aggregation(AggregationBuilders.sum("receivedMoney").field("receivedMoney"))
                .aggregation(AggregationBuilders.sum("unreceivedMoney").field("unreceivedMoney"));
        setCrmDataAuth(queryBuilder);
        sourceBuilder.query(queryBuilder);
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse searchCount = elasticsearchRestTemplate.getClient().search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = searchCount.getAggregations();
            Map<String, Object> countMap = new HashMap<>();
            ParsedSum contractMoney = aggregations.get("contractMoney");
            ParsedSum receivedMoney = aggregations.get("receivedMoney");
            ParsedSum unreceivedMoney = aggregations.get("unreceivedMoney");
            countMap.put("contractMoney", contractMoney.getValue());
            countMap.put("receivedMoney", receivedMoney.getValue());
            countMap.put("unReceivedMoney", unreceivedMoney.getValue());
            basePage.setExtraData(new JSONObject().fluentPut("money", countMap));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return basePage;
    }

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    @Override
    public CrmModel queryById(Long id) {
        CrmModel crmModel;
        if (id != null) {
            crmModel = getBaseMapper().queryById(id, UserUtil.getUserId());
            String createUserId = crmModel.get("createUserId").toString();
            List<String> data = getLeaders(createUserId);
            data.add(createUserId);
//            List<String> data = adminFileService.getLeaders(createUserId).getData();
            List<String> collect = data.stream().filter(e -> e.equals(UserUtil.getUserId().toString())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)&&!createUserId.equals(UserUtil.getUserId().toString())){
                return null;
            }
            crmModel.setLabel(CrmEnum.CONTRACT.getType());
            crmModel.setOwnerUserName(UserCacheUtil.getUserName(crmModel.getOwnerUserId()));
            List<String> nameList = StrUtil.splitTrim((String) crmModel.get("companyUserId"), Const.SEPARATOR);
            String name = nameList.stream().map(str -> UserCacheUtil.getUserName(Long.valueOf(str))).collect(Collectors.joining(Const.SEPARATOR));
            crmModel.put("companyUserName", name);
            crmModel.put("createUserName", UserCacheUtil.getUserName((Long) crmModel.get("createUserId")));
            crmContractDataService.setDataByBatchId(crmModel);
            List<String> stringList = ApplicationContextHolder.getBean(ICrmRoleFieldService.class).queryNoAuthField(crmModel.getLabel());
            String customerId = "customerId";
            if (stringList.contains(customerId)) {
                stringList.add("customerName");
            }
            stringList.forEach(crmModel::remove);
            Double contractMoney = crmModel.get("money") != null ? Double.parseDouble(crmModel.get("money").toString()) : 0D;
            BigDecimal receivedProgress = new BigDecimal(100);
            Double eq = 0D;
            if (!contractMoney.equals(eq)) {
                receivedProgress = new BigDecimal(crmModel.get("receivedMoney") != null ? Double.parseDouble(crmModel.get("receivedMoney").toString()) : 0D).divide(new BigDecimal(contractMoney), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            }
            crmModel.put("receivedProgress", receivedProgress);
        } else {
            crmModel = new CrmModel(CrmEnum.CONTRACT.getType());
        }
        return crmModel;
    }

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.contract, action = CrmMsgActionEnum.save)
    public void addOrUpdate(CrmContractSaveBO crmModel) {
        CrmContract crmContract = BeanUtil.copyProperties(crmModel.getEntity(), CrmContract.class);
        String batchId = StrUtil.isNotEmpty(crmContract.getBatchId()) ? crmContract.getBatchId() : IdUtil.simpleUUID();
        actionRecordUtil.updateRecord(crmModel.getField(), Dict.create().set("batchId", batchId).set("dataTableName", "wk_crm_contract_data"));
        crmContractDataService.saveData(crmModel.getField(), batchId);
        if (crmContract.getStartTime() != null && crmContract.getEndTime() != null && crmContract.getStartTime().compareTo(crmContract.getEndTime()) > 0) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_DATE_ERROR);
        }
        if (crmContract.getDiscountRate() == null) {
            crmContract.setDiscountRate(new BigDecimal("0"));
        }
        if (crmContract.getMoney() == null) {
            crmContract.setMoney(BigDecimal.ZERO);
        }
        ExamineRecordSaveBO examineRecordSaveBO = crmModel.getExamineFlowData();
        ExamineRecordReturnVO examineData = null;
        // 是否新建
        Boolean isAdd = false;

        //提取ID供admin发送消息缓存使用
        Long contractId = crmContract.getContractId() == null ? BaseUtil.getNextId() : crmContract.getContractId();

        if (!CollectionUtil.isEmpty(crmModel.getProduct())) {
            crmContract.setProducts(crmModel.getProduct().stream().map(crm -> new JSONObject().fluentPut("id", crm.getProductId()).fluentPut("name", crm.getName())).collect(Collectors.toList()));
        }
        //把数据放入缓存，供admin系统消息展示预览数据使用
        redis.setNx(CrmCacheKey.CRM_ADMIN_MESSAGE + contractId, 60L, BeanUtil.beanToMap(crmContract));

        if (crmContract.getContractId() == null) {
            if (crmContract.getOwnerUserId() == null) {
                crmContract.setOwnerUserId(UserUtil.getUserId());
            }
            crmContract.setUnreceivedMoney(crmContract.getMoney());
            crmContract.setReceivedMoney(BigDecimal.ZERO);
            crmContract.setCreateUserId(UserUtil.getUserId());
            crmContract.setBatchId(batchId);
            crmContract.setCreateTime(LocalDateTimeUtil.now());
            crmContract.setUpdateTime(LocalDateTimeUtil.now());
            crmContract.setOwnerUserId(UserUtil.getUserId());
            final Optional<CrmModelFiledVO> modelFiledOptional = queryDefaultField().stream().filter(data -> FieldEnum.SERIAL_NUMBER.getType().equals(data.getType())).findFirst();
            if (modelFiledOptional.isPresent()) {
                Map<String, Object> map = BeanUtil.beanToMap(crmContract);
                crmModel.getField().forEach(field -> {
                    map.put(field.getFieldName(), field.getValue());
                });
                final String generateNumber = getBean(ICrmFieldNumberDataService.class).generateNumber(getLabel(), modelFiledOptional.get(), map);
                crmContract.setNum(generateNumber);
            }
            crmContract.setContractId(contractId);
            save(crmContract);
            int five = 5;
            if (crmContract.getCheckStatus() != null && crmContract.getCheckStatus() == five) {
                crmContract.setCheckStatus(5);
            } else {
                this.supplementFieldInfo(1, crmContract.getContractId(), null, examineRecordSaveBO);
                examineRecordSaveBO.setTitle(crmContract.getName());
                examineData = examineService.addExamineRecord(examineRecordSaveBO).getData();
                crmContract.setExamineRecordId(examineData.getRecordId());
                crmContract.setCheckStatus(examineData.getExamineStatus());
            }
            int contractStatus = 10;
            if (crmContract.getCheckStatus() == 1 || crmContract.getCheckStatus() == contractStatus) {
                CrmCustomer customer = ApplicationContextHolder.getBean(ICrmCustomerService.class).getById(crmContract.getCustomerId());
                customer.setDealStatus(1);
                LocalDateTime dealTime = crmContract.getOrderDate() != null ? crmContract.getOrderDate() : LocalDateTimeUtil.now();
                customer.setDealTime(dealTime);
                ApplicationContextHolder.getBean(ICrmCustomerService.class).updateById(customer);
                Map<String, Object> map = new HashMap<>();
                map.put("dealTime", DateUtil.formatLocalDateTime(dealTime));
                map.put("dealStatus", 1);
                ElasticUtil.updateField(elasticsearchRestTemplate, map, customer.getCustomerId(), CrmEnum.CUSTOMER.getIndex());
            }
            updateById(crmContract);
            crmActivityService.addActivity(2, CrmActivityEnum.CONTRACT, crmContract.getContractId());
            actionRecordUtil.addRecord(crmContract.getContractId(), CrmEnum.CONTRACT, crmContract.getName());
        } else {
            isAdd = true;
            CrmContract contract = getById(crmContract.getContractId());
            int contractCheckStatus = 8;
            if (contract.getCheckStatus() == contractCheckStatus) {
                contract.setCheckStatus(5);
            }
            if (contract.getCheckStatus() == 1) {
                throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EXAMINE_PASS_HINT_ERROR);
            }
            if (contract.getCheckStatus() == contractCheckStatus) {
                throw new CrmException(CrmCodeEnum.CRM_CONTRACT_CANCELLATION_ERROR);
            }
            int two = 2;
            int four = 4;
            int five = 5;
            int ten = 10;
            if (!Arrays.asList(two, four, five, ten).contains(contract.getCheckStatus())) {
                throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
            }
            if (crmContract.getCheckStatus() != null && crmContract.getCheckStatus() == five) {
                crmContract.setCheckStatus(5);
            } else {
                this.supplementFieldInfo(1, crmContract.getContractId(), contract.getExamineRecordId(), examineRecordSaveBO);
                examineRecordSaveBO.setTitle(crmContract.getName());
                examineData = examineService.addExamineRecord(examineRecordSaveBO).getData();
                crmContract.setExamineRecordId(examineData.getRecordId());
                crmContract.setCheckStatus(examineData.getExamineStatus());
            }
            BigDecimal decimal = getBaseMapper().queryReceivablesMoney(crmContract.getContractId());
            crmContract.setUnreceivedMoney(crmContract.getMoney().subtract(decimal));
            crmContract.setReceivedMoney(decimal);
            crmBackLogDealService.deleteByTypes(null, CrmEnum.CONTRACT, crmContract.getContractId(), CrmBackLogEnum.END_CONTRACT, CrmBackLogEnum.REMIND_RETURN_VISIT_CONTRACT, CrmBackLogEnum.CHECK_CONTRACT);
            crmContract.setUpdateTime(LocalDateTimeUtil.now());
            actionRecordUtil.updateRecord(BeanUtil.beanToMap(contract), BeanUtil.beanToMap(crmContract), CrmEnum.CONTRACT, crmContract.getName(), crmContract.getContractId());
            updateById(crmContract);
            crmContract = getById(crmContract.getContractId());
            ElasticUtil.batchUpdateEsData(elasticsearchRestTemplate.getClient(), "contract", crmContract.getContractId().toString(), crmContract.getName());

        }

        //判断当前是否提交审核，提交审核需要生成一条系统通知
        if (0 == crmContract.getCheckStatus()) {
            if (examineData != null) {
                actionRecordUtil.addCrmExamineActionRecord(CrmEnum.CONTRACT, examineData.getRecordId(), BehaviorEnum.SUBMIT_EXAMINE, crmContract.getNum());
            }
        }
        int three = 3;
        int ten = 10;
        if (three == crmContract.getCheckStatus() || 0 == crmContract.getCheckStatus() || ten == crmContract.getCheckStatus()) {
            List<Integer> statuss = new ArrayList<>();
            statuss.add(3);
            List<CrmReceivablesPlan> planList = crmReceivablesPlanService.lambdaQuery().eq(CrmReceivablesPlan::getContractId, crmContract.getContractId())
                    .in(CrmReceivablesPlan::getReceivedStatus, statuss).list();
            if (planList.size() > 0) {
                crmReceivablesPlanService.lambdaUpdate().set(CrmReceivablesPlan::getReceivedStatus, 5)
                        .eq(CrmReceivablesPlan::getContractId, crmContract.getContractId())
                        .in(CrmReceivablesPlan::getReceivedStatus, statuss).update();
                for (CrmReceivablesPlan plan : planList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("receivedStatus", 5);
                    map.put("updateTime", DateUtil.formatDateTime(new Date()));
                    ElasticUtil.updateField(getRestTemplate(), map, plan.getReceivablesPlanId(), CrmEnum.RECEIVABLES_PLAN.getIndex());
                }
            }
        }
        List<CrmContractProduct> contractProductList = crmModel.getProduct();
        crmContractProductService.deleteByContractId(crmContract.getContractId());
        /*if (crmContract.getBusinessId() != null) {
            crmBusinessProductService.deleteByBusinessId(crmContract.getBusinessId());
        }*/
        if (contractProductList != null) {
            for (CrmContractProduct crmContractProduct : contractProductList) {
                crmContractProduct.setContractId(crmContract.getContractId());
                crmContractProductService.save(crmContractProduct);
                /*if (crmContract.getBusinessId() != null) {
                    CrmBusinessProduct crmBusinessProduct = BeanUtil.copyProperties(crmContractProduct, CrmBusinessProduct.class);
                    crmBusinessProduct.setRId(null);
                    crmBusinessProduct.setBusinessId(crmContract.getBusinessId());
                    crmBusinessProductService.save(crmBusinessProduct);
                }*/
            }
        }
        crmModel.setEntity(BeanUtil.beanToMap(crmContract));
        savePage(crmModel, crmContract.getContractId(), false);

        if (isAdd) {
            // 发送消息
            MsgBodyBO msgBody = new MsgBodyBO();
            msgBody.setMsgKey(IdUtil.simpleUUID());
            msgBody.setMsgTag(getMsgLabelEnum().name());
            msgBody.setAction(CrmMsgActionEnum.save.name());
            msgBody.setCurrentUser(UserUtil.getUser());
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", crmContract.getContractId());
            operateObject.put("name", crmContract.getName());
            operateObject.put("ownerUserId", crmContract.getOwnerUserId());
            operateObject.put("ownerUserName", UserCacheUtil.getUserName(crmContract.getOwnerUserId()));
            String title = ResourcesUtil.getMessage("m2", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), "@NAME");
            msgBody.setTitle(title);
            msgBody.setOperateObject(Arrays.asList(operateObject));
            AdminMessageUtil.setMsgBody(msgBody);
        }
    }

    @Autowired
    private ICrmBusinessService crmBusinessService;

    @Autowired
    private ICrmContactsService crmContactsService;

    @Override
    public void setOtherField(Map<String, Object> map) {
        String customerName = crmCustomerService.getCustomerName(Convert.toLong(map.get("customerId")));
        map.put("customerName", customerName);
        String businessId = "businessId";
        if (map.containsKey(businessId) && ObjectUtil.isNotEmpty(map.get(businessId))) {
            String businessName = crmBusinessService.getBusinessName(Convert.toLong(map.get("businessId")));
            map.put("businessName", businessName);
        } else {
            map.put("businessName", "");
        }
        String contactsId = "contactsId";
        if (map.containsKey(contactsId) && ObjectUtil.isNotEmpty(map.get(contactsId))) {
            String contactsName = crmContactsService.getContactsName(Convert.toLong(map.get("contactsId")));
            map.put("contactsName", contactsName);
        } else {
            map.put("contactsName", "");
        }
        String companyUserId = "companyUserId";
        if (map.containsKey(companyUserId) && ObjectUtil.isNotEmpty(map.get(companyUserId))) {
            String companyUserName = UserCacheUtil.getSimpleUsers(TagUtil.toLongSet(map.get("companyUserId").toString()))
                    .stream().map(SimpleUser::getRealname).collect(Collectors.joining(","));
            map.put("companyUserName", companyUserName);
        } else {
            map.put("companyUserName", "");
        }
        String ownerUserId = "ownerUserId";
        if (map.containsKey(ownerUserId) && ObjectUtil.isNotEmpty(map.get(ownerUserId))) {
            String ownerUserName = UserCacheUtil.getUserName((Long) map.get("ownerUserId"));
            map.put("ownerUserName", ownerUserName);
        } else {
            map.put("ownerUserName", "");
        }
        String createUserName = UserCacheUtil.getUserName((Long) map.get("createUserId"));
        map.put("createUserName", createUserName);
    }

    @Override
    public Dict getSearchTransferMap() {
        return Dict.create().set("customerId", "customerName").set("businessId", "businessName").set("contactsId", "contactsName").set("customer_id", "customerId");
    }

    /**
     * 删除合同数据
     *
     * @param ids ids
     */
    @Override
    public void deleteByIds(List<Long> ids) {
        //合同
        Integer number = ApplicationContextHolder.getBean(ICrmReceivablesService.class).lambdaQuery().in(CrmReceivables::getContractId, ids).ne(CrmReceivables::getCheckStatus, 7).count();
        if (number > 0) {
            throw new CrmException(CrmCodeEnum.CRM_DATA_JOIN_ERROR);
        }
        ids.forEach(id -> {
            CrmContract contract = getById(id);
            boolean bol = (contract.getCheckStatus() != 4 && contract.getCheckStatus() != 5) && (!UserUtil.isAdmin());
            if (bol) {
                throw new CrmException(CrmCodeEnum.CAN_ONLY_DELETE_WITHDRAWN_AND_SUBMITTED_EXAMINE);
            }
            //删除合同产品关联
            crmContractProductService.deleteByContractId(contract.getContractId());
            //删除跟进记录
            crmActivityService.deleteActivityRecord(Collections.singletonList(id));
            //删除字段操作记录
            crmActionRecordService.deleteActionRecord(CrmEnum.CONTRACT, Collections.singletonList(contract.getContractId()));
            //删除自定义字段
            crmContractDataService.deleteByBatchId(Collections.singletonList(contract.getBatchId()));
            //删除文件
            adminFileService.delete(Collections.singletonList(contract.getBatchId()));
            if (ObjectUtil.isNotEmpty(contract.getExamineRecordId())) {
                examineService.deleteExamineRecord(contract.getExamineRecordId());
            }
            crmReceivablesPlanService.deleteByContractId(contract.getContractId());
            contract.setCheckStatus(7);
            updateById(contract);
        });
        deletePage(ids);
    }

    /**
     * 修改合同负责人
     *
     * @param changOwnerUserBO data
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Message(label = CrmMsgLabelEnum.contract, action = CrmMsgActionEnum.transfer)
    public void changeOwnerUser(CrmChangeOwnerUserBO changOwnerUserBO) {
        Long ownerUserId = changOwnerUserBO.getOwnerUserId();
        String ownerUserName = UserCacheUtil.getUserName(ownerUserId);
        // 操作对象数据
        List<JSONObject> operateObjects = new ArrayList<>();
        changOwnerUserBO.getIds().forEach(id -> {
            CrmContract contract = getById(id);
            int eight = 8;
            if (contract.getCheckStatus() == eight) {
                throw new CrmException(CrmCodeEnum.CRM_CONTRACT_TRANSFER_ERROR);
            }
            contract = getById(id);
            int two = 2;
            if (two == changOwnerUserBO.getTransferType() && !ownerUserId.equals(contract.getOwnerUserId())) {
                ApplicationContextHolder.getBean(ICrmTeamMembersService.class).addSingleMember(getLabel(), contract.getContractId(), contract.getOwnerUserId(), changOwnerUserBO.getPower(), changOwnerUserBO.getExpiresTime(), contract.getName());
            }
            ApplicationContextHolder.getBean(ICrmTeamMembersService.class).deleteMember(getLabel(), new CrmMemberSaveBO(id, ownerUserId));
            contract.setOwnerUserId(ownerUserId);
            updateById(contract);
            actionRecordUtil.addConversionRecord(id, CrmEnum.CONTRACT, ownerUserId, contract.getName());
            //修改es
            Map<String, Object> map = new HashMap<>();
            map.put("ownerUserId", ownerUserId);
            map.put("ownerUserName", ownerUserName);
            updateField(map, Collections.singletonList(id));

            // 构建操作对象数据
            JSONObject operateObject = new JSONObject();
            operateObject.put("id", contract.getContractId());
            operateObject.put("name", contract.getName());
            operateObject.put("ownerUserId", ownerUserId);
            operateObject.put("ownerUserName", ownerUserName);
            operateObjects.add(operateObject);
        });

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.transform.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m3", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(),
                "@NAME", ownerUserName);
        msgBody.setTitle(title);
        msgBody.setOperateObject(operateObjects);
        AdminMessageUtil.setMsgBody(msgBody);
    }


    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    @Override
    @Message(label = CrmMsgLabelEnum.contract, action = CrmMsgActionEnum.excelExport)
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls) {
        List<CrmFieldSortVO> headList = crmFieldService.queryListHead(getLabel().getType(), sortIds);
        exportExcel(search, headList, response, isXls, (record, headMap) -> {
            for (String fieldName : headMap.keySet()) {
                record.put(fieldName, ActionRecordUtil.parseExportValue(record.get(fieldName), headMap.get(fieldName), false));
            }
            String status = "checkStatus";
            if (ObjectUtil.isEmpty(record.get(status))) {
                return;
            }
            String checkStatus;
            //0待审核、1通过、2拒绝、3审核中 4:撤回 5 未提交
            switch (TypeUtils.castToInt(record.get("checkStatus"))) {
                case 1:
                    checkStatus = "通过";
                    break;
                case 2:
                    checkStatus = "拒绝";
                    break;
                case 3:
                    checkStatus = "审核中";
                    break;
                case 4:
                    checkStatus = "撤回";
                    break;
                case 5:
                    checkStatus = "未提交";
                    break;
                case 7:
                    checkStatus = "已删除";
                    break;
                case 8:
                    checkStatus = "作废";
                    break;
                case 10:
                    checkStatus = "正常";
                    break;
                default:
                    checkStatus = "待审核";
            }
            record.put("checkStatus", checkStatus);
        });

        // 发送消息
        MsgBodyBO msgBody = new MsgBodyBO();
        msgBody.setMsgKey(IdUtil.simpleUUID());
        msgBody.setMsgTag(getMsgLabelEnum().name());
        msgBody.setAction(CrmMsgActionEnum.excelExport.name());
        msgBody.setCurrentUser(UserUtil.getUser());
        String title = ResourcesUtil.getMessage("m4", UserUtil.getUser().getRealname(), getMsgLabelEnum().getDesc(), headList.size());
        msgBody.setTitle(title);
        AdminMessageUtil.setMsgBody(msgBody);
    }

    @Override
    public List<CrmModelFiledVO> information(Long contractId) {
        return queryField(contractId, true);
    }

    /**
     * 查询产品通过合同ID
     *
     * @param crmRelationPageBO 合同ID
     * @return data
     */
    @Override
    public JSONObject queryProductListByContractId(CrmRelationPageBO crmRelationPageBO) {
        JSONObject record = getBaseMapper().querySubtotalByContractId(crmRelationPageBO.getContractId());
        String money = "money";
        if (record.getString(money) == null) {
            record.put("money", 0);
        }
        BasePage<JSONObject> page = getBaseMapper().queryProductPageList(crmRelationPageBO.parse(), crmRelationPageBO.getContractId());
        for (JSONObject jsonObject : page.getList()) {
            Integer status = jsonObject.getInteger("status");
            if (status == 1) {
                jsonObject.put("是否上下架", "是");
            } else {
                jsonObject.put("是否上下架", "否");
            }
        }
        record.put("pageNumber", page.getPageNumber());
        record.put("pageSize", page.getPageSize());
        record.put("totalPage", page.getTotalPage());
        record.put("totalRow", page.getTotalRow());
        record.put("list", page.getList());
        return record;
    }

    /**
     * 查询文件数量
     *
     * @param contractId id
     * @return data
     */
    @Override
    public CrmInfoNumVO num(Long contractId) {
        CrmContract crmContract = getById(contractId);
        List<CrmField> crmFields = crmFieldService.queryFileField();
        List<String> batchIdList = new ArrayList<>();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmContractData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmContractData::getValue);
            wrapper.eq(CrmContractData::getBatchId, crmContract.getBatchId());
            wrapper.in(CrmContractData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            batchIdList.addAll(crmContractDataService.listObjs(wrapper, Object::toString));
        }
        batchIdList.add(crmContract.getBatchId());
        batchIdList.addAll(crmActivityService.queryFileBatchId(crmContract.getContractId(), getLabel().getType()));
        Map<String, Object> map = new HashMap<>();
        map.put("contractId", contractId);
        map.put("isAdmin", !UserUtil.isAdmin());
        map.put("userId", UserUtil.getUserId());
        List<Long> receivableUserIds = AuthUtil.queryAuthUserList(CrmEnum.RECEIVABLES, CrmAuthEnum.READ);
        List<Long> returnVisitUserIds = AuthUtil.queryAuthUserList(CrmEnum.RETURN_VISIT, CrmAuthEnum.READ);
        List<Long> invoiceUserIds = AuthUtil.queryAuthUserList(CrmEnum.INVOICE, CrmAuthEnum.READ);
        map.put("receivableUserIds", receivableUserIds);
        map.put("returnVisitUserIds", returnVisitUserIds);
        map.put("invoiceUserIds", invoiceUserIds);
        map.put("atUserId", UserUtil.getUserId());
        map.put("crmAuthEnumValue", CrmAuthEnum.READ.getValue());
        map.put("readOnly", AuthUtil.READ_ONLY_ONE);
        CrmInfoNumVO infoNumVO = getBaseMapper().queryNum(map);
//        infoNumVO.setFileCount(adminFileService.queryNum(batchIdList).getData());
        infoNumVO.setFileCount(null);
        infoNumVO.setMemberCount(ApplicationContextHolder.getBean(ICrmTeamMembersService.class).queryMemberCount(getLabel(), crmContract.getContractId(), crmContract.getOwnerUserId()));
        return infoNumVO;
    }

    /**
     * 查询文件列表
     *
     * @param contractId id
     * @return file
     */
    @Override
    public List<FileEntity> queryFileList(Long contractId) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        List<FileEntity> fileEntityList1 = new ArrayList<>();
        CrmContract crmContract = getById(contractId);
        adminFileService.queryFileList(crmContract.getBatchId()).getData().forEach(fileEntity -> {
            fileEntity.setSource("附件上传");
            fileEntity.setReadOnly(0);
            fileEntityList.add(fileEntity);
        });
        List<CrmField> crmFields = crmFieldService.queryFileField();
        if (crmFields.size() > 0) {
            LambdaQueryWrapper<CrmContractData> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(CrmContractData::getValue);
            wrapper.eq(CrmContractData::getBatchId, crmContract.getBatchId());
            wrapper.in(CrmContractData::getFieldId, crmFields.stream().map(CrmField::getFieldId).collect(Collectors.toList()));
            List<FileEntity> data = adminFileService.queryFileList(crmContractDataService.listObjs(wrapper, Object::toString)).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("合同详情");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        List<String> stringList = crmActivityService.queryFileBatchId(crmContract.getContractId(), getLabel().getType());
        if (stringList.size() > 0) {
            List<FileEntity> data = adminFileService.queryFileList(stringList).getData();
            data.forEach(fileEntity -> {
                fileEntity.setSource("跟进记录");
                fileEntity.setReadOnly(1);
                fileEntityList.add(fileEntity);
            });
        }
        CrmContract crmContract1 = this.getBaseMapper().selectById(crmContract.getContractId());
        String batchId = crmContract1.getBatchId();
        List<CrmContractData> crmContractData = crmContractDataService.getBaseMapper().selectList(new LambdaQueryWrapper<CrmContractData>()
                .eq(CrmContractData::getBatchId, batchId));
        List<CrmContractData> collect1 = crmContractData.stream().filter(e -> "isConfidential".equals(e.getName())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect1)&&"1".equals(collect1.get(0).getValue())){
            fileEntityList.forEach(e->{
                Long userId = UserUtil.getUserId();
                List<String> leaders = getLeaders(e.getCreateUserId().toString());
                leaders.add(e.getCreateUserId().toString());
//            Result<List<String>> leaders1 = adminFileService.getLeaders(e.getCreateUserId().toString());
                List<String> collect = leaders.stream().filter(t -> t.equals(userId.toString())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)){
                    fileEntityList1.add(e);
                }
            });
            return fileEntityList1;
        }
        return fileEntityList;
    }

    public List<String> getLeaders(String id) {
        List<String> leaders=new ArrayList<>();
        return getLeader(leaders,id);
    }
    public List<String> getLeader(List<String> leaders,String id) {
        String adminUser = this.getBaseMapper().selectLeader(id);
        if (StringUtils.isNotBlank(adminUser)){
            leaders.add(adminUser);
            getLeader(leaders,adminUser);
        }
        return leaders;
    }
    @Override
    public BasePage<CrmReceivablesPlan> queryReceivablesPlanListByContractId(CrmRelationPageBO crmRelationPageBO) {
        CrmReceivablesPlanMapper mapper = (CrmReceivablesPlanMapper) crmReceivablesPlanService.getBaseMapper();
        return mapper.queryReceivablesPlanListByContractId(crmRelationPageBO.parse(), crmRelationPageBO.getContractId(), UserUtil.getUserId(), !UserUtil.isAdmin());
    }

    @Override
    public List<SimpleCrmEntity> querySimpleEntity(List<Object> ids) {
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        List<CrmContract> list = lambdaQuery().select(CrmContract::getContractId, CrmContract::getName).in(CrmContract::getContractId, ids).list();
        return list.stream().map(crmContract -> {
            SimpleCrmEntity simpleCrmEntity = new SimpleCrmEntity();
            simpleCrmEntity.setId(crmContract.getContractId());
            simpleCrmEntity.setName(crmContract.getName());
            return simpleCrmEntity;
        }).collect(Collectors.toList());
    }

    @Override
    public String getContractName(Long contractId) {
        return lambdaQuery().select(CrmContract::getName).eq(CrmContract::getContractId, contractId).oneOpt()
                .map(CrmContract::getName).orElse("");
    }


    @Override
    public void updateInformation(CrmUpdateInformationBO updateInformationBO) {
        String batchId = updateInformationBO.getBatchId();
        Long contractId = updateInformationBO.getId();
        CrmContract contract = getById(contractId);
        int eight = 8;
        if (contract.getCheckStatus() == eight) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_CANCELLATION_ERROR);
        }
        if (contract.getCheckStatus() == 1) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EXAMINE_PASS_ERROR);
        }
        int two = 2;
        int four = 4;
        int five = 5;
        int ten = 10;
        if (!Arrays.asList(two, four, five, ten).contains(contract.getCheckStatus())) {
            throw new CrmException(CrmCodeEnum.CRM_CONTRACT_EDIT_ERROR);
        }
        updateInformationBO.getList().forEach(record -> {
            CrmContract oldContract = getById(updateInformationBO.getId());
            uniqueFieldIsAbnormal(record.getString("name"), record.getLong("fieldId"), record.getString("value"), batchId);
            Map<String, Object> oldContractMap = BeanUtil.beanToMap(oldContract);
            String fieldType = "fieldType";
            if (record.getInteger(fieldType) == 1) {
                Map<String, Object> crmContractMap = new HashMap<>(oldContractMap);
                crmContractMap.put(record.getString("fieldName"), record.get("value"));
                CrmContract crmContract = BeanUtil.toBean(crmContractMap, CrmContract.class);
                actionRecordUtil.updateRecord(oldContractMap, crmContractMap, CrmEnum.CONTRACT, crmContract.getName(), crmContract.getContractId());
                update().set(StrUtil.toUnderlineCase(record.getString("fieldName")), record.get("value")).eq("contract_id", updateInformationBO.getId()).update();
                String name = "name";
                String fieldName = "fieldName";
                if (name.equals(record.getString(fieldName))) {
                    ElasticUtil.batchUpdateEsData(elasticsearchRestTemplate.getClient(), "contract", crmContract.getContractId().toString(), crmContract.getName());
                }
            } else if (record.getInteger(fieldType) == 0 || record.getInteger(fieldType) == two) {
                CrmContractData contractData = crmContractDataService.lambdaQuery()
                        .select(CrmContractData::getValue, CrmContractData::getId)
                        .eq(CrmContractData::getFieldId, record.getLong("fieldId"))
                        .eq(CrmContractData::getBatchId, batchId).last("limit 1").one();
                String value = contractData != null ? contractData.getValue() : null;
                actionRecordUtil.publicContentRecord(CrmEnum.CONTRACT, BehaviorEnum.UPDATE, contractId, oldContract.getName(), record, value);
                String newValue = fieldService.convertObjectValueToString(record.getInteger("type"), record.get("value"), record.getString("value"));
                CrmContractData crmContractData = new CrmContractData();
                crmContractData.setId(contractData != null ? contractData.getId() : null);
                crmContractData.setFieldId(record.getLong("fieldId"));
                crmContractData.setName(record.getString("fieldName"));
                crmContractData.setValue(newValue);
                crmContractData.setCreateTime(LocalDateTimeUtil.now());
                crmContractData.setBatchId(batchId);
                crmContractDataService.saveOrUpdate(crmContractData);

            }
            updateField(record, contractId);
        });
        this.lambdaUpdate().set(CrmContract::getUpdateTime, new Date()).eq(CrmContract::getContractId, contractId).update();
    }


    @Override
    public BasePage<JSONObject> queryListByContractId(CrmRelationPageBO crmRelationPageBO) {
        return crmReceivablesService.queryListByContractId(crmRelationPageBO.parse(), crmRelationPageBO.getContractId(), CrmEnum.RECEIVABLES, CrmAuthEnum.READ, AuthUtil.READ_ONLY_ONE);
    }

    @Override
    public List<CrmReceivablesPlan> queryReceivablesPlansByContractId(Long contractId, Long receivablesId) {
        List<CrmReceivablesPlan> recordList = getBaseMapper().queryReceivablesPlansByContractId(contractId);
        if (receivablesId != null) {
            CrmReceivablesPlan receivables = getBaseMapper().queryReceivablesPlansByReceivablesId(receivablesId);
            if (receivables != null) {
                recordList.add(receivables);
            }
        }
        return recordList;
    }

    @Override
    public BasePage<JSONObject> queryReturnVisit(CrmRelationPageBO crmRelationPageBO) {
        List<CrmField> nameList = crmFieldService.lambdaQuery().select(CrmField::getFieldId, CrmField::getFieldName).eq(CrmField::getLabel, CrmEnum.RETURN_VISIT.getType())
                .eq(CrmField::getIsHidden, 0).ne(CrmField::getFieldType, 1).list();
        String conditions = AuthUtil.getCrmAuthSql(CrmEnum.RECEIVABLES, "a", 1, CrmAuthEnum.READ);
        BasePage<JSONObject> basePage = getBaseMapper().queryReturnVisit(crmRelationPageBO.parse(), crmRelationPageBO.getContractId(), conditions, nameList);
        for (JSONObject jsonObject : basePage.getList()) {
            String ownerUserName = UserCacheUtil.getUserName(jsonObject.getLong("ownerUserId"));
            jsonObject.put("ownerUserName", ownerUserName);
        }
        return basePage;
    }

    @Override
    public void contractDiscard(Long contractId) {
        CrmContract contract = getById(contractId);
        actionRecordUtil.addObjectActionRecord(CrmEnum.CONTRACT, contractId, BehaviorEnum.CANCEL_EXAMINE, contract.getName());
        lambdaUpdate().set(CrmContract::getCheckStatus, 8).set(CrmContract::getUpdateTime, new Date()).eq(CrmContract::getContractId, contractId).update();
        Map<String, Object> map = new HashMap<>();
        map.put("checkStatus", 8);
        map.put("updateTime", DateUtil.formatDateTime(new Date()));
        updateField(map, Collections.singletonList(contractId));
        List<Integer> statuss = new ArrayList<>();
        statuss.add(0);
        statuss.add(5);
        List<CrmReceivablesPlan> planList = crmReceivablesPlanService.lambdaQuery().eq(CrmReceivablesPlan::getContractId, contractId)
                .in(CrmReceivablesPlan::getReceivedStatus, statuss).list();
        crmReceivablesPlanService.lambdaUpdate().set(CrmReceivablesPlan::getReceivedStatus, 3)
                .eq(CrmReceivablesPlan::getContractId, contractId)
                .in(CrmReceivablesPlan::getReceivedStatus, statuss).update();
        for (CrmReceivablesPlan plan : planList) {
            map = new HashMap<>();
            map.put("receivedStatus", 3);
            map.put("updateTime", DateUtil.formatDateTime(new Date()));
            ElasticUtil.updateField(getRestTemplate(), map, plan.getReceivablesPlanId(), CrmEnum.RECEIVABLES_PLAN.getIndex());
        }
        //同步状态到mes
        crmExamineRecordService.cancelMesStatus(contract);
    }


    @Override
    public List<String> endContract(CrmEventBO crmEventBO) {
        return getBaseMapper().endContract(crmEventBO);
    }

    @Override
    public List<String> receiveContract(CrmEventBO crmEventBO) {
        return getBaseMapper().receiveContract(crmEventBO);
    }

    @Override
    public BasePage<Map<String, Object>> eventContractPageList(QueryEventCrmPageBO eventCrmPageBO) {
        Long userId = eventCrmPageBO.getUserId();
        Long time = eventCrmPageBO.getTime();
        Integer type = eventCrmPageBO.getType();
        if (userId == null) {
            userId = UserUtil.getUserId();
        }
        List<Long> contractIds;
        List<JSONObject> records = null;
        if (type == 1) {
            AdminConfig adminConfig = adminService.queryFirstConfigByName("expiringContractDays").getData();
            if (adminConfig.getStatus() == 0 || ObjectUtil.isNull(adminConfig)) {
                contractIds = new ArrayList<>();
            } else {
                contractIds = getBaseMapper().endContractList(userId, new Date(time), Integer.valueOf(adminConfig.getValue()));
            }
        } else {
            records = getBaseMapper().receiveContractList(userId, new Date(time));
            contractIds = records.stream().map(record -> record.getLong("contractId")).collect(Collectors.toList());
        }
        if (contractIds.size() == 0) {
            return new BasePage<>();
        }
        List<String> collect = contractIds.stream().map(Object::toString).collect(Collectors.toList());
        CrmSearchBO crmSearchBO = new CrmSearchBO();
        crmSearchBO.setSearchList(Collections.singletonList(new CrmSearchBO.Search("_id", "text", CrmSearchBO.FieldSearchEnum.ID, collect)));
        crmSearchBO.setLabel(CrmEnum.CONTRACT.getType());
        crmSearchBO.setPage(eventCrmPageBO.getPage());
        crmSearchBO.setLimit(eventCrmPageBO.getLimit());
        int two = 2;
        if (type == two) {
            BasePage<Map<String, Object>> page = queryPageList(crmSearchBO);
            for (JSONObject record : records) {
                for (Map<String, Object> map : page.getList()) {
                    if (map.get("contractId").equals(record.getLong("contractId"))) {
                        map.putAll(record.getInnerMap());
                    }
                }
            }
            return page;
        } else {
            return queryPageList(crmSearchBO);
        }
    }

    /**
     * 根据产品ID查询合同
     *
     * @param biParams 参数
     * @return data
     */
    @Override
    public BasePage<Map<String, Object>> queryListByProductId(BiSearchBO biParams) {
        List<CrmContractProduct> products = crmContractProductService
                .lambdaQuery()
                .select(CrmContractProduct::getContractId)
                .eq(CrmContractProduct::getProductId, biParams.getId()).list();
        BiParamsUtil.BiTimeEntity timeEntity = new BiParamsUtil.BiTimeEntity();
        BiParamsUtil.BiTimeEntity biTimeEntity = BiParamsUtil.analyzeType(biParams, CrmAuthEnum.LIST.getMenuId(CrmEnum.CONTRACT));
        CrmSearchBO searchBo = new CrmSearchBO();
        searchBo.setPage(biParams.getPage());
        searchBo.setLimit(biParams.getLimit());
        searchBo.setLabel(getLabel().getType());
        searchBo.setSearch(biParams.getSearch());
        List<String> stringList = products.stream().map(crmContractProduct -> crmContractProduct.getContractId().toString()).collect(Collectors.toList());
        List<CrmSearchBO.Search> searchList = searchBo.getSearchList();
        searchList.add(new CrmSearchBO.Search("contractId", "text", CrmSearchBO.FieldSearchEnum.ID, stringList));
        List<String> userIds = timeEntity.getUserIds().stream().map(Object::toString).collect(Collectors.toList());
        searchList.add(new CrmSearchBO.Search("ownerUserId", "text", CrmSearchBO.FieldSearchEnum.IS, userIds));
        searchList.add(new CrmSearchBO.Search("orderDate", "date", CrmSearchBO.FieldSearchEnum.RANGE, Arrays.asList(DateUtil.formatDate(biTimeEntity.getBeginDate()), DateUtil.formatDate(DateUtil.offsetDay(biTimeEntity.getEndDate(), 1)))));
        searchList.add(new CrmSearchBO.Search("checkStatus", "text", CrmSearchBO.FieldSearchEnum.IS, Arrays.asList("1", "10")));
        return queryPageList(searchBo);
    }

}
