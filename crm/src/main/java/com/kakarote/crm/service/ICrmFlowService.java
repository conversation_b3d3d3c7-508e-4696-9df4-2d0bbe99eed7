package com.kakarote.crm.service;

import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmFlowPageBO;
import com.kakarote.crm.entity.BO.CrmFlowSaveBO;
import com.kakarote.crm.entity.PO.CrmFlow;
import com.kakarote.crm.entity.VO.CrmFlowDataVO;
import com.kakarote.crm.entity.VO.CrmFlowInfoVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 阶段流程主信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface ICrmFlowService extends BaseService<CrmFlow> {


    /**
     * 查询列表
     *
     * @param crmFlowPageBO 查询对象
     * @return page
     */
    public BasePage<CrmFlow> queryList(CrmFlowPageBO crmFlowPageBO);

    /**
     * 保存阶段流程对象
     *
     * @param flowSaveBO data
     */
    public void saveFlow(CrmFlowSaveBO flowSaveBO);

    /**
     * 修改阶段流程状态
     *
     * @param flowId 流程ID
     * @param status 状态
     */
    public void updateFlowStatus(Long flowId, Integer status);


    /**
     * 阶段流程详情
     *
     * @param flowId flowId
     * @return data
     */
    public CrmFlowInfoVO flowInfo(Long flowId);

    /**
     * 查询阶段流程列表
     *
     * @param label  同CRM label
     * @param typeId 对应数据的主键ID
     * @return data
     */
    public CrmFlowDataVO queryFlowSettingList(Integer label, Long typeId);

    /**
     * 保存阶段流程信息
     *
     * @param crmEnum crmEnum
     * @param typeId  对应数据的主键ID
     * @return 当前阶段名称和流程名称
     */
    public Map<String, Object> initFlowData(SimpleUser simpleUser, CrmEnum crmEnum, Long typeId, Long flowId);

    /**
     * 查询项目可用状态组
     *
     * @return list
     */
    public List<Object> queryBusinessSetting();


    /**
     * 查询高级筛选处可用状态组
     * @param label 类型
     * @return list
     */
    public List<Object> queryCrmFlowSearchFieldInfo(Integer label);


    public String queryCrmFlowNameById(Long flowId);
}
