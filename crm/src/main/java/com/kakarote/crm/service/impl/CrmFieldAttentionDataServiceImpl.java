package com.kakarote.crm.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.PO.CrmField;
import com.kakarote.crm.entity.PO.CrmFieldAttentionData;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmFieldAttentionDataMapper;
import com.kakarote.crm.service.ICrmFieldAttentionDataService;
import com.kakarote.crm.service.ICrmFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.kakarote.core.servlet.ApplicationContextHolder.getBean;

/**
 * <p>
 * 关注度字段存值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-23
 */
@Service
public class CrmFieldAttentionDataServiceImpl extends BaseServiceImpl<CrmFieldAttentionDataMapper, CrmFieldAttentionData> implements ICrmFieldAttentionDataService {

    @Autowired
    private ICrmFieldService crmFieldService;

    @Override
    public void saveOrUpdate(CrmEnum crmEnum, CrmModelFiledVO filedVO, Map<String, Object> dataMap, Object id) {
        CrmField crmField = crmFieldService.getById(filedVO.getFieldId());
        Object batchId = dataMap.get("batchId");
        CrmFieldAttentionData attentionData = lambdaQuery()
                .eq(CrmFieldAttentionData::getFieldId,filedVO.getFieldId())
                .eq(CrmFieldAttentionData::getBatchId,batchId).one();
        Boolean flag = true;
        if (StrUtil.isNotEmpty(crmField.getOptions())){
            JSONArray setting = JSON.parseArray(crmField.getOptions());
            if (setting.size() > 0){
                Object value =  dataMap.get(filedVO.getFieldName());
                flag = false;
                if (attentionData != null){
                    attentionData.setFieldValue(TypeUtils.castToString(value));
                    attentionData.setUpdateTime(LocalDateTimeUtil.now());
                    attentionData.setNumDays(setting.getJSONObject(0).getInteger("num"));
                    attentionData.setStarNum(setting.getJSONObject(0).getInteger("value"));
                    updateById(attentionData);
                }else {
                    attentionData = new CrmFieldAttentionData();
                    attentionData.setFieldId(filedVO.getFieldId());
                    attentionData.setCreateTime(LocalDateTimeUtil.now());
                    attentionData.setCreateUserId(UserUtil.getUserId());
                    attentionData.setBatchId(TypeUtils.castToString(batchId));
                    attentionData.setUpdateTime(LocalDateTimeUtil.now());
                    attentionData.setFieldValue(TypeUtils.castToString(value));
                    attentionData.setLabel(crmEnum.getType());
                    attentionData.setTypeId(TypeUtils.castToInt(id));
                    attentionData.setNumDays(setting.getJSONObject(0).getInteger("num"));
                    attentionData.setStarNum(setting.getJSONObject(0).getInteger("value"));
                    save(attentionData);
                }
            }
        }
        if (attentionData == null){
            flag = false;
        }
        if (flag){
            removeById(attentionData.getId());
        }
    }

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public void carryOutAttention() {
        lambdaUpdate().eq(CrmFieldAttentionData::getFieldValue,0).remove();
        List<CrmFieldAttentionData> attentionDataList = getBean(ICrmFieldAttentionDataService.class).
                lambdaQuery().list();
        for (CrmFieldAttentionData attentionData: attentionDataList) {
            JSONObject json = getJson(attentionData.getLabel(),attentionData.getTypeId());
            JSONObject data = baseMapper.queryBatchId(json);
            if (data != null) {
                long betweenDay = DateUtil.between(DateUtil.date(), data.getDate("updateTime"), DateUnit.DAY);
                if (betweenDay > attentionData.getNumDays()) {
                    json.put("fieldId", attentionData.getFieldId());
                    json.put("batchId", attentionData.getBatchId());
                    data = baseMapper.queryData(json);
                    if (data != null && data.getInteger("value") != null && data.getInteger("value") > 0) {
                        CrmEnum crmEnum = CrmEnum.parse(attentionData.getLabel());
                        Integer vlaue = 0;
                        if (attentionData.getStarNum() >= data.getInteger("value")) {
                            json.put("value", 0);
                            json.put("dataId", data.get("id"));
                            baseMapper.updateDate(json);
                            removeById(attentionData.getId());
                        } else {
                            vlaue =  data.getInteger("value") - attentionData.getStarNum();
                            json.put("value", vlaue);
                            json.put("dataId", data.get("id"));
                            baseMapper.updateDate(json);
                            attentionData.setUpdateTime(LocalDateTimeUtil.now());
                            attentionData.setFieldValue(TypeUtils.castToString(data.getInteger("value") - attentionData.getStarNum()));
                            updateById(attentionData);
                        }
                       // ElasticUtil.updateField(elasticsearchRestTemplate, data.getString("name"), vlaue, Collections.singletonList(attentionData.getTypeId()),crmEnum.getIndex());
                    } else {
                        removeById(attentionData.getId());
                    }
                }
            }else {
                removeById(attentionData.getId());
            }
        }
    }


    private JSONObject getJson(Integer label, Integer id) {
        CrmEnum typeEnum = CrmEnum.parse(label);
        JSONObject json = new JSONObject();
        json.put("id",id);
        switch (typeEnum){
            case LEADS:{
                json.put("tableName","wk_crm_leads");
                json.put("paramName","leads_id");
                json.put("tableDataName","wk_crm_leads_data");
                break;
            }
            case CUSTOMER:{
                json.put("tableName","wk_crm_customer");
                json.put("paramName","customer_id");
                json.put("tableDataName","wk_crm_customer_data");
                break;
            }
            case CONTACTS:{
                json.put("tableName","wk_crm_contacts");
                json.put("paramName","contacts_id");
                json.put("tableDataName","wk_crm_contacts_data");
                break;
            }
            case PRODUCT:{
                json.put("tableName","wk_crm_product");
                json.put("paramName","product_id");
                json.put("tableDataName","wk_crm_product_data");
                break;
            }
            case BUSINESS:{
                json.put("tableName","wk_crm_business");
                json.put("paramName","business_id");
                json.put("tableDataName","wk_crm_business_data");
                break;
            }
            case CONTRACT:{
                json.put("tableName","wk_crm_contract");
                json.put("paramName","contract_id");
                json.put("tableDataName","wk_crm_contract_data");
                break;
            }
            case RECEIVABLES:{
                json.put("tableName","wk_crm_receivables");
                json.put("paramName","receivables_id");
                json.put("tableDataName","wk_crm_receivables_data");
                break;
            }
            case INVOICE:{
                json.put("tableName","wk_crm_invoice");
                json.put("paramName","invoice_id");
                json.put("tableDataName","wk_crm_invoice_data");
                break;
            }
            case RETURN_VISIT:{
                json.put("tableName","wk_crm_return_visit");
                json.put("paramName","visit_id");
                json.put("tableDataName","wk_crm_return_visit_data");
                break;
            }
            case RECEIVABLES_PLAN:{
                json.put("tableName","wk_crm_receivables_plan");
                json.put("paramName","receivables_plan_id");
                json.put("tableDataName","wk_crm_receivables_plan_data");
                break;
            }
        }
        return json;
    }
}
