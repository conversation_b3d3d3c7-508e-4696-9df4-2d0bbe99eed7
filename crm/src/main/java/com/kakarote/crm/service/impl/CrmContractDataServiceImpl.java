package com.kakarote.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kakarote.core.common.Const;
import com.kakarote.core.field.FieldService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.PO.CrmContractData;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;
import com.kakarote.crm.mapper.CrmContractDataMapper;
import com.kakarote.crm.service.ICrmContractDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 合同扩展字段数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Service
public class CrmContractDataServiceImpl extends BaseServiceImpl<CrmContractDataMapper, CrmContractData> implements ICrmContractDataService {

    @Autowired
    private FieldService fieldService;

    /**
     * 设置用户数据
     *
     * @param crmModel crmModel
     */
    @Override
    public void setDataByBatchId(CrmModel crmModel) {
        List<CrmContractData> contractDataList = query().eq("batch_id", crmModel.getBatchId()).list();
        contractDataList.forEach(obj -> {
            crmModel.put(obj.getName(), obj.getValue());
        });
    }

    /**
     * 保存自定义字段数据
     *
     * @param array data
     */
    @Override
    public void saveData(List<CrmModelFiledVO> array, String batchId) {
        List<CrmContractData> contractDataList = new ArrayList<>();
        remove(new LambdaQueryWrapper<CrmContractData>().eq(CrmContractData::getBatchId, batchId));
        for (CrmModelFiledVO obj : array) {
            CrmContractData crmContractData = BeanUtil.copyProperties(obj, CrmContractData.class);
            crmContractData.setValue(fieldService.convertObjectValueToString(obj.getType(),obj.getValue(),crmContractData.getValue()));
            crmContractData.setName(obj.getFieldName());
            crmContractData.setCreateTime(LocalDateTimeUtil.now());
            crmContractData.setBatchId(batchId);
            contractDataList.add(crmContractData);
        }
        saveBatch(contractDataList, Const.BATCH_SAVE_SIZE);
    }

    /**
     * 通过batchId删除数据
     *
     * @param batchId data
     */
    @Override
    public void deleteByBatchId(List<String> batchId) {
        LambdaQueryWrapper<CrmContractData> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CrmContractData::getBatchId,batchId);
        remove(wrapper);
    }
}
