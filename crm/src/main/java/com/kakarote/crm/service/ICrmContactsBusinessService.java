package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmContactsBusiness;

/**
 * <p>
 * 项目联系人关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
public interface ICrmContactsBusinessService extends BaseService<CrmContactsBusiness> {
    /**
     * 保存
     * @param business 项目ID
     * @param contactsId 联系人ID
     */
    public void save(Long business,Long contactsId);

    /**
     * 根据联系人ID删除联系人项目关联
     * @param contactsId 联系人ID
     */
    public void removeByContactsId(Long contactsId);

    /**
     * 根据项目ID删除联系人项目关联
     * @param businessId 项目ID
     */
    public void removeByBusinessId(Long... businessId);
}
