package com.kakarote.crm.service;

import com.kakarote.core.entity.BasePage;
import com.kakarote.crm.entity.BO.BiSearchBO;

import java.util.Map;

/**
 *
 * 商业智能查询
 *
 * @description:
 * @author: zyy
 * @date: 2022-02-28
 */
public interface CrmBiSearchService {


    BasePage<Map<String,Object>> queryVisitContractPageList(BiSearchBO biSearchBO);

    /**
     * 客户相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchCustomerPageList(BiSearchBO biSearchBO);

    /**
     * 项目相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchBusinessPageList(BiSearchBO biSearchBO);

    /**
     * 合同相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchContractPageList(BiSearchBO biSearchBO);

    /**
     * 联系人相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchContactsPageList(BiSearchBO biSearchBO);

    /**
     * 发票相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchInvoicePageList(BiSearchBO biSearchBO);

    /**
     * 产品相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchProductPageList(BiSearchBO biSearchBO);

    /**
     * 回款相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchReceivablesPageList(BiSearchBO biSearchBO);

    /**
     * 公海相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchPoolCustomerPageList(BiSearchBO biSearchBO);

    /**
     * 客户跟进数量列表
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> queryCustomerRecordList(BiSearchBO biSearchBO);

    /**
     * 员工满意度分析
     * @param biEntityParams
     * @return
     */
    BasePage<Map<String, Object>> employeesSatisfactionTable(BiSearchBO biEntityParams);

    /**
     * 产品满意度分析
     * @param biEntityParams
     * @return
     */
    BasePage<Map<String, Object>> productSatisfactionTable(BiSearchBO biEntityParams);

    /**
     * 产品成交客户
     * @param biEntityParams
     * @return
     */
    BasePage<Map<String, Object>> queryProductSucceedCustomerList(BiSearchBO biEntityParams);

    /**
     * 回款计划相关筛选
     * @param biSearchBO
     * @return
     */
    BasePage<Map<String, Object>> searchReceivablesPlanPageList(BiSearchBO biSearchBO);
}
