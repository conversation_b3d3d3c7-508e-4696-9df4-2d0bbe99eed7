package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmCustomerAddress;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
public interface ICrmCustomerAddressService extends BaseService<CrmCustomerAddress> {
    /**
     * 新建客户地址
     */
    public void saveAndUpdate(CrmCustomerAddress address);

    /**
     * 根据客户id，查询客户地址
     */
    public List<CrmCustomerAddress> queryListByCustomerId(Long customerId);

    /**
     * 设置主要地址
     */
    public void setIsMain(CrmCustomerAddress address);

    /**
     * 作废客户地址
     */
    public void cancellation(CrmCustomerAddress address);

}
