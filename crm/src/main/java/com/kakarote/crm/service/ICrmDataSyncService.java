package com.kakarote.crm.service;

import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.PageEntity;

import java.util.List;

/**
 * CRM数据同步服务接口
 * 用于处理外部系统直接操作数据库导致的ES数据不一致问题
 * 
 * <AUTHOR>
 */
public interface ICrmDataSyncService {

    /**
     * 同步单个客户数据到ES
     * @param customerId 客户ID
     * @return 是否同步成功
     */
    boolean syncCustomerToEs(Long customerId);

    /**
     * 批量同步客户数据到ES
     * @param customerIds 客户ID列表
     * @return 同步成功的数量
     */
    int batchSyncCustomersToEs(List<Long> customerIds);

    /**
     * 全量同步客户数据到ES
     * @return 同步成功的数量
     */
    int fullSyncCustomersToEs();

    /**
     * 增量同步客户数据到ES
     * 基于更新时间同步最近变更的数据
     * @param minutes 同步最近多少分钟的数据
     * @return 同步成功的数量
     */
    int incrementalSyncCustomersToEs(int minutes);

    /**
     * 处理同步日志表中的待同步数据
     * @return 处理的记录数
     */
    int processSyncLog();

    /**
     * 检查数据一致性
     * 比较MySQL和ES中的数据差异
     * @param pageEntity 分页参数
     * @return 不一致的数据列表
     */
    BasePage<Long> checkDataConsistency(PageEntity pageEntity);

    /**
     * 修复数据不一致问题
     * @param customerIds 需要修复的客户ID列表
     * @return 修复成功的数量
     */
    int repairDataInconsistency(List<Long> customerIds);

    /**
     * 删除ES中的无效数据
     * 删除MySQL中已不存在但ES中仍存在的数据
     * @return 删除的记录数
     */
    int cleanupInvalidEsData();
}
