package com.kakarote.crm.service;

import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.feign.crm.entity.QueryEventCrmPageBO;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.BO.CrmChangeOwnerUserBO;
import com.kakarote.crm.entity.BO.CrmModelSaveBO;
import com.kakarote.crm.entity.BO.CrmSearchBO;
import com.kakarote.crm.entity.BO.CrmUpdateInformationBO;
import com.kakarote.crm.entity.PO.CrmLeads;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 线索表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-21
 */
public interface ICrmLeadsService extends BaseService<CrmLeads> {
    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    public  List<CrmModelFiledVO> queryField(Long id);


    public  List<List<CrmModelFiledVO>> queryFormPositionField(Long id);

    /**
     * 查询所有数据
     *
     * @param search 搜索条件
     * @return
     */
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search);

    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    public CrmModel queryById(Long id);

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     * @param isExcel 是否excel
     */
    public void addOrUpdate(CrmModelSaveBO crmModel,boolean isExcel);

    /**
     * 删除线索数据
     *
     * @param ids ids
     */
    public void deleteByIds(List<Long> ids);

    /**
     * 修改线索负责人
     *
     * @param changeOwnerUserBO   负责人转移BO
     */
    public void changeOwnerUser(CrmChangeOwnerUserBO changeOwnerUserBO);

    /**
     * 线索转客户功能
     *
     * @param leadsIds 线索id
     */
    public void transfer(List<Long> leadsIds);

    /**
     * 下载导入模板
     *
     * @param response 线索id
     * @throws IOException exception
     */
    public void downloadExcel(HttpServletResponse response) throws IOException;

    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    public void exportExcel(HttpServletResponse response, CrmSearchBO search,List<Long> sortIds,Integer isXls);

    /**
     * 标星
     * @param leads 线索id
     */
    public void star(Long leads);

    /**
     * 查询详情页基本信息
     * @param leadsId id
     * @return data
     */
    public List<CrmModelFiledVO> information(Long leadsId);

    /**
     * 查询详情页数量
     * @param leadsId id
     * @return data
     */
    public CrmInfoNumVO num(Long leadsId);

    /**
     * 查询文件列表
     * @param leadsId id
     * @return file
     */
    public List<FileEntity> queryFileList(Long leadsId);

    void updateInformation(CrmUpdateInformationBO updateInformationBO);

    List<String> eventLeads(CrmEventBO crmEventBO);

    BasePage<Map<String, Object>> eventLeadsPageList(QueryEventCrmPageBO eventCrmPageBO);


    default CrmMsgLabelEnum getMsgLabelEnum() {
        return CrmMsgLabelEnum.leads;
    }

}
