package com.kakarote.crm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.AdminMessageBO;
import com.kakarote.core.feign.admin.entity.AdminMessageEnum;
import com.kakarote.core.feign.admin.service.AdminMessageService;
import com.kakarote.core.feign.crm.entity.SimpleCrmInfo;
import com.kakarote.core.feign.examine.entity.ExamineConditionDataBO;
import com.kakarote.core.feign.examine.entity.ExamineMessageBO;
import com.kakarote.core.feign.examine.entity.ExamineRecordLog;
import com.kakarote.core.feign.jxc.entity.JxcExamine;
import com.kakarote.core.feign.jxc.service.JxcExamineService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.ApplicationContextHolder;
import com.kakarote.core.utils.HttpUtil;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.common.ElasticUtil;
import com.kakarote.crm.constant.CrmCodeEnum;
import com.kakarote.crm.constant.CrmEnum;
import com.kakarote.crm.entity.BO.CrmContractModel;
import com.kakarote.crm.entity.BO.CrmContractSendBo;
import com.kakarote.crm.entity.BO.ProductSend;
import com.kakarote.crm.entity.PO.CrmContract;
import com.kakarote.crm.entity.PO.CrmCustomer;
import com.kakarote.crm.entity.PO.CrmInvoice;
import com.kakarote.crm.entity.PO.CrmReceivables;
import com.kakarote.crm.mapper.CrmContractMapper;
import com.kakarote.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 审核记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Service
@Slf4j
public class CrmExamineRecordServiceImpl implements ICrmExamineRecordService {

    @Autowired
    private Redis redis;
    // 复用线程池（全局唯一）
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);
    @Autowired
    private ICrmContractService crmContractService;
    @Autowired
    private CrmContractMapper crmContractMapper;

    @Value("${product-send}")
    private String url;
    @Autowired
    private ICrmReceivablesService crmReceivablesService;

    @Autowired
    private JxcExamineService jxcExamineService;

    @Autowired
    private ICrmReceivablesPlanService receivablesPlanService;

    private static final int TWO = 2;

    private static final int THREE = 3;

    private static final int FOUR = 4;

    /**
     * 更新合同回款金额
     *
     * @param id id
     */
    @Override
    public void updateContractMoney(Long id) {
        CrmReceivables receivables = crmReceivablesService.getById(id);
        CrmContract contract = crmContractService.getById(receivables.getContractId());
        if (contract == null) {
            return;
        }
        BigDecimal bigDecimal = ((CrmContractMapper) crmContractService.getBaseMapper()).queryReceivablesMoney(contract.getContractId());
        if (contract.getMoney() == null) {
            contract.setMoney(new BigDecimal("0"));
        }
        LambdaUpdateWrapper<CrmContract> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CrmContract::getReceivedMoney, bigDecimal);
        updateWrapper.set(CrmContract::getUnreceivedMoney, contract.getMoney().subtract(bigDecimal));
        updateWrapper.eq(CrmContract::getContractId, contract.getContractId());
        crmContractService.update(updateWrapper);

        Map<String, Object> map = new HashMap<>();
        map.put("receivedMoney", bigDecimal);
        map.put("unreceivedMoney", contract.getMoney().subtract(bigDecimal));
        ElasticUtil.updateField(elasticsearchRestTemplate, map, contract.getContractId(), CrmEnum.CONTRACT.getIndex());
    }

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;


    @Override
    public void addMessageForNewExamine(ExamineMessageBO examineMessageBO) {
        this.addMessageForNewExamine(examineMessageBO.getCategoryType(), examineMessageBO.getExamineType(),
                examineMessageBO.getExamineLog(), examineMessageBO.getOwnerUserId());
    }

    @Override
    public void addMessageForNewExamine(Integer categoryType, Integer examineType, Object examineObj, Long ownerUserId) {
        AdminMessageBO adminMessageBO = new AdminMessageBO();
        if (examineType == 1) {
            if (examineObj instanceof ExamineRecordLog) {
                ExamineRecordLog examineLog = (ExamineRecordLog) examineObj;
                adminMessageBO.setUserId(ownerUserId);
                if (categoryType == 1) {
                    CrmContract one = ApplicationContextHolder.getBean(ICrmContractService.class)
                            .lambdaQuery().eq(CrmContract::getExamineRecordId, examineLog.getRecordId()).last(" limit 1").one();
                    if (one == null) {
                        return;
                    }
                    adminMessageBO.setTitle(one.getName());
                    adminMessageBO.setTypeId(one.getContractId());
                    adminMessageBO.setMessageType(AdminMessageEnum.CRM_CONTRACT_EXAMINE.getType());
                } else if (categoryType == TWO) {
                    CrmReceivables one = ApplicationContextHolder.getBean(ICrmReceivablesService.class)
                            .lambdaQuery().eq(CrmReceivables::getExamineRecordId, examineLog.getRecordId()).last(" limit 1").one();
                    if (one == null) {
                        return;
                    }
                    adminMessageBO.setTitle(one.getNumber());
                    adminMessageBO.setTypeId(one.getReceivablesId());
                    adminMessageBO.setMessageType(AdminMessageEnum.CRM_RECEIVABLES_EXAMINE.getType());
                } else if (categoryType == THREE) {
                    CrmInvoice one = ApplicationContextHolder.getBean(ICrmInvoiceService.class)
                            .lambdaQuery().eq(CrmInvoice::getExamineRecordId, examineLog.getRecordId()).last(" limit 1").one();
                    if (one == null) {
                        return;
                    }
                    adminMessageBO.setTitle(one.getInvoiceApplyNumber());
                    adminMessageBO.setTypeId(one.getInvoiceId());
                    adminMessageBO.setMessageType(AdminMessageEnum.CRM_INVOICE_EXAMINE.getType());
                } else if (categoryType == FOUR) {
                    return;
                } else {
                    JxcExamine jxcExamine = new JxcExamine();
                    jxcExamine.setCategoryType(categoryType);
                    jxcExamine.setExamineType(examineType);
                    jxcExamine.setExamineObj((JSONObject) JSONObject.toJSON(examineLog));
                    jxcExamine.setOwnerUserId(ownerUserId);
                    jxcExamineService.examineMessage(jxcExamine);
                    return;
                }
                adminMessageBO.setIds(Collections.singletonList(examineLog.getExamineUserId()));
            }
        } else if (examineType == TWO || examineType == THREE) {
            if (examineObj instanceof ExamineRecordLog) {
                ExamineRecordLog examineRecord = (ExamineRecordLog) examineObj;
                adminMessageBO.setContent(examineRecord.getRemarks());
                adminMessageBO.setUserId(examineRecord.getExamineUserId());
                if (categoryType == 1) {
                    CrmContract one = ApplicationContextHolder.getBean(ICrmContractService.class)
                            .lambdaQuery().eq(CrmContract::getExamineRecordId, examineRecord.getRecordId()).last(" limit 1").one();
                    if (one == null) {
                        return;
                    }
                    adminMessageBO.setTitle(one.getName());
                    adminMessageBO.setTypeId(one.getContractId());
                    adminMessageBO.setMessageType(examineType == 2 ? AdminMessageEnum.CRM_CONTRACT_PASS.getType() : AdminMessageEnum.CRM_CONTRACT_REJECT.getType());
                    adminMessageBO.setIds(Collections.singletonList(one.getOwnerUserId()));
                } else if (categoryType == TWO) {
                    CrmReceivables one = ApplicationContextHolder.getBean(ICrmReceivablesService.class)
                            .lambdaQuery().eq(CrmReceivables::getExamineRecordId, examineRecord.getRecordId()).last(" limit 1").one();
                    if (one == null) {
                        return;
                    }
                    adminMessageBO.setTitle(one.getNumber());
                    adminMessageBO.setTypeId(one.getReceivablesId());
                    adminMessageBO.setMessageType(examineType == 2 ? AdminMessageEnum.CRM_RECEIVABLES_PASS.getType() : AdminMessageEnum.CRM_RECEIVABLES_REJECT.getType());
                    adminMessageBO.setIds(Collections.singletonList(one.getOwnerUserId()));
                } else if (categoryType == THREE) {
                    CrmInvoice one = ApplicationContextHolder.getBean(ICrmInvoiceService.class)
                            .lambdaQuery().eq(CrmInvoice::getExamineRecordId, examineRecord.getRecordId()).last(" limit 1").one();
                    if (one == null) {
                        return;
                    }
                    adminMessageBO.setTitle(one.getInvoiceApplyNumber());
                    adminMessageBO.setTypeId(one.getInvoiceId());
                    adminMessageBO.setMessageType(examineType == 2 ? AdminMessageEnum.CRM_INVOICE_PASS.getType() : AdminMessageEnum.CRM_INVOICE_REJECT.getType());
                    adminMessageBO.setIds(Collections.singletonList(one.getOwnerUserId()));
                } else if (categoryType == FOUR) {
                    return;
                } else {
                    JxcExamine jxcExamine = new JxcExamine();
                    jxcExamine.setCategoryType(categoryType);
                    jxcExamine.setExamineType(examineType);
                    jxcExamine.setExamineObj((JSONObject) JSONObject.toJSON(examineRecord));
                    jxcExamine.setOwnerUserId(ownerUserId);
                    jxcExamineService.examineMessage(jxcExamine);
                    return;
                }
            }
        }
        if (adminMessageBO.getIds() != null && adminMessageBO.getIds().size() > 0) {
            AdminMessageService messageService = ApplicationContextHolder.getBean(AdminMessageService.class);
            messageService.sendMessage(adminMessageBO);
        }

    }


    @Override
    public Map<String, Object> getDataMapForNewExamine(ExamineConditionDataBO examineConditionDataBO) {
        Map<String, Object> dataMap = new HashMap<>(8);
        Integer label = examineConditionDataBO.getLabel();
        Long id = examineConditionDataBO.getTypeId();
        if (label == 1) {
            CrmModel crmModel = crmContractService.queryById(id);
            if (Objects.nonNull(crmModel)){
                List<String> fieldList = examineConditionDataBO.getFieldList();
                fieldList.forEach(fieldName -> dataMap.put(fieldName, crmModel.get(fieldName)));
                dataMap.put("createUserId", crmModel.get("createUserId"));
            }
        } else if (label == TWO) {
            CrmModel crmModel = crmReceivablesService.queryById(id);
            List<String> fieldList = examineConditionDataBO.getFieldList();
            fieldList.forEach(fieldName -> dataMap.put(fieldName, crmModel.get(fieldName)));
            dataMap.put("createUserId", crmModel.get("createUserId"));
        } else if (label == THREE) {
            ICrmInvoiceService crmInvoiceService = ApplicationContextHolder.getBean(ICrmInvoiceService.class);
            LambdaQueryWrapper<CrmInvoice> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CrmInvoice::getInvoiceId, id);
            Map<String, Object> crmModel = crmInvoiceService.getMap(lambdaQueryWrapper);
            List<String> fieldList = examineConditionDataBO.getFieldList();
            fieldList.forEach(fieldName -> dataMap.put(fieldName, crmModel.get(fieldName)));
            dataMap.put("createUserId", crmModel.get("createUserId"));
        }
        return dataMap;
    }

    @Override
    public Boolean updateCheckStatusByNewExamine(ExamineConditionDataBO examineConditionDataBO) {
        Integer categoryType = examineConditionDataBO.getLabel();
        Long id = examineConditionDataBO.getTypeId();
        Integer status = examineConditionDataBO.getCheckStatus();
        int label = 20;
        //审核是否能够撤回的逻辑
        if (categoryType == 1) {
            CrmContract contract = ApplicationContextHolder.getBean(ICrmContractService.class).getById(id);
            if (status == FOUR) {
                if (contract.getCheckStatus() == 1) {
                    throw new CrmException(CrmCodeEnum.CRM_EXAMINE_RECHECK_PASS_ERROR);
                }
            } else if (status == 1) {
                CrmCustomer customer = ApplicationContextHolder.getBean(ICrmCustomerService.class).getById(contract.getCustomerId());
                customer.setDealStatus(1);
                LocalDateTime dealTime = contract.getOrderDate() != null ? contract.getOrderDate() : LocalDateTimeUtil.now();
                customer.setDealTime(dealTime);
                ApplicationContextHolder.getBean(ICrmCustomerService.class).updateById(customer);
                Map<String, Object> map = new HashMap<>();
                map.put("dealTime", DateUtil.formatLocalDateTime(dealTime));
                map.put("dealStatus", 1);
                ElasticUtil.updateField(elasticsearchRestTemplate, map, customer.getCustomerId(), CrmEnum.CUSTOMER.getIndex());
            }
            contract.setCheckStatus(status);
            receivablesPlanService.updateReceivedStatus(CrmEnum.CONTRACT, contract, status);
            ApplicationContextHolder.getBean(ICrmContractService.class).updateById(contract);
            // 2. 提交延迟任务（10分钟后执行）
            scheduler.schedule(() -> {
                // 延迟执行的业务逻辑（如检查合同状态、发送通知等）
                this.doAfter(contract);
            }, 10, TimeUnit.SECONDS);
            ElasticUtil.updateField(elasticsearchRestTemplate, "checkStatus", status, Collections.singletonList(contract.getContractId()), CrmEnum.CONTRACT.getIndex());
        } else if (categoryType == TWO) {
            CrmReceivables receivables = ApplicationContextHolder.getBean(ICrmReceivablesService.class).getById(id);
            receivables.setCheckStatus(status);
            //回款
            ApplicationContextHolder.getBean(ICrmReceivablesService.class).updateById(receivables);
            if (status == FOUR) {
                if (receivables.getCheckStatus() == 1) {
                    throw new CrmException(CrmCodeEnum.CRM_EXAMINE_RECHECK_PASS_ERROR);
                }
            } else if (status == 1) {
                updateContractMoney(id);
            }
            receivablesPlanService.updateReceivedStatus(CrmEnum.RECEIVABLES, receivables, status);
            ElasticUtil.updateField(elasticsearchRestTemplate, "checkStatus", status, Collections.singletonList(receivables.getReceivablesId()), CrmEnum.RECEIVABLES.getIndex());
        } else if (categoryType == THREE) {
            CrmInvoice invoice = ApplicationContextHolder.getBean(ICrmInvoiceService.class).getById(id);
            if (status == FOUR) {
                if (invoice.getCheckStatus() == 1) {
                    throw new CrmException(CrmCodeEnum.CRM_EXAMINE_RECHECK_PASS_ERROR);
                }
            }
            invoice.setCheckStatus(status);
            ApplicationContextHolder.getBean(ICrmInvoiceService.class).updateById(invoice);
            ElasticUtil.updateField(elasticsearchRestTemplate, "checkStatus", status, Collections.singletonList(invoice.getInvoiceId()), CrmEnum.INVOICE.getIndex());
        } else if (categoryType == label) {
            ICrmFlowDataService flowDataService = ApplicationContextHolder.getBean(ICrmFlowDataService.class);
            flowDataService.updateExamineStatus(id, status);
        }
        return true;
    }

    private void doAfter(CrmContract contract) {
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(new Date());
        CrmContractSendBo crmContractSendBo = new CrmContractSendBo();
        CrmContractModel crmContractModel = new CrmContractModel();
        crmContractModel.setContractNo(contract.getNum());
        crmContractModel.setCustomerkey(contract.getCustomerId().toString());
        crmContractModel.setOrderType("Cust");
        crmContractModel.setOrderDate(format);
        crmContractModel.setOrderAmount(contract.getTotalPrice());
        List<JSONObject> jsonObjects = crmContractMapper.selectPrudctList(contract.getContractId());
        List<ProductSend> items=new ArrayList<>();
        BigDecimal bigDecimal=new BigDecimal(0);
        for (JSONObject jsonObject : jsonObjects) {
            ProductSend productSend=new ProductSend();
            productSend.setRemark(jsonObject.getString("remark"));
            productSend.setItemMoney((BigDecimal) jsonObject.get("price"));
            productSend.setItemQty((BigDecimal) jsonObject.get("num"));
            bigDecimal=bigDecimal.add((BigDecimal) jsonObject.get("num"));
            productSend.setItemSalePrice((BigDecimal) jsonObject.get("salesPrice"));
            productSend.setItemUnit(jsonObject.getString("unit"));
            productSend.setItemkey(jsonObject.getString("productId"));
            productSend.setItemMoney((BigDecimal) jsonObject.get("subtotal"));
            items.add(productSend);
        }
        crmContractModel.setItems(items);
        crmContractModel.setOrderQty(bigDecimal);
        crmContractSendBo.setMaster(crmContractModel);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String jsonParams = objectMapper.writeValueAsString(crmContractSendBo);
            JSONObject jsonObject = doPostForJson(url, jsonParams);
            if ("200".equals(jsonObject.get("code").toString())){
                CrmContract crmContract = new CrmContract();
                crmContractMapper.update(new CrmContract(), new LambdaQueryWrapper<CrmContract>()
                        .eq(CrmContract::getContractId, contract.getContractId()));
            }
            log.info("jsonObject:"+jsonObject);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * post请求以及参数是json
     *
     * @param url
     * @param jsonParams
     * @return
     */
    public static JSONObject doPostForJson(String url, String jsonParams) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonObject = null;
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().
                setConnectTimeout(180 * 1000).setConnectionRequestTimeout(180 * 1000)
                .setSocketTimeout(180 * 1000).setRedirectsEnabled(true).build();
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", "application/json");
        try {
            httpPost.setEntity(new StringEntity(jsonParams, ContentType.create("application/json", "utf-8")));
            System.out.println("request parameters" + EntityUtils.toString(httpPost.getEntity()));
            System.out.println("httpPost:" + httpPost);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                String result = EntityUtils.toString(response.getEntity());
                System.out.println("result:" + result);
                jsonObject = JSONObject.parseObject(result);
                return jsonObject;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return jsonObject;
        }
    }
    @Override
    public SimpleCrmInfo getCrmSimpleInfo(ExamineConditionDataBO examineConditionDataBO) {
        SimpleCrmInfo simpleCrmInfo = new SimpleCrmInfo();
        Integer label = examineConditionDataBO.getLabel();
        Long id = examineConditionDataBO.getTypeId();
        if (label == 1) {
            CrmContract crmContract = crmContractService.getById(id);
            if (crmContract == null) {
                return null;
            }
            CrmCustomer crmCustomer = ApplicationContextHolder.getBean(ICrmCustomerService.class).getById(crmContract.getCustomerId());
            simpleCrmInfo.setCategory(crmContract.getName());
            simpleCrmInfo.setCategoryId(crmContract.getContractId());
            simpleCrmInfo.setOrderDate(crmContract.getOrderDate());
            if (crmCustomer != null) {
                simpleCrmInfo.setCategoryCiteId(crmCustomer.getCustomerId());
                simpleCrmInfo.setCategoryCiteName(crmCustomer.getCustomerName());
            }
            simpleCrmInfo.setCreateUser(UserCacheUtil.getSimpleUser(crmContract.getCreateUserId()));
            simpleCrmInfo.setExamineStatus(crmContract.getCheckStatus());
        } else if (label == TWO) {
            CrmReceivables crmReceivables = crmReceivablesService.getById(id);
            if (crmReceivables == null) {
                return null;
            }
            CrmContract crmContract = crmContractService.getById(crmReceivables.getContractId());
            simpleCrmInfo.setCategory(crmReceivables.getNumber());
            simpleCrmInfo.setCategoryId(crmReceivables.getReceivablesId());
            simpleCrmInfo.setReturnTime(crmReceivables.getReturnTime());
            simpleCrmInfo.setCategoryCiteId(crmContract.getContractId());
            simpleCrmInfo.setCategoryCiteName(crmContract.getName());
            simpleCrmInfo.setCreateUser(UserCacheUtil.getSimpleUser(crmReceivables.getCreateUserId()));
            simpleCrmInfo.setExamineStatus(crmContract.getCheckStatus());
            simpleCrmInfo.setExamineStatus(crmReceivables.getCheckStatus());
        } else if (label == THREE) {
            CrmInvoice crmInvoice = ApplicationContextHolder.getBean(ICrmInvoiceService.class).getById(id);
            if (crmInvoice == null) {
                return null;
            }
            CrmContract crmContract = crmContractService.getById(crmInvoice.getContractId());
            simpleCrmInfo.setCategory(crmInvoice.getInvoiceApplyNumber());
            simpleCrmInfo.setCategoryId(crmInvoice.getInvoiceId());
            if (crmInvoice.getRealInvoiceDate() != null) {
                simpleCrmInfo.setRealInvoiceDate(crmInvoice.getRealInvoiceDate().atStartOfDay());
            }
            simpleCrmInfo.setCategoryCiteId(crmContract.getContractId());
            simpleCrmInfo.setCategoryCiteName(crmContract.getName());
            simpleCrmInfo.setCreateUser(UserCacheUtil.getSimpleUser(crmInvoice.getCreateUserId()));
            simpleCrmInfo.setExamineStatus(crmInvoice.getCheckStatus());
        }
        return simpleCrmInfo;
    }

    @Override
    public void cancelMesStatus(CrmContract contract) {
        // 2. 提交延迟任务（10分钟后执行）
        scheduler.schedule(() -> {
            // 延迟执行的业务逻辑（如检查合同状态、发送通知等）
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String,Map<String,String>> map=new HashMap<>();
            Map<String,String> map1=new HashMap<>();
            map.put("master",map1);
            map1.put("cancel","Y");
            map1.put("contractNo",contract.getNum());
            try {
                String jsonParams = objectMapper.writeValueAsString(map);
                JSONObject jsonObject = this.doPostForJson(url, jsonParams);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }, 10, TimeUnit.SECONDS);
    }
}
