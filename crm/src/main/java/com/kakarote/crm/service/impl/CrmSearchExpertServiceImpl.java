package com.kakarote.crm.service.impl;

import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserUtil;
import com.kakarote.crm.entity.PO.CrmSearchExpert;
import com.kakarote.crm.mapper.CrmSearchExpertMapper;
import com.kakarote.crm.service.ICrmSearchExpertService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 高级筛选外漏查询条件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Service
public class CrmSearchExpertServiceImpl extends BaseServiceImpl<CrmSearchExpertMapper, CrmSearchExpert> implements ICrmSearchExpertService {

    @Override
    public void saveAndUpdate(CrmSearchExpert crmSearchExpert) {
        CrmSearchExpert searchExpert = lambdaQuery().eq(CrmSearchExpert::getLabel,crmSearchExpert.getLabel())
                .eq(CrmSearchExpert::getCreateUserId, UserUtil.getUserId()).one();
        if (searchExpert == null){
            crmSearchExpert.setCreateUserId(UserUtil.getUserId());
            save(crmSearchExpert);
        }else {
            searchExpert.setDefaultValue(crmSearchExpert.getDefaultValue());
            updateById(searchExpert);
        }
    }

    @Override
    public CrmSearchExpert queryByLabel(Integer label) {
        CrmSearchExpert searchExpert = lambdaQuery().eq(CrmSearchExpert::getLabel,label)
                .eq(CrmSearchExpert::getCreateUserId, UserUtil.getUserId()).one();
        return searchExpert;
    }
}
