package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.kakarote.core.common.enums.CrmMsgLabelEnum;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.PageEntity;
import com.kakarote.core.feign.crm.entity.CrmEventBO;
import com.kakarote.core.feign.crm.entity.QueryEventCrmPageBO;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.core.servlet.upload.FileEntity;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.BO.*;
import com.kakarote.crm.entity.PO.CrmCustomer;
import com.kakarote.crm.entity.PO.CrmCustomerSetting;
import com.kakarote.crm.entity.VO.CrmDataCheckVO;
import com.kakarote.crm.entity.VO.CrmInfoNumVO;
import com.kakarote.crm.entity.VO.CrmModelFiledVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
public interface ICrmCustomerService extends BaseService<CrmCustomer> {
    /**
     * 查询字段配置
     *
     * @param id 主键ID
     * @return data
     */
    List<CrmModelFiledVO> queryField(Long id);

    List<List<CrmModelFiledVO>> queryFormPositionField(Long id);

    /**
     * 查询所有数据
     *
     * @param search 搜索数据
     * @return data
     */
    public BasePage<Map<String, Object>> queryPageList(CrmSearchBO search);


    public void setPoolDayExportExcel(List<Map<String, Object>> list);

    /**
     * 查询字段配置
     *
     * @param id     主键ID
     * @param poolId poolId
     * @return data
     */
    CrmModel queryById(Long id, Long poolId);

    /**
     * 根据客户id，获取客户摘要
     *
     * @param id 主键ID
     * @return data
     */
    public CrmModel queryDigestById(Long id);

    /**
     * 保存或新增信息
     *
     * @param crmModel model
     */
    public Map<String, Object> addOrUpdate(CrmBusinessSaveBO crmModel, boolean isExcel, Long poolId);

    /**
     * 删除客户数据
     *
     * @param ids ids
     */
    public void deleteByIds(List<Long> ids);


    /**
     * 检测数据有无关联
     *
     * @param ids ids
     */
    JSONObject detectionDataCanBeDelete(List<Long> ids);

    /**
     * 修改客户负责人
     *
     * @param changOwnerUserBO data
     */
    public void changeOwnerUser(CrmChangeOwnerUserBO changOwnerUserBO);

    /**
     * 全部导出
     *
     * @param response resp
     * @param search   搜索对象
     */
    public void exportExcel(HttpServletResponse response, CrmSearchBO search, List<Long> sortIds, Integer isXls);


    /**
     * 客户放入公海
     *
     * @param poolBO bo
     */
    public void updateCustomerByIds(CrmCustomerPoolBO poolBO);

    /**
     * 标星
     *
     * @param customerId 客户id
     */
    public void star(Long customerId);

    /**
     * 设置首要联系人
     *
     * @param contactsBO data
     */
    public void setContacts(CrmFirstContactsBO contactsBO);

    /**
     * 领取或分配客户
     *
     * @param poolBO    bo
     * @param isReceive 领取还是分配
     */
    public void getCustomersByIds(CrmCustomerPoolBO poolBO, Integer isReceive);

    /**
     * 下载导入模板
     *
     * @param isPool   是否下载公海模板
     * @param response resp
     * @throws IOException ex
     */
    public void downloadExcel(boolean isPool, HttpServletResponse response) throws IOException;

    /**
     * 保存客户规则设置
     *
     * @param customerSetting setting
     */
    public void customerSetting(CrmCustomerSetting customerSetting);

    /**
     * 删除客户规则设置
     *
     * @param settingId settingId
     */
    public void deleteCustomerSetting(Long settingId);

    /**
     * 查询详情页基本信息
     *
     * @param customerId id
     * @param poolId     公海ID
     * @return data
     */
    public List<CrmModelFiledVO> information(Long customerId, Long poolId);

    /**
     * 修改客户成交状态
     *
     * @param dealStatus 状态
     * @param ids        ids
     */
    public void setDealStatus(Integer dealStatus, List<Long> ids);

    /**
     * 查询客户规则设置
     *
     * @param pageEntity entity
     * @param type       type
     */
    public BasePage<CrmCustomerSetting> queryCustomerSetting(PageEntity pageEntity, Integer type);

    /**
     * 根据客户ID查询联系人
     *
     * @param pageEntity entity
     * @return data
     */
    public BasePage<CrmModel> queryContacts(CrmContactsPageBO pageEntity);

    /**
     * 根据客户ID查询项目
     *
     * @param pageEntity entity
     * @return data
     */
    public BasePage<Map<String, Object>> queryBusiness(CrmContactsPageBO pageEntity);

    /**
     * 根据客户ID查询合同
     *
     * @param pageEntity entity
     * @return data
     */
    public BasePage<Map<String, Object>> queryContract(CrmContactsPageBO pageEntity);

    /**
     * 锁定或者解锁
     *
     * @param status 状态
     * @param ids    ids
     */
    public void lock(Integer status, List<String> ids);

    public List<SimpleCrmEntity> querySimpleEntity(List<Long> ids);

    /**
     * 跟进客户名称查询客户
     *
     * @param name name
     * @return data
     */
    @Cached(expire = 3600, cacheType = CacheType.REMOTE)
    public SimpleCrmEntity queryFirstCustomerByName(String name);

    /**
     * 查询文件数量
     *
     * @param customerId id
     * @return data
     */
    public CrmInfoNumVO num(Long customerId);

    /**
     * 查询文件列表
     *
     * @param customerId id
     * @return file
     */
    public List<FileEntity> queryFileList(Long customerId);

    /**
     * 获取客户名称
     *
     * @param customerId id
     * @return data
     */
    public String getCustomerName(Long customerId);

    boolean isMaxOwner(Long ownerUserId, List<Long> ids);

    void updateInformation(CrmUpdateInformationBO updateInformationBO);

    List<CrmDataCheckVO> dataCheck(CrmDataCheckBO dataCheckBO);

    BasePage<JSONObject> queryReceivablesPlan(CrmRelationPageBO crmRelationPageBO);

    BasePage<JSONObject> queryReceivables(CrmRelationPageBO crmRelationPageBO);

    BasePage<JSONObject> queryReturnVisit(CrmRelationPageBO crmRelationPageBO);

    BasePage<JSONObject> queryInvoice(CrmRelationPageBO crmRelationPageBO);

    BasePage<JSONObject> queryInvoiceInfo(CrmRelationPageBO crmRelationPageBO);

    BasePage<JSONObject> queryCallRecord(CrmRelationPageBO crmRelationPageBO);

    List<JSONObject> nearbyCustomer(String lng, String lat, Integer type, Integer radius, Long ownerUserId);

    List<String> eventCustomer(CrmEventBO crmEventBO);

    BasePage<Map<String, Object>> eventCustomerPageList(QueryEventCrmPageBO eventCrmPageBO);

    List<Long> forgottenCustomer(Integer day, List<Long> userIds, String search);

    List<Long> unContactCustomer(String search, List<Long> userIds);


    BasePage<JSONObject> queryApplyExamine(CrmRelationPageBO crmRelationPageBO);

    /**
     * 查询客户联系人上下级
     *
     * @param customerId parentId
     * @return data
     */
    public List<CrmContactsBo> queryContactsList(Long customerId);

    default CrmMsgLabelEnum getMsgLabelEnum() {
        return CrmMsgLabelEnum.customer;
    }

    public void updateEs(Long customerId, Long superiorCustomerId);


    public void removeAllData(String tableName);
    /**
     * 查看未跟进客户
     * @param userIds
     * @param beginDate
     * @param endDate
     * @return
     */
    List<String> queryNoRecordCustomerList(List<Long> userIds, Date beginDate, Date endDate);

}
