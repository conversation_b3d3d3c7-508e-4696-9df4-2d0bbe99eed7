package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.common.CrmModel;
import com.kakarote.crm.entity.PO.CrmCustomerSuperior;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-07
 */
public interface ICrmCustomerSuperiorService extends BaseService<CrmCustomerSuperior> {

    /**
     * 关联上级或者下级客户
     */
    public void saveAndUpdate(CrmCustomerSuperior superior);

    /**
     * 关联上级或者下级客户
     */
    public void saveAndSudate(CrmCustomerSuperior superior);

    /**
     * 删除关联上级客户或者下级客户
     */
    public void deleteById(Long id);

    /**
     * 删除关联上级客户或者下级客户
     */
    public void deleteById(CrmCustomerSuperior superior);

    /**
     * 根据客户id，查询客户上下级客户
     */
    public CrmModel querySuperiorById(Long id);

}
