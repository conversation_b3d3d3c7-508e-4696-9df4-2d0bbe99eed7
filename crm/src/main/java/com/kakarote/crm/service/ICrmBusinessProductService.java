package com.kakarote.crm.service;

import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.PO.CrmBusinessProduct;

import java.util.List;

/**
 * <p>
 * 项目产品关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface ICrmBusinessProductService extends BaseService<CrmBusinessProduct> {
    /**
     * 通过项目ID删除项目产品关联
     * @param ids ids
     */
    public void deleteByBusinessId(Long... ids);

    /**
     * 保存项目产品关联
     * @param crmBusinessProductList data
     */
    public void save(List<CrmBusinessProduct> crmBusinessProductList);

    /**
     * 通过产品ID删除项目产品关联
     * @param ids ids
     */
    public void deleteByProductId(Long... ids);

    /**
     * 查询项目下产品
     * @param businessId 项目ID
     * @return data
     */
    public List<CrmBusinessProduct> queryList(Long businessId);
}
