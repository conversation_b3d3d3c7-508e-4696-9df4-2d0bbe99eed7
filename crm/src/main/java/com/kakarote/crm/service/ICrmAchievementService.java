package com.kakarote.crm.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.core.servlet.BaseService;
import com.kakarote.crm.entity.BO.AchievementBO;
import com.kakarote.crm.entity.PO.CrmAchievement;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 业绩目标 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-22
 */
public interface ICrmAchievementService extends BaseService<CrmAchievement> {

    /**
     * 查询业绩目标
     * @param achievementBO bo
     * @return data
     */
    public List<CrmAchievement> queryAchievementList(AchievementBO achievementBO);

    /**
     * 保存业绩目标
     * @param achievement achievement
     */
    public void addAchievement(CrmAchievement achievement);


    /**
     * 验证业绩目标数据
     * @date 2020/11/19 14:39
     * @param crmAchievements
     * @return void
     **/
    public void verifyCrmAchievementData(List<CrmAchievement> crmAchievements);


    /**
     * 下载业绩目标导入模板
     */
    public void downloadExcel(HttpServletResponse response,Integer type) throws IOException;

    /**
     * excel导入业绩目标
     * @param file file
     */
    public JSONObject excelImport(MultipartFile file, Integer type);

}
