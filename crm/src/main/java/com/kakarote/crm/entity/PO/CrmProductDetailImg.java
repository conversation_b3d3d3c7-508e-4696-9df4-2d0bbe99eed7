package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品详情图片
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_crm_product_detail_img")
@ApiModel(value="CrmProductDetailImg对象", description="产品详情图片")
public class CrmProductDetailImg implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "img_id",type = IdType.ASSIGN_ID)
    private Long imgId;

    @ApiModelProperty(value = "产品id")
    private Long productId;

    private String remarks;

    @ApiModelProperty(value = "主图")
    private String mainFileIds;

    private String detailFileIds;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


}
