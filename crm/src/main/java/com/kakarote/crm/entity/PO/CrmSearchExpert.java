package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 高级筛选外漏查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_crm_search_expert")
@ApiModel(value="CrmSearchExpert对象", description="高级筛选外漏查询条件")
public class CrmSearchExpert implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "default_id", type = IdType.ASSIGN_ID)
    private Long defaultId;

    @ApiModelProperty(value = "标签 1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款8.回款计划")
    private Integer label;

    @ApiModelProperty(value = "外漏高级筛选的条件")
    private String defaultValue;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;


}
