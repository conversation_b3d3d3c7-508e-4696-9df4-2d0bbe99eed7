package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName: CrmExportBO
 * @Author: Blue
 * @Description: CrmExportBO
 * @Date: 2021/8/18 10:25
 */
@Data
@ToString
@ApiModel(value="导出传参对象", description="导出传参对象")
public class CrmExportBO {

    @ApiModelProperty(value = "选中导出ids")
    private List<Long> ids;

    @ApiModelProperty(value = "字段id列表")
    private List<Long> sortIds;

    @ApiModelProperty(value = "搜索条件")
    private  CrmSearchBO search;

    @ApiModelProperty(value = " 1.xls 2.csv")
    private  Integer isXls = 1;

    @ApiModelProperty(value = "场景ID")
    private Long sceneId;

    @ApiModelProperty(value = "公海ID")
    private Long poolId;

}
