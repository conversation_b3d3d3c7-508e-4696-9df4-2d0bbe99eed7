package com.kakarote.crm.entity.BO;

import com.kakarote.crm.entity.PO.CrmBusinessProduct;
import com.kakarote.crm.entity.PO.CrmCustomerSuperior;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString
@ApiModel("crm项目保存对象")
public class CrmBusinessSaveBO extends CrmModelSaveBO {
    @ApiModelProperty("项目关联产品列表")
    private List<CrmBusinessProduct> product = new ArrayList<>();

    @ApiModelProperty("联系人ID")
    private Long contactsId;

    @ApiModelProperty("上级客户")
    private CrmCustomerSuperior customerSuperior;
}
