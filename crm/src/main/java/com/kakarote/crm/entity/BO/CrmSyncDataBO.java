package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


@ApiModel("市场活动同步BO")
public class CrmSyncDataBO {

    @ApiModelProperty("市场活动ID")
    private Long marketingId;

    @ApiModelProperty("市场活动IDs")
    private List<Long> rIds;

    @ApiModelProperty("状态")
    public Integer status;

    public Long getMarketingId() {
        return marketingId;
    }

    public void setMarketingId(Long marketingId) {
        this.marketingId = marketingId;
    }

    public List<Long> getrIds() {
        return rIds;
    }

    public void setrIds(List<Long> rIds) {
        this.rIds = rIds;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "CrmSyncDataBO{" +
                "marketingId=" + marketingId +
                ", rIds=" + rIds +
                ", status=" + status +
                '}';
    }
}
