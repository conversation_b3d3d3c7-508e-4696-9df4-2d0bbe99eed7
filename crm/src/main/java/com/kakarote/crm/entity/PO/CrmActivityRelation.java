package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import com.kakarote.core.utils.UserUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 活动关联项目联系人表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_crm_activity_relation")
@ApiModel(value="CrmActivityRelation对象", description="活动关联项目联系人表")
public class CrmActivityRelation implements Serializable {

    private static final long serialVersionUID=1L;

    public CrmActivityRelation(Long activityId, Integer type, Long typeId) {
        this.activityId = activityId;
        this.type = type;
        this.typeId = typeId;
        this.createTime = LocalDateTime.now();
        this.createUserId = UserUtil.getUserId();
    }

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("对应跟进记录ID")
    private Long activityId;

    @ApiModelProperty(value = "类型 3 联系人 5 项目")
    private Integer type;

    @ApiModelProperty(value = "类型对应ID")
    private Long typeId;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}
