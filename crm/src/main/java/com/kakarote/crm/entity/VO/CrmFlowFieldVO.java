package com.kakarote.crm.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("高级筛选处查询阶段流程对象")
public class CrmFlowFieldVO {

    @ApiModelProperty("阶段流程ID")
    private Long flowId;

    @ApiModelProperty("流程名称")
    private String flowName;

    @ApiModelProperty("流程列表")
    private List<SettingFieldVO> settingList;

    @Data
    @ApiModel("高级筛选处查询流程对象")
    public static class SettingFieldVO {

        @ApiModelProperty("查询流程ID")
        private Long settingId;

        @ApiModelProperty("流程名称")
        private String settingName;
    }

}
