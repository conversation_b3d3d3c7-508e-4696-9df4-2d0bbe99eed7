package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * 负责人变更BO
 */
@ApiModel(value = "负责人变更过滤BO")
public class CrmChangeOwnerUserFilterBO {

    @ApiModelProperty("变更的ID列表")
    private Long id;

    @ApiModelProperty("新的负责人ID")
    private Long ownerUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    @Override
    public String toString() {
        return "CrmChangeOwnerUserFilterBO{" +
                "id=" + id +
                ", ownerUserId=" + ownerUserId +
                '}';
    }
}
