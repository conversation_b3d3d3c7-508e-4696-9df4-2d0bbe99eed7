package com.kakarote.crm.entity.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.servlet.upload.FileEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ApiModel("活动记录VO")
public class CrmActivityVO {

    @ApiModelProperty
    private Long id;

    @ApiModelProperty("活动类型 1 跟进记录 2 创建记录 3 项目阶段变更 4 外勤签到 5.通话记录")
    private Integer type;

    @ApiModelProperty("跟进类型")
    private String category;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "活动类型 1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款 8日志 9审批 10日程 11任务 12 发邮件")
    private Integer activityType;

    @ApiModelProperty(value = "活动类型Id")
    private Long activityTypeId;

    @ApiModelProperty(value = "活动操作数据名称")
    private String activityTypeName;

    @ApiModelProperty(value = "活动内容")
    private Object content;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下次联系时间")
    private LocalDateTime nextTime;

    @ApiModelProperty(value = "0 删除 1 正常")
    private Integer status;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @ApiModelProperty(value = "创建人id")
    private SimpleUser createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "批次id")
    private String batchId;

    @ApiModelProperty(value = "日期类型，前端需要")
    private Integer timeType;

    @ApiModelProperty(value = "是否有关联数据   0 没有  1 有")
    private Integer isRelation;

    @ApiModelProperty(value = "文件")
    private List<FileEntity> img;
    private List<FileEntity> file;

    @ApiModelProperty
    private List<Relation> contactsList;

    @ApiModelProperty
    private List<Relation> businessList;

    @ApiModelProperty
    private List<Relation> contractList;

    @ApiModelProperty
    private List<Relation> customerList;

    @ApiModelProperty(value = "昵称")
    private String realname;

    @ApiModelProperty(value = "头像")
    private String userImg;

    @ApiModel("关联对象")
    @Data
    public class Relation{

        private Long Id;

        private String name;

        private Integer type;

    }

    @Override
    public String toString() {
        return "CrmActivityVO{" +
                "id=" + id +
                ", type=" + type +
                ", category='" + category + '\'' +
                ", activityId=" + activityId +
                ", activityType=" + activityType +
                ", activityTypeId=" + activityTypeId +
                ", activityTypeName='" + activityTypeName + '\'' +
                ", content=" + content +
                ", nextTime=" + nextTime +
                ", status=" + status +
                ", createUserId=" + createUserId +
                ", createUser=" + createUser +
                ", createTime=" + createTime +
                ", batchId='" + batchId + '\'' +
                ", timeType=" + timeType +
                ", isRelation=" + isRelation +
                ", img=" + img +
                ", file=" + file +
                ", contactsList=" + contactsList +
                ", businessList=" + businessList +
                ", contractList=" + contractList +
                ", customerList=" + customerList +
                ", realname='" + realname + '\'' +
                ", userImg='" + userImg + '\'' +
                '}';
    }
}
