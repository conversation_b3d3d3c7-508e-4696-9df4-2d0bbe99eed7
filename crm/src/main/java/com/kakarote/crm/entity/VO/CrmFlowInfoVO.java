package com.kakarote.crm.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("阶段流程详情INFO")
public class CrmFlowInfoVO {

    @ApiModelProperty(value = "阶段流程ID")
    private Long flowId;

    @ApiModelProperty(value = "阶段流程名称")
    private String flowName;

    @ApiModelProperty(value = "成功阶段的名称")
    private String successName;

    @ApiModelProperty(value = "失败阶段的名称")
    private String failedName;

    @ApiModelProperty(value = "关联业务对象 1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款8.回款计划")
    private Integer label;

    @ApiModelProperty("部门列表")
    private List<Long> deptList;

    @ApiModelProperty("用户列表")
    private List<Long> userList;

    @ApiModelProperty("阶段列表")
    private List<CrmFlowSettingVO> settingList;

}
