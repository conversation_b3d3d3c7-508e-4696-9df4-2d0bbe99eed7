package com.kakarote.crm.entity.BO;

import com.kakarote.core.feign.crm.entity.BiEntityParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("活动记录保存BO")
public class CrmActivityQueryBO extends BiEntityParams {

    @ApiModelProperty("跟进记录查询类型 1 跟进记录  2 创建记录  3 阶段变更  4外勤签到")
    public Integer recordType;

    @ApiModelProperty("活动类型  1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款 8日志 9审批 10日程 11任务 12 发邮件 13 通话记录 14 回款计划")
    private Integer activityType;

    @ApiModelProperty(" 1 跟进   2 活动类型")
    private Integer queryType;

    @ApiModelProperty("活动类型(查询活动，暂用与前端对上)")
    private Integer crmType;

    @ApiModelProperty("活动类型ID")
    private Long activityTypeId;

    @ApiModelProperty("搜索内容")
    private String content;

    @ApiModelProperty("外勤列表使用，筛选创建人")
    private Long userId;

    @ApiModelProperty("跟进记录筛选创建人")
    private List<Long> userList;

    @ApiModelProperty("是否OA查看 用来过滤冗余数据")
    private Boolean isOa;

    public Boolean getIsOa() {
        if(isOa == null){
            isOa = false;
        }
        return isOa;
    }

    @Override
    public String toString() {
        return "CrmActivityQueryBO{" +
                "recordType=" + recordType +
                ", activityType=" + activityType +
                ", queryType=" + queryType +
                ", crmType=" + crmType +
                ", activityTypeId=" + activityTypeId +
                ", content='" + content + '\'' +
                ", userId=" + userId +
                ", userList=" + userList +
                ", isOa=" + isOa +
                '}';
    }
}
