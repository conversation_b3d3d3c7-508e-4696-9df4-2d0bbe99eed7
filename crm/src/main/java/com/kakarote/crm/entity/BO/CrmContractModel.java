package com.kakarote.crm.entity.BO;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class CrmContractModel {
    private String customerkey;
    private BigDecimal orderAmount;
    private String orderDate;
    private String planDate;
    private BigDecimal orderQty;
    private String orderName;
    private String orderType;
    private String contractNo;
    private String cancel;
    List<ProductSend> items;
}
