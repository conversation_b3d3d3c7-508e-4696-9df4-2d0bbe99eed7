package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * crm活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@TableName("wk_crm_activity")
@ApiModel(value="CrmActivity对象", description="crm活动表")
public class CrmActivity implements Serializable {

    private static final long serialVersionUID=1L;

    public CrmActivity(Long id,Integer type, Long activityId,  Integer activityType,Long activityTypeId, String content, Long createUserId, LocalDateTime createTime, Integer isRelation) {
        this.id = id;
        this.type = type;
        this.activityId = activityId;
        this.activityType = activityType;
        this.activityTypeId = activityTypeId;
        this.content = content;
        this.createUserId = createUserId;
        this.createTime = createTime;
        this.isRelation = isRelation;
    }

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "活动类型 1 跟进记录 2 创建记录 3 项目阶段变更 4 外勤签到 5 呼叫记录")
    private Integer type;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "跟进类型")
    @NotNull(message = "跟进类型不能为空")
    private String category;

    @ApiModelProperty(value = "活动类型 1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款 8日志 9审批 10日程 11任务 12 发邮件")
    @NotNull(message = "类型不能为空")
    private Integer activityType;

    @ApiModelProperty(value = "活动类型Id")
    @NotNull(message = "类型id不能为空")
    private Long activityTypeId;

    @ApiModelProperty(value = "活动内容")
    @NotNull(message = "内容不能为空")
    private String content;

    @ApiModelProperty(value = "下次联系时间")
    private LocalDateTime nextTime;

    @ApiModelProperty(value = "0 删除 1 正常")
    private Integer status;

    @ApiModelProperty(value = "创建人id")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "批次id")
    private String batchId;

    @ApiModelProperty(value = "日期类型，前端需要")
    private Integer timeType;

    @ApiModelProperty(value = "是否有关联数据   0 没有  1 有")
    private Integer isRelation;

    @ApiModelProperty(value = "记录冗余数据统一ID")
    private String pid;


}
