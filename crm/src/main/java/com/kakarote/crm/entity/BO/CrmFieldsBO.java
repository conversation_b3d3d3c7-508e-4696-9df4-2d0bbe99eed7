package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * 保存字段
 */
@Data
@ToString
@ApiModel(value="CrmFields查询对象", description="自定义字段表")
public class CrmFieldsBO {

    @ApiModelProperty(value = "标签 1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款8.回款计划 18 发票")
    private Integer label;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "名称")
    private String name;
}
