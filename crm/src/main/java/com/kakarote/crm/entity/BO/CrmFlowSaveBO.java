package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
@ApiModel("阶段流程保存BO")
public class CrmFlowSaveBO {

    @ApiModelProperty(value = "阶段流程ID")
    private Long flowId;

    @ApiModelProperty(value = "阶段流程名称")
    private String flowName;

    @ApiModelProperty(value = "成功阶段的名称")
    private String successName;

    @ApiModelProperty(value = "失败阶段的名称")
    private String failedName;

    @ApiModelProperty(value = "适用部门")
    private List<Long> deptIdList = Collections.emptyList();

    @ApiModelProperty(value = "适用员工")
    private List<Long> userIdList = Collections.emptyList();

    @ApiModelProperty(value = "关联业务对象 1 线索 2 客户 3 联系人 4 产品 5 项目 6 合同 7回款8.回款计划")
    private Integer label;

    @ApiModelProperty(value = "阶段列表")
    private List<CrmFlowSettingBO> settingList;
}
