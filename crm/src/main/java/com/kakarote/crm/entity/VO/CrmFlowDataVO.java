package com.kakarote.crm.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("阶段对象数据VO")
public class CrmFlowDataVO {

    @ApiModelProperty("label")
    private Integer label;

    @ApiModelProperty("对应主键ID")
    private Long typeId;

    @ApiModelProperty("当前进行的阶段ID")
    private Long dataId;

    @ApiModelProperty(value = "成功阶段的名称")
    private String successName;

    @ApiModelProperty(value = "失败阶段的名称")
    private String failedName;

    @ApiModelProperty("赢单率")
    private Integer rate;

    @ApiModelProperty(value = "最终状态 1 成功 2 失败 3 无效 4 冻结 0 进行中")
    private Integer finalStatus;

    @ApiModelProperty("流程列表")
    private List<CrmFlowDataSetting> settingList;

    @ApiModelProperty
    private String remark;

    @ApiModel("阶段对象Setting数据VO")
    @Data
    public static class CrmFlowDataSetting {

        @ApiModelProperty("主键ID")
        private Long id;

        @ApiModelProperty("流程名称")
        private String flowName;

        @ApiModelProperty("状态 1 通过 0 未开始 2 审核中")
        private Integer status;

        @ApiModelProperty("审核记录ID")
        private Long examineRecordId;

        @ApiModelProperty(value = "赢单率")
        private Integer rate;

        @ApiModelProperty("审核状态")
        private Integer examineStatus = 0;

    }
}
