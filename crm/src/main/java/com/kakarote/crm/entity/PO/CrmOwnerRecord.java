package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 负责人变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_crm_owner_record")
@ApiModel(value="CrmOwnerRecord对象", description="负责人变更记录表")
public class CrmOwnerRecord implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "record_id",type = IdType.ASSIGN_ID)
    private Long recordId;

    @ApiModelProperty(value = "对象id")
    private Long typeId;

    @ApiModelProperty(value = "对象类型")
    private Integer type;

    @ApiModelProperty(value = "上一负责人")
    private Long preOwnerUserId;

    @ApiModelProperty(value = "接手负责人")
    private Long postOwnerUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}
