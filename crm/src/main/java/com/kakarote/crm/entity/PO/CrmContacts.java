package com.kakarote.crm.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 联系人表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wk_crm_contacts")
@ApiModel(value="CrmContacts对象", description="联系人表")
public class CrmContacts implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "contacts_id", type = IdType.ASSIGN_ID)
    private Long contactsId;

    @ApiModelProperty(value = "联系人名称")
    private String name;

    @ApiModelProperty(value = "下次联系时间")
    private LocalDateTime nextTime;

    @ApiModelProperty(value = "手机")
    private String mobile;

    @ApiModelProperty(value = "电话")
    private String telephone;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "职务")
    private String post;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    @ApiModelProperty(value = "负责人ID")
    private Long ownerUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "批次")
    private String batchId;

    @ApiModelProperty(value = "最后跟进时间")
    private LocalDateTime lastTime;

    @TableField(exist = false)
    private String policymakers;

    @ApiModelProperty(value = "上级联系人ID")
    private Long parentContactsId;


}
