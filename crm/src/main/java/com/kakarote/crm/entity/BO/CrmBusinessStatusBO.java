package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@ApiModel("crm联系人保存对象")
public class CrmBusinessStatusBO {

    @ApiModelProperty("项目ID")
    private Long businessId;

    @ApiModelProperty("项目状态ID")
    private Long statusId;

    @ApiModelProperty("1 赢单 2 输单 3 无效")
    private Integer isEnd;

    private String statusRemark;
}
