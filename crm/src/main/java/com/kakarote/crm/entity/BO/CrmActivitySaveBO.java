package com.kakarote.crm.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ApiModel("活动记录保存BO")
public class CrmActivitySaveBO {

    @ApiModelProperty("跟进记录ID")
    private Long id;

    @ApiModelProperty("活动类型")
    private Integer activityType;

    @ApiModelProperty("活动类型ID")
    private Long activityTypeId;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("文件上传batchId")
    private String batchId;

    @ApiModelProperty("跟进类型")
    private String category;

    @ApiModelProperty("下次联系时间")
    private LocalDateTime nextTime;

    @ApiModelProperty("导入跟进时间")
    private LocalDateTime createTime;

    @ApiModelProperty("时间类型，前端需要")
    private Integer timeType;

    @ApiModelProperty(value = "经度")
    private String lng;

    @ApiModelProperty(value = "纬度")
    private String lat;

    @ApiModelProperty(value = "签到地址")
    private String address;

    @ApiModelProperty("项目列表")
    private List<Long> businessIds;

    @ApiModelProperty("联系人列表")
    private List<Long> contactsIds;

    @Override
    public String toString() {
        return "CrmActivitySaveBO{" +
                "id=" + id +
                ", activityType=" + activityType +
                ", activityTypeId=" + activityTypeId +
                ", content='" + content + '\'' +
                ", batchId='" + batchId + '\'' +
                ", category='" + category + '\'' +
                ", nextTime=" + nextTime +
                ", createTime=" + createTime +
                ", timeType=" + timeType +
                ", lng='" + lng + '\'' +
                ", lat='" + lat + '\'' +
                ", address='" + address + '\'' +
                ", businessIds=" + businessIds +
                ", contactsIds=" + contactsIds +
                '}';
    }
}
