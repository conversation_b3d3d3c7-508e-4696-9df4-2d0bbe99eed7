package com.kakarote.crm.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiOperation("仪表盘业绩目标VO")
@Data
public class CrmSalesTrendVO {

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("业绩目标金额")
    private BigDecimal achievement;

    @ApiModelProperty("金额")
    private BigDecimal money;
}
