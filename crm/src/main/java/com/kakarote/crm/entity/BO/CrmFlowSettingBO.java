package com.kakarote.crm.entity.BO;

import com.kakarote.core.feign.examine.entity.ExamineSaveBO;
import com.kakarote.crm.entity.PO.CrmFlowField;
import com.kakarote.crm.entity.PO.CrmFlowTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
@ApiModel("阶段设置BO")
public class CrmFlowSettingBO {

    @ApiModelProperty(value = "阶段名称")
    private String name;

    @ApiModelProperty(value = "赢单率")
    private Integer rate;

    @ApiModelProperty(value = "排序字段")
    private Integer orderNum;

    @ApiModelProperty(value = "表单列表")
    private List<CrmFlowField> formList = Collections.emptyList();;

    @ApiModelProperty(value = "任务列表")
    private List<CrmFlowTask> taskList = Collections.emptyList();

    @ApiModelProperty(value = "审批保存对象")
    private ExamineSaveBO examineSaveBO;
}
