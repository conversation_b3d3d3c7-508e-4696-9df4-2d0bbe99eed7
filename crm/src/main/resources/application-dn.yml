spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password:
    database: 12
    lettuce:
      pool:
        max-active: 300
  datasource:
    url: *************************************************************************************************************************************************************************************
    username: root
    password: abcd.1234
  elasticsearch:
    rest:
#      uris: **************:8705
      uris: 127.0.0.1:9200
      username:
      password:

# 合同同步地址
product-send: http://**************:10001/api/Order/sschOrder/createOrder
