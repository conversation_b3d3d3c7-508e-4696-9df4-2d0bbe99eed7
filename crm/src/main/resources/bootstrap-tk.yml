server:
  port: 0
spring:
  profiles:
    active: core,dev
  application:
    name: crm
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 211.141.162.84:8703
        file-extension: yaml
        prefix: crm
        namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
      discovery:
        enabled: true
        server-addr: 211.141.162.84:8703
        namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 127.0.0.1:8079
      datasource:
        ds1:
          nacos:
            server-addr: 211.141.162.84:8703
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
            namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}_tx_group
  config:
    type: nacos
    nacos:
      namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
      serverAddr: 211.141.162.84:8703
      group: SEATA_GROUP
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 211.141.162.84:8703
      namespace: b0c9be8a-283b-4af7-984c-0243ec78def9
