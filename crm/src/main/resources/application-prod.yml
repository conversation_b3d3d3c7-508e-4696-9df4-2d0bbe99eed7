spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123456
    database: 12
    lettuce:
      pool:
        max-active: 300
  datasource:
    url: ******************************************************************************************************************************************************************************************
    username: root
    password: Psu45@!Q=Ehu
  elasticsearch:
    rest:
      uris: **************:8705
      username: elastic
      password: Psu45@!Q=Ehu

# 合同同步地址
product-send: http://**************:10001/api/Order/sschOrder/createOrder
