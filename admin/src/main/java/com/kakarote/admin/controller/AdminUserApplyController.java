package com.kakarote.admin.controller;


import com.alibaba.fastjson.JSONObject;
import com.kakarote.admin.entity.BO.AdminPageApplyBO;
import com.kakarote.admin.entity.BO.AdminSaveApplyBO;
import com.kakarote.admin.service.IAdminUserApplyService;
import com.kakarote.core.common.ParamAspect;
import com.kakarote.core.common.R;
import com.kakarote.core.common.Result;
import com.kakarote.core.entity.BasePage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 员工申请加入企业历史表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-06
 */
@RestController
@RequestMapping("/adminUserApply")
@Api(tags = "员工申请加入企业历史表相关接口")
public class AdminUserApplyController {

    @Autowired
    private IAdminUserApplyService adminUserApplyService;


    @ApiOperation("新增员工申请加入企业历史表")
    @PostMapping("/addUserApply")
    @ParamAspect
    public Result addUserApply(@RequestBody AdminSaveApplyBO applyBO) {
        adminUserApplyService.addUserApply(applyBO);
        return R.ok();
    }

    @ApiOperation("查询员工申请列表")
    @PostMapping("/queryPageByStatus")
    public Result<BasePage<JSONObject>> queryPageByStatus(@RequestBody AdminPageApplyBO applyBO) {
        BasePage<JSONObject> page = adminUserApplyService.queryPageByStatus(applyBO);
        return R.ok(page);
    }

    @ApiOperation("审核")
    @PostMapping("/checkUserApply")
    public Result checkUserApply(@RequestBody AdminSaveApplyBO applyBO) {
        adminUserApplyService.checkUserApply(applyBO);
        return R.ok();
    }

    @ApiOperation("删除")
    @PostMapping("/deleteUserApply")
    public Result deleteUserApply(@RequestBody AdminSaveApplyBO applyBO) {
        adminUserApplyService.deleteUserApply(applyBO);
        return R.ok();
    }

    @ApiOperation("根据员工id，获取员工名称和公司名称")
    @PostMapping("/queryJsonByUserId")
    @ParamAspect
    public Result<JSONObject> queryJsonByUserId(@RequestParam("userId") @NotNull Long userId) {
      JSONObject json =   adminUserApplyService.queryJsonByUserId(userId);
        return R.ok(json);
    }

}

