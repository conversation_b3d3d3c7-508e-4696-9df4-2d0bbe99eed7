package com.kakarote.admin.controller;


import com.kakarote.admin.entity.VO.AdminMessageConfigVO;
import com.kakarote.admin.service.IAdminMessageConfigService;
import com.kakarote.core.common.Result;
import com.kakarote.core.feign.admin.entity.AdminMessageConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 系统消息通知配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@RestController
@RequestMapping("/adminMessageConfig")
@Api(tags = "系统消息配置相关接口")
public class AdminMessageConfigController {

    @Autowired
    private IAdminMessageConfigService messageConfigService;

    @PostMapping("/queryList/{type}")
    @ApiOperation("查询列表")
    public Result<List<AdminMessageConfigVO>> queryList(@ApiParam("类型 1：crm 3：进销存") @PathVariable("type") Integer type) {
        List<AdminMessageConfigVO> configVOS = messageConfigService.queryList(type);
        return Result.ok(configVOS);
    }

    @PostMapping("/updateConfig")
    @ApiOperation("修改配置")
    public Result updateConfig(@RequestBody List<AdminMessageConfigVO> adminMessageConfigVOS) {
        messageConfigService.updateConfig(adminMessageConfigVOS);
        return Result.ok();
    }

    @PostMapping("/getByLabelAndAction")
    @ApiOperation("根据标签和操作获取消息配置")
    public Result<AdminMessageConfig> getByLabelAndAction(@RequestParam("label") String label, @RequestParam("action") String action){
        AdminMessageConfig config = messageConfigService.getByLabelAndAction(label, action);
        return Result.ok(config);
    }
}

