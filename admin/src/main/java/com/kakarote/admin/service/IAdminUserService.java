package com.kakarote.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.admin.entity.BO.*;
import com.kakarote.admin.entity.PO.AdminUser;
import com.kakarote.admin.entity.VO.*;
import com.kakarote.core.entity.AdminUserQueryBO;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.feign.admin.entity.mall.UserDTO;
import com.kakarote.core.servlet.BaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface IAdminUserService extends BaseService<AdminUser> {

    /**
     * 通过用户名查询用户
     *
     * @param username:用户名
     * @param request:HttpServletRequest
     * @return
     */
    public List<Map<String, Object>> findByUsername(String username, HttpServletRequest request);

    /**
     * 查询企业下所有用户
     *
     * @param adminUserBO 业务对象
     * @return ids
     */
    public BasePage<AdminUserVO> queryUserList(AdminUserBO adminUserBO);


    /**
     * 记录用户数量根据label
     *
     * @param
     * @return data
     */
    public JSONObject countUserByLabel();

    /**
     * 查询该用户下级的用户
     *
     * @param userId 用户ID 0代表全部
     * @return data
     */
    public List<Long> queryChildUserId(Long userId);

    /**
     * 修改用户
     *
     * @param adminUserVO data
     */
    public void setUser(AdminUserVO adminUserVO);

    /**
     * 批量修改用户部门
     *
     * @param adminUserBO:用户列表查询
     */
    public void setUserDept(AdminUserBO adminUserBO);

    /**
     * 新增用户
     *
     * @param adminUserVO data
     */
    public void addUser(AdminUserVO adminUserVO);

    /**
     * 修改用户信息
     *
     * @param adminUser
     */
    public void updateUser(AdminUser adminUser);

    /**
     * 修改用户账号功能
     *
     * @param id       用户ID
     * @param username 新的用户名
     * @param password 新的密码
     * @return 操作状态
     */
    public Integer usernameEdit(Long id, String username, String password);


    /**
     * excel导入员工
     *
     * @param file file
     * @return data
     */
    public JSONObject excelImport(MultipartFile file);

    /**
     * 设置状态
     *
     * @param adminUserStatusBO status
     */
    public void setUserStatus(AdminUserStatusBO adminUserStatusBO);

    /**
     * 重置密码
     *
     * @param adminUserStatusBO status
     */
    public void resetPassword(AdminUserStatusBO adminUserStatusBO);

    /**
     * 根据用户ID查询角色ID
     *
     * @param userId userId
     * @return ids
     */
    public List<Long> queryUserRoleIds(Long userId);

    /**
     * 通讯录查询
     *
     * @param userBookBO data
     * @return data
     */
    public BasePage<UserBookVO> queryListName(UserBookBO userBookBO);

    /**
     * 查询组织架构下员工信息
     *
     * @return 组织架构信息
     */
    public AdminOrganizationVO queryOrganizationInfo();

    /**
     * 切换关注状态
     *
     * @param userId 用户ID
     * @return data
     */
    public void attention(Long userId);

    /**
     * 根据ids查询用户信息
     *
     * @param ids id列表
     * @return data
     */
    public List<SimpleUser> queryUserByIds(List<Long> ids);

    /**
     * 查询正常用户根据id
     *
     * @param ids
     * @return id
     */
    public List<Long> queryNormalUserByIds(List<Long> ids);

    /**
     * 根据部门ids查询用户列表
     *
     * @param ids id列表
     * @return data
     */
    public List<Long> queryUserByDeptIds(List<Long> ids);

    /**
     * 根据部门ID查询用户
     *
     * @param deptIds
     * @return
     */
    List<SimpleUser> queryListByDeptIds(List<Long> deptIds);

    /**
     * 人力资源增加用户
     *
     * @param hrmAddUserBO
     * @return
     */
    void hrmAddUser(HrmAddUserBO hrmAddUserBO);

    /**
     * 查询部门用户
     *
     * @param isAllUser
     * @param deptId:部门id
     * @return
     */
    DeptUserListVO queryDeptUserList(Long deptId, boolean isAllUser);

    /**
     * 查询部门用户根据hrm
     *
     * @param deptUserListByHrmBO
     * @return 用户对象
     */
    Set<HrmSimpleUserVO> queryDeptUserListByHrm(DeptUserListByHrmBO deptUserListByHrmBO);

    /**
     * 查询用户id根据名字
     *
     * @param realNames:名字
     * @return
     */
    List<Long> queryUserIdByRealName(List<String> realNames);

    /**
     * 查询登录用户
     *
     * @param userId:用户id
     * @return UserInfo
     */
    UserInfo queryLoginUserInfo(Long userId);

    /**
     * 查询所有员工
     *
     * @return
     */
    public List<UserInfo> queryAllUserInfoList();

    /**
     * 下载员工导入模板
     *
     * @param response:HttpServletResponse
     * @throws IOException:下载模板io异常
     */
    public void downloadExcel(HttpServletResponse response) throws IOException;

    /**
     * 查询用户、角色、部门信息
     *
     * @param requestBO
     * @return
     */
    UserInfoVO queryUserDeptOrRoleInfo(UserInfoRequestBO requestBO);

    /**
     * 查询用户根据用户ID
     *
     * @param userIds:用户id
     * @return data
     */
    List<Map<String, Object>> getUserByIds(List<Long> userIds);

    /**
     * 获取指定用户的上级用户
     *
     * @param queryBO
     * @return
     */
    List<UserInfo> queryParentByLevel(AdminUserQueryBO queryBO);

    /**
     * 查询当前系统有没有初始化
     * @return data
     */
    public Integer querySystemStatus();

    /**
     * 系统用户初始化
     */
    public void initUser(SystemUserBO systemUserBO);

    /**
     * 清空用户缓存
     *
     * @param userIds 用户ID列表
     */
    void clearUserCache(Long... userIds);

    //===========================================提供商城用户接口开始===========================================
    List<UserInfo> seleListForMall(UserDTO userDTO);

    /**
     * 根据userid获取领导
     * @param id
     * @return
     */
    List<String> getLeaders(String id);
    //===========================================提供商城用户接口结束===========================================

}
