package com.kakarote.admin.service;

import com.kakarote.admin.entity.PO.AdminRoleMenu;
import com.kakarote.core.servlet.BaseService;

import java.util.List;

/**
 * <p>
 * 角色菜单对应关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
public interface IAdminRoleMenuService extends BaseService<AdminRoleMenu> {
    /**
     * 保存角色枚举
     *
     * @param roleId:角色id
     * @param menuIdList:menuIdList
     * @return
     */
    public void saveRoleMenu(Long roleId, List<Long> menuIdList);
}
