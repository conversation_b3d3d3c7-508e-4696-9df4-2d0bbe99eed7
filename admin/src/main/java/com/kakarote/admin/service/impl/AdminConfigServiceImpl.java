package com.kakarote.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kakarote.admin.common.AdminCodeEnum;
import com.kakarote.admin.common.AdminConst;
import com.kakarote.admin.common.AdminModuleEnum;
import com.kakarote.admin.common.AuthPasswordUtil;
import com.kakarote.admin.entity.BO.AdminCompanyBO;
import com.kakarote.admin.entity.BO.AdminInitDataBO;
import com.kakarote.admin.entity.BO.LogWelcomeSpeechBO;
import com.kakarote.admin.entity.PO.*;
import com.kakarote.admin.entity.VO.ModuleSettingVO;
import com.kakarote.admin.mapper.AdminConfigMapper;
import com.kakarote.admin.service.*;
import com.kakarote.core.common.cache.AdminCacheKey;
import com.kakarote.core.common.cache.CrmCacheKey;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.crm.service.CrmAnalysisService;
import com.kakarote.core.feign.finance.service.FinanceService;
import com.kakarote.core.feign.hrm.service.HrmService;
import com.kakarote.core.feign.jxc.service.JxcExamineService;
import com.kakarote.core.feign.oa.OaService;
import com.kakarote.core.feign.work.WorkService;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.BaseUtil;
import com.kakarote.core.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kakarote.admin.common.AdminModuleEnum.CALL;
import static com.kakarote.admin.common.AdminModuleEnum.getValues;

/**
 * <p>
 * 客户规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
@Slf4j
public class AdminConfigServiceImpl extends BaseServiceImpl<AdminConfigMapper, AdminConfig> implements IAdminConfigService {

    @Autowired
    private IAdminRoleService adminRoleService;

    @Autowired
    private IAdminUserService adminUserService;

    private static final String LOG_CONFIG_NAME = "logWelcomeSpeech";

    @Autowired
    private IAdminAttentionService adminAttentionService;

    @Autowired
    private IAdminUserRoleService adminUserRoleService;

    @Autowired
    private CrmAnalysisService crmAnalysisService;

    @Autowired
    private WorkService workService;

    @Autowired
    private OaService oaService;

    @Autowired
    private FinanceService financeService;
    @Autowired
    private HrmService hrmService;

    @Resource
    private JxcExamineService jxcExamineService;

    @Autowired
    private IAdminMessageService adminMessageService;

    @Autowired
    private IAdminConfigService adminConfigService;

    /**
     * 通过name查询系统配置
     *
     * @param names names
     * @return adminConfig
     */
    @Override
    public List<AdminConfig> queryConfigListByName(Object... names) {
        return query().in("name", names).list();
    }

    /**
     * 设置企业配置
     *
     * @param adminConfig config
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAdminConfig(AdminCompanyBO adminConfig) {
        if (StrUtil.isEmpty(adminConfig.getCompanyLogo())) {
            adminConfig.setCompanyLogo(null);

        }
        //企业名称
        AdminConfig companyName = adminConfigService.queryConfigByName("companyName");
        LambdaUpdateWrapper<AdminConfig> updateCompanyNameWrapper = new LambdaUpdateWrapper<>();
        updateCompanyNameWrapper.set(AdminConfig::getValue, adminConfig.getCompanyName());
        updateCompanyNameWrapper.eq(AdminConfig::getSettingId, companyName.getSettingId());
        adminConfigService.update(updateCompanyNameWrapper);
        //主页面LOGO
        AdminConfig companyLogo = adminConfigService.queryConfigByName("companyLogo");
        LambdaUpdateWrapper<AdminConfig> updateCompanyLogoWrapper = new LambdaUpdateWrapper<>();
        updateCompanyLogoWrapper.set(AdminConfig::getValue, adminConfig.getCompanyLogo());
        updateCompanyLogoWrapper.eq(AdminConfig::getSettingId, companyLogo.getSettingId());
        adminConfigService.update(updateCompanyLogoWrapper);
        //登录页面LOGO
        AdminConfig companyLoginLogo = adminConfigService.queryConfigByName("companyLoginLogo");
        LambdaUpdateWrapper<AdminConfig> updateCompanyLoginLogoWrapper = new LambdaUpdateWrapper<>();
        updateCompanyLoginLogoWrapper.set(AdminConfig::getValue, adminConfig.getCompanyLoginLogo());
        updateCompanyLoginLogoWrapper.eq(AdminConfig::getSettingId, companyLoginLogo.getSettingId());
        adminConfigService.update(updateCompanyLoginLogoWrapper);
    }

    /**
     * 查询企业配置
     *
     * @return adminCompanyBO
     */
    @Override
    public AdminCompanyBO queryAdminConfig() {
        List<AdminConfig> configs = adminConfigService.queryConfigListByName("companyName", "companyLogo", "companyLoginLogo");
        Map<String, String> collect = configs.stream().collect(Collectors.toMap(AdminConfig::getName,
                entry -> Optional.ofNullable(entry.getValue()).orElse("")));
        return BeanUtil.toBean(collect, AdminCompanyBO.class);
    }

    /**
     * 查询模块设置
     *
     * @return data
     */
    @Override
    public List<ModuleSettingVO> queryModuleSetting() {
        //应用申请信息
        List<AdminConfig> adminConfigList = queryConfigListByName((Object[]) getValues());
        List<ModuleSettingVO> moduleSettingList = new ArrayList<>();
        adminConfigList.forEach(adminConfig -> {
            ModuleSettingVO moduleSettingVO = new ModuleSettingVO();
            moduleSettingVO.setSettingId(adminConfig.getSettingId());
            moduleSettingVO.setModule(adminConfig.getName());
            moduleSettingVO.setStatus(adminConfig.getStatus());
            moduleSettingVO.setType(adminConfig.getValue());
            moduleSettingVO.setName(adminConfig.getDescription());
            moduleSettingVO.setNumber(1000);
            moduleSettingList.add(moduleSettingVO);
        });
        return moduleSettingList;
    }

    @Autowired
    private Redis redis;

    /**
     * 设置模块的禁用启用
     *
     * @param adminConfig data
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setModuleSetting(AdminConfig adminConfig) {
        String key = "ModuleSetting:Data";
        long timeOut=10L;
        if (redis.setNx(key, timeOut, 1)) {
            lambdaUpdate().set(AdminConfig::getStatus, adminConfig.getStatus()).eq(AdminConfig::getSettingId, adminConfig.getSettingId()).update();
            //查询企业用户，将企业用户的权限缓存清除，重新计算
            List<AdminUser> adminUsers = adminUserService.lambdaQuery().select(AdminUser::getUserId).list();
            //清除缓存
            adminRoleService.authInvalidate(adminUsers.stream().map(AdminUser::getUserId).collect(Collectors.toList()));
            redis.del(key);
        } else {
            throw new CrmException(SystemCodeEnum.SYSTEM_BAD_REQUEST);
        }

    }

    /**
     * 设置日志欢迎语
     *
     * @param stringList data
     */
    @Override
    public void setLogWelcomeSpeech(List<String> stringList) {
        List<AdminConfig> configList = new ArrayList<>();
        stringList.forEach(str -> {
            AdminConfig config = new AdminConfig();
            config.setName(LOG_CONFIG_NAME);
            config.setValue(str);
            config.setStatus(1);
            configList.add(config);
        });
        QueryWrapper<AdminConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", LOG_CONFIG_NAME);
        remove(queryWrapper);
        saveBatch(configList, AdminConst.BATCH_SAVE_SIZE);
    }

    /**
     * 查询日志欢迎语
     *
     * @return data
     */
    @Override
    public List<LogWelcomeSpeechBO> getLogWelcomeSpeechList() {
        List<AdminConfig> adminConfigList = query().select("setting_id", "value").eq("name", LOG_CONFIG_NAME).list();
        return adminConfigList.stream().map(adminConfig -> BeanUtil.copyProperties(adminConfig, LogWelcomeSpeechBO.class)).collect(Collectors.toList());
    }

    /**
     * 根据名称查询配置信息
     *
     * @param name
     * @return data
     */
    @Override
    public AdminConfig queryConfigByName(String name) {
        return query().in("name", name).last(" limit 1").one();
    }

    /**
     * 查询呼叫中心设置
     *
     * @return data
     */
    @Override
    public ModuleSettingVO queryCallModuleSetting() {
        AdminConfig adminConfig = queryConfigByName(CALL.getValue());
        ModuleSettingVO moduleSettingVO = new ModuleSettingVO();
        moduleSettingVO.setSettingId(adminConfig.getSettingId());
        moduleSettingVO.setModule(adminConfig.getName());
        moduleSettingVO.setStatus(adminConfig.getStatus());
        moduleSettingVO.setType(adminConfig.getValue());
        moduleSettingVO.setName(adminConfig.getDescription());
        return moduleSettingVO;
    }


    @Override
    public void updateAdminConfig(AdminConfig adminConfig) {
        saveOrUpdate(adminConfig);
    }

    @Override
    public AdminConfig queryFirstConfigByNameAndValue(String name, String value) {
        return lambdaQuery().eq(AdminConfig::getName, name).eq(AdminConfig::getValue, value).one();
    }

    @Override
    public String verifyPassword(AdminInitDataBO adminInitDataBO) {
        Long userId = UserUtil.getUserId();
        AdminUser user = adminUserService.getById(userId);
        String userName = user.getUsername();
        boolean isPass = AuthPasswordUtil.verify(userName + adminInitDataBO.getPassword(), user.getSalt(), user.getPassword());
        if (isPass) {
            String cacheName = AdminCacheKey.TEMPORARY_ACCESS_CODE_CACHE_KEY + userId;
            String value = String.valueOf(RandomUtil.randomInt(100000, 999999));
            BaseUtil.getRedis().setex(cacheName, 600, value);
            return value;
        }
        throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INVALID_ERROR);
    }


    @Override
    public boolean moduleInitData(AdminInitDataBO adminInitDataBO) {
        Long userId = UserUtil.getUserId();
        String cacheName = AdminCacheKey.TEMPORARY_ACCESS_CODE_CACHE_KEY + userId;
        String temporaryCode = BaseUtil.getRedis().get(cacheName);
        if (StrUtil.isEmpty(temporaryCode)) {
            //超时未操作
            throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_EXPIRE_ERROR);
        }
        if (!temporaryCode.equals(adminInitDataBO.getTemporaryCode())) {
            throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INVALID_ERROR);
        }
        redis.del(CrmCacheKey.CRM_BACKLOG_NUM_CACHE_KEY + userId.toString());
        List<String> modules = adminInitDataBO.getModules();
        if (CollUtil.isNotEmpty(modules)) {
            for (String module : modules) {
                AdminModuleEnum adminModuleEnum = AdminModuleEnum.parse(module);
                if (adminModuleEnum == null) {
                    continue;
                }
                switch (adminModuleEnum) {
                    case TASK_EXAMINE:
                        //oa、work
                        oaService.initOaExamineData();
                        workService.initWorkTask();
                        adminMessageService.lambdaUpdate().eq(AdminMessage::getLabel, 1).remove();
                        break;
                    case CRM:
                        crmAnalysisService.initCrmData();
                        break;
                    case PROJECT:
                        workService.initWorkData();
                        break;
                    case LOG:
                    case OA:
                        oaService.initOaData();
                        adminMessageService.lambdaUpdate().in(AdminMessage::getLabel, 2, 3).remove();
                        break;
                    case BOOK:
                        if (!UserUtil.isAdmin()) {
                            if (this.verifyInitAuth()) {
                                throw new CrmException(SystemCodeEnum.SYSTEM_NO_AUTH);
                            }
                        }
                        log.info("开始初始化通讯录模块数据！");
                        List<AdminUser> adminUsers = adminUserService.lambdaQuery().select(AdminUser::getUserId)
                                .ne(AdminUser::getUserId, UserUtil.getUserId()).list();
                        if (CollUtil.isNotEmpty(adminUsers)) {
                            List<Long> userIds = adminUsers.stream().map(AdminUser::getUserId).collect(Collectors.toList());
                            adminAttentionService.lambdaUpdate().in(AdminAttention::getBeUserId, userIds)
                                    .or().in(AdminAttention::getAttentionUserId, userIds).remove();
                            adminUserRoleService.lambdaUpdate().in(AdminUserRole::getUserId, userIds).remove();
                            adminUserService.removeByIds(userIds);
                        }
                        log.info("通讯录模块数据初始化完成！");
                        break;
                    case CALENDAR:
                        //oa
                        oaService.initCalendarData();
                        adminMessageService.lambdaUpdate().eq(AdminMessage::getLabel, 5).remove();
                        break;
                    case HRM:
                        log.info("人力资源模块重置数据完成！");
                        AdminConfig company = adminConfigService.queryConfigByName("companyName");
                        String companyName = Optional.ofNullable(company.getValue()).orElse("全公司");
                        hrmService.initData(UserUtil.getUserId(), companyName, false);
                        log.info("人力资源模块初始化数据完成！");
                        adminMessageService.lambdaUpdate().eq(AdminMessage::getLabel, 8).remove();
                        break;
                    case FINANCE:
                        financeService.initFinanceData();
                        break;
                    case JXC:
                        // 20220214 wwl 新增 jxc
                        jxcExamineService.initJxcData();
                        break;
                    default:
                        break;
                }
            }
        }
        return true;
    }

    private static final String INIT_AUTH_URL = "/adminConfig/moduleInitData";


    /**
     * 验证非管理员有无权限
     *
     * @param
     * @return boolean
     * @date 2020/11/23 10:35
     **/
    private boolean verifyInitAuth() {
        boolean isNoAuth = false;
        Long userId = UserUtil.getUserId();
        String key = userId.toString();
        List<String> noAuthMenuUrls = BaseUtil.getRedis().get(key);
        if (noAuthMenuUrls != null && noAuthMenuUrls.contains(INIT_AUTH_URL)) {
            isNoAuth = true;
        }
        return isNoAuth;
    }
}
