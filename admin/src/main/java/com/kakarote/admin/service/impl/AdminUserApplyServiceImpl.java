package com.kakarote.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CreateCache;
import com.kakarote.admin.common.AdminCodeEnum;
import com.kakarote.admin.common.AdminConst;
import com.kakarote.admin.entity.BO.AdminPageApplyBO;
import com.kakarote.admin.entity.BO.AdminSaveApplyBO;
import com.kakarote.admin.entity.PO.AdminDept;
import com.kakarote.admin.entity.PO.AdminUser;
import com.kakarote.admin.entity.PO.AdminUserApply;
import com.kakarote.admin.mapper.AdminUserApplyMapper;
import com.kakarote.admin.service.*;
import com.kakarote.core.common.Const;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.UserInfo;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.UserCacheUtil;
import com.kakarote.core.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 员工申请加入企业历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-06
 */
@Service
public class AdminUserApplyServiceImpl extends BaseServiceImpl<AdminUserApplyMapper, AdminUserApply> implements IAdminUserApplyService {

    @Autowired
    private IAdminUserService adminUserService;

    @Autowired
    private IAdminDeptService adminDeptService;

    @Autowired
    private IAdminUserConfigService adminUserConfigService;

    @Autowired
    private IAdminUserRoleService adminUserRoleService;

    @CreateCache(name = Const.ADMIN_USER_NAME_CACHE_NAME, expire = 3, timeUnit = TimeUnit.DAYS)
    private Cache<Long, SimpleUser> userCache;

    @Override
    public void addUserApply(AdminSaveApplyBO applyBO) {
        AdminUserApply adminUserApply = BeanUtil.copyProperties(applyBO, AdminUserApply.class);
        if (!ReUtil.isMatch(AdminConst.DEFAULT_PASSWORD_INTENSITY, adminUserApply.getPassword())) {
            throw new CrmException(AdminCodeEnum.ADMIN_PASSWORD_INTENSITY_ERROR);
        }
       UserInfo userInfo =  UserUtil.setUser(applyBO.getInviteUserId());
        AdminUserApply userApply = lambdaQuery().eq(AdminUserApply::getPhone,applyBO.getPhone())
                .ne(AdminUserApply::getStatus,3).one();
        if (userApply == null) {
            adminUserApply.setCreateTime(LocalDateTimeUtil.now());
            adminUserApply.setUpdateTime(LocalDateTimeUtil.now());
            adminUserApply.setStatus(2);
            save(adminUserApply);
        }else {
            if (userApply.getUserId() != null){
                throw new CrmException(AdminCodeEnum.ADMIN_USER_EXIST_ERROR);
            }
            Integer count = adminUserService.lambdaQuery().eq(AdminUser::getUsername,applyBO.getPhone()).count();
            if (count > 0){
                throw new CrmException(AdminCodeEnum.ADMIN_USER_EXIST_ERROR);
            }
            adminUserApply.setId(userApply.getId());
            adminUserApply.setUpdateTime(LocalDateTimeUtil.now());
            adminUserApply.setStatus(2);
            updateById(adminUserApply);
        }
    }

    @Override
    public BasePage<JSONObject> queryPageByStatus(AdminPageApplyBO applyBO) {
        BasePage<AdminPageApplyBO> userBasePage = new BasePage<>(applyBO.getPage(),applyBO.getLimit());
        return baseMapper.queryUserList(userBasePage,applyBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkUserApply(AdminSaveApplyBO applyBO) {
        lambdaUpdate().set(AdminUserApply::getStatus,applyBO.getStatus()).in(AdminUserApply::getId,applyBO.getIds()).update();
        if (applyBO.getStatus() == 1){
            for (Integer id: applyBO.getIds()) {
                AdminUserApply userApply = getById(id);
                AdminUser adminUserPO = BeanUtil.copyProperties(userApply, AdminUser.class);
                String salt = IdUtil.fastSimpleUUID();
                adminUserPO.setCreateTime(LocalDateTimeUtil.now());
                adminUserPO.setNum(RandomUtil.randomNumbers(15));
                adminUserPO.setMobile(userApply.getPhone());
                adminUserPO.setSalt(salt);
                AdminDept adminDept = adminDeptService.lambdaQuery().eq(AdminDept::getParentId,0).last(" limit 1").one();
                adminUserPO.setDeptId(adminDept.getDeptId());
                adminUserPO.setUsername(userApply.getPhone());
                adminUserPO.setPassword(UserUtil.sign((userApply.getPhone().trim() + userApply.getPassword().trim()), salt));
                adminUserService.save(adminUserPO);
                adminUserConfigService.initUserConfig(adminUserPO.getUserId());
                adminUserRoleService.saveByUserId(adminUserPO.getUserId());

                Long key = adminUserPO.getUserId();
                userCache.put(key, new SimpleUser(adminUserPO.getUserId(),adminUserPO.getImg(),adminUserPO.getRealname(),adminUserPO.getDeptId(),adminDeptService.getNameByDeptId(adminUserPO.getDeptId())));
                lambdaUpdate().set(AdminUserApply::getExamineUserId,UserUtil.getUserId())
                        .set(AdminUserApply::getUserId,adminUserPO.getUserId())
                        .eq(AdminUserApply::getId,id).update();
            }
        }
    }

    @Override
    public void deleteUserApply(AdminSaveApplyBO applyBO) {
        lambdaUpdate().set(AdminUserApply::getStatus,3).in(AdminUserApply::getId,applyBO.getIds()).update();
    }

    @Override
    public JSONObject queryJsonByUserId(Long userId) {
        UserInfo userInfo = UserCacheUtil.getUserInfo(userId);
        JSONObject json  = new JSONObject();
        json.put("userName",userInfo.getRealname());
        json.put("companyName",userInfo.getCompanyName());
        return json;
    }
}
