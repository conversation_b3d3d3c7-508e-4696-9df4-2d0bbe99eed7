package com.kakarote.admin.service;

import com.kakarote.admin.entity.VO.AdminMessageConfigVO;
import com.kakarote.core.feign.admin.entity.AdminMessageConfig;
import com.kakarote.core.servlet.BaseService;

import java.util.List;

/**
 * <p>
 * 系统消息通知配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
public interface IAdminMessageConfigService extends BaseService<AdminMessageConfig> {

    /**
     * 查询消息配置列表页
     * @param type 类型
     * @return data
     */
    public List<AdminMessageConfigVO> queryList(Integer type);

    /**
     * 修改消息推送配置
     * @param adminMessageConfigVOS data
     */
    public void updateConfig(List<AdminMessageConfigVO> adminMessageConfigVOS);

    /**
     * 根据标签和操作获取消息配置
     *
     * @param label
     * @param action
     * @return
     */
    AdminMessageConfig getByLabelAndAction(String label, String action);
}
