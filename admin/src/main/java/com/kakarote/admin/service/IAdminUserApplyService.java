package com.kakarote.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.kakarote.admin.entity.BO.AdminPageApplyBO;
import com.kakarote.admin.entity.BO.AdminSaveApplyBO;
import com.kakarote.admin.entity.PO.AdminUserApply;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.servlet.BaseService;

/**
 * <p>
 * 员工申请加入企业历史表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-06
 */
public interface IAdminUserApplyService extends BaseService<AdminUserApply> {

    /**
     * 新增员工申请加入企业历史表
     *
     * @param applyBO:员工申请加入公司记录
     */
    public void addUserApply(AdminSaveApplyBO applyBO);


    /**
     * 查询员工申请列表
     *
     * @param applyBO:查询员工申请列表查询对象
     * @return data
     */
    public BasePage<JSONObject> queryPageByStatus(AdminPageApplyBO applyBO);

    /**
     * 审核
     *
     * @param applyBO:员工申请加入公司记录
     */
    public void checkUserApply(AdminSaveApplyBO applyBO);

    /**
     * 删除
     *
     * @param applyBO:员工申请加入公司记录
     */
    public void deleteUserApply(AdminSaveApplyBO applyBO);

    /**
     * 根据员工id，获取员工名称和公司名称
     *
     * @param userId:用户id
     * @return data
     */
    public JSONObject queryJsonByUserId(Long userId);

}
