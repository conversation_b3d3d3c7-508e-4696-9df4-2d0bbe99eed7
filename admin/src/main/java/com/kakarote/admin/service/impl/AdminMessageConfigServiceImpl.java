package com.kakarote.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.kakarote.admin.entity.VO.AdminMessageConfigVO;
import com.kakarote.admin.mapper.AdminMessageConfigMapper;
import com.kakarote.admin.service.IAdminMessageConfigService;
import com.kakarote.core.common.Const;
import com.kakarote.core.feign.admin.entity.AdminMessageConfig;
import com.kakarote.core.redis.Redis;
import com.kakarote.core.servlet.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统消息通知配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@Service
public class AdminMessageConfigServiceImpl extends BaseServiceImpl<AdminMessageConfigMapper, AdminMessageConfig> implements IAdminMessageConfigService {

    @Autowired
    private Redis redis;

    /**
     * crm消息label
     */
    private static final List<String> CRM_LABEL_LIST = Arrays.asList("leads", "customer", "contacts", "business", "contract", "receivables", "invoice", "product");

    /**
     * 进销存消息label
     */
    private static final List<String> JXC_LABEL_LIST = Arrays.asList("supplier", "purchase", "retreat", "sale", "sale_return", "receipt", "outbound", "allocation", "inventory", "detailed", "collection", "payment");


    @Override
    public List<AdminMessageConfigVO> queryList(Integer type) {
        int jxcIndex = 3;
        List<String> labelList;
        if (Objects.equals(1, type)) {
            labelList = CRM_LABEL_LIST;
        } else if (Objects.equals(jxcIndex, type)) {
            labelList = JXC_LABEL_LIST;
        } else {
            return Collections.emptyList();
        }
        List<AdminMessageConfig> adminMessageConfigList = lambdaQuery().in(AdminMessageConfig::getLabel, labelList).list();
        if (adminMessageConfigList.isEmpty()) {
            saveMessageConfig();
            queryList(type);
        }
        return adminMessageConfigList.stream().map(adminMessageConfig -> {
            AdminMessageConfigVO messageConfigVO = BeanUtil.copyProperties(adminMessageConfig, AdminMessageConfigVO.class);
            messageConfigVO.setPushCustomUserList(StrUtil.splitTrim(adminMessageConfig.getPushCustomUser(), Const.SEPARATOR).stream().map(Long::parseLong).collect(Collectors.toList()));
            messageConfigVO.setPushParentUserList(StrUtil.splitTrim(adminMessageConfig.getPushParentUser(), Const.SEPARATOR).stream().map(Integer::parseInt).collect(Collectors.toList()));
            messageConfigVO.setPushCustomDeptList(StrUtil.splitTrim(adminMessageConfig.getPushCustomDept(), Const.SEPARATOR).stream().map(Integer::parseInt).collect(Collectors.toList()));
            return messageConfigVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateConfig(List<AdminMessageConfigVO> adminMessageConfigVOS) {
        Date date = new Date();
        List<AdminMessageConfig> messageConfigList = adminMessageConfigVOS.stream().map(adminMessageConfigVO -> {
            AdminMessageConfig adminMessageConfig = BeanUtil.copyProperties(adminMessageConfigVO, AdminMessageConfig.class);
            adminMessageConfig.setUpdateTime(date);
            adminMessageConfig.setPushCustomUser(StrUtil.join(Const.SEPARATOR, adminMessageConfigVO.getPushCustomUserList()));
            adminMessageConfig.setPushParentUser(StrUtil.join(Const.SEPARATOR, adminMessageConfigVO.getPushParentUserList()));
            adminMessageConfig.setPushCustomDept(StrUtil.join(Const.SEPARATOR, adminMessageConfigVO.getPushCustomDeptList()));
            return adminMessageConfig;
        }).collect(Collectors.toList());
        updateBatchById(messageConfigList);
    }

    private void saveMessageConfig() {
        String key = "SaveMessageConfig";
        /* 加锁防止重复插入 */
        long timeOut = 10L;
        if (redis.setNx(key, timeOut, 0)) {
            List<AdminMessageConfig> adminMessageConfigList = new ArrayList<>(64);
            for (String label : CRM_LABEL_LIST) {
                List<String> nameList = CollUtil.newArrayList("save", "transfer", "excelImport", "excelExport");
                switch (label) {
                    case "leads":
                        nameList.add(2, "transform");
                        break;
                    case "customer":
                        nameList.add(2, "termMember");
                        break;
                    case "business":
                    case "contract":
                        nameList.add(2, "termMember");
                        nameList.remove("excelImport");
                        break;
                    case "receivables":
                        nameList.remove("excelImport");
                        break;
                    case "invoice":
                        nameList.remove("transfer");
                        nameList.remove("excelImport");
                        nameList.add(1, "updateInvoiceStatus");
                        break;
                    case "product":
                        nameList.remove("transfer");
                        nameList.remove("save");
                        break;
                    default:
                        break;
                }
                for (String name : nameList) {
                    adminMessageConfigList.add(getDefaultMessageConfig(label, name));
                }
            }
            for (String label : JXC_LABEL_LIST) {
                List<String> nameList = CollUtil.newArrayList("save", "transfer", "setState", "excelExport");
                switch (label) {
                    case "supplier":
                        nameList.remove("setState");
                        break;
                    case "receipt":
                    case "outbound":
                    case "allocation":
                    case "collection":
                    case "payment":
                    case "inventory":
                        nameList.remove("transfer");
                        break;
                    case "detailed":
                        nameList.remove("save");
                        nameList.remove("transfer");
                        nameList.remove("setState");
                    default:
                        break;
                }
                for (String name : nameList) {
                    adminMessageConfigList.add(getDefaultMessageConfig(label, name));
                }
            }
            saveBatch(adminMessageConfigList, Const.BATCH_SAVE_SIZE);
        }

    }

    private AdminMessageConfig getDefaultMessageConfig(String label, String name) {
        AdminMessageConfig adminMessageConfig = new AdminMessageConfig();
        adminMessageConfig.setLabel(label);
        adminMessageConfig.setName(name);
        adminMessageConfig.setPushMessage(1);
        adminMessageConfig.setPushOwnerUser(1);
        adminMessageConfig.setPushOwnerUser(1);
        adminMessageConfig.setPushTermMember(1);
        adminMessageConfig.setPushParentUser(null);
        adminMessageConfig.setPushCustomUser(null);
        adminMessageConfig.setPushBrowser(0);
        adminMessageConfig.setPushSms(0);
        return adminMessageConfig;
    }

    @Override
    public AdminMessageConfig getByLabelAndAction(String label, String action) {
        return lambdaQuery().eq(AdminMessageConfig::getLabel, label)
                .eq(AdminMessageConfig::getName, action)
                .last("limit 1").one();
    }
}
