package com.kakarote.admin.entity.VO;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.kakarote.core.feign.admin.entity.SimpleUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="部门查询对象", description="部门对象")
public class AdminDeptVO {

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "上级部门ID，0为最上级")
    private Long parentId;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "部门负责人")
    private Long ownerUserId;

    @ApiModelProperty(value = "下级部门列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AdminDeptVO> children;

    @ApiModelProperty(value = "部门下员工列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SimpleUser> userList;


    @ApiModelProperty("当前部门在职人数")
    private Integer currentNum;
}
