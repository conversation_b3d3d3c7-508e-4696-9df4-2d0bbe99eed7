package com.kakarote.admin.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel("邮箱验证VO")
@Getter
@Setter
public class AdminEmailVerifyVO {

    @ApiModelProperty("是否是管理员")
    private boolean adminUser;

    @ApiModelProperty("剩余天数")
    private Integer expired;

    @ApiModelProperty("是否已验证")
    private boolean verify;

    @ApiModelProperty("是否一致")
    private boolean same;

    @Override
    public String toString() {
        return "AdminEmailVerifyVO{" +
                "adminUser=" + adminUser +
                ", expired=" + expired +
                ", verify=" + verify +
                ", same=" + same +
                '}';
    }
}
