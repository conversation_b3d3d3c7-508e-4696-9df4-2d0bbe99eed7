package com.kakarote.admin.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 用户信息查询请求
 * @date 2021/8/19 15:24
 */
@Data
@ApiModel(value = "用户信息查询请求BO", description = "用户信息查询请求BO")
public class UserInfoRequestBO {

    @ApiModelProperty(value = "用户ID")
    private List<Long> userIds;

    @ApiModelProperty(value = "部门ID")
    private List<Long> deptIds;

    @ApiModelProperty(value = "角色ID")
    private List<Long> roleIds;
}
