package com.kakarote.admin.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 模块设置VO
 */
@ToString
@Data
@ApiModel("应用管理设置")
public class ModuleSettingVO {

    @ApiModelProperty(value = "设置ID", required = true)
    private Long settingId;

    @ApiModelProperty(value = "模块", required = true)
    private String module;

    @ApiModelProperty(value = "状态 1:启用 0:停用 2:试用中 3:已过期", required = true,allowableValues = "0,1,2,3")
    private Integer status;

    @ApiModelProperty(value = "类型 1:普通应用 2:增值应用 3:未发布应用 4:按次数使用应用", required = true,allowableValues = "1,2,3")
    private String type;

    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @ApiModelProperty(value = "试用截止时间")
    private LocalDateTime endTime;

    @ApiModelProperty("可使用次数")
    private Integer number;


     /**
      * wwl 20220122新增加字段 工商查询功能的已购买次数
     **/
    @ApiModelProperty("已购买次数")
    private Integer paidNumber;

    public ModuleSettingVO() {
    }

    public ModuleSettingVO(Long settingId, String module, Integer status, String type, String name, LocalDateTime endTime, Integer number) {
        this.settingId = settingId;
        this.module = module;
        this.status = status;
        this.type = type;
        this.name = name;
        this.endTime = endTime;
        this.number = number;
    }
}
