package com.kakarote.admin.entity.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value="消息配置VO", description="系统消息通知配置")
public class AdminMessageConfigVO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "消息推送配置一级key，如线索，客户等")
    private String label;

    @ApiModelProperty(value = "消息推送配置二级key，如导入，转移等")
    private String name;

    @ApiModelProperty(value = "是否开启消息推送 0 否 1 是")
    private Integer pushMessage;

    @ApiModelProperty(value = "是否推送负责人 0 否 1 是")
    private Integer pushOwnerUser;

    @ApiModelProperty(value = "是否推送团队成员  0 否 1 是")
    private Integer pushTermMember;

    @ApiModelProperty(value = "是否推送上级，0为不推送，否则为上级层级")
    private List<Integer> pushParentUserList;

    @ApiModelProperty(value = "是否推送自定义用户，用户id数组，最多10位")
    private List<Long> pushCustomUserList;

    @ApiModelProperty(value = "是否推送自定义部门，部门id数组，最多10位")
    private List<Integer> pushCustomDeptList;

    @ApiModelProperty(value = "是否开启浏览器通知 0 否 1 是")
    private Integer pushBrowser;

    @ApiModelProperty(value = "是否开启短信通知 0 否 1 是")
    private Integer pushSms;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
