package com.kakarote.admin.entity.VO;

import com.alibaba.fastjson.JSONObject;

public class AdminSuperUserVo extends AdminUserVO {
    private JSONObject serverUserInfo;

    private Integer emailId;

    private Integer isReadNotice;

    public JSONObject getServerUserInfo() {
        return serverUserInfo;
    }

    public void setServerUserInfo(JSONObject serverUserInfo) {
        this.serverUserInfo = serverUserInfo;
    }

    public Integer getEmailId() {
        return emailId;
    }

    public void setEmailId(Integer emailId) {
        this.emailId = emailId;
    }

    public Integer getIsReadNotice() {
        return isReadNotice;
    }

    public void setIsReadNotice(Integer isReadNotice) {
        this.isReadNotice = isReadNotice;
    }
}
