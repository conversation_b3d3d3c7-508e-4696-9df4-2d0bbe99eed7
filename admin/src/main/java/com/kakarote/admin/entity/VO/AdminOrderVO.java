package com.kakarote.admin.entity.VO;

import com.github.binarywang.wxpay.bean.order.WxPayNativeOrderResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * wwl
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminOrderVO extends WxPayNativeOrderResult{

    private String outTradeNo;

}
