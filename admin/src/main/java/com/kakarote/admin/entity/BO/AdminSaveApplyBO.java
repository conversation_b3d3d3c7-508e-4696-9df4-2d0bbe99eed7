package com.kakarote.admin.entity.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName: AdminSaveApplyBO
 * @Author: Blue
 * @Description: AdminSaveApplyBO
 * @Date: 2021/11/8 10:55
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("员工申请加入公司记录表")
public class AdminSaveApplyBO {

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "主键IDs")
    private List<Integer> ids;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "名称")
    private String realname;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "邀请ID")
    private Long inviteUserId;

    @ApiModelProperty(value = "1 审核通过 2 未审核 3 删除 4.审核拒绝")
    private Integer status;
}
