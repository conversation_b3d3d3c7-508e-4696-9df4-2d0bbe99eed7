<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.admin.mapper.AdminUserApplyMapper">

    <select id="queryUserList" resultType="com.alibaba.fastjson.JSONObject">
        select a.*,b.realname as invite_user_name,c.realname as examine_user_name,a.phone as username
        from wk_admin_user_apply as a
        left join wk_admin_user as b on a.invite_user_id = b.user_id
        left join wk_admin_user as c on a.examine_user_id = c.user_id
        where a.status != 3
        <if test="data.type == 1">
          and  a.status = 2
        </if>
        <if test="data.type != 1">
            and  a.status != 2
        </if>
    </select>
</mapper>
