<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kakarote.admin.mapper.AdminRoleMapper">
    <select id="getRoleMenu" resultType="java.lang.Long">
        select b.menu_id
        from wk_admin_role_menu as a inner join wk_admin_menu as b on a.menu_id = b.menu_id
        where a.role_id = #{roleId}
          and (b.parent_id in (SELECT menu_id FROM wk_admin_menu WHERE parent_id = #{parentId}) or b.parent_id = #{parentId})
    </select>

    <select id="queryDataType" resultType="java.lang.Integer">
        select data_type from wk_admin_role as a
        inner join wk_admin_user_role as b on a.role_id = b.role_id and b.user_id = #{userId}
        inner join wk_admin_role_menu as c on a.role_id = c.role_id and c.menu_id = #{menuId}
        ORDER BY data_type desc limit 1
    </select>
    <delete id="deleteWorkRole" >
        update `wk_work_user` set role_id = #{roleId} where role_id = #{editRoleId}
    </delete>
    <select id="queryRoleByRoleTypeAndUserId" resultType="com.kakarote.admin.entity.PO.AdminRole">
        select a.* from wk_admin_role a
                             join wk_admin_user_role b on a.role_id = b.role_id
        where a.role_type = #{type} and b.user_id = #{userId}
    </select>
    <select id="getUserRoleIds" resultType="java.lang.Long">
        SELECT
            a.id
        FROM
            wk_admin_user_role a
                LEFT JOIN wk_admin_role b ON a.role_id = b.role_id
        WHERE
            b.role_type = #{type}
          AND a.user_id = #{userId}
    </select>
</mapper>
