package com.kakarote.admin.common.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.kakarote.admin.common.TypeEnum;
import com.kakarote.admin.entity.BO.AdminDeptBO;
import com.kakarote.admin.entity.BO.AdminUserStatusBO;
import com.kakarote.admin.entity.VO.AdminUserVO;
import com.kakarote.admin.service.IAdminDeptService;
import com.kakarote.admin.service.IAdminUserService;
import com.kakarote.core.servlet.ApplicationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @创建人 xuepengfei
 * @创建时间 2022/6/14
 * @描述
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "synchronization",consumerGroup = "springboot-mq-consumer")
public class Consumer implements RocketMQListener<String> {
    @Override
    public void onMessage(String message) {
        log.info("Receive message："+message);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject= JSONObject.parseObject(message);
        }catch (Exception e){
            e.printStackTrace();
        }
        String type = jsonObject.getString("type");
        if (TypeEnum.DEPT_ADD.getValue().equals(type)){
            //添加部门
            AdminDeptBO adminDept = BeanUtil.copyProperties(BeanUtil.beanToMap(jsonObject.getJSONObject("entity")), AdminDeptBO.class);
            log.info("adminDept:{}",adminDept);
            ApplicationContextHolder.getBean(IAdminDeptService.class).addDept(adminDept);
        }else if (TypeEnum.DEPT_UPDATE.getValue().equals(type)){
            //修改部门
            AdminDeptBO adminDepts = BeanUtil.copyProperties(BeanUtil.beanToMap(jsonObject.getJSONObject("entity")), AdminDeptBO.class);
            log.info("adminDepts:{}",adminDepts);
            ApplicationContextHolder.getBean(IAdminDeptService.class).setDept(adminDepts);
        }else if (TypeEnum.DEPT_DELETE.getValue().equals(type)){
            //删除部门
            Long deptId = jsonObject.getLong("deptId");
            ApplicationContextHolder.getBean(IAdminDeptService.class).deleteDept(deptId);
        }else if (TypeEnum.USER_ADD.getValue().equals(type)){
            //添加员工
            AdminUserVO adminUserVO = BeanUtil.copyProperties(BeanUtil.beanToMap(jsonObject.getJSONObject("entity")), AdminUserVO.class);
            log.info("adminUserVO:{}",adminUserVO);
             ApplicationContextHolder.getBean(IAdminUserService.class).addUser(adminUserVO);
        }else if (TypeEnum.USER_UPDATE.getValue().equals(type)){
            //修改员工
            AdminUserVO adminUser = BeanUtil.copyProperties(BeanUtil.beanToMap(jsonObject.getJSONObject("entity")), AdminUserVO.class);
            log.info("adminUser:{}",adminUser);
            ApplicationContextHolder.getBean(IAdminUserService.class).setUser(adminUser);
        }else if (TypeEnum.USER_DELETE.getValue().equals(type)){
            //禁用员工
            AdminUserStatusBO adminUserStatusBO = BeanUtil.copyProperties(BeanUtil.beanToMap(jsonObject.getJSONObject("entity")), AdminUserStatusBO.class);
            log.info("adminUserStatusBO:{}",adminUserStatusBO);
             ApplicationContextHolder.getBean(IAdminUserService.class).setUserStatus(adminUserStatusBO);
        }

    }


}
