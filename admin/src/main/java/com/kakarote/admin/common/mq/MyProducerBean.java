package com.kakarote.admin.common.mq;

import com.aliyun.openservices.ons.api.*;

import java.util.Properties;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @ClassName MyProducerBean.java
 * @Description TODO
 * @createTime 2021-09-18
 */
public class MyProducerBean implements Producer {

    private Properties properties;
    private Producer producer;

    public MyProducerBean() {
    }

    @Override
    public void start() {
        if (null != this.properties) {
            this.producer = ONSFactory.createProducer(this.properties);
            this.producer.start();
        }
    }

    @Override
    public void updateCredential(Properties credentialProperties) {
        if (this.producer != null) {
            this.producer.updateCredential(credentialProperties);
        }

    }

    @Override
    public void shutdown() {
        if (this.producer != null) {
            this.producer.shutdown();
        }

    }

    @Override
    public SendResult send(Message message) {
        return this.producer.send(message);
    }

    @Override
    public void sendOneway(Message message) {
        this.producer.sendOneway(message);
    }

    @Override
    public void sendAsync(Message message, SendCallback sendCallback) {
        this.producer.sendAsync(message, sendCallback);
    }

    @Override
    public void setCallbackExecutor(ExecutorService callbackExecutor) {
        this.producer.setCallbackExecutor(callbackExecutor);
    }

    public Properties getProperties() {
        return this.properties;
    }

    public void setProperties(Properties properties) {
        this.properties = properties;
    }

    @Override
    public boolean isStarted() {
        return this.producer.isStarted();
    }

    @Override
    public boolean isClosed() {
        return this.producer.isClosed();
    }
}
