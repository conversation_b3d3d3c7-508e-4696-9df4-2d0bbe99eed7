package com.kakarote.admin.common;

/**
 * @创建人 xuepengfei
 * @创建时间 2022/6/14
 * @描述
 */
public enum TypeEnum {

    /**
     * 同步type类型
     */
    DEPT_ADD("DEPT_ADD"),

    DEPT_UPDATE ("DEPT_UPDATE "),

    DEPT_DELETE("DEPT_DELETE"),

    USER_ADD("USER_ADD"),

    USER_UPDATE("USER_UPDATE"),

    USER_DELETE("USER_DELETE")
    ;

    private final String value;

    private TypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static TypeEnum parse(String value) {
        for (TypeEnum typeEnum : TypeEnum.values()){
            if (typeEnum.getValue().equals(value)){
                return typeEnum;
            }
        }
        return null;
    }

}
