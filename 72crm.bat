@echo off
title ${project.artifactId}
rem -------------------------------------------------------------------------
rem
rem 使用说明：
rem
rem 1: 打包时默认使用application-test.yml配置文件，如需要更改，直接更改下面的命令即可，如 -Dspring.profiles.include=core,prod
rem 2: 此脚本会先检查服务是否已在运行，如果已运行则会先关闭再启动。
rem
rem -------------------------------------------------------------------------

setlocal

rem 定义JAR包名称
set "JAR_NAME=${project.artifactId}-${project.version}.jar"

rem 查找正在运行的服务的进程ID (PID)
echo Checking if service "%JAR_NAME%" is already running...
for /f "tokens=2" %%i in ('jps -l ^| findstr "%JAR_NAME%"') do (
    set "PID=%%i"
)

rem 如果找到了PID，则表示服务正在运行，需要先关闭
if defined PID (
    echo Service is running with PID %PID%. Attempting to stop it...
    taskkill /F /PID %PID%
    echo Service stopped successfully.
    echo.
) else (
    echo Service is not currently running.
)

set "JAVA_OPTS=-Dspring.profiles.active=core,dn"
if "${project.artifactId}" == "gateway" (
    set "JAVA_OPTS="
)

rem -Xms分配堆最小内存，默认为物理内存的1/64；-Xmx分配最大内存，默认为物理内存的1/4 如果程序会崩溃请将此值调高
echo Starting service in background...

rem --- 修改的就是下面这一行 ---
rem 使用 start /B 命令来后台启动 Java 程序，并且不创建新的命令行窗口
start "Run-%JAR_NAME%" /B javaw -Xms128m -Xmx512m %JAVA_OPTS% -jar %JAR_NAME% > ${project.artifactId}.log 2>&1

echo Service started. The command prompt is now free. Check ${project.artifactId}.log for output.

endlocal

echo.
echo Press any key to exit . . .
pause >nul
