package com.kakarote.core.servlet.upload;

import cn.hutool.core.collection.CollectionUtil;
import com.kakarote.core.common.cache.AdminCacheKey;
import com.kakarote.core.utils.BaseUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 腾讯云
 *
 * <AUTHOR>
 * @date 2020/10/16
 */
@Slf4j
public class TencentFileServiceImpl implements FileService {

    private UploadConfig.CosConfig config;

    private static final String JOIN_STR = "-";

    private COSClient clint;

    public TencentFileServiceImpl(UploadConfig.CosConfig config) {
        this.config = config;
        // 1 初始化用户身份信息（secretId, secretKey）。
        COSCredentials cred = new BasicCOSCredentials(config.getSecretId(), config.getSecretKey());
        // 2 设置 bucket 的区域。
        Region region = new Region(config.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        // 3 生成 cos 客户端。
        this.clint = new COSClient(cred, clientConfig);
    }

    /**
     * 上传文件
     *
     * @param inputStream 文件流
     * @param entity      参数对象
     * @return result
     */
    @Override
    public UploadEntity uploadFile(InputStream inputStream, UploadEntity entity) {
        String key;
        if (entity.getIsError()) {
            key = AdminCacheKey.UPLOAD_ERROR + "/" + BaseUtil.getDate() + "/" + entity.getFileId() + JOIN_STR + entity.getName();
        } else {
            key = BaseUtil.getDate() + "/" + entity.getFileId() + JOIN_STR + entity.getName();
        }
        entity.setType(UploadFileEnum.ALI_COS.getConfig());
        if ("1".equals(entity.getIsPublic())) {
            entity.setPath(config.getPublicUrl() + key);
        } else {
            entity.setPath(key);
        }
        try {
            File targetFile = new File(key);
            FileUtils.copyInputStreamToFile(inputStream, targetFile);
            PutObjectRequest putObjectRequest = new PutObjectRequest(config.getBucketName().get(entity.getIsPublic()), key, targetFile);
            clint.putObject(putObjectRequest);
            FileUtils.deleteDirectory(targetFile);
            return entity;
        } catch (IOException e) {
            log.error("生成临时文件【{}】失败！", key);
            return null;
        } finally {
            clint.shutdown();
        }
    }

    /**
     * 删除文件
     *
     * @param entity 参数对象
     * @return result
     */
    @Override
    public void deleteFile(UploadEntity entity) {
        String key = entity.getFileId() + JOIN_STR + entity.getName();
        try {
            clint.deleteObject(config.getBucketName().get(entity.getIsPublic()), key);
        } finally {
            clint.shutdown();
        }
    }

    @Override
    public void deleteFileBatch(String bucketName, List<String> keys) {
        try {
            for (String key : keys) {
                clint.deleteObject(config.getBucketName().get(bucketName), key);
            }
        } finally {
            clint.shutdown();
        }
/*
        官方提供批量删除方法，因为没有code没有测试
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName);
        ArrayList<DeleteObjectsRequest.KeyVersion> keyList = new ArrayList<DeleteObjectsRequest.KeyVersion>();
        deleteObjectsRequest.setKeys(keyList);
        try {
            for (String key : keys){
                keyList.add(new DeleteObjectsRequest.KeyVersion(key));
            }
            DeleteObjectsResult deleteObjectsResult = clint.deleteObjects(deleteObjectsRequest);
            List<DeleteObjectsResult.DeletedObject> deleteObjectResultArray = deleteObjectsResult.getDeletedObjects();
        } catch (MultiObjectDeleteException mde) {
            // 如果部分删除成功部分失败, 返回MultiObjectDeleteException
            List<DeleteObjectsResult.DeletedObject> deleteObjects = mde.getDeletedObjects();
            List<MultiObjectDeleteException.DeleteError> deleteErrors = mde.getErrors();
        } finally {
            clint.shutdown();
        }

 */
    }

    @Override
    public void deleteFileByUrl(String url) {
        String key = url.replace(config.getPublicUrl(), "");
        try {
            clint.deleteObject(config.getBucketName().get("1"), key);
        } finally {
            clint.shutdown();
        }
    }

    /**
     * 重命名文件
     *
     * @param entity   参数对象
     * @param fileName 文件名称
     * @return result
     */
    @Override
    public void renameFile(UploadEntity entity, String fileName) {
        String key = entity.getFileId() + JOIN_STR;
        clint.copyObject(config.getBucketName().get(entity.getIsPublic()), key + entity.getName(), config.getBucketName().get(entity.getIsPublic()), key + fileName);
        deleteFile(entity);
    }

    /**
     * 获取文件
     *
     * @param entity 参数对象
     * @return 文件流，可能为空
     */
    @Override
    public InputStream downFile(UploadEntity entity) {
        COSObjectInputStream cosObjectInput = null;
        try {
            // 获取下载输入流
            GetObjectRequest getObjectRequest = new GetObjectRequest(config.getBucketName().get(entity.getIsPublic()), entity.getPath());
            COSObject cosObject = clint.getObject(getObjectRequest);
            cosObjectInput = cosObject.getObjectContent();
            return cosObjectInput;
        } finally {
            clint.shutdown();
        }
    }

    @Override
    public void downFileByUrl(String url, File file) {
        String key = url.replace(config.getPublicUrl(), "");
        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(config.getBucketName().get("1"), key);
            clint.getObject(getObjectRequest, file);
        } finally {
            clint.shutdown();
        }
    }

    @Override
    public List<String> listKey(String bucketName, List<String> list) {
        List<String> keys = new ArrayList<>();
        list.forEach(li -> {
            String key = AdminCacheKey.UPLOAD_ERROR + "/" + li + "/";
            ObjectListing objectListing = clint.listObjects(config.getBucketName().get(bucketName), key);
            List<String> collect = objectListing.getObjectSummaries().stream().map(COSObjectSummary::getKey).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                clint.deleteObject(config.getBucketName().get(bucketName), key);
            } else {
                keys.addAll(collect);
            }
        });
        return keys;
    }

    @Override
    public void clearFileJob() {
        List<String> list = new ArrayList<>();
        for (int i = 1; i < 8; i++) {
            String pastDate = getPastDate(i);
            list.add(pastDate);
        }
        List<String> keys = listKey(config.getBucketName().get("0"), list);
        if (CollectionUtil.isEmpty(keys)) {
            return;
        }
        //删除
        FileServiceFactory.build().deleteFileBatch("0", keys);
    }
}
