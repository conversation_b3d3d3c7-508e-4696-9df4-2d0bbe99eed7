package com.kakarote.core.servlet;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.kakarote.core.utils.UserUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * mybatisPlus的自动注入
 */
@Component
public class BaseMetaObjectHandler implements MetaObjectHandler {

    /**
     * 创建时间
     */
    public static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 修改时间
     */
    public static final String FIELD_UPDATE_TIME = "updateTime";

    /**
     * 创建人
     */
    public static final String FIELD_CREATE_USER = "createUserId";

    /**
     * 修改人
     */
    public static final String FIELD_UPDATE_USER = "updateUserId";

    /**
     * 插入元对象字段填充（用于插入时对公共字段的填充）
     *
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime localDateTime = LocalDateTime.now();
        this.strictInsertFill(metaObject, FIELD_CREATE_TIME, LocalDateTime.class, localDateTime);
        this.strictInsertFill(metaObject, FIELD_CREATE_USER, Long.class, UserUtil.getUserId());
    }


    @Override
    public MetaObjectHandler fillStrategy(MetaObject metaObject, String fieldName, Object fieldVal) {
        setFieldValByName(fieldName, fieldVal, metaObject);
        return this;
    }

    /**
     * 更新元对象字段填充（用于更新时对公共字段的填充）
     *
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime localDateTime = LocalDateTime.now();
        this.strictUpdateFill(metaObject, FIELD_UPDATE_TIME, LocalDateTime.class, localDateTime);
        this.strictUpdateFill(metaObject, FIELD_UPDATE_USER, Long.class, UserUtil.getUserId());
    }
}
