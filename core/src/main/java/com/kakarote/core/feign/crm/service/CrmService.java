package com.kakarote.core.feign.crm.service;

import com.kakarote.core.common.Result;
import com.kakarote.core.entity.BasePage;
import com.kakarote.core.entity.CrmRoleField;
import com.kakarote.core.feign.crm.entity.*;
import com.kakarote.core.feign.oa.entity.ExamineVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(name = "crm", contextId = "")
public interface CrmService {

    /**
     * 查询客户信息
     *
     * @param ids ids
     * @return entity
     */
    @PostMapping("/crmCustomer/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryCustomerInfo(@RequestBody Collection ids);

    /**
     * 查询客户名
     * @param customerId:客户id
     * @return data
    */
	@PostMapping("/crmCustomer/queryCustomerName")
	Result<String> queryCustomerName(@RequestParam("customerId") Long customerId);

	/**
	 * 查询账单信息
	 * @param ids
	 * @return SimpleCrmEntity
	*/
    @PostMapping("/crmInvoice/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryInvoiceInfo(@RequestBody Collection ids);

    /**
     * 查询应收账单信息
     * @param ids
     * @return
    */
    @PostMapping("/crmReceivables/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryReceivablesInfo(@RequestBody Collection ids);

    /**
     * 查询返回访问信息
     * @param ids
     * @return
    */
    @PostMapping("/crmReturnVisit/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryReturnVisitInfo(@RequestBody Collection ids);


    /**
     * 查询联系人信息
     *
     * @param ids ids
     * @return entity
     */
    @PostMapping("/crmContacts/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryContactsInfo(@RequestBody Collection ids);

    /**
     * 查询项目信息
     *
     * @param ids ids
     * @return entity
     */
    @PostMapping("/crmBusiness/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryBusinessInfo(@RequestBody Collection ids);

    /**
     * 查询合同信息
     *
     * @param ids ids
     * @return entity
     */
    @PostMapping("/crmContract/querySimpleEntity")
    public Result<List<SimpleCrmEntity>> queryContractInfo(@RequestBody Collection ids);

    /**
     * 添加活动记录
     *
     * @param type
     * @param activityType   活动类型
     * @param activityTypeId 类型ID
     * @return
     */
    @PostMapping("/crmActivity/addActivity")
    Result addActivity(@RequestParam("type") Integer type, @RequestParam("activityType") Integer activityType, @RequestParam("activityTypeId") Long activityTypeId);

    /**
     * 添加活动记录 并反推关联数据
     * @param crmActivityBO:活动记录保存
     * @return data
     */
    @PostMapping("/crmActivity/addRelationActivity")
    Result addRelationActivity(@RequestBody CrmActivityBO crmActivityBO);


    /**
     * 批量更新es
     * @param id:数据id
     * @param name:数据名
     * @param type:数据类型
     * @return
    */
    @PostMapping(value = "/crmField/batchUpdateEsData")
    Result batchUpdateEsData(@RequestParam("id") String id, @RequestParam("name") String name, @RequestParam("type")String type);

    /**
     * 更新应收账单计划
     * @param
     * @return 
    */
    @PostMapping("/crmCustomerJob/updateReceivablesPlan")
    Result updateReceivablesPlan();


    /**
     * 客户流入公海
     * @param
     * @return
    */
    @PostMapping("/crmCustomerJob/putInInternational")
    Result putInInternational();


    /**
     * 根据权限查询公海
     * @param
     * @return
    */
    @PostMapping("/crmCustomerPool/queryPoolNameListByAuth")
    Result<List> queryPoolNameListByAuth() ;


    /**
     * 查询审批字段
     * @param label:标签
     * @return 
    */
    @PostMapping("/crmField/queryExamineField")
    public Result<List<ExamineField>> queryExamineField(@RequestParam("label") Integer label);

    /**
     * 查询列表页数据
     * @param search:高级筛选表
     * @return
    */
    @PostMapping("/crmCustomer/queryPageList")
    @ApiOperation("查询列表页数据")
    Result<BasePage<Map<String, Object>>> queryCustomerPageList(@RequestBody CrmSearchBO search);

    /**
     * 待审核发票
     * @param crmBackLogBO:待办事项模块查询BO
     * @return data
    */
    @PostMapping("/crmBackLog/checkInvoice")
    @ApiOperation("待审核发票")
    public Result<BasePage<Map<String, Object>>> checkInvoice(@RequestBody CrmBackLogBO crmBackLogBO);

    /**
     * 待审核回款
     * @param crmBackLogBO:待办事项模块查询BO
     * @return data
    */
    @PostMapping("/crmBackLog/checkReceivables")
    @ApiOperation("待审核回款")
    public Result<BasePage<Map<String, Object>>> checkReceivables(@RequestBody CrmBackLogBO crmBackLogBO);

    /**
     * 待审核合同
     * @param crmBackLogBO:待办事项模块查询BO
     * @return data
    */
    @PostMapping("/crmBackLog/checkContract")
    @ApiOperation("待审核合同")
    public Result<BasePage<Map<String, Object>>> checkContract(@RequestBody CrmBackLogBO crmBackLogBO);

    /**
     * 待审核办公审批
     * @param crmBackLogBO:待办事项模块查询BO
     * @return data
    */
    @PostMapping("/crmBackLog/checkOa")
    @ApiOperation("待审核办公审批")
    public Result<BasePage<ExamineVO>> checkOa(@RequestBody CrmBackLogBO crmBackLogBO);

    @ApiOperation("查询用户所属字段权限")
    @PostMapping(value = "/crmField/queryUserFieldAuth")
    public Result<List<CrmRoleField>> queryUserFieldAuth(@RequestParam Integer label, @RequestParam Integer authLevel);
}
