package com.kakarote.core.feign.work;

import com.kakarote.core.common.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "work", contextId = "taskJob")
public interface WorkService {

    /**
     * 修改任务job
     *
     * @param
     * @return
     */
    @PostMapping("/workTask/updateTaskJob")
    Result updateTaskJob();


    /**
     * 初始化work数据
     *
     * @param
     * @return
     */
    @PostMapping("/work/initWorkData")
    Result<Boolean> initWorkData();


    /**
     * 初始化work任务
     *
     * @param
     * @return
     */
    @PostMapping("/work/initWorkWorkTask")
    Result<Boolean> initWorkTask();

}
