package com.kakarote.core.common.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Objects;

/**
 * 进销存的模块枚举
 *
 * <AUTHOR>
 * @date 2021年4月1日17:17:09
 */
public enum JxcEnum {

    PRODUCT(1, "产品"),
    SUPPLIER(2, "供应商"),
    PURCHASE(3, "采购订单"),
    RETREAT(4, "采购退货单"),
    SALE(5, "销售订单"),
    SALE_RETURN(6, "销售退货单"),
    RECEIPT(7, "入库单"),
    OUTBOUND(8, "出库单"),
    PAYMENT(9, "付款单"),
    COLLECTION(10, "回款单"),
    INVENTORY(11, "盘点"),
    ALLOCATION(12, "调拨"),
    DETAILED(13, "出入库明细"),
    INVENTORY_RECEIPT(14, "盘点入库"),
    WAREHOUSE(15, "仓库"),
    WAREHOUSE_PRODUCT(16, "仓库盘点"),
    NULL(0, "NULL");

    /**
     * 类型
     */
    private final int type;

    /**
     * 名称
     */
    private final String remarks;

    JxcEnum(int type, String remarks) {
        this.type = type;
        this.remarks = remarks;
    }

    public String getTableName() {
        return name().toLowerCase();
    }

    /**
     * 获取主键ID
     * @param camelCase 是否驼峰
     * @return primaryKey
     */
    public String getPrimaryKey(boolean camelCase) {
        String name = name().toLowerCase();
        if(camelCase){
            return StrUtil.toCamelCase(name) + "Id";
        }
        return name + "_id";
    }

    public String getPrimaryKey() {
        return getPrimaryKey(true);
    }

    public static JxcEnum parse(Integer type) {
        for (JxcEnum jxcEnum : JxcEnum.values()) {
            if (Objects.equals(jxcEnum.getType(),type)) {
                return jxcEnum;
            }
        }
        return NULL;
    }

    public static JxcEnum parse(String name) {
        for (JxcEnum crmEnum : values()) {
            if (crmEnum.name().equals(name)) {
                return crmEnum;
            }
        }
        return NULL;
    }

    public int getType() {
        return type;
    }

    public String getRemarks() {
        return remarks;
    }

	public String getIndex() {
		return "jxc_" + name().toLowerCase();
	}

    public String getTable() {
        return name().toLowerCase();
    }

    public String getTableId(){
    	return StrUtil.toCamelCase(name().toLowerCase()) + "Id";
	}
}
