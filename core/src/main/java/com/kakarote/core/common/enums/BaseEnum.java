package com.kakarote.core.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;

import java.io.Serializable;
import java.util.Objects;

/**
 * 通用枚举类信息
 *
 * @param <T>
 * <AUTHOR>
 */
public interface BaseEnum<E extends Enum<E>, T extends Serializable> extends IEnum<String> {


    /**
     * 枚举的key，int或者string类型
     *
     * @return
     */
    T getKey();

    /**
     * 获取枚举值
     *
     * @return data
     */
    @Override
    String getValue();

    /**
     * 获取枚举名称
     *
     * @return name
     */
    String getName();

    /**
     * json数据的返回
     *
     * @return jsonString
     */
    String toJsonString();

    /**
     * 通用的格式化枚举工具类
     *
     * @param clazz clazz
     * @param key   枚举的key
     * @param <T>   枚举的实际类型
     * @return data
     */
    @JSONCreator
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    static <T extends BaseEnum<?, ?>> T parse(Class<T> clazz, Object key) {
        if (clazz.isEnum()) {
            T[] constants = clazz.getEnumConstants();
            for (T constant : constants) {
                if (Objects.equals(constant.getKey(), key)) {
                    return constant;
                }
            }
            return null;
        }
        return null;
    }

    /**
     * 通用的格式化枚举工具类
     *
     * @param clazz       clazz
     * @param key         枚举的key
     * @param <T>         枚举的实际类型
     * @param defaultEnum 默认枚举
     * @return data
     */
    static <T extends BaseEnum<?, ?>> T parseOrDefault(Class<T> clazz, Object key, T defaultEnum) {
        if (clazz.isEnum()) {
            T[] constants = clazz.getEnumConstants();
            for (T constant : constants) {
                if (Objects.equals(constant.getKey(), key)) {
                    return constant;
                }
            }
            return defaultEnum;
        }
        return defaultEnum;
    }
}
