package com.kakarote.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.kakarote.core.common.enums.CrmRelationTypeEnum;
import com.kakarote.core.entity.CrmRelationDTO;
import com.kakarote.core.feign.crm.entity.SimpleCrmEntity;
import com.kakarote.core.feign.crm.service.CrmService;
import com.kakarote.core.servlet.ApplicationContextHolder;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @program: wk_crm
 * @className Utils
 * @description: crmRelation工具类
 * @author: jiao sir
 * @create: 2021-11-22 11:01
 **/
public class CrmRelationUtils {

    private static final CrmRelationDTO TEMP_CRM_RELATION = new CrmRelationDTO();


    /**
     * 获取crm关系的map数据
     *
     * @param relationIdsMap type - <id,crmIds> map
     * @param ids            日志或审批的ids
     * @return java.util.Map<java.lang.Long, com.kakarote.core.entity.CrmRelationDTO>
     * <AUTHOR> sir
     * @date 2021/11/22
     */
    public static Map<Long, CrmRelationDTO> getCrmRelationMap(Map<Integer, Map<Long, Set<Long>>> relationIdsMap, Collection<Long> ids) {
        CrmService crmService = ApplicationContextHolder.getBean(CrmService.class);
        // 查询客户list
        Map<Long, SimpleCrmEntity> customerListMap = getCrmEntityMap(crmService
                .queryCustomerInfo(getAllId(relationIdsMap, CrmRelationTypeEnum.CUSTOMER.getType()))
                .getData());
        // 查询项目list
        Map<Long, SimpleCrmEntity> businessListMap = getCrmEntityMap(crmService
                .queryBusinessInfo(getAllId(relationIdsMap, CrmRelationTypeEnum.BUSINESS.getType()))
                .getData());
        // 查询联系人list
        Map<Long, SimpleCrmEntity> contactsListMap = getCrmEntityMap(crmService
                .queryContactsInfo(getAllId(relationIdsMap, CrmRelationTypeEnum.CONTACTS.getType()))
                .getData());
        // 查询合同list
        Map<Long, SimpleCrmEntity> contractListMap = getCrmEntityMap(crmService
                .queryContractInfo(getAllId(relationIdsMap, CrmRelationTypeEnum.CONTRACT.getType()))
                .getData());
        // 定义结果map
        Map<Long, CrmRelationDTO> crmRelationsMap = new HashMap<>(ids.size());
        // 遍历map处理结果
        ids.forEach(id -> {
            // 定义dto对象
            CrmRelationDTO logRelationDTO = new CrmRelationDTO();
            logRelationDTO.setCustomerList(getCrmEntity(id, relationIdsMap.get(CrmRelationTypeEnum.CUSTOMER.getType()), customerListMap));
            logRelationDTO.setBusinessList(getCrmEntity(id, relationIdsMap.get(CrmRelationTypeEnum.BUSINESS.getType()), businessListMap));
            logRelationDTO.setContactsList(getCrmEntity(id, relationIdsMap.get(CrmRelationTypeEnum.CONTACTS.getType()), contactsListMap));
            logRelationDTO.setContractList(getCrmEntity(id, relationIdsMap.get(CrmRelationTypeEnum.CONTRACT.getType()), contractListMap));
            crmRelationsMap.put(id, logRelationDTO);
        });
        return crmRelationsMap;
    }

    /**
     * 获取crm关系的数据
     *
     * @param relationIdsMap 关系idsMap
     * @param id             日志或审批的id
     * @return com.kakarote.core.entity.CrmRelationDTO
     * <AUTHOR> sir
     * @date 2021/11/22
     */
    public static CrmRelationDTO getCrmRelation(Map<Integer, Set<Long>> relationIdsMap, Long id) {
        if (CollUtil.isEmpty(relationIdsMap)) {
            return TEMP_CRM_RELATION;
        }
        Set<Long> ids = new HashSet<>(1);
        ids.add(id);
        // 定义 type - <id,crmIds> 的map
        Map<Integer, Map<Long, Set<Long>>> map = new HashMap<>(1);
        // 遍历原有的
        relationIdsMap.forEach((k, v) -> {
            // 定义 id-crmIds map
            Map<Long, Set<Long>> longSetMap = new HashMap<>(1);
            // 放入
            longSetMap.put(id, v);
            // 放入
            map.put(k, longSetMap);
        });
        Map<Long, CrmRelationDTO> crmRelationMap = getCrmRelationMap(map, ids);
        return crmRelationMap.get(id);
    }


    /**
     * 获取关联crm的数据
     *
     * @param crmRelationDTO 关系对象
     * @return java.lang.String
     * <AUTHOR> sir
     * @date 2021/11/22
     */
    public static String getRelateCrmWork(CrmRelationDTO crmRelationDTO) {
        if (Objects.isNull(crmRelationDTO)) {
            return StrUtil.EMPTY;
        }
        StringBuilder relateCrmWorkSb = new StringBuilder();
        handleExportLogCrmRelation("客户", relateCrmWorkSb, crmRelationDTO.getCustomerList());
        handleExportLogCrmRelation("联系人", relateCrmWorkSb, crmRelationDTO.getContactsList());
        handleExportLogCrmRelation("项目", relateCrmWorkSb, crmRelationDTO.getBusinessList());
        handleExportLogCrmRelation("合同", relateCrmWorkSb, crmRelationDTO.getContractList());
        return relateCrmWorkSb.toString().trim();
    }


    /**
     * 获取crm实体map
     *
     * @param entities 实例列表
     * @return java.util.Map<java.lang.String, com.kakarote.core.feign.crm.entity.SimpleCrmEntity>
     * <AUTHOR> sir
     * @date 2021/11/20
     */
    private static Map<Long, SimpleCrmEntity> getCrmEntityMap(List<SimpleCrmEntity> entities) {
        return entities.stream().collect(Collectors.toMap(SimpleCrmEntity::getId, entity -> entity));
    }


    /**
     * 获取所有id
     *
     * @param relationIdsMap ids map
     * @param type           类型
     * @return java.util.Set<java.lang.Long>
     * <AUTHOR> sir  &&  zyy
     * @date 2021/12/23
     */
    private static Set<Long> getAllId(Map<Integer, Map<Long, Set<Long>>> relationIdsMap, int type) {
        Map<Long, Set<Long>> value = relationIdsMap.get(type);
        if(CollectionUtil.isEmpty(value)){
            return new HashSet<>();
        }
        Collection<Set<Long>> values = value.values();
        Set<Long> hashSet = new HashSet<>(value.size() * values.size());
        values.forEach(hashSet::addAll);
        return hashSet;
    }


    /**
     * 获取对象列表
     *
     * @param id             id
     * @param relationIdsMap id-crmIds map
     * @param crmEntityMap   crmMap
     * @return java.util.List<com.kakarote.core.feign.crm.entity.SimpleCrmEntity>
     * <AUTHOR> sir
     * @date 2021/11/23
     */
    private static List<SimpleCrmEntity> getCrmEntity(Long id, Map<Long, Set<Long>> relationIdsMap, Map<Long, SimpleCrmEntity> crmEntityMap) {
        if (CollUtil.isNotEmpty(relationIdsMap)) {
            // 获取对应的crm-id集合
            Set<Long> crmIds = relationIdsMap.get(id);
            if (CollUtil.isNotEmpty(crmIds)) {
                // 定义列表
                List<SimpleCrmEntity> crmEntities = new ArrayList<>(crmIds.size());
                // 遍历ids
                crmIds.forEach(crmId -> {
                    // 从map中获取此id对应的对象
                    SimpleCrmEntity simpleCrmEntity = crmEntityMap.get(crmId);
                    // 判断
                    if (simpleCrmEntity != null) {
                        crmEntities.add(simpleCrmEntity);
                    }
                });
                return crmEntities;
            } else {
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }

    /***
     * 处理导入日志的crm关联
     *
     * @param crmModeName crm模块名称
     * @param sb sb
     * @param crmList crmList
     * @return void
     * <AUTHOR> sir
     * @date 2021/11/20
     */
    private static void handleExportLogCrmRelation(String crmModeName, StringBuilder sb, List<SimpleCrmEntity> crmList) {
        if (CollUtil.isNotEmpty(crmList)) {
            sb.append(crmModeName)
                    .append(" 【");
            for (int i = 0; i < crmList.size(); i++) {
                sb.append(crmList.get(i).getName())
                        .append("】");
                if (crmList.size() - 1 > i) {
                    sb.append("、【");
                } else {
                    sb.append("\n");
                }
            }
        }
    }

}
