spring:
  main:
    allow-bean-definition-overriding: true
  mvc:
    throw-exception-if-no-handler-found: true
    favicon:
      enabled: false
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      # 上传文件临时目录，需要先创建此目录
      location:
  resources:
    add-mappings: false
  jackson:
    dateFormat: yyyy-MM-dd HH:mm:ss
    timeZone: GMT+8
mybatis-plus:
  configuration:
    call-setters-on-nulls: true
  mapper-locations: classpath:/mapper/xml/*.xml

ribbon:
  okhttp:
    enabled: true
feign:
  client:
    config:
      default:
        connect-timeout: 60000
        read-timeout: 60000

  sentinel:
    enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
jetcache:
  statIntervalMinutes: 0
  areaInCacheName: false
  remote:
    default:
      type: redis
      keyConvertor: fastjson
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${spring.redis.host}
      port: ${spring.redis.port}
      password: ${spring.redis.password}
      expireAfterWriteInMillis: 1800000

crm:
  domain: lls-crm.qhhrly.cn
  upload:
    #上传类型 1 本地 2 阿里云OSS 3 腾讯云COS 4 七牛云QNC
    config: 1
    oss:
      endpoint:
      accessKeyId:
      accessKeySecret:
      publicUrl:
      bucketName:
        0:
        1:
    local:
      publicUrl: https://lls-crm.qhhrly.cn/file/
      uploadPath:
        0: D:/upload/private
        1: D:/upload/public
    cos:
      region:
      secretId:
      secretKey:
      publicUrl:
      bucketName:
        0:
        1:
    qnc:
      accessKey:
      secretKey:
      publicUrl:
      privateUrl:
      bucketName:
        0:
        1:

