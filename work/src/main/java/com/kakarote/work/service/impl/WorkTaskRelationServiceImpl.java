package com.kakarote.work.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.kakarote.core.common.enums.CrmRelationTypeEnum;
import com.kakarote.core.feign.crm.entity.ActivityContent;
import com.kakarote.core.feign.crm.entity.CrmActivityBO;
import com.kakarote.core.feign.crm.service.CrmService;
import com.kakarote.core.servlet.BaseServiceImpl;
import com.kakarote.core.utils.TagUtil;
import com.kakarote.work.entity.PO.WorkTaskRelation;
import com.kakarote.work.mapper.WorkTaskRelationMapper;
import com.kakarote.work.service.IWorkTaskRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 任务关联业务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-18
 */
@Service
public class WorkTaskRelationServiceImpl extends BaseServiceImpl<WorkTaskRelationMapper, WorkTaskRelation> implements IWorkTaskRelationService {

    /**
     * crmService
     */
    private CrmService crmService;

    @Override
    public void saveWorkTaskRelation(WorkTaskRelation workTaskRelation) {
        // 处理ids
        if (Objects.nonNull(workTaskRelation)) {
            Long taskId = workTaskRelation.getTaskId();
            // 获取关联的业务转集合//TODO 可以让前端直接传入集合
            List<WorkTaskRelation> workTaskRelations = new ArrayList<>(16);
            // 添加关系
            List<Long>  customerIds= new ArrayList<>();
            List<Long>  contactsIds= new ArrayList<>();
            List<Long>  businessIds= new ArrayList<>();
            List<Long>  contractIds= new ArrayList<>();
            handleRelation(taskId, CrmRelationTypeEnum.CUSTOMER.getType(), workTaskRelation.getCustomerIds(), workTaskRelations,customerIds);
            handleRelation(taskId, CrmRelationTypeEnum.CONTACTS.getType(), workTaskRelation.getContactsIds(), workTaskRelations,contactsIds);
            handleRelation(taskId, CrmRelationTypeEnum.BUSINESS.getType(), workTaskRelation.getBusinessIds(), workTaskRelations,businessIds);
            handleRelation(taskId, CrmRelationTypeEnum.CONTRACT.getType(), workTaskRelation.getContractIds(), workTaskRelations,contractIds);
            // 删除原有
            lambdaUpdate().eq(WorkTaskRelation::getTaskId, taskId)
                    .remove();
            if (CollUtil.isNotEmpty(workTaskRelations)) {
                // 保存
                saveBatch(workTaskRelations, workTaskRelations.size());
                // 保存活动记录

                ActivityContent activityContent = new ActivityContent();
                activityContent.setName(workTaskRelation.getName());
                activityContent.setContentType(11);
                activityContent.setOwnerUserId(workTaskRelation.getOwnerUserId());
                activityContent.setStartTime(workTaskRelation.getStartTime()==null?null: LocalDateTimeUtil.formatNormal(workTaskRelation.getStartTime().atStartOfDay()));
                activityContent.setEndTime(workTaskRelation.getEndTime()==null?null:LocalDateTimeUtil.formatNormal(workTaskRelation.getEndTime().atStartOfDay()));
                CrmActivityBO crmActivityBO = new CrmActivityBO(2, 11, taskId, activityContent);
                crmActivityBO.setCustomerIds(customerIds);
                crmActivityBO.setContactsIds(contactsIds);
                crmActivityBO.setBusinessIds(businessIds);
                crmActivityBO.setContractIds(contractIds);
                crmService.addRelationActivity(crmActivityBO);
                crmService.addActivity(2, 11, taskId);
            }
        }
    }


    /**
     * 处理crm关联
     *
     * @param taskId            id
     * @param type              类型
     * @param relationIds       关系ids
     * @param workTaskRelations 关系列表
     * <AUTHOR> sir
     * @date 2021/11/19
     */
    private void handleRelation(Long taskId, int type, String relationIds, List<WorkTaskRelation> workTaskRelations,List<Long> longs) {
        TagUtil.toLongSet(relationIds)
                .forEach(id -> {
                    WorkTaskRelation build = WorkTaskRelation
                            .builder()
                            .relationId(id)
                            .taskId(taskId)
                            .type(type)
                            .build();
                    workTaskRelations.add(build);
                    longs.add(id);
                });
    }

    @Autowired
    public void setCrmService(CrmService crmService) {
        this.crmService = crmService;
    }
}
