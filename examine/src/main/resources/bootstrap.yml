server:
  port: 0
spring:
  profiles:
    active: core,dev
  application:
    name: examine
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        prefix: examine
        namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
      discovery:
        enabled: true
        server-addr: 127.0.0.1:8848
        namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
    sentinel:
      filter:
        enabled: false
      transport:
        dashboard: 127.0.0.1:8079
      datasource:
        flow:
          server-addr: 127.0.0.1:8848
          dataId: ${spring.application.name}-flow-rules
          groupId: SENTINEL_GROUP
          rule-type: flow
          namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}_tx_group
  config:
    type: nacos
    nacos:
      namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
      serverAddr: 127.0.0.1:8848
      group: SEATA_GROUP
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 127.0.0.1:8848
      namespace: 61f7992c-53ab-442f-bf17-e399a060aee8
