package com.kakarote.examine.controller;


import cn.hutool.core.util.ObjectUtil;
import com.kakarote.core.common.ApiExplain;
import com.kakarote.core.common.Result;
import com.kakarote.core.common.enums.SystemCodeEnum;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.examine.entity.ExamineRecordReturnVO;
import com.kakarote.core.feign.examine.entity.ExamineRecordSaveBO;
import com.kakarote.examine.entity.BO.ExamineBO;
import com.kakarote.examine.entity.PO.ExamineRecordLog;
import com.kakarote.examine.entity.VO.ExamineRecordLogVO;
import com.kakarote.examine.entity.VO.ExamineRecordVO;
import com.kakarote.examine.service.IExamineRecordLogService;
import com.kakarote.examine.service.IExamineRecordService;
import com.kakarote.examine.service.IExamineService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 审核记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-19
 */
@RestController
@RequestMapping("/examineRecord")
public class ExamineRecordController {

    @Autowired
    private IExamineRecordService examineRecordService;

    @Autowired
    private IExamineRecordLogService examineRecordLogService;

    @Autowired
    private IExamineService examineService;

    @PostMapping("/addExamineRecord")
    @ApiExplain("添加审批记录")
    public Result<ExamineRecordReturnVO> addExamineRecord(@RequestBody ExamineRecordSaveBO examineRecordSaveBO) {
        ExamineRecordReturnVO examineRecordVO = examineRecordService.addExamineRecord(examineRecordSaveBO);
        return Result.ok(examineRecordVO);
    }

    @PostMapping("/auditExamine")
    @ApiOperation("进行审批")
    public Result auditExamine(@RequestBody ExamineBO examineBO) {
        examineRecordService.auditExamine(examineBO);
        return Result.ok();
    }

    @PostMapping("/batchAuditExamine")
    @ApiOperation("进行审批(待办批量)")
    public Result batchAuditExamine(@RequestBody ExamineBO examineBO) {
        // pc
        if (ObjectUtil.isNull(examineBO.getType())) {
            List<ExamineBO> examineBOS = examineBO.getExamineBOS();
            if (ObjectUtil.length(examineBOS) == 0) {
                throw new CrmException(SystemCodeEnum.SYSTEM_NO_SUCH_PARAMENT_ERROR);
            }
            for (ExamineBO bo : examineBOS) {
                examineRecordService.auditExamine(bo);
            }
        }
        // app
        else {
            examineService.batchAuditExamine(examineBO);
        }
        return Result.ok();
    }

    @PostMapping("/queryExamineRecordLog")
    @ApiOperation("获取完整审批历史记录")
    public Result<List<ExamineRecordLogVO>> queryExamineRecord(@RequestParam(value = "ownerUserId", required = false) String ownerUserId, @RequestParam("recordId") Long recordId) {
        return Result.ok(examineRecordLogService.queryExamineRecordLog(recordId, ownerUserId));
    }


    @PostMapping("/queryExamineRecordInfo")
    @ApiOperation("获取未结束审批流程的待审核人")
    public Result<ExamineRecordReturnVO> queryExamineRecordInfo(@RequestParam("recordId") Long recordId) {
        return Result.ok(examineRecordService.queryExamineRecordInfo(recordId));
    }


    @PostMapping("/queryExamineLogById")
    @ApiExplain("获取指定审批历史记录")
    public Result<ExamineRecordLog> queryExamineLogById(@RequestParam("examineLogId") Integer examineLogId) {
        return Result.ok(examineRecordLogService.getById(examineLogId));
    }

    @PostMapping("/deleteExamineRecord")
    @ApiExplain("同步删除审批数据")
    public Result<Boolean> deleteExamineRecord(@RequestParam("recordId") Long recordId) {
        return Result.ok(examineRecordService.deleteExamineRecord(recordId));
    }

    @PostMapping("/updateExamineRecord")
    @ApiExplain("同步修改审批状态")
    public Result<Boolean> updateExamineRecord(@RequestParam("recordId") Long recordId, @RequestParam("examineStatus") Integer examineStatus) {
        return Result.ok(examineRecordService.updateExamineRecord(recordId, examineStatus));
    }


    @PostMapping("/deleteExamineRecordAndLog")
    @ApiExplain("初始化用删除审批历史数据")
    public Result<Boolean> deleteExamineRecordAndLog(@RequestParam("label") Integer label) {
        return Result.ok(examineRecordService.deleteExamineRecordAndLog(label));
    }

    @PostMapping("/queryExamineRecordList")
    @ApiOperation("获取审批详情")
    public Result<ExamineRecordVO> queryExamineRecordList(@RequestParam("recordId") Long recordId, @RequestParam(value = "ownerUserId", required = false) Long ownerUserId) {
        return Result.ok(examineRecordService.queryExamineRecordList(recordId, ownerUserId));
    }


}

