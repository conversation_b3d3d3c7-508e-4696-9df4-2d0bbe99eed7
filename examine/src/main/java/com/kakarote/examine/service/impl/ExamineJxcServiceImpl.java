package com.kakarote.examine.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.kakarote.core.exception.CrmException;
import com.kakarote.core.feign.crm.entity.ExamineField;
import com.kakarote.core.feign.examine.entity.ExamineFlowConditionDataVO;
import com.kakarote.core.feign.jxc.service.JxcExamineService;
import com.kakarote.examine.constant.ExamineCodeEnum;
import com.kakarote.examine.constant.ExamineConst;
import com.kakarote.examine.service.ExamineModuleService;
import com.kakarote.examine.service.IExamineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */

@Service("examineJxcService")
public class ExamineJxcServiceImpl implements ExamineModuleService {

    @Autowired
    private JxcExamineService jxcExamineService;

    @Autowired
    private IExamineService examineService;

    @Override
    public List<ExamineField> queryExamineField(Integer label, Long categoryId) {
        int five=5;
        int six=6;
        int seven=7;
        int eight=8;
        //进销存原有接口使用的标识不一致 部分需要转换
        if (ListUtil.toList(five,six,seven,eight).contains(label)){
            label = label - 2;
        }
        List<ExamineField> examineFields = jxcExamineService.queryExamineField(label).getData();
        examineFields.forEach(examineField -> examineField.setFieldName(StrUtil.toCamelCase(examineField.getFieldName())));
        return examineFields;
    }

    @Override
    public void updateCheckStatus(Integer label, Long typeId, Integer checkStatus) {
        jxcExamineService.examine(label,checkStatus,typeId);
    }

    @Override
    public void checkStatus(Integer label, Long typeId, Integer checkStatus, Integer oldCheckStatus) {
        int four=4;
        if (checkStatus == four) {
            //当前审核已通过不可撤回
            if (oldCheckStatus == 1) {
                throw new CrmException(ExamineCodeEnum.EXAMINE_RECHECK_PASS_ERROR);
            }
        }
    }

    @Override
    public Map<String, Object> getConditionMap(Integer label, Long typeId, Long recordId) {
        Map<String, Object> map = new HashMap<>(4);
        List<String> fieldList = new ArrayList<>();
        List<ExamineFlowConditionDataVO> conditionDataVoS = examineService.previewFiledName(label,recordId,null);
        if (conditionDataVoS != null){
            fieldList = conditionDataVoS.stream().map(ExamineFlowConditionDataVO::getFieldName).collect(Collectors.toList());
            fieldList.removeIf(StrUtil::isEmpty);
        }
        if (CollUtil.isEmpty(fieldList)){
            return map;
        }
        Map<String, Object> beanMap = jxcExamineService.examineFieldDataMap(label, typeId).getData();
        fieldList.forEach(field -> map.put(field,beanMap.get(field)));
        map.put(ExamineConst.CREATE_USER_ID,beanMap.get(ExamineConst.CREATE_USER_ID));
        return map;
    }
}
