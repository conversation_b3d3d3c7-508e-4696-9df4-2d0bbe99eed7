<?xml version="1.0" encoding="UTF-8"?>
<assembly
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/2.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    <id>make-assembly</id>
    <formats>
        <format>zip</format>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.parent.basedir}</directory>
            <outputDirectory>/</outputDirectory>
            <filtered>true</filtered>
            <includes>
                <include>72crm.sh</include>
            </includes>
            <fileMode>755</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>${project.parent.basedir}</directory>
            <outputDirectory>/</outputDirectory>
            <filtered>true</filtered>
            <includes>
                <include>72crm.bat</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/config/</directory>
            <outputDirectory>config</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/public/</directory>
            <outputDirectory>public</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/lib/</directory>
            <outputDirectory>lib</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/</directory>
            <includes>
                <!--<include>${pom.artifactId}-${pom.version}.jar</include>-->
                <include>*.jar</include>
            </includes>
            <outputDirectory>/</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>